# AG Grid Quartz Theme POC

A Vue.js proof of concept demonstrating AG Grid with custom expandable rows, pinned headers, and the modern Quartz theme.

## Features

### ✅ Implemented Features

- **Quartz Theme**: Modern AG Grid theme with Inter font and custom styling
- **Pinned/Sticky Headers**: Table headers remain visible when scrolling
- **Custom Expandable Rows**: Click expand buttons to show detailed information
- **Two Table Types**: 
  - ASI Streams table
  - H.264 Streams table
- **Detail Panels**: Each expanded row shows:
  - Sources table (max 2 rows)
  - Destinations table (unlimited rows)
- **Responsive Design**: Clean, modern UI
- **Interactive Features**: 
  - Sorting on all columns
  - Filtering on name and bandwidth columns
  - Pagination (10 rows per page)
  - Row selection
  - Smooth animations

### 🎨 Theme Configuration

- **Quartz Theme**: Modern, clean AG Grid theme
- **Inter Font**: Google Font integration for professional typography
- **Browser Color Scheme**: Inherits system light/dark mode
- **Custom Header Size**: 14px for optimal readability

## Project Structure

```
src/
├── components/
│   ├── ASITable.vue          # ASI streams table with expandable rows
│   ├── H264Table.vue         # H.264 streams table with expandable rows
│   ├── DetailPanel.vue       # Reusable detail panel for Sources/Destinations
│   └── __tests__/
│       └── ASITable.spec.ts  # Unit tests
├── assets/
│   └── main.css              # Global styles
├── main.ts                   # Theme configuration and app setup
└── App.vue                   # Main application component
```

## Data Structure

### ASI & H.264 Tables
- **Parent Row**: `[id, name, bandwidth]`
- **Sources**: `[id, name, building]` (max 2 rows)
- **Destinations**: `[id, name, building]` (unlimited rows)

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm run test:unit

# Build for production
npm run build
```

### Usage

1. Open http://localhost:5173 in your browser
2. View the two tables: ASI Streams and H.264 Streams
3. Click the expand button (▶) on any row to view Sources and Destinations
4. Click again (▼) to collapse the detail view
5. Use sorting, filtering, and pagination features
6. Scroll to see sticky headers in action

## Technical Implementation

### AG Grid Configuration
- **Community Edition**: Uses only free AG Grid features
- **Custom Expandable Rows**: Custom implementation instead of enterprise master-detail
- **Pinned Columns**: Expand button and ID columns pinned to the left
- **Quartz Theme**: Modern theme with Inter font integration
- **Performance**: Optimized with virtual scrolling and pagination

### Vue 3 Features Used
- **Composition API**: `<script setup>` syntax
- **TypeScript**: Full type safety
- **Reactive Data**: `ref()` for reactive state management
- **Component Communication**: Props for parent-child data flow

### Custom Expandable Implementation
- **Expand Buttons**: Custom cell renderer with toggle functionality
- **State Management**: Reactive Set to track expanded rows
- **Detail Rendering**: Vue components rendered below grid when expanded
- **Smooth UX**: Button states update automatically

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## License

MIT License - feel free to use this POC as a starting point for your projects.
