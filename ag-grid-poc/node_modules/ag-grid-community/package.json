{"name": "ag-grid-community", "version": "33.3.2", "description": "Advanced Data Grid / Data Table supporting Javascript / Typescript / React / Angular / Vue", "main": "./dist/package/main.cjs.js", "types": "./dist/types/src/main.d.ts", "module": "./dist/package/main.esm.mjs", "exports": {".": {"import": "./dist/package/main.esm.mjs", "types": "./dist/types/src/main.d.ts", "require": "./dist/package/main.cjs.js", "default": "./dist/package/main.cjs.js"}, "./styles/ag-grid-no-native-widgets.css": "./styles/ag-grid-no-native-widgets.css", "./styles/ag-grid-no-native-widgets.min.css": "./styles/ag-grid-no-native-widgets.min.css", "./styles/ag-grid.css": "./styles/ag-grid.css", "./styles/ag-grid.min.css": "./styles/ag-grid.min.css", "./styles/ag-theme-alpine-no-font.css": "./styles/ag-theme-alpine-no-font.css", "./styles/ag-theme-alpine-no-font.min.css": "./styles/ag-theme-alpine-no-font.min.css", "./styles/ag-theme-alpine.css": "./styles/ag-theme-alpine.css", "./styles/ag-theme-alpine.min.css": "./styles/ag-theme-alpine.min.css", "./styles/ag-theme-balham-no-font.css": "./styles/ag-theme-balham-no-font.css", "./styles/ag-theme-balham-no-font.min.css": "./styles/ag-theme-balham-no-font.min.css", "./styles/ag-theme-balham.css": "./styles/ag-theme-balham.css", "./styles/ag-theme-balham.min.css": "./styles/ag-theme-balham.min.css", "./styles/ag-theme-material-no-font.css": "./styles/ag-theme-material-no-font.css", "./styles/ag-theme-material-no-font.min.css": "./styles/ag-theme-material-no-font.min.css", "./styles/ag-theme-material.css": "./styles/ag-theme-material.css", "./styles/ag-theme-material.min.css": "./styles/ag-theme-material.min.css", "./styles/ag-theme-quartz-no-font.css": "./styles/ag-theme-quartz-no-font.css", "./styles/ag-theme-quartz-no-font.min.css": "./styles/ag-theme-quartz-no-font.min.css", "./styles/ag-theme-quartz.css": "./styles/ag-theme-quartz.css", "./styles/ag-theme-quartz.min.css": "./styles/ag-theme-quartz.min.css", "./styles/agGridAlpineFont.css": "./styles/agGridAlpineFont.css", "./styles/agGridAlpineFont.min.css": "./styles/agGridAlpineFont.min.css", "./styles/agGridBalhamFont.css": "./styles/agGridBalhamFont.css", "./styles/agGridBalhamFont.min.css": "./styles/agGridBalhamFont.min.css", "./styles/agGridClassicFont.css": "./styles/agGridClassicFont.css", "./styles/agGridClassicFont.min.css": "./styles/agGridClassicFont.min.css", "./styles/agGridMaterialFont.css": "./styles/agGridMaterialFont.css", "./styles/agGridMaterialFont.min.css": "./styles/agGridMaterialFont.min.css", "./styles/agGridQuartzFont.css": "./styles/agGridQuartzFont.css", "./styles/agGridQuartzFont.min.css": "./styles/agGridQuartzFont.min.css", "./styles": "./styles/_index.scss"}, "sideEffects": ["./styles/ag-grid-no-native-widgets.css", "./styles/ag-grid-no-native-widgets.min.css", "./styles/ag-grid.css", "./styles/ag-grid.min.css", "./styles/ag-theme-alpine-no-font.css", "./styles/ag-theme-alpine-no-font.min.css", "./styles/ag-theme-alpine.css", "./styles/ag-theme-alpine.min.css", "./styles/ag-theme-balham-no-font.css", "./styles/ag-theme-balham-no-font.min.css", "./styles/ag-theme-balham.css", "./styles/ag-theme-balham.min.css", "./styles/ag-theme-material-no-font.css", "./styles/ag-theme-material-no-font.min.css", "./styles/ag-theme-material.css", "./styles/ag-theme-material.min.css", "./styles/ag-theme-quartz-no-font.css", "./styles/ag-theme-quartz-no-font.min.css", "./styles/ag-theme-quartz.css", "./styles/ag-theme-quartz.min.css", "./styles/agGridAlpineFont.css", "./styles/agGridAlpineFont.min.css", "./styles/agGridBalhamFont.css", "./styles/agGridBalhamFont.min.css", "./styles/agGridClassicFont.css", "./styles/agGridClassicFont.min.css", "./styles/agGridMaterialFont.css", "./styles/agGridMaterialFont.min.css", "./styles/agGridQuartzFont.css", "./styles/agGridQuartzFont.min.css"], "repository": {"type": "git", "url": "https://github.com/ag-grid/ag-grid.git"}, "keywords": ["ag", "ag-grid", "datagrid", "data-grid", "data grid", "datatable", "data-table", "data table", "grid", "table", "react", "table", "angular", "angular data grid", "angular table", "angular-component", "react", "react data grid", "react table", "react-component", "reactjs", "vue", "v<PERSON><PERSON><PERSON>"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ag-grid/ag-grid/issues"}, "browserslist": ["> 1%", "last 2 versions", "not ie >= 0", "not ie_mob >= 0", "not blackberry > 0"], "homepage": "https://www.ag-grid.com/", "dependencies": {"ag-charts-types": "11.3.2"}, "devDependencies": {"source-map-loader": "^5.0.0", "gulp": "^4.0.0", "gulp-replace": "^1.0.0", "gulp-rename": "^2.0.0", "ts-loader": "^9.5.1", "style-loader": "^3.3.4", "css-loader": "^6.10.0", "postcss-loader": "^8.1.0", "webpack-cli": "^5.1.4", "terser-webpack-plugin": "^5.3.10", "postcss-preset-env": "^9.5.0", "web-streams-polyfill": "^3.3.2", "blob-polyfill": "^7.0.20220408", "compression-streams-polyfill": "^0.1.7", "text-encoding-polyfill": "^0.6.7", "@types/jest": "^29.5.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.7.0"}}