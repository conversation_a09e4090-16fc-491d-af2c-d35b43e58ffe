@font-face {
  font-family: "agGrid<PERSON><PERSON><PERSON>";
  src: url(data:font/woff2;charset=utf-8;base64,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);
  font-weight: normal;
  font-style: normal;
}
.ag-theme-balham,
.ag-theme-balham-dark,
.ag-theme-balham-auto-dark {
  --ag-balham-active-color: #0091ea;
  --ag-foreground-color: #000;
  --ag-background-color: #fff;
  --ag-header-background-color: #f5f7f7;
  --ag-tooltip-background-color: #cbd0d3;
  --ag-subheader-background-color: #e2e9eb;
  --ag-control-panel-background-color: #f5f7f7;
  --ag-border-color: #bdc3c7;
  --ag-odd-row-background-color: #fcfdfe;
  --ag-row-hover-color: #ecf0f1;
  --ag-column-hover-color: #ecf0f1;
  --ag-input-border-color: #95a5a6;
  --ag-invalid-color: #e02525;
  --ag-input-disabled-background-color: #ebebeb;
  --ag-checkbox-unchecked-color: #7f8c8d;
  --ag-input-focus-border-color: #719ece;
  --ag-advanced-filter-join-pill-color: #f08e8d;
  --ag-advanced-filter-column-pill-color: #a6e194;
  --ag-advanced-filter-option-pill-color: #f3c08b;
  --ag-advanced-filter-value-pill-color: #85c0e4;
  --ag-find-match-color: var(--ag-foreground-color);
  --ag-find-match-background-color: #ffff00;
  --ag-find-active-match-color: var(--ag-foreground-color);
  --ag-find-active-match-background-color: #ffa500;
  --ag-input-focus-box-shadow: 0 0 2px 1px var(--ag-input-focus-border-color);
  --ag-range-selection-border-color: var(--ag-balham-active-color);
  --ag-checkbox-checked-color: var(--ag-balham-active-color);
  --ag-checkbox-background-color: var(--ag-background-color);
  --ag-panel-background-color: var(--ag-header-background-color);
  --ag-secondary-foreground-color: rgba(0, 0, 0, 0.54);
  --ag-disabled-foreground-color: rgba(0, 0, 0, 0.38);
  --ag-subheader-toolbar-background-color: rgba(226, 233, 235, 0.5);
  --ag-row-border-color: rgba(189, 195, 199, 0.58);
  --ag-chip-background-color: rgba(0, 0, 0, 0.1);
  --ag-range-selection-background-color: rgba(0, 145, 234, 0.2);
  --ag-range-selection-background-color-2: rgba(0, 145, 234, 0.36);
  --ag-range-selection-background-color-3: rgba(0, 145, 234, 0.49);
  --ag-range-selection-background-color-4: rgba(0, 145, 234, 0.59);
  --ag-selected-row-background-color: rgba(0, 145, 234, 0.28);
  --ag-header-column-separator-color: rgba(189, 195, 199, 0.5);
  --ag-input-disabled-border-color: rgba(149, 165, 166, 0.3);
  --ag-row-numbers-selected-color: color-mix(in srgb, transparent, var(--ag-balham-active-color) 50%);
  --ag-header-column-separator-display: block;
  --ag-header-column-separator-height: 50%;
  --ag-grid-size: 4px;
  --ag-icon-size: 16px;
  --ag-row-height: calc(var(--ag-grid-size) * 7);
  --ag-header-height: calc(var(--ag-grid-size) * 8);
  --ag-list-item-height: calc(var(--ag-grid-size) * 6);
  --ag-row-group-indent-size: calc(var(--ag-grid-size) * 3 + var(--ag-icon-size));
  --ag-cell-horizontal-padding: calc(var(--ag-grid-size) * 3);
  --ag-input-height: calc(var(--ag-grid-size) * 4);
  --ag-chart-menu-panel-width: 240px;
  --ag-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell,
      "Helvetica Neue", sans-serif;
  --ag-font-size: 12px;
  --ag-icon-font-family: agGridBalham;
  --ag-border-radius: 2px;
  --ag-checkbox-border-radius: 3px;
  --ag-card-shadow: none;
}

.ag-theme-balham-dark {
  --ag-balham-active-color: #00b0ff;
  --ag-foreground-color: #f5f5f5;
  --ag-background-color: #2d3436;
  --ag-header-background-color: #1c1c1c;
  --ag-tooltip-background-color: #1c1f20;
  --ag-subheader-background-color: #111;
  --ag-control-panel-background-color: #202020;
  --ag-border-color: #424242;
  --ag-odd-row-background-color: #262c2e;
  --ag-row-hover-color: #3d4749;
  --ag-column-hover-color: #3d4749;
  --ag-input-border-color: #f0f0f0;
  --ag-input-disabled-background-color: rgba(48, 46, 46, 0.3);
  --ag-modal-overlay-background-color: rgba(45, 52, 54, 0.66);
  --ag-checkbox-unchecked-color: #ecf0f1;
  --ag-advanced-filter-join-pill-color: #7a3a37;
  --ag-advanced-filter-column-pill-color: #355f2d;
  --ag-advanced-filter-option-pill-color: #5a3168;
  --ag-advanced-filter-value-pill-color: #374c86;
  --ag-find-match-color: var(--ag-background-color);
  --ag-find-active-match-color: var(--ag-background-color);
  --ag-secondary-foreground-color: var(--ag-foreground-color);
  --ag-disabled-foreground-color: rgba(245, 245, 245, 0.38);
  --ag-subheader-toolbar-background-color: rgba(17, 17, 17, 0.5);
  --ag-row-border-color: #5c5c5c;
  --ag-chip-background-color: rgba(245, 245, 245, 0.08);
  --ag-range-selection-background-color: rgba(0, 176, 255, 0.2);
  --ag-range-selection-background-color-2: rgba(0, 176, 255, 0.36);
  --ag-range-selection-background-color-3: rgba(0, 176, 255, 0.49);
  --ag-range-selection-background-color-4: rgba(0, 176, 255, 0.59);
  --ag-selected-row-background-color: rgba(0, 176, 255, 0.28);
  --ag-header-column-separator-color: rgba(66, 66, 66, 0.5);
  --ag-input-disabled-border-color: rgba(240, 240, 240, 0.3);
  --ag-header-foreground-color: rgba(245, 245, 245, 0.64);
  --ag-toggle-button-off-background-color: transparent;
  --ag-toggle-button-off-border-color: var(--ag-foreground-color);
  --ag-range-selection-chart-category-background-color: rgba(26, 177, 74, 0.5);
  --ag-range-selection-chart-background-color: rgba(45, 166, 255, 0.5);
  --ag-input-focus-box-shadow: 0 0 4px 1.5px var(--ag-input-focus-border-color);
  --ag-row-loading-skeleton-effect-color: rgba(202, 203, 204, 0.4);
  color-scheme: dark;
}

@media (prefers-color-scheme: dark) {
  .ag-theme-balham-auto-dark {
    --ag-balham-active-color: #00b0ff;
    --ag-foreground-color: #f5f5f5;
    --ag-background-color: #2d3436;
    --ag-header-background-color: #1c1c1c;
    --ag-tooltip-background-color: #1c1f20;
    --ag-subheader-background-color: #111;
    --ag-control-panel-background-color: #202020;
    --ag-border-color: #424242;
    --ag-odd-row-background-color: #262c2e;
    --ag-row-hover-color: #3d4749;
    --ag-column-hover-color: #3d4749;
    --ag-input-border-color: #f0f0f0;
    --ag-input-disabled-background-color: rgba(48, 46, 46, 0.3);
    --ag-modal-overlay-background-color: rgba(45, 52, 54, 0.66);
    --ag-checkbox-unchecked-color: #ecf0f1;
    --ag-advanced-filter-join-pill-color: #7a3a37;
    --ag-advanced-filter-column-pill-color: #355f2d;
    --ag-advanced-filter-option-pill-color: #5a3168;
    --ag-advanced-filter-value-pill-color: #374c86;
    --ag-find-match-color: var(--ag-background-color);
    --ag-find-active-match-color: var(--ag-background-color);
    --ag-secondary-foreground-color: var(--ag-foreground-color);
    --ag-disabled-foreground-color: rgba(245, 245, 245, 0.38);
    --ag-subheader-toolbar-background-color: rgba(17, 17, 17, 0.5);
    --ag-row-border-color: #5c5c5c;
    --ag-chip-background-color: rgba(245, 245, 245, 0.08);
    --ag-range-selection-background-color: rgba(0, 176, 255, 0.2);
    --ag-range-selection-background-color-2: rgba(0, 176, 255, 0.36);
    --ag-range-selection-background-color-3: rgba(0, 176, 255, 0.49);
    --ag-range-selection-background-color-4: rgba(0, 176, 255, 0.59);
    --ag-selected-row-background-color: rgba(0, 176, 255, 0.28);
    --ag-header-column-separator-color: rgba(66, 66, 66, 0.5);
    --ag-input-disabled-border-color: rgba(240, 240, 240, 0.3);
    --ag-header-foreground-color: rgba(245, 245, 245, 0.64);
    --ag-toggle-button-off-background-color: transparent;
    --ag-toggle-button-off-border-color: var(--ag-foreground-color);
    --ag-range-selection-chart-category-background-color: rgba(26, 177, 74, 0.5);
    --ag-range-selection-chart-background-color: rgba(45, 166, 255, 0.5);
    --ag-input-focus-box-shadow: 0 0 4px 1.5px var(--ag-input-focus-border-color);
    --ag-row-loading-skeleton-effect-color: rgba(202, 203, 204, 0.4);
    color-scheme: dark;
  }
}
.ag-theme-balham .ag-filter-toolpanel-header,
.ag-theme-balham .ag-filter-toolpanel-search,
.ag-theme-balham .ag-status-bar,
.ag-theme-balham .ag-header-row,
.ag-theme-balham .ag-row-number-cell,
.ag-theme-balham .ag-multi-filter-group-title-bar,
.ag-theme-balham-dark .ag-filter-toolpanel-header,
.ag-theme-balham-dark .ag-filter-toolpanel-search,
.ag-theme-balham-dark .ag-status-bar,
.ag-theme-balham-dark .ag-header-row,
.ag-theme-balham-dark .ag-row-number-cell,
.ag-theme-balham-dark .ag-multi-filter-group-title-bar,
.ag-theme-balham-auto-dark .ag-filter-toolpanel-header,
.ag-theme-balham-auto-dark .ag-filter-toolpanel-search,
.ag-theme-balham-auto-dark .ag-status-bar,
.ag-theme-balham-auto-dark .ag-header-row,
.ag-theme-balham-auto-dark .ag-row-number-cell,
.ag-theme-balham-auto-dark .ag-multi-filter-group-title-bar {
  font-weight: 600;
  color: var(--ag-header-foreground-color);
}
.ag-theme-balham .ag-ltr input[class^=ag-]:not([type]), .ag-theme-balham .ag-ltr input[class^=ag-][type=text], .ag-theme-balham .ag-ltr input[class^=ag-][type=number], .ag-theme-balham .ag-ltr input[class^=ag-][type=tel], .ag-theme-balham .ag-ltr input[class^=ag-][type=date], .ag-theme-balham .ag-ltr input[class^=ag-][type=datetime-local], .ag-theme-balham .ag-ltr textarea[class^=ag-], .ag-theme-balham-dark .ag-ltr input[class^=ag-]:not([type]), .ag-theme-balham-dark .ag-ltr input[class^=ag-][type=text], .ag-theme-balham-dark .ag-ltr input[class^=ag-][type=number], .ag-theme-balham-dark .ag-ltr input[class^=ag-][type=tel], .ag-theme-balham-dark .ag-ltr input[class^=ag-][type=date], .ag-theme-balham-dark .ag-ltr input[class^=ag-][type=datetime-local], .ag-theme-balham-dark .ag-ltr textarea[class^=ag-], .ag-theme-balham-auto-dark .ag-ltr input[class^=ag-]:not([type]), .ag-theme-balham-auto-dark .ag-ltr input[class^=ag-][type=text], .ag-theme-balham-auto-dark .ag-ltr input[class^=ag-][type=number], .ag-theme-balham-auto-dark .ag-ltr input[class^=ag-][type=tel], .ag-theme-balham-auto-dark .ag-ltr input[class^=ag-][type=date], .ag-theme-balham-auto-dark .ag-ltr input[class^=ag-][type=datetime-local], .ag-theme-balham-auto-dark .ag-ltr textarea[class^=ag-] {
  padding-left: var(--ag-grid-size);
}

.ag-theme-balham .ag-rtl input[class^=ag-]:not([type]), .ag-theme-balham .ag-rtl input[class^=ag-][type=text], .ag-theme-balham .ag-rtl input[class^=ag-][type=number], .ag-theme-balham .ag-rtl input[class^=ag-][type=tel], .ag-theme-balham .ag-rtl input[class^=ag-][type=date], .ag-theme-balham .ag-rtl input[class^=ag-][type=datetime-local], .ag-theme-balham .ag-rtl textarea[class^=ag-], .ag-theme-balham-dark .ag-rtl input[class^=ag-]:not([type]), .ag-theme-balham-dark .ag-rtl input[class^=ag-][type=text], .ag-theme-balham-dark .ag-rtl input[class^=ag-][type=number], .ag-theme-balham-dark .ag-rtl input[class^=ag-][type=tel], .ag-theme-balham-dark .ag-rtl input[class^=ag-][type=date], .ag-theme-balham-dark .ag-rtl input[class^=ag-][type=datetime-local], .ag-theme-balham-dark .ag-rtl textarea[class^=ag-], .ag-theme-balham-auto-dark .ag-rtl input[class^=ag-]:not([type]), .ag-theme-balham-auto-dark .ag-rtl input[class^=ag-][type=text], .ag-theme-balham-auto-dark .ag-rtl input[class^=ag-][type=number], .ag-theme-balham-auto-dark .ag-rtl input[class^=ag-][type=tel], .ag-theme-balham-auto-dark .ag-rtl input[class^=ag-][type=date], .ag-theme-balham-auto-dark .ag-rtl input[class^=ag-][type=datetime-local], .ag-theme-balham-auto-dark .ag-rtl textarea[class^=ag-] {
  padding-right: var(--ag-grid-size);
}

.ag-theme-balham .ag-column-drop-vertical-empty-message,
.ag-theme-balham .ag-status-bar,
.ag-theme-balham-dark .ag-column-drop-vertical-empty-message,
.ag-theme-balham-dark .ag-status-bar,
.ag-theme-balham-auto-dark .ag-column-drop-vertical-empty-message,
.ag-theme-balham-auto-dark .ag-status-bar {
  font-weight: 600;
  color: var(--ag-disabled-foreground-color);
}
.ag-theme-balham.ag-dnd-ghost,
.ag-theme-balham-dark.ag-dnd-ghost,
.ag-theme-balham-auto-dark.ag-dnd-ghost {
  font-size: var(--ag-font-size);
  font-weight: 600;
}
.ag-theme-balham .ag-tab,
.ag-theme-balham-dark .ag-tab,
.ag-theme-balham-auto-dark .ag-tab {
  border: 1px solid transparent;
  padding: var(--ag-grid-size) calc(var(--ag-grid-size) * 2);
  margin: var(--ag-grid-size);
  margin-bottom: -1px;
}
.ag-theme-balham .ag-tab-selected,
.ag-theme-balham-dark .ag-tab-selected,
.ag-theme-balham-auto-dark .ag-tab-selected {
  background-color: var(--ag-background-color);
  border-color: var(--ag-border-color);
  border-bottom-color: transparent;
}
.ag-theme-balham .ag-tabs-header,
.ag-theme-balham-dark .ag-tabs-header,
.ag-theme-balham-auto-dark .ag-tabs-header {
  border-bottom: 1px solid var(--ag-border-color);
}
.ag-theme-balham .ag-column-drop-cell,
.ag-theme-balham-dark .ag-column-drop-cell,
.ag-theme-balham-auto-dark .ag-column-drop-cell {
  height: calc(var(--ag-grid-size) * 6);
}
.ag-theme-balham .ag-column-drop-vertical-title,
.ag-theme-balham-dark .ag-column-drop-vertical-title,
.ag-theme-balham-auto-dark .ag-column-drop-vertical-title {
  color: var(--ag-foreground-color);
}
.ag-theme-balham .ag-column-drop-vertical-cell,
.ag-theme-balham-dark .ag-column-drop-vertical-cell,
.ag-theme-balham-auto-dark .ag-column-drop-vertical-cell {
  margin-left: calc(var(--ag-grid-size) * 2);
  margin-right: calc(var(--ag-grid-size) * 2);
}
.ag-theme-balham .ag-column-drop-vertical-cell-text,
.ag-theme-balham-dark .ag-column-drop-vertical-cell-text,
.ag-theme-balham-auto-dark .ag-column-drop-vertical-cell-text {
  margin-left: calc(var(--ag-grid-size) * 2);
}
.ag-theme-balham .ag-column-drop-vertical-icon,
.ag-theme-balham-dark .ag-column-drop-vertical-icon,
.ag-theme-balham-auto-dark .ag-column-drop-vertical-icon {
  color: var(--ag-secondary-foreground-color);
}
.ag-theme-balham .ag-ltr .ag-column-drop-vertical-empty-message, .ag-theme-balham-dark .ag-ltr .ag-column-drop-vertical-empty-message, .ag-theme-balham-auto-dark .ag-ltr .ag-column-drop-vertical-empty-message {
  padding-left: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
  padding-right: var(--ag-grid-size);
}

.ag-theme-balham .ag-rtl .ag-column-drop-vertical-empty-message, .ag-theme-balham-dark .ag-rtl .ag-column-drop-vertical-empty-message, .ag-theme-balham-auto-dark .ag-rtl .ag-column-drop-vertical-empty-message {
  padding-right: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
  padding-left: var(--ag-grid-size);
}

.ag-theme-balham .ag-column-drop-horizontal,
.ag-theme-balham-dark .ag-column-drop-horizontal,
.ag-theme-balham-auto-dark .ag-column-drop-horizontal {
  height: var(--ag-header-height);
}
.ag-theme-balham .ag-column-drop-empty,
.ag-theme-balham-dark .ag-column-drop-empty,
.ag-theme-balham-auto-dark .ag-column-drop-empty {
  color: var(--ag-disabled-foreground-color);
}
.ag-theme-balham .ag-column-drop-horizontal-cell-text,
.ag-theme-balham-dark .ag-column-drop-horizontal-cell-text,
.ag-theme-balham-auto-dark .ag-column-drop-horizontal-cell-text {
  margin-left: calc(var(--ag-grid-size) * 2);
}
.ag-theme-balham .ag-column-drop-vertical,
.ag-theme-balham-dark .ag-column-drop-vertical,
.ag-theme-balham-auto-dark .ag-column-drop-vertical {
  padding-top: calc(var(--ag-grid-size) * 2);
}
.ag-theme-balham .ag-column-select-column-readonly.ag-icon-grip,
.ag-theme-balham .ag-column-select-column-readonly .ag-icon-grip,
.ag-theme-balham-dark .ag-column-select-column-readonly.ag-icon-grip,
.ag-theme-balham-dark .ag-column-select-column-readonly .ag-icon-grip,
.ag-theme-balham-auto-dark .ag-column-select-column-readonly.ag-icon-grip,
.ag-theme-balham-auto-dark .ag-column-select-column-readonly .ag-icon-grip {
  opacity: 0.35;
}
.ag-theme-balham .ag-menu-header,
.ag-theme-balham-dark .ag-menu-header,
.ag-theme-balham-auto-dark .ag-menu-header {
  background-color: var(--ag-header-background-color);
}
.ag-theme-balham .ag-overlay-loading-center,
.ag-theme-balham-dark .ag-overlay-loading-center,
.ag-theme-balham-auto-dark .ag-overlay-loading-center {
  background-color: var(--ag-background-color);
  border: 1px solid var(--ag-border-color);
  color: var(--ag-foreground-color);
  padding: calc(var(--ag-grid-size) * 4);
}
.ag-theme-balham .ag-tooltip,
.ag-theme-balham-dark .ag-tooltip,
.ag-theme-balham-auto-dark .ag-tooltip {
  border: none;
}
.ag-theme-balham .ag-panel-title-bar-button-icon,
.ag-theme-balham-dark .ag-panel-title-bar-button-icon,
.ag-theme-balham-auto-dark .ag-panel-title-bar-button-icon {
  font-size: calc(var(--ag-icon-size) + var(--ag-grid-size));
}
.ag-theme-balham .ag-panel,
.ag-theme-balham-dark .ag-panel,
.ag-theme-balham-auto-dark .ag-panel {
  background-color: var(--ag-header-background-color);
}
.ag-theme-balham .ag-chart-data-section,
.ag-theme-balham .ag-chart-format-section,
.ag-theme-balham .ag-chart-advanced-settings-section,
.ag-theme-balham-dark .ag-chart-data-section,
.ag-theme-balham-dark .ag-chart-format-section,
.ag-theme-balham-dark .ag-chart-advanced-settings-section,
.ag-theme-balham-auto-dark .ag-chart-data-section,
.ag-theme-balham-auto-dark .ag-chart-format-section,
.ag-theme-balham-auto-dark .ag-chart-advanced-settings-section {
  padding-bottom: calc(var(--ag-grid-size) * 0.5);
}
.ag-theme-balham .ag-group-toolbar,
.ag-theme-balham-dark .ag-group-toolbar,
.ag-theme-balham-auto-dark .ag-group-toolbar {
  background-color: var(--ag-subheader-toolbar-background-color);
}
.ag-theme-balham .ag-chart-tab,
.ag-theme-balham-dark .ag-chart-tab,
.ag-theme-balham-auto-dark .ag-chart-tab {
  padding-top: calc(var(--ag-grid-size) * 0.5);
}
.ag-theme-balham .ag-charts-format-sub-level-group-item,
.ag-theme-balham-dark .ag-charts-format-sub-level-group-item,
.ag-theme-balham-auto-dark .ag-charts-format-sub-level-group-item {
  margin-bottom: calc(var(--ag-grid-size) * 1.5);
}
.ag-theme-balham .ag-filter-active .ag-icon-filter,
.ag-theme-balham-dark .ag-filter-active .ag-icon-filter,
.ag-theme-balham-auto-dark .ag-filter-active .ag-icon-filter {
  color: var(--ag-balham-active-color);
}
.ag-theme-balham .ag-color-input input[class^=ag-][type=text].ag-input-field-input,
.ag-theme-balham-dark .ag-color-input input[class^=ag-][type=text].ag-input-field-input,
.ag-theme-balham-auto-dark .ag-color-input input[class^=ag-][type=text].ag-input-field-input {
  min-height: calc(var(--ag-icon-size) + 4px);
}
.ag-theme-balham .ag-list-item-hovered::after,
.ag-theme-balham-dark .ag-list-item-hovered::after,
.ag-theme-balham-auto-dark .ag-list-item-hovered::after {
  background-color: var(--ag-balham-active-color);
}
.ag-theme-balham .ag-pill .ag-pill-button:hover,
.ag-theme-balham-dark .ag-pill .ag-pill-button:hover,
.ag-theme-balham-auto-dark .ag-pill .ag-pill-button:hover {
  color: var(--ag-balham-active-color);
}
.ag-theme-balham .ag-header-highlight-before::after,
.ag-theme-balham .ag-header-highlight-after::after,
.ag-theme-balham-dark .ag-header-highlight-before::after,
.ag-theme-balham-dark .ag-header-highlight-after::after,
.ag-theme-balham-auto-dark .ag-header-highlight-before::after,
.ag-theme-balham-auto-dark .ag-header-highlight-after::after {
  background-color: var(--ag-balham-active-color);
}
.ag-theme-balham .ag-advanced-filter-builder-item-button-disabled .ag-icon,
.ag-theme-balham .ag-disabled .ag-icon,
.ag-theme-balham .ag-column-select-column-group-readonly .ag-icon,
.ag-theme-balham [disabled] .ag-icon,
.ag-theme-balham-dark .ag-advanced-filter-builder-item-button-disabled .ag-icon,
.ag-theme-balham-dark .ag-disabled .ag-icon,
.ag-theme-balham-dark .ag-column-select-column-group-readonly .ag-icon,
.ag-theme-balham-dark [disabled] .ag-icon,
.ag-theme-balham-auto-dark .ag-advanced-filter-builder-item-button-disabled .ag-icon,
.ag-theme-balham-auto-dark .ag-disabled .ag-icon,
.ag-theme-balham-auto-dark .ag-column-select-column-group-readonly .ag-icon,
.ag-theme-balham-auto-dark [disabled] .ag-icon {
  color: var(--ag-disabled-foreground-color);
}
