@font-face {
  font-family: "agGridMaterial";
  src: url(data:font/woff2;charset=utf-8;base64,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);
  font-weight: normal;
  font-style: normal;
}
.ag-theme-material,
.ag-theme-material-dark,
.ag-theme-material-auto-dark {
  --ag-material-primary-color: #3f51b5;
  --ag-material-accent-color: #ff4081;
  --ag-foreground-color: rgba(0, 0, 0, 0.87);
  --ag-secondary-foreground-color: rgba(0, 0, 0, 0.54);
  --ag-disabled-foreground-color: rgba(0, 0, 0, 0.38);
  --ag-background-color: #fff;
  --ag-header-background-color: #fff;
  --ag-tooltip-background-color: #fff;
  --ag-subheader-background-color: #eee;
  --ag-subheader-toolbar-background-color: rgba(238, 238, 238, 0.5);
  --ag-header-cell-hover-background-color: #f2f2f2;
  --ag-chip-background-color: #e2e2e2;
  --ag-range-selection-background-color: rgba(122, 134, 203, 0.1);
  --ag-range-selection-background-color-2: rgba(122, 134, 203, 0.19);
  --ag-range-selection-background-color-3: rgba(122, 134, 203, 0.27);
  --ag-range-selection-background-color-4: rgba(122, 134, 203, 0.34);
  --ag-row-numbers-selected-color: color-mix(in srgb, transparent, var(--ag-material-accent-color) 50%);
  --ag-range-selection-highlight-color: #fce4ec;
  --ag-row-hover-color: #fafafa;
  --ag-column-hover-color: #fafafa;
  --ag-control-panel-background-color: #fafafa;
  --ag-selected-row-background-color: rgba(33, 150, 243, 0.3);
  --ag-checkbox-unchecked-color: #333;
  --ag-value-change-value-highlight-background-color: #00acc1;
  --ag-side-button-selected-background-color: transparent;
  --ag-advanced-filter-join-pill-color: #f08e8d;
  --ag-advanced-filter-column-pill-color: #a6e194;
  --ag-advanced-filter-option-pill-color: #f3c08b;
  --ag-advanced-filter-value-pill-color: #85c0e4;
  --ag-find-match-color: var(--ag-foreground-color);
  --ag-find-match-background-color: #ffff00;
  --ag-find-active-match-color: var(--ag-foreground-color);
  --ag-find-active-match-background-color: #ffa500;
  --ag-range-selection-border-color: var(--ag-material-primary-color);
  --ag-checkbox-checked-color: var(--ag-material-accent-color);
  --ag-borders: none;
  --ag-borders-critical: solid 1px;
  --ag-border-color: #e2e2e2;
  --ag-grid-size: 8px;
  --ag-icon-size: 18px;
  --ag-header-height: calc(var(--ag-grid-size) * 7);
  --ag-row-height: calc(var(--ag-grid-size) * 6);
  --ag-cell-horizontal-padding: calc(var(--ag-grid-size) * 3);
  --ag-list-item-height: calc(var(--ag-grid-size) * 4);
  --ag-row-group-indent-size: calc(var(--ag-grid-size) * 3 + var(--ag-icon-size));
  --ag-filter-tool-panel-sub-level-row-height: calc(var(--ag-grid-size) * 4);
  --ag-checkbox-border-radius: 2px;
  --ag-toggle-button-switch-border-width: 2px;
  --ag-toggle-button-height: var(--ag-icon-size);
  --ag-widget-container-horizontal-padding: calc(var(--ag-grid-size) * 1.5);
  --ag-widget-container-vertical-padding: calc(var(--ag-grid-size) * 2);
  --ag-widget-vertical-spacing: calc(var(--ag-grid-size) * 1.75);
  --ag-font-family: Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", Oxygen-Sans, Ubuntu, Cantarell,
      "Helvetica Neue", sans-serif;
  --ag-font-size: 13px;
  --ag-icon-font-family: agGridMaterial;
  --ag-selected-tab-underline-color: var(--ag-material-primary-color);
  --ag-selected-tab-underline-width: 2px;
  --ag-input-focus-border-color: var(--ag-material-primary-color);
  --ag-input-focus-box-shadow: 0 0 0 5px rgba(32, 33, 36, 0.122);
  --ag-card-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14),
      0 1px 5px 0 rgba(0, 0, 0, 0.12);
  --ag-card-radius: 2px;
  --ag-invalid-color: #e02525;
}

.ag-theme-material-dark {
  --ag-material-primary-color: #3f51b5;
  --ag-material-accent-color: #bb86fcff;
  --ag-range-selection-border-color: var(--ag-material-accent-color);
  --ag-find-match-color: var(--ag-background-color);
  --ag-find-active-match-color: var(--ag-background-color);
  --ag-background-color: #121212ff;
  --ag-foreground-color: #ffffffff;
  --ag-data-color: #f5f5f5ff;
  --ag-header-cell-hover-background-color: #000000ff;
  --ag-advanced-filter-join-pill-color: #7a3a37ff;
  --ag-advanced-filter-column-pill-color: #355f2dff;
  --ag-advanced-filter-option-pill-color: #5a3168ff;
  --ag-advanced-filter-value-pill-color: #374c86ff;
  --ag-input-disabled-border-color: #3a434eff;
  --ag-input-disabled-background-color: #68686e12;
  --ag-selected-row-background-color: #bb86fc33;
  --ag-row-hover-color: #bb86fc33;
  --ag-column-hover-color: #f5f5f50d;
  --ag-range-selection-background-color: #bb86fc1a;
  --ag-range-selection-background-color-2: #bb86fc30;
  --ag-range-selection-background-color-3: #bb86fc45;
  --ag-range-selection-background-color-4: #bb86fc57;
  --ag-border-color: #383838ff;
  --ag-secondary-border-color: #383838ff;
  --ag-header-background-color: #121212ff;
  --ag-tooltip-background-color: #212b38ff;
  --ag-odd-row-background-color: #121212ff;
  --ag-control-panel-background-color: #2c2c2cff;
  --ag-subheader-background-color: #ffffff0d;
  --ag-subheader-toolbar-background-color: #2c2c2cff;
  --ag-invalid-color: #e02525ff;
  --ag-checkbox-unchecked-color: #797e87ff;
  --ag-checkbox-background-color: #121212ff;
  --ag-secondary-foreground-color: #f5f5f5ff;
  --ag-input-border-color: #383838ff;
  --ag-input-border-color-invalid: #e02525ff;
  --ag-disabled-foreground-color: #f5f5f580;
  --ag-chip-background-color: #22262812;
  --ag-side-button-selected-background-color: #2c2c2cff;
  --ag-selected-tab-underline-color: #3f51b5ff;
  --ag-modal-overlay-background-color: #121212a8;
  --ag-value-change-delta-up-color: #43a047a8;
  --ag-value-change-delta-down-color: #e53935ff;
  --ag-menu-background-color: #2c2c2cff;
  --ag-row-loading-skeleton-effect-color: rgba(202, 203, 204, 0.4);
  color-scheme: dark;
}

@media (prefers-color-scheme: dark) {
  .ag-theme-material-auto-dark {
    --ag-material-primary-color: #3f51b5;
    --ag-material-accent-color: #bb86fcff;
    --ag-range-selection-border-color: var(--ag-material-accent-color);
    --ag-find-match-color: var(--ag-background-color);
    --ag-find-active-match-color: var(--ag-background-color);
    --ag-background-color: #121212ff;
    --ag-foreground-color: #ffffffff;
    --ag-data-color: #f5f5f5ff;
    --ag-header-cell-hover-background-color: #000000ff;
    --ag-advanced-filter-join-pill-color: #7a3a37ff;
    --ag-advanced-filter-column-pill-color: #355f2dff;
    --ag-advanced-filter-option-pill-color: #5a3168ff;
    --ag-advanced-filter-value-pill-color: #374c86ff;
    --ag-input-disabled-border-color: #3a434eff;
    --ag-input-disabled-background-color: #68686e12;
    --ag-selected-row-background-color: #bb86fc33;
    --ag-row-hover-color: #bb86fc33;
    --ag-column-hover-color: #f5f5f50d;
    --ag-range-selection-background-color: #bb86fc1a;
    --ag-range-selection-background-color-2: #bb86fc30;
    --ag-range-selection-background-color-3: #bb86fc45;
    --ag-range-selection-background-color-4: #bb86fc57;
    --ag-border-color: #383838ff;
    --ag-secondary-border-color: #383838ff;
    --ag-header-background-color: #121212ff;
    --ag-tooltip-background-color: #212b38ff;
    --ag-odd-row-background-color: #121212ff;
    --ag-control-panel-background-color: #2c2c2cff;
    --ag-subheader-background-color: #ffffff0d;
    --ag-subheader-toolbar-background-color: #2c2c2cff;
    --ag-invalid-color: #e02525ff;
    --ag-checkbox-unchecked-color: #797e87ff;
    --ag-checkbox-background-color: #121212ff;
    --ag-secondary-foreground-color: #f5f5f5ff;
    --ag-input-border-color: #383838ff;
    --ag-input-border-color-invalid: #e02525ff;
    --ag-disabled-foreground-color: #f5f5f580;
    --ag-chip-background-color: #22262812;
    --ag-side-button-selected-background-color: #2c2c2cff;
    --ag-selected-tab-underline-color: #3f51b5ff;
    --ag-modal-overlay-background-color: #121212a8;
    --ag-value-change-delta-up-color: #43a047a8;
    --ag-value-change-delta-down-color: #e53935ff;
    --ag-menu-background-color: #2c2c2cff;
    --ag-row-loading-skeleton-effect-color: rgba(202, 203, 204, 0.4);
    color-scheme: dark;
  }
}
.ag-theme-material .ag-filter-toolpanel-header,
.ag-theme-material .ag-filter-toolpanel-search,
.ag-theme-material .ag-status-bar,
.ag-theme-material .ag-header-row,
.ag-theme-material .ag-row-number-cell,
.ag-theme-material .ag-panel-title-bar-title,
.ag-theme-material .ag-multi-filter-group-title-bar,
.ag-theme-material-dark .ag-filter-toolpanel-header,
.ag-theme-material-dark .ag-filter-toolpanel-search,
.ag-theme-material-dark .ag-status-bar,
.ag-theme-material-dark .ag-header-row,
.ag-theme-material-dark .ag-row-number-cell,
.ag-theme-material-dark .ag-panel-title-bar-title,
.ag-theme-material-dark .ag-multi-filter-group-title-bar,
.ag-theme-material-auto-dark .ag-filter-toolpanel-header,
.ag-theme-material-auto-dark .ag-filter-toolpanel-search,
.ag-theme-material-auto-dark .ag-status-bar,
.ag-theme-material-auto-dark .ag-header-row,
.ag-theme-material-auto-dark .ag-row-number-cell,
.ag-theme-material-auto-dark .ag-panel-title-bar-title,
.ag-theme-material-auto-dark .ag-multi-filter-group-title-bar {
  font-size: calc(var(--ag-font-size) - 1px);
  font-weight: 600;
  color: var(--ag-header-foreground-color);
}
.ag-theme-material .ag-tab,
.ag-theme-material-dark .ag-tab,
.ag-theme-material-auto-dark .ag-tab {
  height: calc(var(--ag-grid-size) * 4.5);
  flex: 1 1 auto;
}
.ag-theme-material .ag-tabs-header,
.ag-theme-material .ag-column-drop-horizontal,
.ag-theme-material-dark .ag-tabs-header,
.ag-theme-material-dark .ag-column-drop-horizontal,
.ag-theme-material-auto-dark .ag-tabs-header,
.ag-theme-material-auto-dark .ag-column-drop-horizontal {
  background-color: var(--ag-subheader-background-color);
}
.ag-theme-material .ag-tabs-body,
.ag-theme-material-dark .ag-tabs-body,
.ag-theme-material-auto-dark .ag-tabs-body {
  padding: calc(var(--ag-grid-size) * 0.5) 0;
}
.ag-theme-material .ag-tabs-body .ag-menu-list,
.ag-theme-material-dark .ag-tabs-body .ag-menu-list,
.ag-theme-material-auto-dark .ag-tabs-body .ag-menu-list {
  padding-top: 0;
  padding-bottom: 0;
}
.ag-theme-material .ag-header-cell,
.ag-theme-material .ag-header-group-cell,
.ag-theme-material-dark .ag-header-cell,
.ag-theme-material-dark .ag-header-group-cell,
.ag-theme-material-auto-dark .ag-header-cell,
.ag-theme-material-auto-dark .ag-header-group-cell {
  transition: background-color 0.5s;
}
.ag-theme-material .ag-row-last:not(.ag-row-first) .ag-cell-inline-editing,
.ag-theme-material-dark .ag-row-last:not(.ag-row-first) .ag-cell-inline-editing,
.ag-theme-material-auto-dark .ag-row-last:not(.ag-row-first) .ag-cell-inline-editing {
  bottom: 0;
}
.ag-theme-material .ag-cell-inline-editing,
.ag-theme-material-dark .ag-cell-inline-editing,
.ag-theme-material-auto-dark .ag-cell-inline-editing {
  padding: var(--ag-grid-size);
  height: calc(var(--ag-row-height) + var(--ag-grid-size) * 3);
  border-color: var(--ag-border-color) !important;
}
.ag-theme-material .ag-has-focus .ag-cell-inline-editing,
.ag-theme-material-dark .ag-has-focus .ag-cell-inline-editing,
.ag-theme-material-auto-dark .ag-has-focus .ag-cell-inline-editing {
  border-color: var(--ag-input-focus-border-color) !important;
}
.ag-theme-material .ag-column-drop-vertical,
.ag-theme-material-dark .ag-column-drop-vertical,
.ag-theme-material-auto-dark .ag-column-drop-vertical {
  border-bottom: solid 1px;
  border-bottom-color: var(--ag-border-color);
  padding-top: var(--ag-grid-size);
}
.ag-theme-material .ag-column-drop-vertical.ag-last-column-drop,
.ag-theme-material-dark .ag-column-drop-vertical.ag-last-column-drop,
.ag-theme-material-auto-dark .ag-column-drop-vertical.ag-last-column-drop {
  border-bottom: none;
}
.ag-theme-material .ag-column-drop-vertical-cell,
.ag-theme-material-dark .ag-column-drop-vertical-cell,
.ag-theme-material-auto-dark .ag-column-drop-vertical-cell {
  margin-left: 0;
}
.ag-theme-material .ag-column-drop-vertical-empty-message,
.ag-theme-material-dark .ag-column-drop-vertical-empty-message,
.ag-theme-material-auto-dark .ag-column-drop-vertical-empty-message {
  font-size: calc(var(--ag-font-size) - 1px);
  font-weight: 600;
  color: var(--ag-disabled-foreground-color);
}
.ag-theme-material .ag-ltr .ag-column-drop-vertical-empty-message, .ag-theme-material-dark .ag-ltr .ag-column-drop-vertical-empty-message, .ag-theme-material-auto-dark .ag-ltr .ag-column-drop-vertical-empty-message {
  padding-left: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
  padding-right: var(--ag-grid-size);
}

.ag-theme-material .ag-rtl .ag-column-drop-vertical-empty-message, .ag-theme-material-dark .ag-rtl .ag-column-drop-vertical-empty-message, .ag-theme-material-auto-dark .ag-rtl .ag-column-drop-vertical-empty-message {
  padding-right: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
  padding-left: var(--ag-grid-size);
}

.ag-theme-material .ag-status-bar,
.ag-theme-material-dark .ag-status-bar,
.ag-theme-material-auto-dark .ag-status-bar {
  border: solid 1px;
  border-color: var(--ag-border-color);
}
.ag-theme-material .ag-column-panel-column-select,
.ag-theme-material-dark .ag-column-panel-column-select,
.ag-theme-material-auto-dark .ag-column-panel-column-select {
  border-top: solid 1px;
  border-top-color: var(--ag-border-color);
}
.ag-theme-material .ag-column-select,
.ag-theme-material .ag-column-select-header,
.ag-theme-material-dark .ag-column-select,
.ag-theme-material-dark .ag-column-select-header,
.ag-theme-material-auto-dark .ag-column-select,
.ag-theme-material-auto-dark .ag-column-select-header {
  border-bottom: solid 1px;
  border-bottom-color: var(--ag-border-color);
}
.ag-theme-material .ag-column-select-header,
.ag-theme-material-dark .ag-column-select-header,
.ag-theme-material-auto-dark .ag-column-select-header {
  height: var(--ag-header-height);
}
.ag-theme-material .ag-group-title-bar,
.ag-theme-material-dark .ag-group-title-bar,
.ag-theme-material-auto-dark .ag-group-title-bar {
  padding: calc(var(--ag-grid-size) * 0.75) var(--ag-grid-size);
}
.ag-theme-material .ag-charts-format-sub-level-group-title-bar,
.ag-theme-material-dark .ag-charts-format-sub-level-group-title-bar,
.ag-theme-material-auto-dark .ag-charts-format-sub-level-group-title-bar {
  padding: calc(var(--ag-grid-size) * 0.5) var(--ag-grid-size);
}
.ag-theme-material .ag-chart-data-section,
.ag-theme-material .ag-chart-format-section,
.ag-theme-material .ag-chart-advanced-settings-section,
.ag-theme-material-dark .ag-chart-data-section,
.ag-theme-material-dark .ag-chart-format-section,
.ag-theme-material-dark .ag-chart-advanced-settings-section,
.ag-theme-material-auto-dark .ag-chart-data-section,
.ag-theme-material-auto-dark .ag-chart-format-section,
.ag-theme-material-auto-dark .ag-chart-advanced-settings-section {
  padding-bottom: calc(var(--ag-grid-size) * 0.5);
}
.ag-theme-material input[class^=ag-]:not([type]),
.ag-theme-material input[class^=ag-][type=text],
.ag-theme-material input[class^=ag-][type=number],
.ag-theme-material input[class^=ag-][type=tel],
.ag-theme-material input[class^=ag-][type=date],
.ag-theme-material input[class^=ag-][type=datetime-local],
.ag-theme-material textarea[class^=ag-],
.ag-theme-material-dark input[class^=ag-]:not([type]),
.ag-theme-material-dark input[class^=ag-][type=text],
.ag-theme-material-dark input[class^=ag-][type=number],
.ag-theme-material-dark input[class^=ag-][type=tel],
.ag-theme-material-dark input[class^=ag-][type=date],
.ag-theme-material-dark input[class^=ag-][type=datetime-local],
.ag-theme-material-dark textarea[class^=ag-],
.ag-theme-material-auto-dark input[class^=ag-]:not([type]),
.ag-theme-material-auto-dark input[class^=ag-][type=text],
.ag-theme-material-auto-dark input[class^=ag-][type=number],
.ag-theme-material-auto-dark input[class^=ag-][type=tel],
.ag-theme-material-auto-dark input[class^=ag-][type=date],
.ag-theme-material-auto-dark input[class^=ag-][type=datetime-local],
.ag-theme-material-auto-dark textarea[class^=ag-] {
  background: transparent;
  color: var(--ag-foreground-color);
  font-family: inherit;
  font-size: inherit;
  padding-bottom: var(--ag-grid-size);
  border-width: 0;
  border-radius: 0;
  border-bottom: 2px solid;
  border-bottom-color: var(--ag-border-color);
}
.ag-theme-material input[class^=ag-]:not([type]):not(textarea),
.ag-theme-material input[class^=ag-][type=text]:not(textarea),
.ag-theme-material input[class^=ag-][type=number]:not(textarea),
.ag-theme-material input[class^=ag-][type=tel]:not(textarea),
.ag-theme-material input[class^=ag-][type=date]:not(textarea),
.ag-theme-material input[class^=ag-][type=datetime-local]:not(textarea),
.ag-theme-material textarea[class^=ag-]:not(textarea),
.ag-theme-material-dark input[class^=ag-]:not([type]):not(textarea),
.ag-theme-material-dark input[class^=ag-][type=text]:not(textarea),
.ag-theme-material-dark input[class^=ag-][type=number]:not(textarea),
.ag-theme-material-dark input[class^=ag-][type=tel]:not(textarea),
.ag-theme-material-dark input[class^=ag-][type=date]:not(textarea),
.ag-theme-material-dark input[class^=ag-][type=datetime-local]:not(textarea),
.ag-theme-material-dark textarea[class^=ag-]:not(textarea),
.ag-theme-material-auto-dark input[class^=ag-]:not([type]):not(textarea),
.ag-theme-material-auto-dark input[class^=ag-][type=text]:not(textarea),
.ag-theme-material-auto-dark input[class^=ag-][type=number]:not(textarea),
.ag-theme-material-auto-dark input[class^=ag-][type=tel]:not(textarea),
.ag-theme-material-auto-dark input[class^=ag-][type=date]:not(textarea),
.ag-theme-material-auto-dark input[class^=ag-][type=datetime-local]:not(textarea),
.ag-theme-material-auto-dark textarea[class^=ag-]:not(textarea) {
  height: calc(var(--ag-grid-size) * 5);
}
.ag-theme-material input[class^=ag-]:not([type]):focus,
.ag-theme-material input[class^=ag-][type=text]:focus,
.ag-theme-material input[class^=ag-][type=number]:focus,
.ag-theme-material input[class^=ag-][type=tel]:focus,
.ag-theme-material input[class^=ag-][type=date]:focus,
.ag-theme-material input[class^=ag-][type=datetime-local]:focus,
.ag-theme-material textarea[class^=ag-]:focus,
.ag-theme-material-dark input[class^=ag-]:not([type]):focus,
.ag-theme-material-dark input[class^=ag-][type=text]:focus,
.ag-theme-material-dark input[class^=ag-][type=number]:focus,
.ag-theme-material-dark input[class^=ag-][type=tel]:focus,
.ag-theme-material-dark input[class^=ag-][type=date]:focus,
.ag-theme-material-dark input[class^=ag-][type=datetime-local]:focus,
.ag-theme-material-dark textarea[class^=ag-]:focus,
.ag-theme-material-auto-dark input[class^=ag-]:not([type]):focus,
.ag-theme-material-auto-dark input[class^=ag-][type=text]:focus,
.ag-theme-material-auto-dark input[class^=ag-][type=number]:focus,
.ag-theme-material-auto-dark input[class^=ag-][type=tel]:focus,
.ag-theme-material-auto-dark input[class^=ag-][type=date]:focus,
.ag-theme-material-auto-dark input[class^=ag-][type=datetime-local]:focus,
.ag-theme-material-auto-dark textarea[class^=ag-]:focus {
  border-bottom: 2px solid;
  border-bottom-color: var(--ag-material-primary-color);
  outline: none;
  box-shadow: none;
}
.ag-theme-material input[class^=ag-]:not([type])::placeholder,
.ag-theme-material input[class^=ag-][type=text]::placeholder,
.ag-theme-material input[class^=ag-][type=number]::placeholder,
.ag-theme-material input[class^=ag-][type=tel]::placeholder,
.ag-theme-material input[class^=ag-][type=date]::placeholder,
.ag-theme-material input[class^=ag-][type=datetime-local]::placeholder,
.ag-theme-material textarea[class^=ag-]::placeholder,
.ag-theme-material-dark input[class^=ag-]:not([type])::placeholder,
.ag-theme-material-dark input[class^=ag-][type=text]::placeholder,
.ag-theme-material-dark input[class^=ag-][type=number]::placeholder,
.ag-theme-material-dark input[class^=ag-][type=tel]::placeholder,
.ag-theme-material-dark input[class^=ag-][type=date]::placeholder,
.ag-theme-material-dark input[class^=ag-][type=datetime-local]::placeholder,
.ag-theme-material-dark textarea[class^=ag-]::placeholder,
.ag-theme-material-auto-dark input[class^=ag-]:not([type])::placeholder,
.ag-theme-material-auto-dark input[class^=ag-][type=text]::placeholder,
.ag-theme-material-auto-dark input[class^=ag-][type=number]::placeholder,
.ag-theme-material-auto-dark input[class^=ag-][type=tel]::placeholder,
.ag-theme-material-auto-dark input[class^=ag-][type=date]::placeholder,
.ag-theme-material-auto-dark input[class^=ag-][type=datetime-local]::placeholder,
.ag-theme-material-auto-dark textarea[class^=ag-]::placeholder {
  color: var(--ag-disabled-foreground-color);
}
.ag-theme-material input[class^=ag-]:not([type]):disabled,
.ag-theme-material input[class^=ag-][type=text]:disabled,
.ag-theme-material input[class^=ag-][type=number]:disabled,
.ag-theme-material input[class^=ag-][type=tel]:disabled,
.ag-theme-material input[class^=ag-][type=date]:disabled,
.ag-theme-material input[class^=ag-][type=datetime-local]:disabled,
.ag-theme-material textarea[class^=ag-]:disabled,
.ag-theme-material-dark input[class^=ag-]:not([type]):disabled,
.ag-theme-material-dark input[class^=ag-][type=text]:disabled,
.ag-theme-material-dark input[class^=ag-][type=number]:disabled,
.ag-theme-material-dark input[class^=ag-][type=tel]:disabled,
.ag-theme-material-dark input[class^=ag-][type=date]:disabled,
.ag-theme-material-dark input[class^=ag-][type=datetime-local]:disabled,
.ag-theme-material-dark textarea[class^=ag-]:disabled,
.ag-theme-material-auto-dark input[class^=ag-]:not([type]):disabled,
.ag-theme-material-auto-dark input[class^=ag-][type=text]:disabled,
.ag-theme-material-auto-dark input[class^=ag-][type=number]:disabled,
.ag-theme-material-auto-dark input[class^=ag-][type=tel]:disabled,
.ag-theme-material-auto-dark input[class^=ag-][type=date]:disabled,
.ag-theme-material-auto-dark input[class^=ag-][type=datetime-local]:disabled,
.ag-theme-material-auto-dark textarea[class^=ag-]:disabled {
  border-bottom: 1px solid;
  border-bottom-color: var(--ag-border-color);
}
.ag-theme-material input[class^=ag-]:not([type]):invalid,
.ag-theme-material input[class^=ag-][type=text]:invalid,
.ag-theme-material input[class^=ag-][type=number]:invalid,
.ag-theme-material input[class^=ag-][type=tel]:invalid,
.ag-theme-material input[class^=ag-][type=date]:invalid,
.ag-theme-material input[class^=ag-][type=datetime-local]:invalid,
.ag-theme-material textarea[class^=ag-]:invalid,
.ag-theme-material-dark input[class^=ag-]:not([type]):invalid,
.ag-theme-material-dark input[class^=ag-][type=text]:invalid,
.ag-theme-material-dark input[class^=ag-][type=number]:invalid,
.ag-theme-material-dark input[class^=ag-][type=tel]:invalid,
.ag-theme-material-dark input[class^=ag-][type=date]:invalid,
.ag-theme-material-dark input[class^=ag-][type=datetime-local]:invalid,
.ag-theme-material-dark textarea[class^=ag-]:invalid,
.ag-theme-material-auto-dark input[class^=ag-]:not([type]):invalid,
.ag-theme-material-auto-dark input[class^=ag-][type=text]:invalid,
.ag-theme-material-auto-dark input[class^=ag-][type=number]:invalid,
.ag-theme-material-auto-dark input[class^=ag-][type=tel]:invalid,
.ag-theme-material-auto-dark input[class^=ag-][type=date]:invalid,
.ag-theme-material-auto-dark input[class^=ag-][type=datetime-local]:invalid,
.ag-theme-material-auto-dark textarea[class^=ag-]:invalid {
  border-width: 0;
  border-bottom: 1px solid;
  border-bottom-color: var(--ag-invalid-color);
  color: var(--ag-invalid-color);
}
.ag-theme-material .ag-standard-button,
.ag-theme-material-dark .ag-standard-button,
.ag-theme-material-auto-dark .ag-standard-button {
  appearance: none;
  background-color: transparent;
  border: 0;
  color: var(--ag-material-primary-color);
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  padding: 0;
  text-transform: uppercase;
}
.ag-theme-material .ag-standard-button:disabled,
.ag-theme-material-dark .ag-standard-button:disabled,
.ag-theme-material-auto-dark .ag-standard-button:disabled {
  color: var(--ag-disabled-foreground-color);
  background-color: var(--ag-input-disabled-background-color);
  border-color: var(--ag-input-disabled-border-color);
}
.ag-theme-material.ag-dnd-ghost,
.ag-theme-material-dark.ag-dnd-ghost,
.ag-theme-material-auto-dark.ag-dnd-ghost {
  font-size: calc(var(--ag-font-size) - 1px);
  font-weight: 600;
}
.ag-theme-material .ag-filter-toolpanel-header,
.ag-theme-material-dark .ag-filter-toolpanel-header,
.ag-theme-material-auto-dark .ag-filter-toolpanel-header {
  height: calc(var(--ag-grid-size) * 4);
}
.ag-theme-material .ag-filter-toolpanel-group-level-0-header,
.ag-theme-material-dark .ag-filter-toolpanel-group-level-0-header,
.ag-theme-material-auto-dark .ag-filter-toolpanel-group-level-0-header {
  height: calc(var(--ag-grid-size) * 7);
}
.ag-theme-material .ag-ltr .ag-filter-apply-panel-button, .ag-theme-material .ag-ltr .ag-advanced-filter-apply-button, .ag-theme-material .ag-ltr .ag-advanced-filter-builder-button, .ag-theme-material-dark .ag-ltr .ag-filter-apply-panel-button, .ag-theme-material-dark .ag-ltr .ag-advanced-filter-apply-button, .ag-theme-material-dark .ag-ltr .ag-advanced-filter-builder-button, .ag-theme-material-auto-dark .ag-ltr .ag-filter-apply-panel-button, .ag-theme-material-auto-dark .ag-ltr .ag-advanced-filter-apply-button, .ag-theme-material-auto-dark .ag-ltr .ag-advanced-filter-builder-button {
  margin-left: var(--ag-grid-size);
}

.ag-theme-material .ag-rtl .ag-filter-apply-panel-button, .ag-theme-material .ag-rtl .ag-advanced-filter-apply-button, .ag-theme-material .ag-rtl .ag-advanced-filter-builder-button, .ag-theme-material-dark .ag-rtl .ag-filter-apply-panel-button, .ag-theme-material-dark .ag-rtl .ag-advanced-filter-apply-button, .ag-theme-material-dark .ag-rtl .ag-advanced-filter-builder-button, .ag-theme-material-auto-dark .ag-rtl .ag-filter-apply-panel-button, .ag-theme-material-auto-dark .ag-rtl .ag-advanced-filter-apply-button, .ag-theme-material-auto-dark .ag-rtl .ag-advanced-filter-builder-button {
  margin-right: var(--ag-grid-size);
}

.ag-theme-material .ag-layout-auto-height .ag-center-cols-viewport,
.ag-theme-material .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-material .ag-layout-print .ag-center-cols-viewport,
.ag-theme-material .ag-layout-print .ag-center-cols-container,
.ag-theme-material-dark .ag-layout-auto-height .ag-center-cols-viewport,
.ag-theme-material-dark .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-material-dark .ag-layout-print .ag-center-cols-viewport,
.ag-theme-material-dark .ag-layout-print .ag-center-cols-container,
.ag-theme-material-auto-dark .ag-layout-auto-height .ag-center-cols-viewport,
.ag-theme-material-auto-dark .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-material-auto-dark .ag-layout-print .ag-center-cols-viewport,
.ag-theme-material-auto-dark .ag-layout-print .ag-center-cols-container {
  min-height: 150px;
}
.ag-theme-material .ag-picker-field-wrapper:focus-within,
.ag-theme-material-dark .ag-picker-field-wrapper:focus-within,
.ag-theme-material-auto-dark .ag-picker-field-wrapper:focus-within {
  box-shadow: 0 0 0 1px var(--ag-material-primary-color);
}
.ag-theme-material .ag-rich-select-list,
.ag-theme-material-dark .ag-rich-select-list,
.ag-theme-material-auto-dark .ag-rich-select-list {
  box-shadow: rgba(0, 0, 0, 0.2) 0px 5px 5px -3px, rgba(0, 0, 0, 0.14) 0px 8px 10px 1px, rgba(0, 0, 0, 0.12) 0px 3px 14px 2px;
}
.ag-theme-material .ag-advanced-filter-builder-button-label,
.ag-theme-material-dark .ag-advanced-filter-builder-button-label,
.ag-theme-material-auto-dark .ag-advanced-filter-builder-button-label {
  text-transform: uppercase;
}
.ag-theme-material .ag-filter-active .ag-icon-filter,
.ag-theme-material-dark .ag-filter-active .ag-icon-filter,
.ag-theme-material-auto-dark .ag-filter-active .ag-icon-filter {
  color: var(--ag-material-accent-color);
}
.ag-theme-material .ag-list-item-hovered::after,
.ag-theme-material-dark .ag-list-item-hovered::after,
.ag-theme-material-auto-dark .ag-list-item-hovered::after {
  background-color: var(--ag-material-primary-color);
}
.ag-theme-material .ag-pill .ag-pill-button:hover,
.ag-theme-material-dark .ag-pill .ag-pill-button:hover,
.ag-theme-material-auto-dark .ag-pill .ag-pill-button:hover {
  color: var(--ag-material-primary-color);
}
.ag-theme-material .ag-header-highlight-before::after,
.ag-theme-material .ag-header-highlight-after::after,
.ag-theme-material-dark .ag-header-highlight-before::after,
.ag-theme-material-dark .ag-header-highlight-after::after,
.ag-theme-material-auto-dark .ag-header-highlight-before::after,
.ag-theme-material-auto-dark .ag-header-highlight-after::after {
  background-color: var(--ag-material-primary-color);
}
.ag-theme-material .ag-advanced-filter-builder-item-button-disabled .ag-icon,
.ag-theme-material .ag-disabled .ag-icon,
.ag-theme-material .ag-column-select-column-group-readonly .ag-icon,
.ag-theme-material [disabled] .ag-icon,
.ag-theme-material-dark .ag-advanced-filter-builder-item-button-disabled .ag-icon,
.ag-theme-material-dark .ag-disabled .ag-icon,
.ag-theme-material-dark .ag-column-select-column-group-readonly .ag-icon,
.ag-theme-material-dark [disabled] .ag-icon,
.ag-theme-material-auto-dark .ag-advanced-filter-builder-item-button-disabled .ag-icon,
.ag-theme-material-auto-dark .ag-disabled .ag-icon,
.ag-theme-material-auto-dark .ag-column-select-column-group-readonly .ag-icon,
.ag-theme-material-auto-dark [disabled] .ag-icon {
  color: var(--ag-disabled-foreground-color);
}
