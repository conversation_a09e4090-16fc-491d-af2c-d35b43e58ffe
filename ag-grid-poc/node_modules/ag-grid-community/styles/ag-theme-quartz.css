@font-face {
  font-family: "agGridQuartz";
  src: url(data:font/woff2;charset=utf-8;base64,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);
  font-weight: normal;
  font-style: normal;
}
.ag-theme-quartz,
.ag-theme-quartz-dark,
.ag-theme-quartz-auto-dark {
  --ag-active-color: #2196f3;
  --ag-background-color: #fff;
  --ag-foreground-color: #181d1f;
  --ag-border-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 15%);
  --ag-secondary-border-color: var(--ag-border-color);
  --ag-header-background-color: color-mix(in srgb, var(--ag-background-color), var(--ag-foreground-color) 2%);
  --ag-tooltip-background-color: var(--ag-header-background-color);
  --ag-control-panel-background-color: var(--ag-header-background-color);
  --ag-subheader-background-color: transparent;
  --ag-invalid-color: #e02525;
  --ag-checkbox-unchecked-color: color-mix(in srgb, var(--ag-background-color), var(--ag-foreground-color) 30%);
  --ag-advanced-filter-join-pill-color: #f08e8d;
  --ag-advanced-filter-column-pill-color: #a6e194;
  --ag-advanced-filter-option-pill-color: #f3c08b;
  --ag-advanced-filter-value-pill-color: #85c0e4;
  --ag-header-column-resize-handle-color: var(--ag-secondary-border-color);
  --ag-icon-font-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 90%);
  --ag-find-match-color: var(--ag-foreground-color);
  --ag-find-match-background-color: #ffff00;
  --ag-find-active-match-color: var(--ag-foreground-color);
  --ag-find-active-match-background-color: #ffa500;
  --ag-panel-background-color: color-mix(in srgb, var(--ag-background-color), var(--ag-foreground-color) 3%);
  --ag-panel-border-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 20%);
  --ag-menu-background-color: color-mix(in srgb, var(--ag-background-color), var(--ag-foreground-color) 3%);
  --ag-menu-border-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 20%);
  --ag-selected-row-background-color: color-mix(in srgb, transparent, var(--ag-active-color) 8%);
  --ag-row-hover-color: color-mix(in srgb, transparent, var(--ag-active-color) 12%);
  --ag-column-hover-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 5%);
  --ag-input-focus-border-color: var(--ag-active-color);
  --ag-input-focus-box-shadow: 0 0 0 3px color-mix(in srgb, transparent, var(--ag-input-focus-border-color) 47%);
  --ag-range-selection-background-color: color-mix(in srgb, transparent, var(--ag-active-color) 20%);
  --ag-range-selection-background-color-2: color-mix(in srgb, transparent, var(--ag-active-color) 36%);
  --ag-range-selection-background-color-3: color-mix(in srgb, transparent, var(--ag-active-color) 49%);
  --ag-range-selection-background-color-4: color-mix(in srgb, transparent, var(--ag-active-color) 59%);
  --ag-row-numbers-selected-color: color-mix(in srgb, transparent, var(--ag-active-color) 50%);
  --ag-checkbox-background-color: var(--ag-background-color);
  --ag-checkbox-checked-color: var(--ag-active-color);
  --ag-range-selection-border-color: var(--ag-active-color);
  --ag-secondary-foreground-color: var(--ag-foreground-color);
  --ag-input-border-color: var(--ag-border-color);
  --ag-input-border-color-invalid: var(--ag-invalid-color);
  --ag-disabled-foreground-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 50%);
  --ag-chip-background-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 7%);
  --ag-chip-border-color: color-mix(in srgb, var(--ag-header-background-color), var(--ag-foreground-color) 13%);
  --ag-input-disabled-border-color: var(--ag-border-color);
  --ag-input-disabled-background-color: color-mix(in srgb, var(--ag-background-color), var(--ag-foreground-color) 6%);
  --ag-modal-overlay-background-color: color-mix(in srgb, transparent, var(--ag-background-color) 66%);
  --ag-chart-menu-label-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 80%);
  --ag-chart-menu-pill-select-button-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 70%);
  --ag-borders: solid 1px;
  --ag-border-radius: 4px;
  --ag-wrapper-border-radius: 8px;
  --ag-borders-side-button: none;
  --ag-side-button-selected-background-color: transparent;
  --ag-header-column-resize-handle-display: block;
  --ag-header-column-resize-handle-width: 2px;
  --ag-header-column-resize-handle-height: 30%;
  --ag-grid-size: 8px;
  --ag-icon-size: 16px;
  --ag-header-height: calc(var(--ag-font-size) + var(--ag-grid-size) * 4.25);
  --ag-row-height: calc(var(--ag-font-size) + var(--ag-grid-size) * 3.5);
  --ag-list-item-height: calc(
      var(--ag-icon-size) + var(--ag-widget-vertical-spacing)
  );
  --ag-column-select-indent-size: var(--ag-icon-size);
  --ag-set-filter-indent-size: var(--ag-icon-size);
  --ag-filter-tool-panel-group-indent: var(--ag-grid-size);
  --ag-advanced-filter-builder-indent-size: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
  --ag-cell-horizontal-padding: calc(var(--ag-grid-size) * 2);
  --ag-cell-widget-spacing: calc(var(--ag-grid-size) * 1.5);
  --ag-widget-container-vertical-padding: calc(var(--ag-grid-size) * 1.5);
  --ag-widget-container-horizontal-padding: calc(var(--ag-grid-size) * 1.5);
  --ag-widget-horizontal-spacing: calc(var(--ag-grid-size) * 1.5);
  --ag-widget-vertical-spacing: calc(var(--ag-grid-size) * 1);
  --ag-toggle-button-height: 18px;
  --ag-toggle-button-width: 28px;
  --ag-toggle-button-border-width: 2px;
  --ag-font-family: "IBM Plex Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu,
      Cantarell, "Helvetica Neue", sans-serif;
  --ag-font-size: 14px;
  --ag-icon-font-family: agGridQuartz;
  --ag-tab-min-width: 290px;
  --ag-chart-menu-panel-width: 260px;
  --ag-card-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
  --ag-popup-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.15);
  --ag-side-bar-panel-width: 250px;
}

.ag-theme-quartz-dark {
  --ag-background-color: color-mix(in srgb, #fff, #182230 97%);
  --ag-foreground-color: #fff;
  --ag-border-color: rgba(255, 255, 255, 0.16);
  --ag-secondary-border-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 10%);
  --ag-header-background-color: color-mix(in srgb, #fff, #182230 93%);
  --ag-tooltip-background-color: color-mix(in srgb, #fff, #182230 96%);
  --ag-control-panel-background-color: color-mix(in srgb, #fff, #182230 93%);
  --ag-input-disabled-background-color: #68686e12;
  --ag-card-shadow: 0 1px 20px 1px black;
  --ag-input-border-color: var(--ag-border-color);
  --ag-input-disabled-border-color: rgba(255, 255, 255, 0.07);
  --ag-checkbox-unchecked-color: color-mix(in srgb, var(--ag-background-color), var(--ag-foreground-color) 40%);
  --ag-row-hover-color: color-mix(in srgb, transparent, var(--ag-active-color) 20%);
  --ag-selected-row-background-color: var(--ag-row-hover-color);
  --ag-panel-background-color: color-mix(in srgb, var(--ag-background-color), var(--ag-foreground-color) 10%);
  --ag-panel-border-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 10%);
  --ag-menu-background-color: color-mix(in srgb, var(--ag-background-color), var(--ag-foreground-color) 10%);
  --ag-menu-border-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 10%);
  --ag-advanced-filter-join-pill-color: #7a3a37;
  --ag-advanced-filter-column-pill-color: #355f2d;
  --ag-advanced-filter-option-pill-color: #5a3168;
  --ag-advanced-filter-value-pill-color: #374c86;
  --ag-find-match-color: var(--ag-background-color);
  --ag-find-active-match-color: var(--ag-background-color);
  --ag-popup-shadow: 0 0px 20px rgba(0, 0, 0, 0.3);
  --ag-row-loading-skeleton-effect-color: rgba(202, 203, 204, 0.4);
  color-scheme: dark;
}

@media (prefers-color-scheme: dark) {
  .ag-theme-quartz-auto-dark {
    --ag-background-color: color-mix(in srgb, #fff, #182230 97%);
    --ag-foreground-color: #fff;
    --ag-border-color: rgba(255, 255, 255, 0.16);
    --ag-secondary-border-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 10%);
    --ag-header-background-color: color-mix(in srgb, #fff, #182230 93%);
    --ag-tooltip-background-color: color-mix(in srgb, #fff, #182230 96%);
    --ag-control-panel-background-color: color-mix(in srgb, #fff, #182230 93%);
    --ag-input-disabled-background-color: #68686e12;
    --ag-card-shadow: 0 1px 20px 1px black;
    --ag-input-border-color: var(--ag-border-color);
    --ag-input-disabled-border-color: rgba(255, 255, 255, 0.07);
    --ag-checkbox-unchecked-color: color-mix(in srgb, var(--ag-background-color), var(--ag-foreground-color) 40%);
    --ag-row-hover-color: color-mix(in srgb, transparent, var(--ag-active-color) 20%);
    --ag-selected-row-background-color: var(--ag-row-hover-color);
    --ag-panel-background-color: color-mix(in srgb, var(--ag-background-color), var(--ag-foreground-color) 10%);
    --ag-panel-border-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 10%);
    --ag-menu-background-color: color-mix(in srgb, var(--ag-background-color), var(--ag-foreground-color) 10%);
    --ag-menu-border-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 10%);
    --ag-advanced-filter-join-pill-color: #7a3a37;
    --ag-advanced-filter-column-pill-color: #355f2d;
    --ag-advanced-filter-option-pill-color: #5a3168;
    --ag-advanced-filter-value-pill-color: #374c86;
    --ag-find-match-color: var(--ag-background-color);
    --ag-find-active-match-color: var(--ag-background-color);
    --ag-popup-shadow: 0 0px 20px rgba(0, 0, 0, 0.3);
    --ag-row-loading-skeleton-effect-color: rgba(202, 203, 204, 0.4);
    color-scheme: dark;
  }
}
.ag-theme-quartz .ag-filter-toolpanel-header,
.ag-theme-quartz .ag-filter-toolpanel-search,
.ag-theme-quartz .ag-status-bar,
.ag-theme-quartz .ag-header-row,
.ag-theme-quartz .ag-row-number-cell,
.ag-theme-quartz .ag-panel-title-bar-title,
.ag-theme-quartz .ag-multi-filter-group-title-bar,
.ag-theme-quartz-dark .ag-filter-toolpanel-header,
.ag-theme-quartz-dark .ag-filter-toolpanel-search,
.ag-theme-quartz-dark .ag-status-bar,
.ag-theme-quartz-dark .ag-header-row,
.ag-theme-quartz-dark .ag-row-number-cell,
.ag-theme-quartz-dark .ag-panel-title-bar-title,
.ag-theme-quartz-dark .ag-multi-filter-group-title-bar,
.ag-theme-quartz-auto-dark .ag-filter-toolpanel-header,
.ag-theme-quartz-auto-dark .ag-filter-toolpanel-search,
.ag-theme-quartz-auto-dark .ag-status-bar,
.ag-theme-quartz-auto-dark .ag-header-row,
.ag-theme-quartz-auto-dark .ag-row-number-cell,
.ag-theme-quartz-auto-dark .ag-panel-title-bar-title,
.ag-theme-quartz-auto-dark .ag-multi-filter-group-title-bar {
  font-weight: 500;
  color: var(--ag-header-foreground-color);
}
.ag-theme-quartz input[class^=ag-]:not([type]),
.ag-theme-quartz input[class^=ag-][type=text],
.ag-theme-quartz input[class^=ag-][type=number],
.ag-theme-quartz input[class^=ag-][type=tel],
.ag-theme-quartz input[class^=ag-][type=date],
.ag-theme-quartz input[class^=ag-][type=datetime-local],
.ag-theme-quartz textarea[class^=ag-],
.ag-theme-quartz-dark input[class^=ag-]:not([type]),
.ag-theme-quartz-dark input[class^=ag-][type=text],
.ag-theme-quartz-dark input[class^=ag-][type=number],
.ag-theme-quartz-dark input[class^=ag-][type=tel],
.ag-theme-quartz-dark input[class^=ag-][type=date],
.ag-theme-quartz-dark input[class^=ag-][type=datetime-local],
.ag-theme-quartz-dark textarea[class^=ag-],
.ag-theme-quartz-auto-dark input[class^=ag-]:not([type]),
.ag-theme-quartz-auto-dark input[class^=ag-][type=text],
.ag-theme-quartz-auto-dark input[class^=ag-][type=number],
.ag-theme-quartz-auto-dark input[class^=ag-][type=tel],
.ag-theme-quartz-auto-dark input[class^=ag-][type=date],
.ag-theme-quartz-auto-dark input[class^=ag-][type=datetime-local],
.ag-theme-quartz-auto-dark textarea[class^=ag-] {
  min-height: calc(var(--ag-grid-size) * 4);
  border-radius: var(--ag-border-radius);
}
.ag-theme-quartz .ag-ltr input[class^=ag-]:not([type]), .ag-theme-quartz .ag-ltr input[class^=ag-][type=text], .ag-theme-quartz .ag-ltr input[class^=ag-][type=number], .ag-theme-quartz .ag-ltr input[class^=ag-][type=tel], .ag-theme-quartz .ag-ltr input[class^=ag-][type=date], .ag-theme-quartz .ag-ltr input[class^=ag-][type=datetime-local], .ag-theme-quartz .ag-ltr textarea[class^=ag-], .ag-theme-quartz-dark .ag-ltr input[class^=ag-]:not([type]), .ag-theme-quartz-dark .ag-ltr input[class^=ag-][type=text], .ag-theme-quartz-dark .ag-ltr input[class^=ag-][type=number], .ag-theme-quartz-dark .ag-ltr input[class^=ag-][type=tel], .ag-theme-quartz-dark .ag-ltr input[class^=ag-][type=date], .ag-theme-quartz-dark .ag-ltr input[class^=ag-][type=datetime-local], .ag-theme-quartz-dark .ag-ltr textarea[class^=ag-], .ag-theme-quartz-auto-dark .ag-ltr input[class^=ag-]:not([type]), .ag-theme-quartz-auto-dark .ag-ltr input[class^=ag-][type=text], .ag-theme-quartz-auto-dark .ag-ltr input[class^=ag-][type=number], .ag-theme-quartz-auto-dark .ag-ltr input[class^=ag-][type=tel], .ag-theme-quartz-auto-dark .ag-ltr input[class^=ag-][type=date], .ag-theme-quartz-auto-dark .ag-ltr input[class^=ag-][type=datetime-local], .ag-theme-quartz-auto-dark .ag-ltr textarea[class^=ag-] {
  padding-left: var(--ag-grid-size);
}

.ag-theme-quartz .ag-rtl input[class^=ag-]:not([type]), .ag-theme-quartz .ag-rtl input[class^=ag-][type=text], .ag-theme-quartz .ag-rtl input[class^=ag-][type=number], .ag-theme-quartz .ag-rtl input[class^=ag-][type=tel], .ag-theme-quartz .ag-rtl input[class^=ag-][type=date], .ag-theme-quartz .ag-rtl input[class^=ag-][type=datetime-local], .ag-theme-quartz .ag-rtl textarea[class^=ag-], .ag-theme-quartz-dark .ag-rtl input[class^=ag-]:not([type]), .ag-theme-quartz-dark .ag-rtl input[class^=ag-][type=text], .ag-theme-quartz-dark .ag-rtl input[class^=ag-][type=number], .ag-theme-quartz-dark .ag-rtl input[class^=ag-][type=tel], .ag-theme-quartz-dark .ag-rtl input[class^=ag-][type=date], .ag-theme-quartz-dark .ag-rtl input[class^=ag-][type=datetime-local], .ag-theme-quartz-dark .ag-rtl textarea[class^=ag-], .ag-theme-quartz-auto-dark .ag-rtl input[class^=ag-]:not([type]), .ag-theme-quartz-auto-dark .ag-rtl input[class^=ag-][type=text], .ag-theme-quartz-auto-dark .ag-rtl input[class^=ag-][type=number], .ag-theme-quartz-auto-dark .ag-rtl input[class^=ag-][type=tel], .ag-theme-quartz-auto-dark .ag-rtl input[class^=ag-][type=date], .ag-theme-quartz-auto-dark .ag-rtl input[class^=ag-][type=datetime-local], .ag-theme-quartz-auto-dark .ag-rtl textarea[class^=ag-] {
  padding-right: var(--ag-grid-size);
}

.ag-theme-quartz .ag-picker-field-wrapper,
.ag-theme-quartz-dark .ag-picker-field-wrapper,
.ag-theme-quartz-auto-dark .ag-picker-field-wrapper {
  min-height: calc(var(--ag-grid-size) * 4);
}
.ag-theme-quartz .ag-tab,
.ag-theme-quartz-dark .ag-tab,
.ag-theme-quartz-auto-dark .ag-tab {
  padding: var(--ag-grid-size);
  border-left: var(--ag-borders) transparent;
  border-right: var(--ag-borders) transparent;
  flex: 1 1 auto;
}
.ag-theme-quartz .ag-tab-selected,
.ag-theme-quartz-dark .ag-tab-selected,
.ag-theme-quartz-auto-dark .ag-tab-selected {
  background-color: var(--ag-background-color);
}
.ag-theme-quartz .ag-ltr .ag-tab-selected:not(:first-of-type), .ag-theme-quartz-dark .ag-ltr .ag-tab-selected:not(:first-of-type), .ag-theme-quartz-auto-dark .ag-ltr .ag-tab-selected:not(:first-of-type) {
  border-left-color: var(--ag-border-color);
}

.ag-theme-quartz .ag-rtl .ag-tab-selected:not(:first-of-type), .ag-theme-quartz-dark .ag-rtl .ag-tab-selected:not(:first-of-type), .ag-theme-quartz-auto-dark .ag-rtl .ag-tab-selected:not(:first-of-type) {
  border-right-color: var(--ag-border-color);
}

.ag-theme-quartz .ag-ltr .ag-tab-selected:not(:last-of-type), .ag-theme-quartz-dark .ag-ltr .ag-tab-selected:not(:last-of-type), .ag-theme-quartz-auto-dark .ag-ltr .ag-tab-selected:not(:last-of-type) {
  border-right-color: var(--ag-border-color);
}

.ag-theme-quartz .ag-rtl .ag-tab-selected:not(:last-of-type), .ag-theme-quartz-dark .ag-rtl .ag-tab-selected:not(:last-of-type), .ag-theme-quartz-auto-dark .ag-rtl .ag-tab-selected:not(:last-of-type) {
  border-left-color: var(--ag-border-color);
}

.ag-theme-quartz .ag-tab:not(.ag-tab-selected),
.ag-theme-quartz-dark .ag-tab:not(.ag-tab-selected),
.ag-theme-quartz-auto-dark .ag-tab:not(.ag-tab-selected) {
  opacity: 0.7;
}
.ag-theme-quartz .ag-tab:not(.ag-tab-selected):hover,
.ag-theme-quartz-dark .ag-tab:not(.ag-tab-selected):hover,
.ag-theme-quartz-auto-dark .ag-tab:not(.ag-tab-selected):hover {
  opacity: 1;
}
.ag-theme-quartz .ag-menu,
.ag-theme-quartz-dark .ag-menu,
.ag-theme-quartz-auto-dark .ag-menu {
  color: color-mix(in srgb, transparent, var(--ag-foreground-color) 95%);
}
.ag-theme-quartz .ag-panel-content-wrapper .ag-column-select,
.ag-theme-quartz-dark .ag-panel-content-wrapper .ag-column-select,
.ag-theme-quartz-auto-dark .ag-panel-content-wrapper .ag-column-select {
  background-color: var(--ag-control-panel-background-color);
  color: color-mix(in srgb, transparent, var(--ag-foreground-color) 95%);
}
.ag-theme-quartz .ag-menu-header,
.ag-theme-quartz-dark .ag-menu-header,
.ag-theme-quartz-auto-dark .ag-menu-header {
  background-color: var(--ag-control-panel-background-color);
}
.ag-theme-quartz .ag-menu-option,
.ag-theme-quartz-dark .ag-menu-option,
.ag-theme-quartz-auto-dark .ag-menu-option {
  font-weight: 500;
  cursor: pointer;
}
.ag-theme-quartz .ag-ltr .ag-menu-option-popup-pointer .ag-icon, .ag-theme-quartz-dark .ag-ltr .ag-menu-option-popup-pointer .ag-icon, .ag-theme-quartz-auto-dark .ag-ltr .ag-menu-option-popup-pointer .ag-icon {
  text-align: right;
}

.ag-theme-quartz .ag-rtl .ag-menu-option-popup-pointer .ag-icon, .ag-theme-quartz-dark .ag-rtl .ag-menu-option-popup-pointer .ag-icon, .ag-theme-quartz-auto-dark .ag-rtl .ag-menu-option-popup-pointer .ag-icon {
  text-align: left;
}

.ag-theme-quartz .ag-tabs-header,
.ag-theme-quartz-dark .ag-tabs-header,
.ag-theme-quartz-auto-dark .ag-tabs-header {
  border-bottom: var(--ag-borders) var(--ag-border-color);
  display: flex;
  background-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 5%);
}
.ag-theme-quartz .ag-side-bar,
.ag-theme-quartz-dark .ag-side-bar,
.ag-theme-quartz-auto-dark .ag-side-bar {
  background-color: var(--ag-control-panel-background-color);
  min-width: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
}
.ag-theme-quartz .ag-side-buttons,
.ag-theme-quartz-dark .ag-side-buttons,
.ag-theme-quartz-auto-dark .ag-side-buttons {
  padding: 0;
  align-self: stretch;
  width: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
  background: var(--ag-control-panel-background-color);
}
.ag-theme-quartz .ag-side-button,
.ag-theme-quartz-dark .ag-side-button,
.ag-theme-quartz-auto-dark .ag-side-button {
  border-top: var(--ag-borders) transparent;
  border-bottom: var(--ag-borders) transparent;
}
@media (max-resolution: 1.5x) {
  .ag-theme-quartz .ag-side-button-label,
  .ag-theme-quartz-dark .ag-side-button-label,
  .ag-theme-quartz-auto-dark .ag-side-button-label {
    font-family: "Segoe UI", var(--ag-font-family);
    transform: rotate(0.05deg);
  }
}
.ag-theme-quartz .ag-side-button.ag-selected,
.ag-theme-quartz-dark .ag-side-button.ag-selected,
.ag-theme-quartz-auto-dark .ag-side-button.ag-selected {
  border-bottom-color: var(--ag-border-color);
  background-color: var(--ag-background-color);
}
.ag-theme-quartz .ag-side-button.ag-selected:not(:first-of-type),
.ag-theme-quartz-dark .ag-side-button.ag-selected:not(:first-of-type),
.ag-theme-quartz-auto-dark .ag-side-button.ag-selected:not(:first-of-type) {
  border-top-color: var(--ag-border-color);
}
.ag-theme-quartz .ag-column-panel-column-select,
.ag-theme-quartz-dark .ag-column-panel-column-select,
.ag-theme-quartz-auto-dark .ag-column-panel-column-select {
  border-top: none;
  border-bottom: 1px solid var(--ag-secondary-border-color);
}
.ag-theme-quartz .ag-filter-toolpanel-search,
.ag-theme-quartz-dark .ag-filter-toolpanel-search,
.ag-theme-quartz-auto-dark .ag-filter-toolpanel-search {
  height: initial;
  margin-top: var(--ag-widget-container-vertical-padding);
}
.ag-theme-quartz .ag-filter-toolpanel-search-input,
.ag-theme-quartz-dark .ag-filter-toolpanel-search-input,
.ag-theme-quartz-auto-dark .ag-filter-toolpanel-search-input {
  margin: 0;
}
.ag-theme-quartz .ag-filter-apply-panel,
.ag-theme-quartz-dark .ag-filter-apply-panel,
.ag-theme-quartz-auto-dark .ag-filter-apply-panel {
  border: none;
  padding-top: var(--ag-widget-vertical-spacing);
}
.ag-theme-quartz .ag-chart-tabbed-menu-body,
.ag-theme-quartz-dark .ag-chart-tabbed-menu-body,
.ag-theme-quartz-auto-dark .ag-chart-tabbed-menu-body {
  position: relative;
}
.ag-theme-quartz .ag-chart-tabbed-menu-body::after,
.ag-theme-quartz-dark .ag-chart-tabbed-menu-body::after,
.ag-theme-quartz-auto-dark .ag-chart-tabbed-menu-body::after {
  content: "";
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  right: 0;
  height: 16px;
  background: linear-gradient(var(--ag-control-panel-background-color), transparent);
}
.ag-theme-quartz .ag-charts-settings-group-title-bar,
.ag-theme-quartz .ag-charts-data-group-title-bar,
.ag-theme-quartz .ag-charts-format-top-level-group-title-bar,
.ag-theme-quartz .ag-charts-advanced-settings-top-level-group-title-bar,
.ag-theme-quartz .ag-charts-settings-group-container,
.ag-theme-quartz-dark .ag-charts-settings-group-title-bar,
.ag-theme-quartz-dark .ag-charts-data-group-title-bar,
.ag-theme-quartz-dark .ag-charts-format-top-level-group-title-bar,
.ag-theme-quartz-dark .ag-charts-advanced-settings-top-level-group-title-bar,
.ag-theme-quartz-dark .ag-charts-settings-group-container,
.ag-theme-quartz-auto-dark .ag-charts-settings-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-data-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-format-top-level-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-advanced-settings-top-level-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-settings-group-container {
  border-top: none;
  font-weight: 500;
}
.ag-theme-quartz .ag-chart-mini-thumbnail,
.ag-theme-quartz-dark .ag-chart-mini-thumbnail,
.ag-theme-quartz-auto-dark .ag-chart-mini-thumbnail {
  background-color: var(--ag-background-color);
  margin-top: 0;
  margin-bottom: 0;
}
.ag-theme-quartz .ag-chart-settings-nav-bar,
.ag-theme-quartz-dark .ag-chart-settings-nav-bar,
.ag-theme-quartz-auto-dark .ag-chart-settings-nav-bar {
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}
.ag-theme-quartz .ag-charts-format-sub-level-group-title-bar,
.ag-theme-quartz-dark .ag-charts-format-sub-level-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-format-sub-level-group-title-bar {
  background: none;
  font-weight: 500;
}
.ag-theme-quartz .ag-chart-data-section .ag-label:not(.ag-group-title-bar),
.ag-theme-quartz .ag-chart-format-section .ag-label:not(.ag-group-title-bar),
.ag-theme-quartz-dark .ag-chart-data-section .ag-label:not(.ag-group-title-bar),
.ag-theme-quartz-dark .ag-chart-format-section .ag-label:not(.ag-group-title-bar),
.ag-theme-quartz-auto-dark .ag-chart-data-section .ag-label:not(.ag-group-title-bar),
.ag-theme-quartz-auto-dark .ag-chart-format-section .ag-label:not(.ag-group-title-bar) {
  color: var(--ag-chart-menu-label-color);
}
.ag-theme-quartz .ag-chart-data-section .ag-label-align-top .ag-label,
.ag-theme-quartz .ag-chart-format-section .ag-label-align-top .ag-label,
.ag-theme-quartz-dark .ag-chart-data-section .ag-label-align-top .ag-label,
.ag-theme-quartz-dark .ag-chart-format-section .ag-label-align-top .ag-label,
.ag-theme-quartz-auto-dark .ag-chart-data-section .ag-label-align-top .ag-label,
.ag-theme-quartz-auto-dark .ag-chart-format-section .ag-label-align-top .ag-label {
  margin-top: calc(var(--ag-widget-vertical-spacing) * 0.5);
  margin-bottom: var(--ag-widget-vertical-spacing);
}
.ag-theme-quartz .ag-chart-data-section .ag-slider.ag-label-align-top .ag-label,
.ag-theme-quartz .ag-chart-format-section .ag-slider.ag-label-align-top .ag-label,
.ag-theme-quartz-dark .ag-chart-data-section .ag-slider.ag-label-align-top .ag-label,
.ag-theme-quartz-dark .ag-chart-format-section .ag-slider.ag-label-align-top .ag-label,
.ag-theme-quartz-auto-dark .ag-chart-data-section .ag-slider.ag-label-align-top .ag-label,
.ag-theme-quartz-auto-dark .ag-chart-format-section .ag-slider.ag-label-align-top .ag-label {
  margin-bottom: 0;
}
.ag-theme-quartz .ag-chart-data-section label,
.ag-theme-quartz .ag-chart-format-section label,
.ag-theme-quartz-dark .ag-chart-data-section label,
.ag-theme-quartz-dark .ag-chart-format-section label,
.ag-theme-quartz-auto-dark .ag-chart-data-section label,
.ag-theme-quartz-auto-dark .ag-chart-format-section label {
  display: inline-block;
}
.ag-theme-quartz .ag-chart-format-wrapper,
.ag-theme-quartz .ag-chart-data-wrapper,
.ag-theme-quartz .ag-charts-format-top-level-group,
.ag-theme-quartz .ag-charts-format-top-level-group-title-bar,
.ag-theme-quartz .ag-charts-format-top-level-group .ag-charts-format-top-level-group-container,
.ag-theme-quartz .ag-charts-format-top-level-group-item,
.ag-theme-quartz .ag-charts-format-sub-level-group,
.ag-theme-quartz .ag-charts-format-sub-level-group-title-bar,
.ag-theme-quartz .ag-charts-format-sub-level-group-container,
.ag-theme-quartz .ag-charts-format-sub-level-group-item:last-child,
.ag-theme-quartz .ag-charts-format-sub-level-group-container > *,
.ag-theme-quartz .ag-charts-data-group-title-bar,
.ag-theme-quartz .ag-charts-data-group-container,
.ag-theme-quartz .ag-charts-settings-group-title-bar,
.ag-theme-quartz .ag-charts-settings-group-container,
.ag-theme-quartz-dark .ag-chart-format-wrapper,
.ag-theme-quartz-dark .ag-chart-data-wrapper,
.ag-theme-quartz-dark .ag-charts-format-top-level-group,
.ag-theme-quartz-dark .ag-charts-format-top-level-group-title-bar,
.ag-theme-quartz-dark .ag-charts-format-top-level-group .ag-charts-format-top-level-group-container,
.ag-theme-quartz-dark .ag-charts-format-top-level-group-item,
.ag-theme-quartz-dark .ag-charts-format-sub-level-group,
.ag-theme-quartz-dark .ag-charts-format-sub-level-group-title-bar,
.ag-theme-quartz-dark .ag-charts-format-sub-level-group-container,
.ag-theme-quartz-dark .ag-charts-format-sub-level-group-item:last-child,
.ag-theme-quartz-dark .ag-charts-format-sub-level-group-container > *,
.ag-theme-quartz-dark .ag-charts-data-group-title-bar,
.ag-theme-quartz-dark .ag-charts-data-group-container,
.ag-theme-quartz-dark .ag-charts-settings-group-title-bar,
.ag-theme-quartz-dark .ag-charts-settings-group-container,
.ag-theme-quartz-auto-dark .ag-chart-format-wrapper,
.ag-theme-quartz-auto-dark .ag-chart-data-wrapper,
.ag-theme-quartz-auto-dark .ag-charts-format-top-level-group,
.ag-theme-quartz-auto-dark .ag-charts-format-top-level-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-format-top-level-group .ag-charts-format-top-level-group-container,
.ag-theme-quartz-auto-dark .ag-charts-format-top-level-group-item,
.ag-theme-quartz-auto-dark .ag-charts-format-sub-level-group,
.ag-theme-quartz-auto-dark .ag-charts-format-sub-level-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-format-sub-level-group-container,
.ag-theme-quartz-auto-dark .ag-charts-format-sub-level-group-item:last-child,
.ag-theme-quartz-auto-dark .ag-charts-format-sub-level-group-container > *,
.ag-theme-quartz-auto-dark .ag-charts-data-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-data-group-container,
.ag-theme-quartz-auto-dark .ag-charts-settings-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-settings-group-container {
  padding: 0;
  margin: 0;
}
.ag-theme-quartz .ag-charts-format-top-level-group,
.ag-theme-quartz .ag-charts-data-group,
.ag-theme-quartz-dark .ag-charts-format-top-level-group,
.ag-theme-quartz-dark .ag-charts-data-group,
.ag-theme-quartz-auto-dark .ag-charts-format-top-level-group,
.ag-theme-quartz-auto-dark .ag-charts-data-group {
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}
.ag-theme-quartz .ag-charts-format-top-level-group-title-bar,
.ag-theme-quartz .ag-charts-data-group-title-bar,
.ag-theme-quartz .ag-charts-settings-group-title-bar,
.ag-theme-quartz-dark .ag-charts-format-top-level-group-title-bar,
.ag-theme-quartz-dark .ag-charts-data-group-title-bar,
.ag-theme-quartz-dark .ag-charts-settings-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-format-top-level-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-data-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-settings-group-title-bar {
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
}
.ag-theme-quartz .ag-charts-format-top-level-group .ag-charts-format-top-level-group-container,
.ag-theme-quartz .ag-charts-data-group .ag-charts-data-group-container,
.ag-theme-quartz .ag-charts-settings-group .ag-charts-settings-group-container,
.ag-theme-quartz-dark .ag-charts-format-top-level-group .ag-charts-format-top-level-group-container,
.ag-theme-quartz-dark .ag-charts-data-group .ag-charts-data-group-container,
.ag-theme-quartz-dark .ag-charts-settings-group .ag-charts-settings-group-container,
.ag-theme-quartz-auto-dark .ag-charts-format-top-level-group .ag-charts-format-top-level-group-container,
.ag-theme-quartz-auto-dark .ag-charts-data-group .ag-charts-data-group-container,
.ag-theme-quartz-auto-dark .ag-charts-settings-group .ag-charts-settings-group-container {
  padding: 0 var(--ag-widget-container-horizontal-padding);
}
.ag-theme-quartz .ag-charts-format-sub-level-group-title-bar,
.ag-theme-quartz-dark .ag-charts-format-sub-level-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-format-sub-level-group-title-bar {
  padding: var(--ag-widget-vertical-spacing) 0;
}
.ag-theme-quartz .ag-charts-format-sub-level-group-container,
.ag-theme-quartz-dark .ag-charts-format-sub-level-group-container,
.ag-theme-quartz-auto-dark .ag-charts-format-sub-level-group-container {
  padding-top: var(--ag-widget-vertical-spacing);
  padding-bottom: var(--ag-widget-container-vertical-padding);
}
.ag-theme-quartz .ag-charts-format-top-level-group-container > *,
.ag-theme-quartz .ag-charts-format-sub-level-group-container > *,
.ag-theme-quartz-dark .ag-charts-format-top-level-group-container > *,
.ag-theme-quartz-dark .ag-charts-format-sub-level-group-container > *,
.ag-theme-quartz-auto-dark .ag-charts-format-top-level-group-container > *,
.ag-theme-quartz-auto-dark .ag-charts-format-sub-level-group-container > * {
  margin-bottom: var(--ag-widget-vertical-spacing);
}
.ag-theme-quartz .ag-charts-data-group-item,
.ag-theme-quartz-dark .ag-charts-data-group-item,
.ag-theme-quartz-auto-dark .ag-charts-data-group-item {
  padding-bottom: var(--ag-widget-container-vertical-padding);
}
.ag-theme-quartz .ag-chart-settings-mini-wrapper,
.ag-theme-quartz-dark .ag-chart-settings-mini-wrapper,
.ag-theme-quartz-auto-dark .ag-chart-settings-mini-wrapper {
  padding-bottom: var(--ag-widget-container-vertical-padding);
}
.ag-theme-quartz .ag-chart-advanced-settings-section,
.ag-theme-quartz-dark .ag-chart-advanced-settings-section,
.ag-theme-quartz-auto-dark .ag-chart-advanced-settings-section {
  padding-top: var(--ag-widget-container-vertical-padding);
  padding-bottom: var(--ag-widget-container-vertical-padding);
}
.ag-theme-quartz .ag-charts-advanced-settings-top-level-group .ag-charts-advanced-settings-top-level-group-title-bar,
.ag-theme-quartz .ag-charts-advanced-settings-top-level-group .ag-charts-advanced-settings-top-level-group-container,
.ag-theme-quartz-dark .ag-charts-advanced-settings-top-level-group .ag-charts-advanced-settings-top-level-group-title-bar,
.ag-theme-quartz-dark .ag-charts-advanced-settings-top-level-group .ag-charts-advanced-settings-top-level-group-container,
.ag-theme-quartz-auto-dark .ag-charts-advanced-settings-top-level-group .ag-charts-advanced-settings-top-level-group-title-bar,
.ag-theme-quartz-auto-dark .ag-charts-advanced-settings-top-level-group .ag-charts-advanced-settings-top-level-group-container {
  padding: 0 var(--ag-widget-container-horizontal-padding);
}
.ag-theme-quartz .ag-charts-advanced-settings-top-level-group-container,
.ag-theme-quartz-dark .ag-charts-advanced-settings-top-level-group-container,
.ag-theme-quartz-auto-dark .ag-charts-advanced-settings-top-level-group-container {
  margin: 0;
}
.ag-theme-quartz .ag-charts-advanced-settings-top-level-group-item,
.ag-theme-quartz-dark .ag-charts-advanced-settings-top-level-group-item,
.ag-theme-quartz-auto-dark .ag-charts-advanced-settings-top-level-group-item {
  margin-top: calc(var(--ag-widget-vertical-spacing) * 2);
  margin-bottom: 0;
}
.ag-theme-quartz .ag-ltr .ag-group-title-bar-icon, .ag-theme-quartz-dark .ag-ltr .ag-group-title-bar-icon, .ag-theme-quartz-auto-dark .ag-ltr .ag-group-title-bar-icon {
  margin-right: var(--ag-grid-size);
}

.ag-theme-quartz .ag-rtl .ag-group-title-bar-icon, .ag-theme-quartz-dark .ag-rtl .ag-group-title-bar-icon, .ag-theme-quartz-auto-dark .ag-rtl .ag-group-title-bar-icon {
  margin-left: var(--ag-grid-size);
}

.ag-theme-quartz .ag-spectrum-color,
.ag-theme-quartz .ag-spectrum-fill,
.ag-theme-quartz-dark .ag-spectrum-color,
.ag-theme-quartz-dark .ag-spectrum-fill,
.ag-theme-quartz-auto-dark .ag-spectrum-color,
.ag-theme-quartz-auto-dark .ag-spectrum-fill {
  border-radius: var(--ag-border-radius);
}
.ag-theme-quartz .ag-spectrum-dragger,
.ag-theme-quartz-dark .ag-spectrum-dragger,
.ag-theme-quartz-auto-dark .ag-spectrum-dragger {
  border-radius: 18px;
  height: 18px;
  width: 18px;
  border: 3px solid white;
}
.ag-theme-quartz .ag-spectrum-tools,
.ag-theme-quartz-dark .ag-spectrum-tools,
.ag-theme-quartz-auto-dark .ag-spectrum-tools {
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
}
.ag-theme-quartz .ag-spectrum-tool,
.ag-theme-quartz-dark .ag-spectrum-tool,
.ag-theme-quartz-auto-dark .ag-spectrum-tool {
  height: 12px;
}
.ag-theme-quartz .ag-spectrum-hue-background,
.ag-theme-quartz .ag-spectrum-alpha-background,
.ag-theme-quartz-dark .ag-spectrum-hue-background,
.ag-theme-quartz-dark .ag-spectrum-alpha-background,
.ag-theme-quartz-auto-dark .ag-spectrum-hue-background,
.ag-theme-quartz-auto-dark .ag-spectrum-alpha-background {
  border-radius: 12px;
}
.ag-theme-quartz .ag-spectrum-slider,
.ag-theme-quartz-dark .ag-spectrum-slider,
.ag-theme-quartz-auto-dark .ag-spectrum-slider {
  margin-top: -15px;
  width: 18px;
  height: 18px;
  border-radius: 18px;
  border: 3px solid rgb(248, 248, 248);
}
.ag-theme-quartz .ag-recent-colors,
.ag-theme-quartz-dark .ag-recent-colors,
.ag-theme-quartz-auto-dark .ag-recent-colors {
  margin-left: var(--ag-grid-size);
  margin-right: var(--ag-grid-size);
  margin-bottom: 2px;
}
.ag-theme-quartz .ag-color-input-color,
.ag-theme-quartz .ag-color-picker-color,
.ag-theme-quartz .ag-recent-color,
.ag-theme-quartz-dark .ag-color-input-color,
.ag-theme-quartz-dark .ag-color-picker-color,
.ag-theme-quartz-dark .ag-recent-color,
.ag-theme-quartz-auto-dark .ag-color-input-color,
.ag-theme-quartz-auto-dark .ag-color-picker-color,
.ag-theme-quartz-auto-dark .ag-recent-color {
  border-radius: 4px;
}
.ag-theme-quartz .ag-recent-color,
.ag-theme-quartz-dark .ag-recent-color,
.ag-theme-quartz-auto-dark .ag-recent-color {
  border: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}
.ag-theme-quartz.ag-dnd-ghost,
.ag-theme-quartz-dark.ag-dnd-ghost,
.ag-theme-quartz-auto-dark.ag-dnd-ghost {
  font-weight: 500;
}
.ag-theme-quartz .ag-standard-button,
.ag-theme-quartz-dark .ag-standard-button,
.ag-theme-quartz-auto-dark .ag-standard-button {
  font-family: inherit;
  appearance: none;
  -webkit-appearance: none;
  border-radius: var(--ag-border-radius);
  border: solid 1px var(--ag-input-border-color);
  background-color: var(--ag-background-color);
  padding: var(--ag-grid-size) calc(var(--ag-grid-size) * 2);
  cursor: pointer;
}
.ag-theme-quartz .ag-standard-button:hover,
.ag-theme-quartz-dark .ag-standard-button:hover,
.ag-theme-quartz-auto-dark .ag-standard-button:hover {
  background-color: var(--ag-row-hover-color);
}
.ag-theme-quartz .ag-standard-button:active,
.ag-theme-quartz-dark .ag-standard-button:active,
.ag-theme-quartz-auto-dark .ag-standard-button:active {
  border-color: var(--ag-active-color);
}
.ag-theme-quartz .ag-standard-button:disabled,
.ag-theme-quartz-dark .ag-standard-button:disabled,
.ag-theme-quartz-auto-dark .ag-standard-button:disabled {
  color: var(--ag-disabled-foreground-color);
  background-color: var(--ag-input-disabled-background-color);
  border-color: var(--ag-input-disabled-border-color);
}
.ag-theme-quartz .ag-column-drop-cell,
.ag-theme-quartz-dark .ag-column-drop-cell,
.ag-theme-quartz-auto-dark .ag-column-drop-cell {
  border-radius: calc(var(--ag-grid-size) * 3);
  height: calc(var(--ag-grid-size) * 3);
  padding: 0 var(--ag-grid-size);
}
.ag-theme-quartz .ag-column-drop-cell-button,
.ag-theme-quartz-dark .ag-column-drop-cell-button,
.ag-theme-quartz-auto-dark .ag-column-drop-cell-button {
  min-width: 0;
  margin: 0;
}
.ag-theme-quartz .ag-column-drop-cell-drag-handle,
.ag-theme-quartz-dark .ag-column-drop-cell-drag-handle,
.ag-theme-quartz-auto-dark .ag-column-drop-cell-drag-handle {
  margin-left: 0;
}
.ag-theme-quartz .ag-column-drop-vertical,
.ag-theme-quartz-dark .ag-column-drop-vertical,
.ag-theme-quartz-auto-dark .ag-column-drop-vertical {
  min-height: 75px;
}
.ag-theme-quartz .ag-column-drop-vertical-title-bar,
.ag-theme-quartz-dark .ag-column-drop-vertical-title-bar,
.ag-theme-quartz-auto-dark .ag-column-drop-vertical-title-bar {
  padding: var(--ag-widget-container-vertical-padding) calc(var(--ag-grid-size) * 2) 0;
}
.ag-theme-quartz .ag-ltr .ag-column-drop-vertical-icon, .ag-theme-quartz-dark .ag-ltr .ag-column-drop-vertical-icon, .ag-theme-quartz-auto-dark .ag-ltr .ag-column-drop-vertical-icon {
  margin-left: 0;
  margin-right: var(--ag-widget-horizontal-spacing);
}

.ag-theme-quartz .ag-rtl .ag-column-drop-vertical-icon, .ag-theme-quartz-dark .ag-rtl .ag-column-drop-vertical-icon, .ag-theme-quartz-auto-dark .ag-rtl .ag-column-drop-vertical-icon {
  margin-right: 0;
  margin-left: var(--ag-widget-horizontal-spacing);
}

.ag-theme-quartz .ag-column-drop-vertical-empty-message,
.ag-theme-quartz-dark .ag-column-drop-vertical-empty-message,
.ag-theme-quartz-auto-dark .ag-column-drop-vertical-empty-message {
  display: flex;
  align-items: center;
  justify-content: center;
  border: dashed 1px;
  border-color: var(--ag-border-color);
  margin: calc(var(--ag-grid-size) * 1.5) calc(var(--ag-grid-size) * 2);
  padding: calc(var(--ag-grid-size) * 2);
}
.ag-theme-quartz .ag-column-drop-empty-message,
.ag-theme-quartz-dark .ag-column-drop-empty-message,
.ag-theme-quartz-auto-dark .ag-column-drop-empty-message {
  color: var(--ag-foreground-color);
}
.ag-theme-quartz .ag-pill-select .ag-column-drop,
.ag-theme-quartz-dark .ag-pill-select .ag-column-drop,
.ag-theme-quartz-auto-dark .ag-pill-select .ag-column-drop {
  min-height: unset;
}
.ag-theme-quartz .ag-pill-select .ag-picker-field-display,
.ag-theme-quartz-dark .ag-pill-select .ag-picker-field-display,
.ag-theme-quartz-auto-dark .ag-pill-select .ag-picker-field-display {
  font-weight: 500;
  color: var(--ag-chart-menu-pill-select-button-color);
}
.ag-theme-quartz .ag-pill-select .ag-picker-field-icon .ag-icon,
.ag-theme-quartz-dark .ag-pill-select .ag-picker-field-icon .ag-icon,
.ag-theme-quartz-auto-dark .ag-pill-select .ag-picker-field-icon .ag-icon {
  color: var(--ag-chart-menu-pill-select-button-color);
}
.ag-theme-quartz .ag-status-bar,
.ag-theme-quartz-dark .ag-status-bar,
.ag-theme-quartz-auto-dark .ag-status-bar {
  font-weight: normal;
}
.ag-theme-quartz .ag-status-name-value,
.ag-theme-quartz-dark .ag-status-name-value,
.ag-theme-quartz-auto-dark .ag-status-name-value {
  padding: var(--ag-widget-container-vertical-padding) 0;
}
.ag-theme-quartz .ag-status-name-value-value,
.ag-theme-quartz .ag-paging-number,
.ag-theme-quartz .ag-paging-row-summary-panel-number,
.ag-theme-quartz-dark .ag-status-name-value-value,
.ag-theme-quartz-dark .ag-paging-number,
.ag-theme-quartz-dark .ag-paging-row-summary-panel-number,
.ag-theme-quartz-auto-dark .ag-status-name-value-value,
.ag-theme-quartz-auto-dark .ag-paging-number,
.ag-theme-quartz-auto-dark .ag-paging-row-summary-panel-number {
  font-weight: 500;
}
.ag-theme-quartz .ag-column-drop-cell-button,
.ag-theme-quartz-dark .ag-column-drop-cell-button,
.ag-theme-quartz-auto-dark .ag-column-drop-cell-button {
  opacity: 0.75;
}
.ag-theme-quartz .ag-column-drop-cell-button:hover,
.ag-theme-quartz-dark .ag-column-drop-cell-button:hover,
.ag-theme-quartz-auto-dark .ag-column-drop-cell-button:hover {
  opacity: 1;
}
.ag-theme-quartz .ag-header-cell-menu-button,
.ag-theme-quartz .ag-header-cell-filter-button,
.ag-theme-quartz .ag-panel-title-bar-button,
.ag-theme-quartz .ag-header-expand-icon,
.ag-theme-quartz .ag-column-group-icons,
.ag-theme-quartz .ag-set-filter-group-icons,
.ag-theme-quartz .ag-group-expanded .ag-icon,
.ag-theme-quartz .ag-group-contracted .ag-icon,
.ag-theme-quartz .ag-chart-settings-prev,
.ag-theme-quartz .ag-chart-settings-next,
.ag-theme-quartz .ag-group-title-bar-icon,
.ag-theme-quartz .ag-column-select-header-icon,
.ag-theme-quartz .ag-floating-filter-button-button,
.ag-theme-quartz .ag-filter-toolpanel-expand,
.ag-theme-quartz .ag-panel-title-bar-button-icon,
.ag-theme-quartz .ag-chart-menu-icon,
.ag-theme-quartz-dark .ag-header-cell-menu-button,
.ag-theme-quartz-dark .ag-header-cell-filter-button,
.ag-theme-quartz-dark .ag-panel-title-bar-button,
.ag-theme-quartz-dark .ag-header-expand-icon,
.ag-theme-quartz-dark .ag-column-group-icons,
.ag-theme-quartz-dark .ag-set-filter-group-icons,
.ag-theme-quartz-dark .ag-group-expanded .ag-icon,
.ag-theme-quartz-dark .ag-group-contracted .ag-icon,
.ag-theme-quartz-dark .ag-chart-settings-prev,
.ag-theme-quartz-dark .ag-chart-settings-next,
.ag-theme-quartz-dark .ag-group-title-bar-icon,
.ag-theme-quartz-dark .ag-column-select-header-icon,
.ag-theme-quartz-dark .ag-floating-filter-button-button,
.ag-theme-quartz-dark .ag-filter-toolpanel-expand,
.ag-theme-quartz-dark .ag-panel-title-bar-button-icon,
.ag-theme-quartz-dark .ag-chart-menu-icon,
.ag-theme-quartz-auto-dark .ag-header-cell-menu-button,
.ag-theme-quartz-auto-dark .ag-header-cell-filter-button,
.ag-theme-quartz-auto-dark .ag-panel-title-bar-button,
.ag-theme-quartz-auto-dark .ag-header-expand-icon,
.ag-theme-quartz-auto-dark .ag-column-group-icons,
.ag-theme-quartz-auto-dark .ag-set-filter-group-icons,
.ag-theme-quartz-auto-dark .ag-group-expanded .ag-icon,
.ag-theme-quartz-auto-dark .ag-group-contracted .ag-icon,
.ag-theme-quartz-auto-dark .ag-chart-settings-prev,
.ag-theme-quartz-auto-dark .ag-chart-settings-next,
.ag-theme-quartz-auto-dark .ag-group-title-bar-icon,
.ag-theme-quartz-auto-dark .ag-column-select-header-icon,
.ag-theme-quartz-auto-dark .ag-floating-filter-button-button,
.ag-theme-quartz-auto-dark .ag-filter-toolpanel-expand,
.ag-theme-quartz-auto-dark .ag-panel-title-bar-button-icon,
.ag-theme-quartz-auto-dark .ag-chart-menu-icon {
  --ag-quartz-icon-hover-color: color-mix(in srgb, transparent, var(--ag-foreground-color) 10%);
}
.ag-theme-quartz .ag-header-cell-menu-button:hover,
.ag-theme-quartz .ag-header-cell-filter-button:hover,
.ag-theme-quartz .ag-panel-title-bar-button:hover,
.ag-theme-quartz .ag-header-expand-icon:hover,
.ag-theme-quartz .ag-column-group-icons:hover,
.ag-theme-quartz .ag-set-filter-group-icons:hover,
.ag-theme-quartz .ag-group-expanded .ag-icon:hover,
.ag-theme-quartz .ag-group-contracted .ag-icon:hover,
.ag-theme-quartz .ag-chart-settings-prev:hover,
.ag-theme-quartz .ag-chart-settings-next:hover,
.ag-theme-quartz .ag-group-title-bar-icon:hover,
.ag-theme-quartz .ag-column-select-header-icon:hover,
.ag-theme-quartz .ag-floating-filter-button-button:hover,
.ag-theme-quartz .ag-filter-toolpanel-expand:hover,
.ag-theme-quartz .ag-panel-title-bar-button-icon:hover,
.ag-theme-quartz .ag-chart-menu-icon:hover,
.ag-theme-quartz-dark .ag-header-cell-menu-button:hover,
.ag-theme-quartz-dark .ag-header-cell-filter-button:hover,
.ag-theme-quartz-dark .ag-panel-title-bar-button:hover,
.ag-theme-quartz-dark .ag-header-expand-icon:hover,
.ag-theme-quartz-dark .ag-column-group-icons:hover,
.ag-theme-quartz-dark .ag-set-filter-group-icons:hover,
.ag-theme-quartz-dark .ag-group-expanded .ag-icon:hover,
.ag-theme-quartz-dark .ag-group-contracted .ag-icon:hover,
.ag-theme-quartz-dark .ag-chart-settings-prev:hover,
.ag-theme-quartz-dark .ag-chart-settings-next:hover,
.ag-theme-quartz-dark .ag-group-title-bar-icon:hover,
.ag-theme-quartz-dark .ag-column-select-header-icon:hover,
.ag-theme-quartz-dark .ag-floating-filter-button-button:hover,
.ag-theme-quartz-dark .ag-filter-toolpanel-expand:hover,
.ag-theme-quartz-dark .ag-panel-title-bar-button-icon:hover,
.ag-theme-quartz-dark .ag-chart-menu-icon:hover,
.ag-theme-quartz-auto-dark .ag-header-cell-menu-button:hover,
.ag-theme-quartz-auto-dark .ag-header-cell-filter-button:hover,
.ag-theme-quartz-auto-dark .ag-panel-title-bar-button:hover,
.ag-theme-quartz-auto-dark .ag-header-expand-icon:hover,
.ag-theme-quartz-auto-dark .ag-column-group-icons:hover,
.ag-theme-quartz-auto-dark .ag-set-filter-group-icons:hover,
.ag-theme-quartz-auto-dark .ag-group-expanded .ag-icon:hover,
.ag-theme-quartz-auto-dark .ag-group-contracted .ag-icon:hover,
.ag-theme-quartz-auto-dark .ag-chart-settings-prev:hover,
.ag-theme-quartz-auto-dark .ag-chart-settings-next:hover,
.ag-theme-quartz-auto-dark .ag-group-title-bar-icon:hover,
.ag-theme-quartz-auto-dark .ag-column-select-header-icon:hover,
.ag-theme-quartz-auto-dark .ag-floating-filter-button-button:hover,
.ag-theme-quartz-auto-dark .ag-filter-toolpanel-expand:hover,
.ag-theme-quartz-auto-dark .ag-panel-title-bar-button-icon:hover,
.ag-theme-quartz-auto-dark .ag-chart-menu-icon:hover {
  border-radius: 1px;
  background-color: var(--ag-quartz-icon-hover-color);
  box-shadow: 0 0 0 4px var(--ag-quartz-icon-hover-color);
}
.ag-theme-quartz .ag-filter-active,
.ag-theme-quartz-dark .ag-filter-active,
.ag-theme-quartz-auto-dark .ag-filter-active {
  --ag-quartz-icon-active-color: color-mix(in srgb, transparent, var(--ag-active-color) 14%);
  --ag-quartz-icon-hover-color: color-mix(in srgb, transparent, var(--ag-active-color) 28%);
  position: relative;
  border-radius: 1px;
  background-color: var(--ag-quartz-icon-active-color);
  box-shadow: 0 0 0 4px var(--ag-quartz-icon-active-color);
}
.ag-theme-quartz .ag-filter-active::after,
.ag-theme-quartz-dark .ag-filter-active::after,
.ag-theme-quartz-auto-dark .ag-filter-active::after {
  content: "";
  position: absolute;
  width: 6px;
  height: 6px;
  top: -1px;
  right: -1px;
  border-radius: 50%;
  background-color: var(--ag-active-color);
}
.ag-theme-quartz .ag-filter-active .ag-icon-filter,
.ag-theme-quartz-dark .ag-filter-active .ag-icon-filter,
.ag-theme-quartz-auto-dark .ag-filter-active .ag-icon-filter {
  clip-path: path("M8,0C8,4.415 11.585,8 16,8L16,16L0,16L0,0L8,0Z");
}
.ag-theme-quartz .ag-chart-menu,
.ag-theme-quartz-dark .ag-chart-menu,
.ag-theme-quartz-auto-dark .ag-chart-menu {
  --ag-icon-size: 20px;
  background-color: color-mix(in srgb, transparent, var(--ag-background-color) 30%);
  padding: 4px 2px;
}
.ag-theme-quartz .ag-chart-menu-icon,
.ag-theme-quartz-dark .ag-chart-menu-icon,
.ag-theme-quartz-auto-dark .ag-chart-menu-icon {
  opacity: 0.8;
}
.ag-theme-quartz .ag-drag-handle,
.ag-theme-quartz-dark .ag-drag-handle,
.ag-theme-quartz-auto-dark .ag-drag-handle {
  color: var(--ag-icon-font-color);
}
.ag-theme-quartz .ag-menu-option-icon,
.ag-theme-quartz .ag-compact-menu-option-icon,
.ag-theme-quartz-dark .ag-menu-option-icon,
.ag-theme-quartz-dark .ag-compact-menu-option-icon,
.ag-theme-quartz-auto-dark .ag-menu-option-icon,
.ag-theme-quartz-auto-dark .ag-compact-menu-option-icon {
  width: var(--ag-icon-size);
  cursor: pointer;
}
.ag-theme-quartz .ag-ltr .ag-menu-option-icon, .ag-theme-quartz .ag-ltr .ag-compact-menu-option-icon, .ag-theme-quartz-dark .ag-ltr .ag-menu-option-icon, .ag-theme-quartz-dark .ag-ltr .ag-compact-menu-option-icon, .ag-theme-quartz-auto-dark .ag-ltr .ag-menu-option-icon, .ag-theme-quartz-auto-dark .ag-ltr .ag-compact-menu-option-icon {
  padding-left: calc(var(--ag-grid-size) * 1.5);
}

.ag-theme-quartz .ag-rtl .ag-menu-option-icon, .ag-theme-quartz .ag-rtl .ag-compact-menu-option-icon, .ag-theme-quartz-dark .ag-rtl .ag-menu-option-icon, .ag-theme-quartz-dark .ag-rtl .ag-compact-menu-option-icon, .ag-theme-quartz-auto-dark .ag-rtl .ag-menu-option-icon, .ag-theme-quartz-auto-dark .ag-rtl .ag-compact-menu-option-icon {
  padding-right: calc(var(--ag-grid-size) * 1.5);
}

.ag-theme-quartz .ag-chart-settings-card-item.ag-not-selected:hover,
.ag-theme-quartz-dark .ag-chart-settings-card-item.ag-not-selected:hover,
.ag-theme-quartz-auto-dark .ag-chart-settings-card-item.ag-not-selected:hover {
  opacity: 0.35;
}
.ag-theme-quartz .ag-ltr .ag-panel-title-bar-button, .ag-theme-quartz-dark .ag-ltr .ag-panel-title-bar-button, .ag-theme-quartz-auto-dark .ag-ltr .ag-panel-title-bar-button {
  margin-left: calc(var(--ag-grid-size) * 2);
  margin-right: var(--ag-grid-size);
}

.ag-theme-quartz .ag-rtl .ag-panel-title-bar-button, .ag-theme-quartz-dark .ag-rtl .ag-panel-title-bar-button, .ag-theme-quartz-auto-dark .ag-rtl .ag-panel-title-bar-button {
  margin-right: calc(var(--ag-grid-size) * 2);
  margin-left: var(--ag-grid-size);
}

.ag-theme-quartz .ag-multi-filter-group-title-bar,
.ag-theme-quartz-dark .ag-multi-filter-group-title-bar,
.ag-theme-quartz-auto-dark .ag-multi-filter-group-title-bar {
  padding: calc(var(--ag-grid-size) * 1.5) var(--ag-grid-size);
}
.ag-theme-quartz .ag-ltr .ag-filter-toolpanel-instance-body, .ag-theme-quartz-dark .ag-ltr .ag-filter-toolpanel-instance-body, .ag-theme-quartz-auto-dark .ag-ltr .ag-filter-toolpanel-instance-body {
  padding-left: var(--ag-grid-size);
}

.ag-theme-quartz .ag-rtl .ag-filter-toolpanel-instance-body, .ag-theme-quartz-dark .ag-rtl .ag-filter-toolpanel-instance-body, .ag-theme-quartz-auto-dark .ag-rtl .ag-filter-toolpanel-instance-body {
  padding-right: var(--ag-grid-size);
}

.ag-theme-quartz .ag-filter-toolpanel-instance-filter,
.ag-theme-quartz-dark .ag-filter-toolpanel-instance-filter,
.ag-theme-quartz-auto-dark .ag-filter-toolpanel-instance-filter {
  border: none;
  background-color: var(--ag-control-panel-background-color);
}
.ag-theme-quartz .ag-ltr .ag-filter-toolpanel-instance-filter, .ag-theme-quartz-dark .ag-ltr .ag-filter-toolpanel-instance-filter, .ag-theme-quartz-auto-dark .ag-ltr .ag-filter-toolpanel-instance-filter {
  margin-left: calc(var(--ag-icon-size) * 0.5);
}

.ag-theme-quartz .ag-rtl .ag-filter-toolpanel-instance-filter, .ag-theme-quartz-dark .ag-rtl .ag-filter-toolpanel-instance-filter, .ag-theme-quartz-auto-dark .ag-rtl .ag-filter-toolpanel-instance-filter {
  margin-right: calc(var(--ag-icon-size) * 0.5);
}

.ag-theme-quartz .ag-filter-toolpanel-group-level-0,
.ag-theme-quartz-dark .ag-filter-toolpanel-group-level-0,
.ag-theme-quartz-auto-dark .ag-filter-toolpanel-group-level-0 {
  border-top: none;
}
.ag-theme-quartz .ag-filter-toolpanel-header,
.ag-theme-quartz-dark .ag-filter-toolpanel-header,
.ag-theme-quartz-auto-dark .ag-filter-toolpanel-header {
  height: initial;
  padding-top: var(--ag-grid-size);
  padding-bottom: var(--ag-grid-size);
}
.ag-theme-quartz .ag-filter-toolpanel-group-item,
.ag-theme-quartz-dark .ag-filter-toolpanel-group-item,
.ag-theme-quartz-auto-dark .ag-filter-toolpanel-group-item {
  margin: 0;
}
.ag-theme-quartz .ag-layout-auto-height .ag-center-cols-viewport,
.ag-theme-quartz .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-quartz .ag-layout-print .ag-center-cols-viewport,
.ag-theme-quartz .ag-layout-print .ag-center-cols-container,
.ag-theme-quartz-dark .ag-layout-auto-height .ag-center-cols-viewport,
.ag-theme-quartz-dark .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-quartz-dark .ag-layout-print .ag-center-cols-viewport,
.ag-theme-quartz-dark .ag-layout-print .ag-center-cols-container,
.ag-theme-quartz-auto-dark .ag-layout-auto-height .ag-center-cols-viewport,
.ag-theme-quartz-auto-dark .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-quartz-auto-dark .ag-layout-print .ag-center-cols-viewport,
.ag-theme-quartz-auto-dark .ag-layout-print .ag-center-cols-container {
  min-height: 150px;
}
.ag-theme-quartz .ag-date-time-list-page-entry-is-current,
.ag-theme-quartz-dark .ag-date-time-list-page-entry-is-current,
.ag-theme-quartz-auto-dark .ag-date-time-list-page-entry-is-current {
  background-color: var(--ag-active-color);
}
.ag-theme-quartz .ag-advanced-filter-builder-button,
.ag-theme-quartz-dark .ag-advanced-filter-builder-button,
.ag-theme-quartz-auto-dark .ag-advanced-filter-builder-button {
  padding: var(--ag-grid-size);
  font-weight: 600;
}
.ag-theme-quartz .ag-advanced-filter-builder-item-button-disabled .ag-icon,
.ag-theme-quartz .ag-disabled .ag-icon,
.ag-theme-quartz .ag-column-select-column-group-readonly .ag-icon,
.ag-theme-quartz [disabled] .ag-icon,
.ag-theme-quartz-dark .ag-advanced-filter-builder-item-button-disabled .ag-icon,
.ag-theme-quartz-dark .ag-disabled .ag-icon,
.ag-theme-quartz-dark .ag-column-select-column-group-readonly .ag-icon,
.ag-theme-quartz-dark [disabled] .ag-icon,
.ag-theme-quartz-auto-dark .ag-advanced-filter-builder-item-button-disabled .ag-icon,
.ag-theme-quartz-auto-dark .ag-disabled .ag-icon,
.ag-theme-quartz-auto-dark .ag-column-select-column-group-readonly .ag-icon,
.ag-theme-quartz-auto-dark [disabled] .ag-icon {
  opacity: 0.6;
}
.ag-theme-quartz .ag-icon-grip,
.ag-theme-quartz-dark .ag-icon-grip,
.ag-theme-quartz-auto-dark .ag-icon-grip {
  opacity: 0.7;
}
.ag-theme-quartz .ag-column-select-column-readonly.ag-icon-grip,
.ag-theme-quartz .ag-column-select-column-readonly .ag-icon-grip,
.ag-theme-quartz-dark .ag-column-select-column-readonly.ag-icon-grip,
.ag-theme-quartz-dark .ag-column-select-column-readonly .ag-icon-grip,
.ag-theme-quartz-auto-dark .ag-column-select-column-readonly.ag-icon-grip,
.ag-theme-quartz-auto-dark .ag-column-select-column-readonly .ag-icon-grip {
  opacity: 0.35;
}
.ag-theme-quartz .ag-column-select-header-filter-wrapper .ag-input-wrapper::before,
.ag-theme-quartz .ag-filter-toolpanel-search .ag-input-wrapper::before,
.ag-theme-quartz .ag-mini-filter .ag-input-wrapper::before,
.ag-theme-quartz .ag-filter-filter .ag-input-wrapper::before,
.ag-theme-quartz-dark .ag-column-select-header-filter-wrapper .ag-input-wrapper::before,
.ag-theme-quartz-dark .ag-filter-toolpanel-search .ag-input-wrapper::before,
.ag-theme-quartz-dark .ag-mini-filter .ag-input-wrapper::before,
.ag-theme-quartz-dark .ag-filter-filter .ag-input-wrapper::before,
.ag-theme-quartz-auto-dark .ag-column-select-header-filter-wrapper .ag-input-wrapper::before,
.ag-theme-quartz-auto-dark .ag-filter-toolpanel-search .ag-input-wrapper::before,
.ag-theme-quartz-auto-dark .ag-mini-filter .ag-input-wrapper::before,
.ag-theme-quartz-auto-dark .ag-filter-filter .ag-input-wrapper::before {
  position: absolute;
  display: block;
  width: 12px;
  height: 12px;
  background-image: url("data:image/svg+xml;charset=utf-8;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMDAwIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMS41Ij48cGF0aCBkPSJNNS4zIDlhMy43IDMuNyAwIDEgMCAwLTcuNSAzLjcgMy43IDAgMCAwIDAgNy41Wk0xMC41IDEwLjUgOC4zIDguMiIvPjwvc3ZnPg==");
  background-position: 50% 50%;
  background-size: contain;
  opacity: 40%;
  content: "";
  filter: var(--ag-icon-filter);
}
.ag-theme-quartz .ag-ltr .ag-column-select-header-filter-wrapper .ag-input-wrapper::before, .ag-theme-quartz .ag-ltr .ag-filter-toolpanel-search .ag-input-wrapper::before, .ag-theme-quartz .ag-ltr .ag-mini-filter .ag-input-wrapper::before, .ag-theme-quartz .ag-ltr .ag-filter-filter .ag-input-wrapper::before, .ag-theme-quartz-dark .ag-ltr .ag-column-select-header-filter-wrapper .ag-input-wrapper::before, .ag-theme-quartz-dark .ag-ltr .ag-filter-toolpanel-search .ag-input-wrapper::before, .ag-theme-quartz-dark .ag-ltr .ag-mini-filter .ag-input-wrapper::before, .ag-theme-quartz-dark .ag-ltr .ag-filter-filter .ag-input-wrapper::before, .ag-theme-quartz-auto-dark .ag-ltr .ag-column-select-header-filter-wrapper .ag-input-wrapper::before, .ag-theme-quartz-auto-dark .ag-ltr .ag-filter-toolpanel-search .ag-input-wrapper::before, .ag-theme-quartz-auto-dark .ag-ltr .ag-mini-filter .ag-input-wrapper::before, .ag-theme-quartz-auto-dark .ag-ltr .ag-filter-filter .ag-input-wrapper::before {
  margin-left: var(--ag-grid-size);
}

.ag-theme-quartz .ag-rtl .ag-column-select-header-filter-wrapper .ag-input-wrapper::before, .ag-theme-quartz .ag-rtl .ag-filter-toolpanel-search .ag-input-wrapper::before, .ag-theme-quartz .ag-rtl .ag-mini-filter .ag-input-wrapper::before, .ag-theme-quartz .ag-rtl .ag-filter-filter .ag-input-wrapper::before, .ag-theme-quartz-dark .ag-rtl .ag-column-select-header-filter-wrapper .ag-input-wrapper::before, .ag-theme-quartz-dark .ag-rtl .ag-filter-toolpanel-search .ag-input-wrapper::before, .ag-theme-quartz-dark .ag-rtl .ag-mini-filter .ag-input-wrapper::before, .ag-theme-quartz-dark .ag-rtl .ag-filter-filter .ag-input-wrapper::before, .ag-theme-quartz-auto-dark .ag-rtl .ag-column-select-header-filter-wrapper .ag-input-wrapper::before, .ag-theme-quartz-auto-dark .ag-rtl .ag-filter-toolpanel-search .ag-input-wrapper::before, .ag-theme-quartz-auto-dark .ag-rtl .ag-mini-filter .ag-input-wrapper::before, .ag-theme-quartz-auto-dark .ag-rtl .ag-filter-filter .ag-input-wrapper::before {
  margin-right: var(--ag-grid-size);
}

.ag-theme-quartz .ag-ltr .ag-column-select-header-filter-wrapper input.ag-text-field-input, .ag-theme-quartz .ag-ltr .ag-column-select-header-filter-wrapper input.ag-number-field-input, .ag-theme-quartz .ag-ltr .ag-filter-toolpanel-search input.ag-text-field-input, .ag-theme-quartz .ag-ltr .ag-filter-toolpanel-search input.ag-number-field-input, .ag-theme-quartz .ag-ltr .ag-mini-filter input.ag-text-field-input, .ag-theme-quartz .ag-ltr .ag-mini-filter input.ag-number-field-input, .ag-theme-quartz .ag-ltr .ag-filter-filter input.ag-text-field-input, .ag-theme-quartz .ag-ltr .ag-filter-filter input.ag-number-field-input, .ag-theme-quartz-dark .ag-ltr .ag-column-select-header-filter-wrapper input.ag-text-field-input, .ag-theme-quartz-dark .ag-ltr .ag-column-select-header-filter-wrapper input.ag-number-field-input, .ag-theme-quartz-dark .ag-ltr .ag-filter-toolpanel-search input.ag-text-field-input, .ag-theme-quartz-dark .ag-ltr .ag-filter-toolpanel-search input.ag-number-field-input, .ag-theme-quartz-dark .ag-ltr .ag-mini-filter input.ag-text-field-input, .ag-theme-quartz-dark .ag-ltr .ag-mini-filter input.ag-number-field-input, .ag-theme-quartz-dark .ag-ltr .ag-filter-filter input.ag-text-field-input, .ag-theme-quartz-dark .ag-ltr .ag-filter-filter input.ag-number-field-input, .ag-theme-quartz-auto-dark .ag-ltr .ag-column-select-header-filter-wrapper input.ag-text-field-input, .ag-theme-quartz-auto-dark .ag-ltr .ag-column-select-header-filter-wrapper input.ag-number-field-input, .ag-theme-quartz-auto-dark .ag-ltr .ag-filter-toolpanel-search input.ag-text-field-input, .ag-theme-quartz-auto-dark .ag-ltr .ag-filter-toolpanel-search input.ag-number-field-input, .ag-theme-quartz-auto-dark .ag-ltr .ag-mini-filter input.ag-text-field-input, .ag-theme-quartz-auto-dark .ag-ltr .ag-mini-filter input.ag-number-field-input, .ag-theme-quartz-auto-dark .ag-ltr .ag-filter-filter input.ag-text-field-input, .ag-theme-quartz-auto-dark .ag-ltr .ag-filter-filter input.ag-number-field-input {
  padding-left: 26px;
}

.ag-theme-quartz .ag-rtl .ag-column-select-header-filter-wrapper input.ag-text-field-input, .ag-theme-quartz .ag-rtl .ag-column-select-header-filter-wrapper input.ag-number-field-input, .ag-theme-quartz .ag-rtl .ag-filter-toolpanel-search input.ag-text-field-input, .ag-theme-quartz .ag-rtl .ag-filter-toolpanel-search input.ag-number-field-input, .ag-theme-quartz .ag-rtl .ag-mini-filter input.ag-text-field-input, .ag-theme-quartz .ag-rtl .ag-mini-filter input.ag-number-field-input, .ag-theme-quartz .ag-rtl .ag-filter-filter input.ag-text-field-input, .ag-theme-quartz .ag-rtl .ag-filter-filter input.ag-number-field-input, .ag-theme-quartz-dark .ag-rtl .ag-column-select-header-filter-wrapper input.ag-text-field-input, .ag-theme-quartz-dark .ag-rtl .ag-column-select-header-filter-wrapper input.ag-number-field-input, .ag-theme-quartz-dark .ag-rtl .ag-filter-toolpanel-search input.ag-text-field-input, .ag-theme-quartz-dark .ag-rtl .ag-filter-toolpanel-search input.ag-number-field-input, .ag-theme-quartz-dark .ag-rtl .ag-mini-filter input.ag-text-field-input, .ag-theme-quartz-dark .ag-rtl .ag-mini-filter input.ag-number-field-input, .ag-theme-quartz-dark .ag-rtl .ag-filter-filter input.ag-text-field-input, .ag-theme-quartz-dark .ag-rtl .ag-filter-filter input.ag-number-field-input, .ag-theme-quartz-auto-dark .ag-rtl .ag-column-select-header-filter-wrapper input.ag-text-field-input, .ag-theme-quartz-auto-dark .ag-rtl .ag-column-select-header-filter-wrapper input.ag-number-field-input, .ag-theme-quartz-auto-dark .ag-rtl .ag-filter-toolpanel-search input.ag-text-field-input, .ag-theme-quartz-auto-dark .ag-rtl .ag-filter-toolpanel-search input.ag-number-field-input, .ag-theme-quartz-auto-dark .ag-rtl .ag-mini-filter input.ag-text-field-input, .ag-theme-quartz-auto-dark .ag-rtl .ag-mini-filter input.ag-number-field-input, .ag-theme-quartz-auto-dark .ag-rtl .ag-filter-filter input.ag-text-field-input, .ag-theme-quartz-auto-dark .ag-rtl .ag-filter-filter input.ag-number-field-input {
  padding-right: 26px;
}

.ag-theme-quartz .ag-ltr .ag-column-select-add-group-indent, .ag-theme-quartz-dark .ag-ltr .ag-column-select-add-group-indent, .ag-theme-quartz-auto-dark .ag-ltr .ag-column-select-add-group-indent {
  margin-left: calc(var(--ag-icon-size) + var(--ag-grid-size) * 1.5);
}

.ag-theme-quartz .ag-rtl .ag-column-select-add-group-indent, .ag-theme-quartz-dark .ag-rtl .ag-column-select-add-group-indent, .ag-theme-quartz-auto-dark .ag-rtl .ag-column-select-add-group-indent {
  margin-right: calc(var(--ag-icon-size) + var(--ag-grid-size) * 1.5);
}

.ag-theme-quartz .ag-text-field-input[disabled],
.ag-theme-quartz .ag-menu-option-disabled,
.ag-theme-quartz-dark .ag-text-field-input[disabled],
.ag-theme-quartz-dark .ag-menu-option-disabled,
.ag-theme-quartz-auto-dark .ag-text-field-input[disabled],
.ag-theme-quartz-auto-dark .ag-menu-option-disabled {
  cursor: not-allowed;
}
.ag-theme-quartz .ag-checkbox-input-wrapper.ag-checked.ag-disabled,
.ag-theme-quartz-dark .ag-checkbox-input-wrapper.ag-checked.ag-disabled,
.ag-theme-quartz-auto-dark .ag-checkbox-input-wrapper.ag-checked.ag-disabled {
  --ag-checkbox-checked-color: var(--ag-checkbox-unchecked-color);
}
.ag-theme-quartz .ag-checkbox-input,
.ag-theme-quartz .ag-toggle-button-input,
.ag-theme-quartz .ag-radio-button-input,
.ag-theme-quartz input[class^=ag-][type=range],
.ag-theme-quartz-dark .ag-checkbox-input,
.ag-theme-quartz-dark .ag-toggle-button-input,
.ag-theme-quartz-dark .ag-radio-button-input,
.ag-theme-quartz-dark input[class^=ag-][type=range],
.ag-theme-quartz-auto-dark .ag-checkbox-input,
.ag-theme-quartz-auto-dark .ag-toggle-button-input,
.ag-theme-quartz-auto-dark .ag-radio-button-input,
.ag-theme-quartz-auto-dark input[class^=ag-][type=range] {
  cursor: pointer;
}
.ag-theme-quartz .ag-details-row,
.ag-theme-quartz-dark .ag-details-row,
.ag-theme-quartz-auto-dark .ag-details-row {
  padding: calc(var(--ag-grid-size) * 3.75);
}
.ag-theme-quartz .ag-list-item-hovered::after,
.ag-theme-quartz-dark .ag-list-item-hovered::after,
.ag-theme-quartz-auto-dark .ag-list-item-hovered::after {
  background-color: var(--ag-active-color);
}
.ag-theme-quartz .ag-pill .ag-pill-button:hover,
.ag-theme-quartz-dark .ag-pill .ag-pill-button:hover,
.ag-theme-quartz-auto-dark .ag-pill .ag-pill-button:hover {
  color: var(--ag-active-color);
}
.ag-theme-quartz .ag-header-highlight-before::after,
.ag-theme-quartz .ag-header-highlight-after::after,
.ag-theme-quartz-dark .ag-header-highlight-before::after,
.ag-theme-quartz-dark .ag-header-highlight-after::after,
.ag-theme-quartz-auto-dark .ag-header-highlight-before::after,
.ag-theme-quartz-auto-dark .ag-header-highlight-after::after {
  background-color: var(--ag-active-color);
}

.ag-theme-quartz-dark .ag-column-select-header-filter-wrapper .ag-input-wrapper::before,
.ag-theme-quartz-dark .ag-filter-toolpanel-search .ag-input-wrapper::before,
.ag-theme-quartz-dark .ag-mini-filter .ag-input-wrapper::before,
.ag-theme-quartz-dark .ag-filter-filter .ag-input-wrapper::before {
  opacity: 66%;
  filter: invert(100%);
}
.ag-theme-quartz-dark .ag-chart-menu {
  background-color: color-mix(in srgb, rgba(24, 39, 50, 0.3), var(--ag-background-color) 30%);
}
.ag-theme-quartz-dark .ag-text-field-input::placeholder {
  color: var(--ag-data-color);
  opacity: 0.8;
}
