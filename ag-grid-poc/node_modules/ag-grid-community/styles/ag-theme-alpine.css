@font-face {
  font-family: "agGrid<PERSON>lpine";
  src: url(data:font/woff2;charset=utf-8;base64,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);
  font-weight: normal;
  font-style: normal;
}
.ag-theme-alpine,
.ag-theme-alpine-dark,
.ag-theme-alpine-auto-dark {
  --ag-alpine-active-color: #2196f3;
  --ag-selected-row-background-color: rgba(33, 150, 243, 0.3);
  --ag-row-hover-color: rgba(33, 150, 243, 0.1);
  --ag-column-hover-color: rgba(33, 150, 243, 0.1);
  --ag-input-focus-border-color: rgba(33, 150, 243, 0.4);
  --ag-range-selection-background-color: rgba(33, 150, 243, 0.2);
  --ag-range-selection-background-color-2: rgba(33, 150, 243, 0.36);
  --ag-range-selection-background-color-3: rgba(33, 150, 243, 0.49);
  --ag-range-selection-background-color-4: rgba(33, 150, 243, 0.59);
  --ag-row-numbers-selected-color: color-mix(in srgb, transparent, var(--ag-alpine-active-color) 50%);
  --ag-background-color: #fff;
  --ag-foreground-color: #181d1f;
  --ag-border-color: #babfc7;
  --ag-secondary-border-color: #dde2eb;
  --ag-header-background-color: #f8f8f8;
  --ag-tooltip-background-color: #f8f8f8;
  --ag-odd-row-background-color: #fcfcfc;
  --ag-control-panel-background-color: #f8f8f8;
  --ag-subheader-background-color: #fff;
  --ag-invalid-color: #e02525;
  --ag-checkbox-unchecked-color: #999;
  --ag-advanced-filter-join-pill-color: #f08e8d;
  --ag-advanced-filter-column-pill-color: #a6e194;
  --ag-advanced-filter-option-pill-color: #f3c08b;
  --ag-advanced-filter-value-pill-color: #85c0e4;
  --ag-find-match-color: var(--ag-foreground-color);
  --ag-find-match-background-color: #ffff00;
  --ag-find-active-match-color: var(--ag-foreground-color);
  --ag-find-active-match-background-color: #ffa500;
  --ag-checkbox-background-color: var(--ag-background-color);
  --ag-checkbox-checked-color: var(--ag-alpine-active-color);
  --ag-range-selection-border-color: var(--ag-alpine-active-color);
  --ag-secondary-foreground-color: var(--ag-foreground-color);
  --ag-input-border-color: var(--ag-border-color);
  --ag-input-border-color-invalid: var(--ag-invalid-color);
  --ag-input-focus-box-shadow: 0 0 2px 0.1rem var(--ag-input-focus-border-color);
  --ag-panel-background-color: var(--ag-header-background-color);
  --ag-menu-background-color: var(--ag-header-background-color);
  --ag-disabled-foreground-color: rgba(24, 29, 31, 0.5);
  --ag-chip-background-color: rgba(24, 29, 31, 0.07);
  --ag-input-disabled-border-color: rgba(186, 191, 199, 0.3);
  --ag-input-disabled-background-color: rgba(186, 191, 199, 0.15);
  --ag-borders: solid 1px;
  --ag-border-radius: 3px;
  --ag-borders-side-button: none;
  --ag-side-button-selected-background-color: transparent;
  --ag-header-column-resize-handle-display: block;
  --ag-header-column-resize-handle-width: 2px;
  --ag-header-column-resize-handle-height: 30%;
  --ag-grid-size: 6px;
  --ag-icon-size: 16px;
  --ag-row-height: calc(var(--ag-grid-size) * 7);
  --ag-header-height: calc(var(--ag-grid-size) * 8);
  --ag-list-item-height: calc(var(--ag-grid-size) * 4);
  --ag-column-select-indent-size: var(--ag-icon-size);
  --ag-set-filter-indent-size: var(--ag-icon-size);
  --ag-advanced-filter-builder-indent-size: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
  --ag-cell-horizontal-padding: calc(var(--ag-grid-size) * 3);
  --ag-cell-widget-spacing: calc(var(--ag-grid-size) * 2);
  --ag-widget-container-vertical-padding: calc(var(--ag-grid-size) * 2);
  --ag-widget-container-horizontal-padding: calc(var(--ag-grid-size) * 2);
  --ag-widget-vertical-spacing: calc(var(--ag-grid-size) * 1.5);
  --ag-toggle-button-height: 18px;
  --ag-toggle-button-width: 28px;
  --ag-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell,
      "Helvetica Neue", sans-serif;
  --ag-font-size: 13px;
  --ag-icon-font-family: agGridAlpine;
  --ag-selected-tab-underline-color: var(--ag-alpine-active-color);
  --ag-selected-tab-underline-width: 2px;
  --ag-selected-tab-underline-transition-speed: 0.3s;
  --ag-tab-min-width: 240px;
  --ag-card-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
  --ag-popup-shadow: var(--ag-card-shadow);
  --ag-side-bar-panel-width: 250px;
}

.ag-theme-alpine-dark {
  --ag-background-color: #181d1f;
  --ag-foreground-color: #fff;
  --ag-border-color: #68686e;
  --ag-secondary-border-color: rgba(88, 86, 82, 0.5);
  --ag-modal-overlay-background-color: rgba(24, 29, 31, 0.66);
  --ag-header-background-color: #222628;
  --ag-tooltip-background-color: #222628;
  --ag-odd-row-background-color: #222628;
  --ag-control-panel-background-color: #222628;
  --ag-subheader-background-color: #000;
  --ag-input-disabled-background-color: #282c2f;
  --ag-input-focus-box-shadow: 0 0 2px 0.5px rgba(255, 255, 255, 0.5), 0 0 4px 3px var(--ag-input-focus-border-color);
  --ag-card-shadow: 0 1px 20px 1px black;
  --ag-disabled-foreground-color: rgba(255, 255, 255, 0.5);
  --ag-chip-background-color: rgba(255, 255, 255, 0.07);
  --ag-input-disabled-border-color: rgba(104, 104, 110, 0.3);
  --ag-input-disabled-background-color: rgba(104, 104, 110, 0.07);
  --ag-advanced-filter-join-pill-color: #7a3a37;
  --ag-advanced-filter-column-pill-color: #355f2d;
  --ag-advanced-filter-option-pill-color: #5a3168;
  --ag-advanced-filter-value-pill-color: #374c86;
  --ag-find-match-color: var(--ag-background-color);
  --ag-find-active-match-color: var(--ag-background-color);
  --ag-row-loading-skeleton-effect-color: rgba(202, 203, 204, 0.4);
  color-scheme: dark;
}

@media (prefers-color-scheme: dark) {
  .ag-theme-alpine-auto-dark {
    --ag-background-color: #181d1f;
    --ag-foreground-color: #fff;
    --ag-border-color: #68686e;
    --ag-secondary-border-color: rgba(88, 86, 82, 0.5);
    --ag-modal-overlay-background-color: rgba(24, 29, 31, 0.66);
    --ag-header-background-color: #222628;
    --ag-tooltip-background-color: #222628;
    --ag-odd-row-background-color: #222628;
    --ag-control-panel-background-color: #222628;
    --ag-subheader-background-color: #000;
    --ag-input-disabled-background-color: #282c2f;
    --ag-input-focus-box-shadow: 0 0 2px 0.5px rgba(255, 255, 255, 0.5), 0 0 4px 3px var(--ag-input-focus-border-color);
    --ag-card-shadow: 0 1px 20px 1px black;
    --ag-disabled-foreground-color: rgba(255, 255, 255, 0.5);
    --ag-chip-background-color: rgba(255, 255, 255, 0.07);
    --ag-input-disabled-border-color: rgba(104, 104, 110, 0.3);
    --ag-input-disabled-background-color: rgba(104, 104, 110, 0.07);
    --ag-advanced-filter-join-pill-color: #7a3a37;
    --ag-advanced-filter-column-pill-color: #355f2d;
    --ag-advanced-filter-option-pill-color: #5a3168;
    --ag-advanced-filter-value-pill-color: #374c86;
    --ag-find-match-color: var(--ag-background-color);
    --ag-find-active-match-color: var(--ag-background-color);
    --ag-row-loading-skeleton-effect-color: rgba(202, 203, 204, 0.4);
    color-scheme: dark;
  }
}
.ag-theme-alpine .ag-filter-toolpanel-header,
.ag-theme-alpine .ag-filter-toolpanel-search,
.ag-theme-alpine .ag-status-bar,
.ag-theme-alpine .ag-header-row,
.ag-theme-alpine .ag-row-number-cell,
.ag-theme-alpine .ag-panel-title-bar-title,
.ag-theme-alpine .ag-multi-filter-group-title-bar,
.ag-theme-alpine-dark .ag-filter-toolpanel-header,
.ag-theme-alpine-dark .ag-filter-toolpanel-search,
.ag-theme-alpine-dark .ag-status-bar,
.ag-theme-alpine-dark .ag-header-row,
.ag-theme-alpine-dark .ag-row-number-cell,
.ag-theme-alpine-dark .ag-panel-title-bar-title,
.ag-theme-alpine-dark .ag-multi-filter-group-title-bar,
.ag-theme-alpine-auto-dark .ag-filter-toolpanel-header,
.ag-theme-alpine-auto-dark .ag-filter-toolpanel-search,
.ag-theme-alpine-auto-dark .ag-status-bar,
.ag-theme-alpine-auto-dark .ag-header-row,
.ag-theme-alpine-auto-dark .ag-row-number-cell,
.ag-theme-alpine-auto-dark .ag-panel-title-bar-title,
.ag-theme-alpine-auto-dark .ag-multi-filter-group-title-bar {
  font-weight: 700;
  color: var(--ag-header-foreground-color);
}
.ag-theme-alpine .ag-row,
.ag-theme-alpine-dark .ag-row,
.ag-theme-alpine-auto-dark .ag-row {
  font-size: calc(var(--ag-font-size) + 1px);
}
.ag-theme-alpine input[class^=ag-]:not([type]),
.ag-theme-alpine input[class^=ag-][type=text],
.ag-theme-alpine input[class^=ag-][type=number],
.ag-theme-alpine input[class^=ag-][type=tel],
.ag-theme-alpine input[class^=ag-][type=date],
.ag-theme-alpine input[class^=ag-][type=datetime-local],
.ag-theme-alpine textarea[class^=ag-],
.ag-theme-alpine-dark input[class^=ag-]:not([type]),
.ag-theme-alpine-dark input[class^=ag-][type=text],
.ag-theme-alpine-dark input[class^=ag-][type=number],
.ag-theme-alpine-dark input[class^=ag-][type=tel],
.ag-theme-alpine-dark input[class^=ag-][type=date],
.ag-theme-alpine-dark input[class^=ag-][type=datetime-local],
.ag-theme-alpine-dark textarea[class^=ag-],
.ag-theme-alpine-auto-dark input[class^=ag-]:not([type]),
.ag-theme-alpine-auto-dark input[class^=ag-][type=text],
.ag-theme-alpine-auto-dark input[class^=ag-][type=number],
.ag-theme-alpine-auto-dark input[class^=ag-][type=tel],
.ag-theme-alpine-auto-dark input[class^=ag-][type=date],
.ag-theme-alpine-auto-dark input[class^=ag-][type=datetime-local],
.ag-theme-alpine-auto-dark textarea[class^=ag-] {
  min-height: calc(var(--ag-grid-size) * 4);
  border-radius: var(--ag-border-radius);
}
.ag-theme-alpine .ag-ltr input[class^=ag-]:not([type]), .ag-theme-alpine .ag-ltr input[class^=ag-][type=text], .ag-theme-alpine .ag-ltr input[class^=ag-][type=number], .ag-theme-alpine .ag-ltr input[class^=ag-][type=tel], .ag-theme-alpine .ag-ltr input[class^=ag-][type=date], .ag-theme-alpine .ag-ltr input[class^=ag-][type=datetime-local], .ag-theme-alpine .ag-ltr textarea[class^=ag-], .ag-theme-alpine-dark .ag-ltr input[class^=ag-]:not([type]), .ag-theme-alpine-dark .ag-ltr input[class^=ag-][type=text], .ag-theme-alpine-dark .ag-ltr input[class^=ag-][type=number], .ag-theme-alpine-dark .ag-ltr input[class^=ag-][type=tel], .ag-theme-alpine-dark .ag-ltr input[class^=ag-][type=date], .ag-theme-alpine-dark .ag-ltr input[class^=ag-][type=datetime-local], .ag-theme-alpine-dark .ag-ltr textarea[class^=ag-], .ag-theme-alpine-auto-dark .ag-ltr input[class^=ag-]:not([type]), .ag-theme-alpine-auto-dark .ag-ltr input[class^=ag-][type=text], .ag-theme-alpine-auto-dark .ag-ltr input[class^=ag-][type=number], .ag-theme-alpine-auto-dark .ag-ltr input[class^=ag-][type=tel], .ag-theme-alpine-auto-dark .ag-ltr input[class^=ag-][type=date], .ag-theme-alpine-auto-dark .ag-ltr input[class^=ag-][type=datetime-local], .ag-theme-alpine-auto-dark .ag-ltr textarea[class^=ag-] {
  padding-left: var(--ag-grid-size);
}

.ag-theme-alpine .ag-rtl input[class^=ag-]:not([type]), .ag-theme-alpine .ag-rtl input[class^=ag-][type=text], .ag-theme-alpine .ag-rtl input[class^=ag-][type=number], .ag-theme-alpine .ag-rtl input[class^=ag-][type=tel], .ag-theme-alpine .ag-rtl input[class^=ag-][type=date], .ag-theme-alpine .ag-rtl input[class^=ag-][type=datetime-local], .ag-theme-alpine .ag-rtl textarea[class^=ag-], .ag-theme-alpine-dark .ag-rtl input[class^=ag-]:not([type]), .ag-theme-alpine-dark .ag-rtl input[class^=ag-][type=text], .ag-theme-alpine-dark .ag-rtl input[class^=ag-][type=number], .ag-theme-alpine-dark .ag-rtl input[class^=ag-][type=tel], .ag-theme-alpine-dark .ag-rtl input[class^=ag-][type=date], .ag-theme-alpine-dark .ag-rtl input[class^=ag-][type=datetime-local], .ag-theme-alpine-dark .ag-rtl textarea[class^=ag-], .ag-theme-alpine-auto-dark .ag-rtl input[class^=ag-]:not([type]), .ag-theme-alpine-auto-dark .ag-rtl input[class^=ag-][type=text], .ag-theme-alpine-auto-dark .ag-rtl input[class^=ag-][type=number], .ag-theme-alpine-auto-dark .ag-rtl input[class^=ag-][type=tel], .ag-theme-alpine-auto-dark .ag-rtl input[class^=ag-][type=date], .ag-theme-alpine-auto-dark .ag-rtl input[class^=ag-][type=datetime-local], .ag-theme-alpine-auto-dark .ag-rtl textarea[class^=ag-] {
  padding-right: var(--ag-grid-size);
}

.ag-theme-alpine .ag-tab,
.ag-theme-alpine-dark .ag-tab,
.ag-theme-alpine-auto-dark .ag-tab {
  padding: calc(var(--ag-grid-size) * 1.5);
  transition: color 0.4s;
  flex: 1 1 auto;
}
.ag-theme-alpine .ag-tab-selected,
.ag-theme-alpine-dark .ag-tab-selected,
.ag-theme-alpine-auto-dark .ag-tab-selected {
  color: var(--ag-alpine-active-color);
}
.ag-theme-alpine .ag-menu,
.ag-theme-alpine-dark .ag-menu,
.ag-theme-alpine-auto-dark .ag-menu {
  background-color: var(--ag-control-panel-background-color);
}
.ag-theme-alpine .ag-panel-content-wrapper .ag-column-select,
.ag-theme-alpine-dark .ag-panel-content-wrapper .ag-column-select,
.ag-theme-alpine-auto-dark .ag-panel-content-wrapper .ag-column-select {
  background-color: var(--ag-control-panel-background-color);
}
.ag-theme-alpine .ag-menu-header,
.ag-theme-alpine-dark .ag-menu-header,
.ag-theme-alpine-auto-dark .ag-menu-header {
  background-color: var(--ag-control-panel-background-color);
  padding-top: 1px;
}
.ag-theme-alpine .ag-tabs-header,
.ag-theme-alpine-dark .ag-tabs-header,
.ag-theme-alpine-auto-dark .ag-tabs-header {
  border-bottom: var(--ag-borders) var(--ag-border-color);
}
.ag-theme-alpine .ag-charts-settings-group-title-bar,
.ag-theme-alpine .ag-charts-data-group-title-bar,
.ag-theme-alpine .ag-charts-format-top-level-group-title-bar,
.ag-theme-alpine .ag-charts-advanced-settings-top-level-group-title-bar,
.ag-theme-alpine-dark .ag-charts-settings-group-title-bar,
.ag-theme-alpine-dark .ag-charts-data-group-title-bar,
.ag-theme-alpine-dark .ag-charts-format-top-level-group-title-bar,
.ag-theme-alpine-dark .ag-charts-advanced-settings-top-level-group-title-bar,
.ag-theme-alpine-auto-dark .ag-charts-settings-group-title-bar,
.ag-theme-alpine-auto-dark .ag-charts-data-group-title-bar,
.ag-theme-alpine-auto-dark .ag-charts-format-top-level-group-title-bar,
.ag-theme-alpine-auto-dark .ag-charts-advanced-settings-top-level-group-title-bar {
  padding: var(--ag-grid-size) calc(var(--ag-grid-size) * 2);
  line-height: calc(var(--ag-icon-size) + var(--ag-grid-size) - 2px);
}
.ag-theme-alpine .ag-chart-mini-thumbnail,
.ag-theme-alpine-dark .ag-chart-mini-thumbnail,
.ag-theme-alpine-auto-dark .ag-chart-mini-thumbnail {
  background-color: var(--ag-background-color);
}
.ag-theme-alpine .ag-chart-settings-nav-bar,
.ag-theme-alpine-dark .ag-chart-settings-nav-bar,
.ag-theme-alpine-auto-dark .ag-chart-settings-nav-bar {
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}
.ag-theme-alpine .ag-ltr .ag-group-title-bar-icon, .ag-theme-alpine-dark .ag-ltr .ag-group-title-bar-icon, .ag-theme-alpine-auto-dark .ag-ltr .ag-group-title-bar-icon {
  margin-right: var(--ag-grid-size);
}

.ag-theme-alpine .ag-rtl .ag-group-title-bar-icon, .ag-theme-alpine-dark .ag-rtl .ag-group-title-bar-icon, .ag-theme-alpine-auto-dark .ag-rtl .ag-group-title-bar-icon {
  margin-left: var(--ag-grid-size);
}

.ag-theme-alpine .ag-charts-format-top-level-group-toolbar,
.ag-theme-alpine .ag-charts-advanced-settings-top-level-group-toolbar,
.ag-theme-alpine-dark .ag-charts-format-top-level-group-toolbar,
.ag-theme-alpine-dark .ag-charts-advanced-settings-top-level-group-toolbar,
.ag-theme-alpine-auto-dark .ag-charts-format-top-level-group-toolbar,
.ag-theme-alpine-auto-dark .ag-charts-advanced-settings-top-level-group-toolbar {
  margin-top: var(--ag-grid-size);
}
.ag-theme-alpine .ag-ltr .ag-charts-format-top-level-group-toolbar, .ag-theme-alpine .ag-ltr .ag-charts-advanced-settings-top-level-group-toolbar, .ag-theme-alpine-dark .ag-ltr .ag-charts-format-top-level-group-toolbar, .ag-theme-alpine-dark .ag-ltr .ag-charts-advanced-settings-top-level-group-toolbar, .ag-theme-alpine-auto-dark .ag-ltr .ag-charts-format-top-level-group-toolbar, .ag-theme-alpine-auto-dark .ag-ltr .ag-charts-advanced-settings-top-level-group-toolbar {
  padding-left: calc(var(--ag-icon-size) * 0.5 + var(--ag-grid-size) * 2);
}

.ag-theme-alpine .ag-rtl .ag-charts-format-top-level-group-toolbar, .ag-theme-alpine .ag-rtl .ag-charts-advanced-settings-top-level-group-toolbar, .ag-theme-alpine-dark .ag-rtl .ag-charts-format-top-level-group-toolbar, .ag-theme-alpine-dark .ag-rtl .ag-charts-advanced-settings-top-level-group-toolbar, .ag-theme-alpine-auto-dark .ag-rtl .ag-charts-format-top-level-group-toolbar, .ag-theme-alpine-auto-dark .ag-rtl .ag-charts-advanced-settings-top-level-group-toolbar {
  padding-right: calc(var(--ag-icon-size) * 0.5 + var(--ag-grid-size) * 2);
}

.ag-theme-alpine .ag-charts-format-sub-level-group,
.ag-theme-alpine-dark .ag-charts-format-sub-level-group,
.ag-theme-alpine-auto-dark .ag-charts-format-sub-level-group {
  border-left: dashed 1px;
  border-left-color: var(--ag-border-color);
  padding-left: var(--ag-grid-size);
  margin-bottom: calc(var(--ag-grid-size) * 2);
}
.ag-theme-alpine .ag-charts-format-sub-level-group-title-bar,
.ag-theme-alpine-dark .ag-charts-format-sub-level-group-title-bar,
.ag-theme-alpine-auto-dark .ag-charts-format-sub-level-group-title-bar {
  padding-top: 0;
  padding-bottom: 0;
  background: none;
  font-weight: 700;
}
.ag-theme-alpine .ag-charts-format-sub-level-group-container,
.ag-theme-alpine-dark .ag-charts-format-sub-level-group-container,
.ag-theme-alpine-auto-dark .ag-charts-format-sub-level-group-container {
  padding-bottom: 0;
}
.ag-theme-alpine .ag-charts-format-sub-level-group-item:last-child,
.ag-theme-alpine-dark .ag-charts-format-sub-level-group-item:last-child,
.ag-theme-alpine-auto-dark .ag-charts-format-sub-level-group-item:last-child {
  margin-bottom: 0;
}
.ag-theme-alpine.ag-dnd-ghost,
.ag-theme-alpine-dark.ag-dnd-ghost,
.ag-theme-alpine-auto-dark.ag-dnd-ghost {
  font-size: calc(var(--ag-font-size) - 1px);
  font-weight: 700;
}
.ag-theme-alpine .ag-side-buttons,
.ag-theme-alpine-dark .ag-side-buttons,
.ag-theme-alpine-auto-dark .ag-side-buttons {
  width: calc(var(--ag-grid-size) * 5);
}
.ag-theme-alpine .ag-standard-button,
.ag-theme-alpine-dark .ag-standard-button,
.ag-theme-alpine-auto-dark .ag-standard-button {
  font-family: inherit;
  appearance: none;
  -webkit-appearance: none;
  border-radius: var(--ag-border-radius);
  border: 1px solid;
  border-color: var(--ag-alpine-active-color);
  color: var(--ag-alpine-active-color);
  background-color: var(--ag-background-color);
  font-weight: 600;
  padding: var(--ag-grid-size) calc(var(--ag-grid-size) * 2);
}
.ag-theme-alpine .ag-standard-button:hover,
.ag-theme-alpine-dark .ag-standard-button:hover,
.ag-theme-alpine-auto-dark .ag-standard-button:hover {
  border-color: var(--ag-alpine-active-color);
  background-color: var(--ag-row-hover-color);
}
.ag-theme-alpine .ag-standard-button:active,
.ag-theme-alpine-dark .ag-standard-button:active,
.ag-theme-alpine-auto-dark .ag-standard-button:active {
  border-color: var(--ag-alpine-active-color);
  background-color: var(--ag-alpine-active-color);
  color: var(--ag-background-color);
}
.ag-theme-alpine .ag-standard-button:disabled,
.ag-theme-alpine-dark .ag-standard-button:disabled,
.ag-theme-alpine-auto-dark .ag-standard-button:disabled {
  color: var(--ag-disabled-foreground-color);
  background-color: var(--ag-input-disabled-background-color);
  border-color: var(--ag-input-disabled-border-color);
}
.ag-theme-alpine .ag-column-drop-vertical,
.ag-theme-alpine-dark .ag-column-drop-vertical,
.ag-theme-alpine-auto-dark .ag-column-drop-vertical {
  min-height: 75px;
}
.ag-theme-alpine .ag-column-drop-vertical-title-bar,
.ag-theme-alpine-dark .ag-column-drop-vertical-title-bar,
.ag-theme-alpine-auto-dark .ag-column-drop-vertical-title-bar {
  padding: calc(var(--ag-grid-size) * 2);
  padding-bottom: 0px;
}
.ag-theme-alpine .ag-column-drop-vertical-empty-message,
.ag-theme-alpine-dark .ag-column-drop-vertical-empty-message,
.ag-theme-alpine-auto-dark .ag-column-drop-vertical-empty-message {
  display: flex;
  align-items: center;
  border: dashed 1px;
  border-color: var(--ag-border-color);
  margin: calc(var(--ag-grid-size) * 2);
  padding: calc(var(--ag-grid-size) * 2);
}
.ag-theme-alpine .ag-column-drop-empty-message,
.ag-theme-alpine-dark .ag-column-drop-empty-message,
.ag-theme-alpine-auto-dark .ag-column-drop-empty-message {
  color: var(--ag-foreground-color);
  opacity: 0.75;
}
.ag-theme-alpine .ag-pill-select .ag-column-drop,
.ag-theme-alpine-dark .ag-pill-select .ag-column-drop,
.ag-theme-alpine-auto-dark .ag-pill-select .ag-column-drop {
  min-height: unset;
}
.ag-theme-alpine .ag-status-bar,
.ag-theme-alpine-dark .ag-status-bar,
.ag-theme-alpine-auto-dark .ag-status-bar {
  font-weight: normal;
}
.ag-theme-alpine .ag-status-name-value-value,
.ag-theme-alpine-dark .ag-status-name-value-value,
.ag-theme-alpine-auto-dark .ag-status-name-value-value {
  font-weight: 700;
}
.ag-theme-alpine .ag-paging-number,
.ag-theme-alpine .ag-paging-row-summary-panel-number,
.ag-theme-alpine-dark .ag-paging-number,
.ag-theme-alpine-dark .ag-paging-row-summary-panel-number,
.ag-theme-alpine-auto-dark .ag-paging-number,
.ag-theme-alpine-auto-dark .ag-paging-row-summary-panel-number {
  font-weight: 700;
}
.ag-theme-alpine .ag-column-drop-cell-button,
.ag-theme-alpine-dark .ag-column-drop-cell-button,
.ag-theme-alpine-auto-dark .ag-column-drop-cell-button {
  opacity: 0.5;
}
.ag-theme-alpine .ag-column-drop-cell-button:hover,
.ag-theme-alpine-dark .ag-column-drop-cell-button:hover,
.ag-theme-alpine-auto-dark .ag-column-drop-cell-button:hover {
  opacity: 0.75;
}
.ag-theme-alpine .ag-column-select-column-readonly.ag-icon-grip,
.ag-theme-alpine .ag-column-select-column-readonly .ag-icon-grip,
.ag-theme-alpine-dark .ag-column-select-column-readonly.ag-icon-grip,
.ag-theme-alpine-dark .ag-column-select-column-readonly .ag-icon-grip,
.ag-theme-alpine-auto-dark .ag-column-select-column-readonly.ag-icon-grip,
.ag-theme-alpine-auto-dark .ag-column-select-column-readonly .ag-icon-grip {
  opacity: 0.35;
}
.ag-theme-alpine .ag-header-cell-menu-button:hover,
.ag-theme-alpine .ag-header-cell-filter-button:hover,
.ag-theme-alpine .ag-side-button-button:hover,
.ag-theme-alpine .ag-tab:hover,
.ag-theme-alpine .ag-panel-title-bar-button:hover,
.ag-theme-alpine .ag-header-expand-icon:hover,
.ag-theme-alpine .ag-column-group-icons:hover,
.ag-theme-alpine .ag-set-filter-group-icons:hover,
.ag-theme-alpine .ag-group-expanded .ag-icon:hover,
.ag-theme-alpine .ag-group-contracted .ag-icon:hover,
.ag-theme-alpine .ag-chart-settings-prev:hover,
.ag-theme-alpine .ag-chart-settings-next:hover,
.ag-theme-alpine .ag-group-title-bar-icon:hover,
.ag-theme-alpine .ag-column-select-header-icon:hover,
.ag-theme-alpine .ag-floating-filter-button-button:hover,
.ag-theme-alpine .ag-filter-toolpanel-expand:hover,
.ag-theme-alpine .ag-chart-menu-icon:hover,
.ag-theme-alpine-dark .ag-header-cell-menu-button:hover,
.ag-theme-alpine-dark .ag-header-cell-filter-button:hover,
.ag-theme-alpine-dark .ag-side-button-button:hover,
.ag-theme-alpine-dark .ag-tab:hover,
.ag-theme-alpine-dark .ag-panel-title-bar-button:hover,
.ag-theme-alpine-dark .ag-header-expand-icon:hover,
.ag-theme-alpine-dark .ag-column-group-icons:hover,
.ag-theme-alpine-dark .ag-set-filter-group-icons:hover,
.ag-theme-alpine-dark .ag-group-expanded .ag-icon:hover,
.ag-theme-alpine-dark .ag-group-contracted .ag-icon:hover,
.ag-theme-alpine-dark .ag-chart-settings-prev:hover,
.ag-theme-alpine-dark .ag-chart-settings-next:hover,
.ag-theme-alpine-dark .ag-group-title-bar-icon:hover,
.ag-theme-alpine-dark .ag-column-select-header-icon:hover,
.ag-theme-alpine-dark .ag-floating-filter-button-button:hover,
.ag-theme-alpine-dark .ag-filter-toolpanel-expand:hover,
.ag-theme-alpine-dark .ag-chart-menu-icon:hover,
.ag-theme-alpine-auto-dark .ag-header-cell-menu-button:hover,
.ag-theme-alpine-auto-dark .ag-header-cell-filter-button:hover,
.ag-theme-alpine-auto-dark .ag-side-button-button:hover,
.ag-theme-alpine-auto-dark .ag-tab:hover,
.ag-theme-alpine-auto-dark .ag-panel-title-bar-button:hover,
.ag-theme-alpine-auto-dark .ag-header-expand-icon:hover,
.ag-theme-alpine-auto-dark .ag-column-group-icons:hover,
.ag-theme-alpine-auto-dark .ag-set-filter-group-icons:hover,
.ag-theme-alpine-auto-dark .ag-group-expanded .ag-icon:hover,
.ag-theme-alpine-auto-dark .ag-group-contracted .ag-icon:hover,
.ag-theme-alpine-auto-dark .ag-chart-settings-prev:hover,
.ag-theme-alpine-auto-dark .ag-chart-settings-next:hover,
.ag-theme-alpine-auto-dark .ag-group-title-bar-icon:hover,
.ag-theme-alpine-auto-dark .ag-column-select-header-icon:hover,
.ag-theme-alpine-auto-dark .ag-floating-filter-button-button:hover,
.ag-theme-alpine-auto-dark .ag-filter-toolpanel-expand:hover,
.ag-theme-alpine-auto-dark .ag-chart-menu-icon:hover {
  color: var(--ag-alpine-active-color);
}
.ag-theme-alpine .ag-header-cell-menu-button:hover .ag-icon,
.ag-theme-alpine .ag-header-cell-filter-button:hover .ag-icon,
.ag-theme-alpine .ag-side-button-button:hover .ag-icon,
.ag-theme-alpine .ag-panel-title-bar-button:hover .ag-icon,
.ag-theme-alpine .ag-floating-filter-button-button:hover .ag-icon,
.ag-theme-alpine-dark .ag-header-cell-menu-button:hover .ag-icon,
.ag-theme-alpine-dark .ag-header-cell-filter-button:hover .ag-icon,
.ag-theme-alpine-dark .ag-side-button-button:hover .ag-icon,
.ag-theme-alpine-dark .ag-panel-title-bar-button:hover .ag-icon,
.ag-theme-alpine-dark .ag-floating-filter-button-button:hover .ag-icon,
.ag-theme-alpine-auto-dark .ag-header-cell-menu-button:hover .ag-icon,
.ag-theme-alpine-auto-dark .ag-header-cell-filter-button:hover .ag-icon,
.ag-theme-alpine-auto-dark .ag-side-button-button:hover .ag-icon,
.ag-theme-alpine-auto-dark .ag-panel-title-bar-button:hover .ag-icon,
.ag-theme-alpine-auto-dark .ag-floating-filter-button-button:hover .ag-icon {
  color: inherit;
}
.ag-theme-alpine .ag-filter-active .ag-icon-filter,
.ag-theme-alpine-dark .ag-filter-active .ag-icon-filter,
.ag-theme-alpine-auto-dark .ag-filter-active .ag-icon-filter {
  color: var(--ag-alpine-active-color);
}
.ag-theme-alpine .ag-chart-settings-card-item.ag-not-selected:hover,
.ag-theme-alpine-dark .ag-chart-settings-card-item.ag-not-selected:hover,
.ag-theme-alpine-auto-dark .ag-chart-settings-card-item.ag-not-selected:hover {
  opacity: 0.35;
}
.ag-theme-alpine .ag-ltr .ag-panel-title-bar-button, .ag-theme-alpine-dark .ag-ltr .ag-panel-title-bar-button, .ag-theme-alpine-auto-dark .ag-ltr .ag-panel-title-bar-button {
  margin-left: calc(var(--ag-grid-size) * 2);
  margin-right: var(--ag-grid-size);
}

.ag-theme-alpine .ag-rtl .ag-panel-title-bar-button, .ag-theme-alpine-dark .ag-rtl .ag-panel-title-bar-button, .ag-theme-alpine-auto-dark .ag-rtl .ag-panel-title-bar-button {
  margin-right: calc(var(--ag-grid-size) * 2);
  margin-left: var(--ag-grid-size);
}

.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group-container, .ag-theme-alpine-dark .ag-ltr .ag-filter-toolpanel-group-container, .ag-theme-alpine-auto-dark .ag-ltr .ag-filter-toolpanel-group-container {
  padding-left: var(--ag-grid-size);
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group-container, .ag-theme-alpine-dark .ag-rtl .ag-filter-toolpanel-group-container, .ag-theme-alpine-auto-dark .ag-rtl .ag-filter-toolpanel-group-container {
  padding-right: var(--ag-grid-size);
}

.ag-theme-alpine .ag-filter-toolpanel-instance-filter,
.ag-theme-alpine-dark .ag-filter-toolpanel-instance-filter,
.ag-theme-alpine-auto-dark .ag-filter-toolpanel-instance-filter {
  border: none;
  background-color: var(--ag-control-panel-background-color);
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-instance-filter, .ag-theme-alpine-dark .ag-ltr .ag-filter-toolpanel-instance-filter, .ag-theme-alpine-auto-dark .ag-ltr .ag-filter-toolpanel-instance-filter {
  border-left: dashed 1px;
  border-left-color: var(--ag-border-color);
  margin-left: calc(var(--ag-icon-size) * 0.5);
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-instance-filter, .ag-theme-alpine-dark .ag-rtl .ag-filter-toolpanel-instance-filter, .ag-theme-alpine-auto-dark .ag-rtl .ag-filter-toolpanel-instance-filter {
  border-right: dashed 1px;
  border-right-color: var(--ag-border-color);
  margin-right: calc(var(--ag-icon-size) * 0.5);
}

.ag-theme-alpine .ag-set-filter-list,
.ag-theme-alpine-dark .ag-set-filter-list,
.ag-theme-alpine-auto-dark .ag-set-filter-list {
  padding-top: calc(var(--ag-grid-size) * 0.5);
  padding-bottom: calc(var(--ag-grid-size) * 0.5);
}
.ag-theme-alpine .ag-layout-auto-height .ag-center-cols-viewport,
.ag-theme-alpine .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-alpine .ag-layout-print .ag-center-cols-viewport,
.ag-theme-alpine .ag-layout-print .ag-center-cols-container,
.ag-theme-alpine-dark .ag-layout-auto-height .ag-center-cols-viewport,
.ag-theme-alpine-dark .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-alpine-dark .ag-layout-print .ag-center-cols-viewport,
.ag-theme-alpine-dark .ag-layout-print .ag-center-cols-container,
.ag-theme-alpine-auto-dark .ag-layout-auto-height .ag-center-cols-viewport,
.ag-theme-alpine-auto-dark .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-alpine-auto-dark .ag-layout-print .ag-center-cols-viewport,
.ag-theme-alpine-auto-dark .ag-layout-print .ag-center-cols-container {
  min-height: 150px;
}
.ag-theme-alpine .ag-date-time-list-page-entry-is-current,
.ag-theme-alpine-dark .ag-date-time-list-page-entry-is-current,
.ag-theme-alpine-auto-dark .ag-date-time-list-page-entry-is-current {
  background-color: var(--ag-alpine-active-color);
}
.ag-theme-alpine .ag-advanced-filter-builder-button,
.ag-theme-alpine-dark .ag-advanced-filter-builder-button,
.ag-theme-alpine-auto-dark .ag-advanced-filter-builder-button {
  padding: var(--ag-grid-size);
  font-weight: 600;
}
.ag-theme-alpine .ag-list-item-hovered::after,
.ag-theme-alpine-dark .ag-list-item-hovered::after,
.ag-theme-alpine-auto-dark .ag-list-item-hovered::after {
  background-color: var(--ag-alpine-active-color);
}
.ag-theme-alpine .ag-pill .ag-pill-button:hover,
.ag-theme-alpine-dark .ag-pill .ag-pill-button:hover,
.ag-theme-alpine-auto-dark .ag-pill .ag-pill-button:hover {
  color: var(--ag-alpine-active-color);
}
.ag-theme-alpine .ag-header-highlight-before::after,
.ag-theme-alpine .ag-header-highlight-after::after,
.ag-theme-alpine-dark .ag-header-highlight-before::after,
.ag-theme-alpine-dark .ag-header-highlight-after::after,
.ag-theme-alpine-auto-dark .ag-header-highlight-before::after,
.ag-theme-alpine-auto-dark .ag-header-highlight-after::after {
  background-color: var(--ag-alpine-active-color);
}
.ag-theme-alpine .ag-advanced-filter-builder-item-button-disabled .ag-icon,
.ag-theme-alpine .ag-disabled .ag-icon,
.ag-theme-alpine .ag-column-select-column-group-readonly .ag-icon,
.ag-theme-alpine [disabled] .ag-icon,
.ag-theme-alpine-dark .ag-advanced-filter-builder-item-button-disabled .ag-icon,
.ag-theme-alpine-dark .ag-disabled .ag-icon,
.ag-theme-alpine-dark .ag-column-select-column-group-readonly .ag-icon,
.ag-theme-alpine-dark [disabled] .ag-icon,
.ag-theme-alpine-auto-dark .ag-advanced-filter-builder-item-button-disabled .ag-icon,
.ag-theme-alpine-auto-dark .ag-disabled .ag-icon,
.ag-theme-alpine-auto-dark .ag-column-select-column-group-readonly .ag-icon,
.ag-theme-alpine-auto-dark [disabled] .ag-icon {
  color: var(--ag-disabled-foreground-color);
}
