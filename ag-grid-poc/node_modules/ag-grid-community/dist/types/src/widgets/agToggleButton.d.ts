import type { AgCheckboxParams } from '../interfaces/agFieldParams';
import { AgCheckbox } from './agCheckbox';
import type { ComponentSelector } from './component';
export interface AgToggleButtonParams extends AgCheckboxParams {
}
export declare class AgToggleButton extends AgCheckbox<AgToggleButtonParams> {
    constructor(config?: AgToggleButtonParams);
    setValue(value: boolean, silent?: boolean): this;
}
export declare const AgToggleButtonSelector: ComponentSelector;
