export declare const agToggleButtonCSS = ".ag-toggle-button{flex:none;min-width:unset;width:unset}.ag-toggle-button-input-wrapper{background-color:var(--ag-toggle-button-off-background-color);border-radius:calc(var(--ag-toggle-button-height)*.5);flex:none;height:var(--ag-toggle-button-height);max-width:var(--ag-toggle-button-width);min-width:var(--ag-toggle-button-width);position:relative;transition:background-color .1s;:where(.ag-toggle-button-input){-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;display:block;height:var(--ag-toggle-button-height);margin:0;max-width:var(--ag-toggle-button-width);min-width:var(--ag-toggle-button-width);opacity:0}&:before{background-color:var(--ag-toggle-button-switch-background-color);border-radius:100%;content:\"\";display:block;height:calc(var(--ag-toggle-button-height) - var(--ag-toggle-button-switch-inset)*2);left:var(--ag-toggle-button-switch-inset);pointer-events:none;position:absolute;top:var(--ag-toggle-button-switch-inset);transition:left .1s;width:calc(var(--ag-toggle-button-height) - var(--ag-toggle-button-switch-inset)*2)}&.ag-checked{background-color:var(--ag-toggle-button-on-background-color);&:before{left:calc(100% - var(--ag-toggle-button-height) + var(--ag-toggle-button-switch-inset))}}&:focus-within{box-shadow:var(--ag-focus-shadow)}&.ag-disabled{opacity:.5}}";
