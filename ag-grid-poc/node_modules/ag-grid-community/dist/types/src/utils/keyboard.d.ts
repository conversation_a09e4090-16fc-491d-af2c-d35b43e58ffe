import type { AgColumn } from '../entities/agColumn';
import type { GridOptionsService } from '../gridOptionsService';
import type { IRowNode } from '../interfaces/iRowNode';
export declare function _isEventFromPrintableCharacter(event: KeyboardEvent): boolean;
/**
 * Allows user to tell the grid to skip specific keyboard events
 * @param {GridOptionsService} gos
 * @param {KeyboardEvent} keyboardEvent
 * @param {IRowNode} rowNode
 * @param {Column} column
 * @param {boolean} editing
 * @returns {boolean}
 */
export declare function _isUserSuppressingKeyboardEvent(gos: GridOptionsService, keyboardEvent: KeyboardEvent, rowNode: IRowNode, column: AgColumn, editing: boolean): boolean;
