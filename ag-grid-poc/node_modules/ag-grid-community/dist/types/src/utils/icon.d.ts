import type { BeanCollection } from '../context/context';
import type { AgColumn } from '../entities/agColumn';
export type IconName = 'columnGroupOpened' | 'columnGroupClosed' | 'columnSelectClosed' | 'columnSelectOpen' | 'columnSelectIndeterminate' | 'accordionClosed' | 'accordionOpen' | 'accordionIndeterminate' | 'columnMovePin' | 'columnMoveHide' | 'columnMoveMove' | 'columnMoveLeft' | 'columnMoveRight' | 'columnMoveGroup' | 'columnMoveValue' | 'columnMovePivot' | 'dropNotAllowed' | 'groupContracted' | 'groupExpanded' | 'setFilterGroupClosed' | 'setFilterGroupOpen' | 'setFilterGroupIndeterminate' | 'setFilterLoading' | 'chart' | 'close' | 'cancel' | 'check' | 'first' | 'previous' | 'next' | 'last' | 'linked' | 'unlinked' | 'colorPicker' | 'groupLoading' | 'menu' | 'legacyMenu' | 'loadingMenuItems' | 'menuAlt' | 'filter' | 'filterActive' | 'filterTab' | 'filtersToolPanel' | 'columns' | 'columnsToolPanel' | 'maximize' | 'minimize' | 'menuPin' | 'menuValue' | 'menuAddRowGroup' | 'menuRemoveRowGroup' | 'clipboardCopy' | 'clipboardCut' | 'clipboardPaste' | 'pivotPanel' | 'rowGroupPanel' | 'valuePanel' | 'columnDrag' | 'rowDrag' | 'rowPin' | 'rowPinTop' | 'rowPinBottom' | 'rowUnpin' | 'save' | 'csvExport' | 'excelExport' | 'smallDown' | 'selectOpen' | 'richSelectOpen' | 'richSelectRemove' | 'smallLeft' | 'smallRight' | 'panelDelimiter' | 'panelDelimiterRtl' | 'subMenuOpen' | 'subMenuOpenRtl' | 'smallUp' | 'sortAscending' | 'sortDescending' | 'sortUnSort' | 'advancedFilterBuilder' | 'advancedFilterBuilderDrag' | 'advancedFilterBuilderInvalid' | 'advancedFilterBuilderMoveUp' | 'advancedFilterBuilderMoveDown' | 'advancedFilterBuilderAdd' | 'advancedFilterBuilderRemove' | 'advancedFilterBuilderSelectOpen' | 'chartsMenu' | 'chartsMenuEdit' | 'chartsMenuAdvancedSettings' | 'chartsMenuAdd' | 'chartsColorPicker' | 'chartsThemePrevious' | 'chartsThemeNext' | 'chartsDownload' | 'ensureColumnVisible' | 'checkboxChecked' | 'checkboxIndeterminate' | 'checkboxUnchecked' | 'radioButtonOn' | 'radioButtonOff';
export type IconValue = 'expanded' | 'contracted' | 'tree-closed' | 'tree-open' | 'tree-indeterminate' | 'pin' | 'eye-slash' | 'arrows' | 'left' | 'right' | 'group' | 'aggregation' | 'pivot' | 'not-allowed' | 'chart' | 'cross' | 'cancel' | 'tick' | 'first' | 'previous' | 'next' | 'last' | 'linked' | 'unlinked' | 'color-picker' | 'loading' | 'menu' | 'menu-alt' | 'filter' | 'columns' | 'maximize' | 'minimize' | 'copy' | 'cut' | 'paste' | 'grip' | 'save' | 'csv' | 'excel' | 'small-down' | 'small-left' | 'small-right' | 'small-up' | 'asc' | 'desc' | 'none' | 'up' | 'down' | 'plus' | 'minus' | 'settings' | 'checkbox-checked' | 'checkbox-indeterminate' | 'checkbox-unchecked' | 'radio-button-on' | 'radio-button-off' | 'eye' | 'column-arrow' | 'un-pin' | 'pinned-bottom' | 'pinned-top';
/**
 * If icon provided, use this (either a string, or a function callback).
 * if not, then use the default icon from the theme.
 * Technically `iconName` could be any string, if using user-provided icons map.
 * However, in most cases we're providing a specific icon name, so better to have type-checking.
 */
export declare function _createIcon(iconName: IconName, beans: BeanCollection, column: AgColumn | null): Element;
/**
 * Technically `iconName` could be any string, if using user-provided icons map.
 * However, in most cases we're providing a specific icon name, so better to have type-checking.
 */
export declare function _createIconNoSpan(iconName: IconName, beans: BeanCollection, column?: AgColumn | null): Element | undefined;
