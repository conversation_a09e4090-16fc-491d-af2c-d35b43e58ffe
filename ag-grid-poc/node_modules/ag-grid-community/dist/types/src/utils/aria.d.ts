import type { SortDirection } from '../entities/colDef';
import type { LocaleTextFunc } from '../misc/locale/localeUtils';
export type ColumnSortState = 'ascending' | 'descending' | 'other' | 'none';
export declare function _setAriaRole(element: Element, role?: string | null): void;
export declare function _getAriaSortState(sortDirection: SortDirection | 'mixed'): ColumnSortState;
export declare function _getAriaLevel(element: Element): number;
export declare function _getAriaPosInSet(element: Element): number;
export declare function _getAriaLabel(element: Element): string | null;
export declare function _setAriaLabel(element: Element, label?: string | null): void;
export declare function _setAriaLabelledBy(element: Element, labelledBy?: string): void;
export declare function _setAriaDescribedBy(element: Element, describedby?: string): void;
export declare function _setAriaLive(element: Element, live?: 'polite' | 'assertive' | 'off' | null): void;
export declare function _setAriaAtomic(element: Element, atomic: boolean | null): void;
export declare function _setAriaRelevant(element: Element, relevant: 'additions' | 'additions text' | 'all' | 'removals' | 'text' | null): void;
export declare function _setAriaLevel(element: Element, level: number): void;
export declare function _setAriaDisabled(element: Element, disabled: boolean): void;
export declare function _setAriaHidden(element: Element, hidden: boolean): void;
export declare function _setAriaActiveDescendant(element: Element, descendantId: string | null): void;
export declare function _setAriaExpanded(element: Element, expanded: boolean): void;
export declare function _removeAriaExpanded(element: Element): void;
export declare function _setAriaSetSize(element: Element, setsize: number): void;
export declare function _setAriaPosInSet(element: Element, position: number): void;
export declare function _setAriaMultiSelectable(element: Element, multiSelectable: boolean): void;
export declare function _setAriaRowCount(element: Element, rowCount: number): void;
export declare function _setAriaRowIndex(element: Element, rowIndex: number): void;
export declare function _setAriaRowSpan(element: Element, spanCount: number): void;
export declare function _setAriaColCount(element: Element, colCount: number): void;
export declare function _setAriaColIndex(element: Element, colIndex: number): void;
export declare function _setAriaColSpan(element: Element, colSpan: number): void;
export declare function _setAriaSort(element: Element, sort: ColumnSortState): void;
export declare function _removeAriaSort(element: Element): void;
export declare function _setAriaSelected(element: Element, selected?: boolean): void;
export declare function _setAriaChecked(element: Element, checked?: boolean): void;
export declare function _setAriaControls(controllerElement: Element, controlledElement: Element): void;
export declare function _setAriaHasPopup(element: Element, hasPopup: 'menu' | 'listbox' | 'tree' | 'grid' | 'dialog' | boolean): void;
export declare function _getAriaCheckboxStateName(translate: LocaleTextFunc, state?: boolean): string;
