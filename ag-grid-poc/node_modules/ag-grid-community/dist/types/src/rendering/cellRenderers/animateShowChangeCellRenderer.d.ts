import { Component } from '../../widgets/component';
import type { ICellRenderer } from './iCellRenderer';
export declare class AnimateShowChangeCellRenderer extends Component implements ICellRenderer {
    private lastValue;
    private eValue;
    private eDelta;
    private refreshCount;
    constructor();
    init(params: any): void;
    private showDelta;
    private setTimerToRemoveDelta;
    private hideDeltaValue;
    refresh(params: any, isInitialRender?: boolean): boolean;
}
