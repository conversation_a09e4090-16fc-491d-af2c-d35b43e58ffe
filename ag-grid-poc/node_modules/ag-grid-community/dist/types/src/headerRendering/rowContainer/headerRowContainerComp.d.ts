import type { ColumnPinnedType } from '../../interfaces/iColumn';
import { Component } from '../../widgets/component';
export declare class HeaderRowContainerComp extends Component {
    private eCenterContainer;
    private eRowContainer;
    private pinned;
    private headerRowComps;
    private rowCompsList;
    constructor(pinned: ColumnPinnedType);
    postConstruct(): void;
    private selectAndSetTemplate;
    destroy(): void;
    private destroyRowComp;
    private setCtrls;
}
