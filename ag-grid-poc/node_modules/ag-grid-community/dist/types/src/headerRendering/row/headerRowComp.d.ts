import { Component } from '../../widgets/component';
import type { HeaderRowCtrl } from './headerRowCtrl';
export type HeaderRowType = 'group' | 'column' | 'filter';
export declare class HeaderRowComp extends Component {
    private readonly ctrl;
    private headerComps;
    constructor(ctrl: HeaderRowCtrl);
    postConstruct(): void;
    destroy(): void;
    private setHeaderCtrls;
    private createHeaderComp;
}
