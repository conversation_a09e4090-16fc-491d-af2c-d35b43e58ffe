import type { ResizeFeature } from '../../../columnResize/resizeFeature';
import type { BeanStub } from '../../../context/beanStub';
import type { AgColumn } from '../../../entities/agColumn';
import type { HeaderClassParams } from '../../../entities/colDef';
import type { IHeader } from '../../../interfaces/iHeader';
import type { UserCompDetails } from '../../../interfaces/iUserCompDetails';
import type { ColumnSortState } from '../../../utils/aria';
import type { IAbstractHeaderCellComp } from '../abstractCell/abstractHeaderCellCtrl';
import { AbstractHeaderCellCtrl } from '../abstractCell/abstractHeaderCellCtrl';
export interface IHeaderCellComp extends IAbstractHeaderCellComp {
    setWidth(width: string): void;
    setAriaSort(sort?: ColumnSortState): void;
    setUserCompDetails(compDetails: UserCompDetails): void;
    getUserCompInstance(): IHeader | undefined;
}
type HeaderAriaDescriptionKey = 'filter' | 'menu' | 'sort' | 'selectAll' | 'filterButton';
type RefreshFunction = 'updateSortable' | 'tooltip' | 'headerClasses' | 'headerStyles' | 'wrapText' | 'measuring' | 'resize';
export declare class HeaderCellCtrl extends AbstractHeaderCellCtrl<IHeaderCellComp, AgColumn, ResizeFeature> {
    private refreshFunctions;
    private selectAllFeature?;
    private sortable;
    private displayName;
    private draggable;
    private menuEnabled;
    private openFilterEnabled;
    private dragSourceElement;
    private userCompDetails;
    private userHeaderClasses;
    private ariaDescriptionProperties;
    private tooltipFeature;
    setComp(comp: IHeaderCellComp, eGui: HTMLElement, eResize: HTMLElement, eHeaderCompWrapper: HTMLElement, compBeanInput: BeanStub | undefined): void;
    protected resizeHeader(delta: number, shiftKey: boolean): void;
    protected getHeaderClassParams(): HeaderClassParams;
    private setupUserComp;
    private setCompDetails;
    private lookupUserCompDetails;
    private createParams;
    private setupSelectAll;
    getSelectAllGui(): HTMLElement | undefined;
    protected handleKeyDown(e: KeyboardEvent): void;
    private onEnterKeyDown;
    private showMenuOnKeyPress;
    private onFocusIn;
    private onFocusOut;
    private setupTooltip;
    private setupStylesFromColDef;
    private setupClassesFromColDef;
    setDragSource(eSource: HTMLElement | undefined): void;
    private updateState;
    setRefreshFunction(name: RefreshFunction, func: () => void): void;
    private refresh;
    private refreshHeaderComp;
    private attemptHeaderCompRefresh;
    private calculateDisplayName;
    private checkDisplayName;
    private workOutDraggable;
    private setupWidth;
    private setupMovingCss;
    private setupMenuClass;
    private setupSortableClass;
    private setupFilterClass;
    private setupWrapTextClass;
    private onHeaderHighlightChanged;
    protected onDisplayedColumnsChanged(): void;
    private onHeaderHeightChanged;
    private refreshSpanHeaderHeight;
    private refreshAriaSort;
    private refreshAriaMenu;
    private refreshAriaFilterButton;
    private refreshAriaFiltered;
    setAriaDescriptionProperty(property: HeaderAriaDescriptionKey, value: string | null): void;
    announceAriaDescription(): void;
    private refreshAria;
    private addColumnHoverListener;
    private addActiveHeaderMouseListeners;
    private handleMouseOverChange;
    private setActiveHeader;
    getAnchorElementForMenu(isFilter?: boolean): HTMLElement;
    destroy(): void;
}
export {};
