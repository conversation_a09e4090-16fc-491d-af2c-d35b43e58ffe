import { AbstractHeaderCellComp } from '../abstractCell/abstractHeaderCellComp';
import type { HeaderFilterCellCtrl } from './headerFilterCellCtrl';
export declare class HeaderFilterCellComp extends AbstractHeaderCellComp<HeaderFilterCellCtrl> {
    private readonly eFloatingFilterBody;
    private readonly eButtonWrapper;
    private readonly eButtonShowMainFilter;
    private floatingFilterComp;
    private compPromise;
    constructor(ctrl: HeaderFilterCellCtrl);
    postConstruct(): void;
    private setCompDetails;
    destroy(): void;
    private destroyFloatingFilterComp;
    private afterCompCreated;
}
