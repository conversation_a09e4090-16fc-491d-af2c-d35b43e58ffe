import type { NamedBean } from '../context/bean';
import { BeanStub } from '../context/beanStub';
import type { AgColumn } from '../entities/agColumn';
import type { RowNode } from '../entities/rowNode';
import type { RowPinningState } from '../interfaces/gridState';
import type { IPinnedRowModel } from '../interfaces/iPinnedRowModel';
import type { RowPinnedType } from '../interfaces/iRowNode';
export declare class PinnedRowModel extends BeanStub implements NamedBean, IPinnedRowModel {
    beanName: "pinnedRowModel";
    private inner;
    postConstruct(): void;
    reset(): void;
    isEmpty(container: NonNullable<RowPinnedType>): boolean;
    isManual(): boolean;
    isRowsToRender(container: NonNullable<RowPinnedType>): boolean;
    pinRow(node: RowNode<any>, container: RowPinnedType, column?: AgColumn | null): void;
    ensureRowHeightsValid(): boolean;
    getPinnedRowById(id: string, container: NonNullable<RowPinnedType>): RowNode<any> | undefined;
    getPinnedTopTotalHeight(): number;
    getPinnedBottomTotalHeight(): number;
    getPinnedTopRowCount(): number;
    getPinnedBottomRowCount(): number;
    getPinnedTopRow(index: number): RowNode | undefined;
    getPinnedBottomRow(index: number): RowNode | undefined;
    forEachPinnedRow(container: NonNullable<RowPinnedType>, callback: (node: RowNode, index: number) => void): void;
    getPinnedState(): RowPinningState;
    setPinnedState(state: RowPinningState): void;
    setGrandTotalPinned(value: RowPinnedType): void;
    getGrandTotalPinned(): RowPinnedType;
}
