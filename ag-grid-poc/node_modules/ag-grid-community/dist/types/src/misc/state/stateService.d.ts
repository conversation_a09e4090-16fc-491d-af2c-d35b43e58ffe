import type { NamedBean } from '../../context/bean';
import { BeanStub } from '../../context/beanStub';
import type { GridState } from '../../interfaces/gridState';
export declare class StateService extends BeanStub implements NamedBean {
    beanName: "stateSvc";
    private updateRowGroupExpansionStateTimer;
    private isClientSideRowModel;
    private cachedState;
    private suppressEvents;
    private queuedUpdateSources;
    private dispatchStateUpdateEventDebounced;
    private onRowGroupOpenedDebounced;
    private onRowSelectedDebounced;
    private columnStates?;
    private columnGroupStates?;
    private staleStateKeys;
    postConstruct(): void;
    destroy(): void;
    private getInitialState;
    getState(): GridState;
    private setupStateOnGridReady;
    private setupStateOnColumnsInitialised;
    private setupStateOnRowCountReady;
    private setupStateOnFirstDataRendered;
    private getColumnState;
    private setColumnState;
    private setColumnPivotState;
    private getColumnGroupState;
    private setColumnGroupState;
    private getFilterState;
    private setFilterState;
    private getRangeSelectionState;
    private setCellSelectionState;
    private getScrollState;
    private setScrollState;
    private getSideBarState;
    private getFocusedCellState;
    private setFocusedCellState;
    private getPaginationState;
    private setPaginationState;
    private getRowSelectionState;
    private setRowSelectionState;
    private getRowGroupExpansionState;
    private getRowPinningState;
    private setRowPinningState;
    private setRowGroupExpansionState;
    private updateColumnState;
    private updateCachedState;
    private setCachedStateValue;
    private refreshStaleState;
    private dispatchStateUpdateEvent;
    private dispatchQueuedStateUpdateEvents;
    private suppressEventsAndDispatchInitEvent;
}
