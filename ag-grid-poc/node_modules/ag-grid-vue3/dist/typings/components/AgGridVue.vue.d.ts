import type { AdvancedFilterBuilderVisibleChangedEvent, AsyncTransactionsFlushedEvent, BodyScrollEndEvent, BodyScrollEvent, CellClickedEvent, CellContextMenuEvent, CellDoubleClickedEvent, CellEditRequestEvent, CellEditingStartedEvent, CellEditingStoppedEvent, CellFocusedEvent, CellKeyDownEvent, CellMouseDownEvent, CellMouseOutEvent, CellMouseOverEvent, CellSelectionChangedEvent, CellSelectionDeleteEndEvent, CellSelectionDeleteStartEvent, CellValueChangedEvent, ChartCreatedEvent, ChartDestroyedEvent, ChartOptionsChangedEvent, ChartRangeSelectionChangedEvent, ColumnEverythingChangedEvent, ColumnGroupOpenedEvent, ColumnHeaderClickedEvent, ColumnHeaderContextMenuEvent, ColumnHeaderMouseLeaveEvent, ColumnHeaderMouseOverEvent, ColumnMenuVisibleChangedEvent, ColumnMovedEvent, ColumnPinnedEvent, ColumnPivotChangedEvent, ColumnPivotModeChangedEvent, ColumnResizedEvent, ColumnRowGroupChangedEvent, ColumnValueChangedEvent, ColumnVisibleEvent, ComponentStateChangedEvent, ContextMenuVisibleChangedEvent, CutEndEvent, CutStartEvent, DisplayedColumnsChangedEvent, DragCancelledEvent, DragStartedEvent, DragStoppedEvent, ExpandOrCollapseAllEvent, FillEndEvent, FillStartEvent, FilterChangedEvent, FilterModifiedEvent, FilterOpenedEvent, FindChangedEvent, FirstDataRenderedEvent, FullWidthCellKeyDownEvent, GridColumnsChangedEvent, GridPreDestroyedEvent, GridReadyEvent, GridSizeChangedEvent, HeaderFocusedEvent, ModelUpdatedEvent, NewColumnsLoadedEvent, PaginationChangedEvent, PasteEndEvent, PasteStartEvent, PinnedRowDataChangedEvent, PinnedRowsChangedEvent, PivotMaxColumnsExceededEvent, RangeDeleteEndEvent, RangeDeleteStartEvent, RangeSelectionChangedEvent, RedoEndedEvent, RedoStartedEvent, RowClickedEvent, RowDataUpdatedEvent, RowDoubleClickedEvent, RowDragCancelEvent, RowDragEndEvent, RowDragEnterEvent, RowDragLeaveEvent, RowDragMoveEvent, RowEditingStartedEvent, RowEditingStoppedEvent, RowGroupOpenedEvent, RowResizeEndedEvent, RowResizeStartedEvent, RowSelectedEvent, RowValueChangedEvent, SelectionChangedEvent, SortChangedEvent, StateUpdatedEvent, StoreRefreshedEvent, ToolPanelSizeChangedEvent, ToolPanelVisibleChangedEvent, TooltipHideEvent, TooltipShowEvent, UndoEndedEvent, UndoStartedEvent, ViewportChangedEvent, VirtualColumnsChangedEvent, VirtualRowRemovedEvent } from 'ag-grid-community';
import type { Ref } from 'vue';
import type { ColDef, GridApi, GridOptions, IRowNode } from 'ag-grid-community';
declare const _default: <TData = any>(__VLS_props: {
    readonly "onUpdate:modelValue"?: ((...args: unknown[]) => any) | undefined;
    modelValue?: TData[] | undefined;
    gridOptions?: GridOptions<TData> | undefined;
    modules?: import("ag-grid-community").Module[] | undefined;
    statusBar?: {
        statusPanels: import("ag-grid-community").StatusPanelDef[];
    } | undefined;
    sideBar?: string | boolean | string[] | import("ag-grid-community").SideBarDef | null | undefined;
    suppressContextMenu?: boolean | undefined;
    preventDefaultOnContextMenu?: boolean | undefined;
    allowContextMenuWithControlKey?: boolean | undefined;
    columnMenu?: "legacy" | "new" | undefined;
    suppressMenuHide?: boolean | undefined;
    enableBrowserTooltips?: boolean | undefined;
    tooltipTrigger?: "hover" | "focus" | undefined;
    tooltipShowDelay?: number | undefined;
    tooltipHideDelay?: number | undefined;
    tooltipMouseTrack?: boolean | undefined;
    tooltipShowMode?: "standard" | "whenTruncated" | undefined;
    tooltipInteraction?: boolean | undefined;
    popupParent?: HTMLElement | null | undefined;
    copyHeadersToClipboard?: boolean | undefined;
    copyGroupHeadersToClipboard?: boolean | undefined;
    clipboardDelimiter?: string | undefined;
    suppressCopyRowsToClipboard?: boolean | undefined;
    suppressCopySingleCellRanges?: boolean | undefined;
    suppressLastEmptyLineOnPaste?: boolean | undefined;
    suppressClipboardPaste?: boolean | undefined;
    suppressClipboardApi?: boolean | undefined;
    suppressCutToClipboard?: boolean | undefined;
    columnDefs?: (ColDef<any, any> | import("ag-grid-community").ColGroupDef<TData>)[] | null | undefined;
    defaultColDef?: ColDef<any, any> | undefined;
    defaultColGroupDef?: Partial<import("ag-grid-community").ColGroupDef<TData>> | undefined;
    columnTypes?: {
        [key: string]: import("ag-grid-community").ColTypeDef<TData>;
    } | undefined;
    dataTypeDefinitions?: {
        [cellDataType: string]: import("ag-grid-community").DataTypeDefinition<TData>;
    } | undefined;
    maintainColumnOrder?: boolean | undefined;
    enableStrictPivotColumnOrder?: boolean | undefined;
    suppressFieldDotNotation?: boolean | undefined;
    headerHeight?: number | undefined;
    groupHeaderHeight?: number | undefined;
    floatingFiltersHeight?: number | undefined;
    pivotHeaderHeight?: number | undefined;
    pivotGroupHeaderHeight?: number | undefined;
    allowDragFromColumnsToolPanel?: boolean | undefined;
    suppressMovableColumns?: boolean | undefined;
    suppressColumnMoveAnimation?: boolean | undefined;
    suppressMoveWhenColumnDragging?: boolean | undefined;
    suppressDragLeaveHidesColumns?: boolean | undefined;
    suppressGroupChangesColumnVisibility?: boolean | "suppressHideOnGroup" | "suppressShowOnUngroup" | undefined;
    suppressMakeColumnVisibleAfterUnGroup?: boolean | undefined;
    suppressRowGroupHidesColumns?: boolean | undefined;
    colResizeDefault?: "shift" | undefined;
    suppressAutoSize?: boolean | undefined;
    autoSizePadding?: number | undefined;
    skipHeaderOnAutoSize?: boolean | undefined;
    autoSizeStrategy?: import("ag-grid-community").SizeColumnsToFitGridStrategy | import("ag-grid-community").SizeColumnsToFitProvidedWidthStrategy | import("ag-grid-community").SizeColumnsToContentStrategy | undefined;
    components?: {
        [p: string]: any;
    } | undefined;
    editType?: "fullRow" | undefined;
    singleClickEdit?: boolean | undefined;
    suppressClickEdit?: boolean | undefined;
    readOnlyEdit?: boolean | undefined;
    stopEditingWhenCellsLoseFocus?: boolean | undefined;
    enterNavigatesVertically?: boolean | undefined;
    enterNavigatesVerticallyAfterEdit?: boolean | undefined;
    enableCellEditingOnBackspace?: boolean | undefined;
    undoRedoCellEditing?: boolean | undefined;
    undoRedoCellEditingLimit?: number | undefined;
    defaultCsvExportParams?: import("ag-grid-community").CsvExportParams | undefined;
    suppressCsvExport?: boolean | undefined;
    defaultExcelExportParams?: import("ag-grid-community").ExcelExportParams | undefined;
    suppressExcelExport?: boolean | undefined;
    excelStyles?: import("ag-grid-community").ExcelStyle[] | undefined;
    findSearchValue?: string | undefined;
    findOptions?: import("ag-grid-community").FindOptions | undefined;
    quickFilterText?: string | undefined;
    cacheQuickFilter?: boolean | undefined;
    includeHiddenColumnsInQuickFilter?: boolean | undefined;
    quickFilterParser?: ((quickFilter: string) => string[]) | undefined;
    quickFilterMatcher?: ((quickFilterParts: string[], rowQuickFilterAggregateText: string) => boolean) | undefined;
    applyQuickFilterBeforePivotOrAgg?: boolean | undefined;
    excludeChildrenWhenTreeDataFiltering?: boolean | undefined;
    enableAdvancedFilter?: boolean | undefined;
    alwaysPassFilter?: ((rowNode: IRowNode<TData>) => boolean) | undefined;
    includeHiddenColumnsInAdvancedFilter?: boolean | undefined;
    advancedFilterParent?: HTMLElement | null | undefined;
    advancedFilterBuilderParams?: import("ag-grid-community").IAdvancedFilterBuilderParams | undefined;
    suppressAdvancedFilterEval?: boolean | undefined;
    suppressSetFilterByDefault?: boolean | undefined;
    enableCharts?: boolean | undefined;
    chartThemes?: string[] | undefined;
    customChartThemes?: {
        [name: string]: import("ag-charts-types").AgChartTheme;
    } | undefined;
    chartThemeOverrides?: import("ag-charts-types").AgChartThemeOverrides | undefined;
    chartToolPanelsDef?: import("ag-grid-community").ChartToolPanelsDef | undefined;
    chartMenuItems?: (import("ag-grid-community").DefaultChartMenuItem | import("ag-grid-community").MenuItemDef<any, any>)[] | import("ag-grid-community").GetChartMenuItems<TData, any> | undefined;
    loadingCellRenderer?: any;
    loadingCellRendererParams?: any;
    loadingCellRendererSelector?: import("ag-grid-community").LoadingCellRendererSelectorFunc<TData> | undefined;
    localeText?: {
        [key: string]: string;
    } | undefined;
    masterDetail?: boolean | undefined;
    keepDetailRows?: boolean | undefined;
    keepDetailRowsCount?: number | undefined;
    detailCellRenderer?: any;
    detailCellRendererParams?: any;
    detailRowHeight?: number | undefined;
    detailRowAutoHeight?: boolean | undefined;
    context?: any;
    alignedGrids?: import("ag-grid-community").AlignedGrid[] | (() => import("ag-grid-community").AlignedGrid[]) | undefined;
    tabIndex?: number | undefined;
    rowBuffer?: number | undefined;
    valueCache?: boolean | undefined;
    valueCacheNeverExpires?: boolean | undefined;
    enableCellExpressions?: boolean | undefined;
    suppressTouch?: boolean | undefined;
    suppressFocusAfterRefresh?: boolean | undefined;
    suppressBrowserResizeObserver?: boolean | undefined;
    suppressPropertyNamesCheck?: boolean | undefined;
    suppressChangeDetection?: boolean | undefined;
    debug?: boolean | undefined;
    loading?: boolean | undefined;
    overlayLoadingTemplate?: string | undefined;
    loadingOverlayComponent?: any;
    loadingOverlayComponentParams?: any;
    suppressLoadingOverlay?: boolean | undefined;
    overlayNoRowsTemplate?: string | undefined;
    noRowsOverlayComponent?: any;
    noRowsOverlayComponentParams?: any;
    suppressNoRowsOverlay?: boolean | undefined;
    pagination?: boolean | undefined;
    paginationPageSize?: number | undefined;
    paginationPageSizeSelector?: boolean | number[] | undefined;
    paginationAutoPageSize?: boolean | undefined;
    paginateChildRows?: boolean | undefined;
    suppressPaginationPanel?: boolean | undefined;
    pivotMode?: boolean | undefined;
    pivotPanelShow?: "always" | "onlyWhenPivoting" | "never" | undefined;
    pivotMaxGeneratedColumns?: number | undefined;
    pivotDefaultExpanded?: number | undefined;
    pivotColumnGroupTotals?: "before" | "after" | undefined;
    pivotRowTotals?: "before" | "after" | undefined;
    pivotSuppressAutoColumn?: boolean | undefined;
    suppressExpandablePivotGroups?: boolean | undefined;
    functionsReadOnly?: boolean | undefined;
    aggFuncs?: {
        [key: string]: import("ag-grid-community").IAggFunc<TData, any>;
    } | undefined;
    suppressAggFuncInHeader?: boolean | undefined;
    alwaysAggregateAtRootLevel?: boolean | undefined;
    aggregateOnlyChangedColumns?: boolean | undefined;
    suppressAggFilteredOnly?: boolean | undefined;
    removePivotHeaderRowWhenSingleValueColumn?: boolean | undefined;
    animateRows?: boolean | undefined;
    cellFlashDuration?: number | undefined;
    cellFadeDuration?: number | undefined;
    allowShowChangeAfterFilter?: boolean | undefined;
    domLayout?: import("ag-grid-community").DomLayoutType | undefined;
    ensureDomOrder?: boolean | undefined;
    enableCellSpan?: boolean | undefined;
    enableRtl?: boolean | undefined;
    suppressColumnVirtualisation?: boolean | undefined;
    suppressMaxRenderedRowRestriction?: boolean | undefined;
    suppressRowVirtualisation?: boolean | undefined;
    rowDragManaged?: boolean | undefined;
    suppressRowDrag?: boolean | undefined;
    suppressMoveWhenRowDragging?: boolean | undefined;
    rowDragEntireRow?: boolean | undefined;
    rowDragMultiRow?: boolean | undefined;
    rowDragText?: ((params: import("ag-grid-community").IRowDragItem, dragItemCount: number) => string) | undefined;
    dragAndDropImageComponent?: any;
    dragAndDropImageComponentParams?: any;
    fullWidthCellRenderer?: any;
    fullWidthCellRendererParams?: any;
    embedFullWidthRows?: boolean | undefined;
    groupDisplayType?: import("ag-grid-community").RowGroupingDisplayType | undefined;
    groupDefaultExpanded?: number | undefined;
    autoGroupColumnDef?: ColDef<TData, any> | undefined;
    groupMaintainOrder?: boolean | undefined;
    groupSelectsChildren?: boolean | undefined;
    groupLockGroupColumns?: number | undefined;
    groupAggFiltering?: boolean | import("ag-grid-community").IsRowFilterable<TData> | undefined;
    groupTotalRow?: "top" | "bottom" | import("ag-grid-community").UseGroupTotalRow<TData> | undefined;
    grandTotalRow?: "top" | "bottom" | "pinnedTop" | "pinnedBottom" | undefined;
    suppressStickyTotalRow?: boolean | "grand" | "group" | undefined;
    groupSuppressBlankHeader?: boolean | undefined;
    groupSelectsFiltered?: boolean | undefined;
    showOpenedGroup?: boolean | undefined;
    groupHideParentOfSingleChild?: boolean | "leafGroupsOnly" | undefined;
    groupRemoveSingleChildren?: boolean | undefined;
    groupRemoveLowestSingleChildren?: boolean | undefined;
    groupHideOpenParents?: boolean | undefined;
    groupAllowUnbalanced?: boolean | undefined;
    rowGroupPanelShow?: "always" | "never" | "onlyWhenGrouping" | undefined;
    groupRowRenderer?: any;
    groupRowRendererParams?: any;
    treeData?: boolean | undefined;
    treeDataChildrenField?: string | undefined;
    treeDataParentIdField?: string | undefined;
    rowGroupPanelSuppressSort?: boolean | undefined;
    suppressGroupRowsSticky?: boolean | undefined;
    pinnedTopRowData?: any[] | undefined;
    pinnedBottomRowData?: any[] | undefined;
    enableRowPinning?: boolean | "top" | "bottom" | undefined;
    isRowPinnable?: import("ag-grid-community").IsRowPinnable<TData> | undefined;
    isRowPinned?: import("ag-grid-community").IsRowPinned<TData> | undefined;
    rowModelType?: import("ag-grid-community").RowModelType | undefined;
    rowData?: TData[] | null | undefined;
    asyncTransactionWaitMillis?: number | undefined;
    suppressModelUpdateAfterUpdateTransaction?: boolean | undefined;
    datasource?: import("ag-grid-community").IDatasource | undefined;
    cacheOverflowSize?: number | undefined;
    infiniteInitialRowCount?: number | undefined;
    serverSideInitialRowCount?: number | undefined;
    suppressServerSideFullWidthLoadingRow?: boolean | undefined;
    cacheBlockSize?: number | undefined;
    maxBlocksInCache?: number | undefined;
    maxConcurrentDatasourceRequests?: number | undefined;
    blockLoadDebounceMillis?: number | undefined;
    purgeClosedRowNodes?: boolean | undefined;
    serverSideDatasource?: import("ag-grid-community").IServerSideDatasource<any> | undefined;
    serverSideSortAllLevels?: boolean | undefined;
    serverSideEnableClientSideSort?: boolean | undefined;
    serverSideOnlyRefreshFilteredGroups?: boolean | undefined;
    serverSidePivotResultFieldSeparator?: string | undefined;
    viewportDatasource?: import("ag-grid-community").IViewportDatasource | undefined;
    viewportRowModelPageSize?: number | undefined;
    viewportRowModelBufferSize?: number | undefined;
    alwaysShowHorizontalScroll?: boolean | undefined;
    alwaysShowVerticalScroll?: boolean | undefined;
    debounceVerticalScrollbar?: boolean | undefined;
    suppressHorizontalScroll?: boolean | undefined;
    suppressScrollOnNewData?: boolean | undefined;
    suppressScrollWhenPopupsAreOpen?: boolean | undefined;
    suppressAnimationFrame?: boolean | undefined;
    suppressMiddleClickScrolls?: boolean | undefined;
    suppressPreventDefaultOnMouseWheel?: boolean | undefined;
    scrollbarWidth?: number | undefined;
    rowSelection?: "single" | "multiple" | import("ag-grid-community").RowSelectionOptions<TData> | undefined;
    cellSelection?: boolean | import("ag-grid-community").CellSelectionOptions<TData> | undefined;
    rowMultiSelectWithClick?: boolean | undefined;
    suppressRowDeselection?: boolean | undefined;
    suppressRowClickSelection?: boolean | undefined;
    suppressCellFocus?: boolean | undefined;
    suppressHeaderFocus?: boolean | undefined;
    selectionColumnDef?: import("ag-grid-community").SelectionColumnDef | undefined;
    rowNumbers?: boolean | import("ag-grid-community").RowNumbersOptions | undefined;
    suppressMultiRangeSelection?: boolean | undefined;
    enableCellTextSelection?: boolean | undefined;
    enableRangeSelection?: boolean | undefined;
    enableRangeHandle?: boolean | undefined;
    enableFillHandle?: boolean | undefined;
    fillHandleDirection?: "x" | "y" | "xy" | undefined;
    suppressClearOnFillReduction?: boolean | undefined;
    sortingOrder?: import("ag-grid-community").SortDirection[] | undefined;
    accentedSort?: boolean | undefined;
    unSortIcon?: boolean | undefined;
    suppressMultiSort?: boolean | undefined;
    alwaysMultiSort?: boolean | undefined;
    multiSortKey?: "ctrl" | undefined;
    suppressMaintainUnsortedOrder?: boolean | undefined;
    icons?: {
        [key: string]: string | ((...args: any[]) => any);
    } | undefined;
    rowHeight?: number | undefined;
    rowStyle?: import("ag-grid-community").RowStyle | undefined;
    rowClass?: string | string[] | undefined;
    rowClassRules?: import("ag-grid-community").RowClassRules<TData> | undefined;
    suppressRowHoverHighlight?: boolean | undefined;
    suppressRowTransform?: boolean | undefined;
    columnHoverHighlight?: boolean | undefined;
    gridId?: string | undefined;
    deltaSort?: boolean | undefined;
    treeDataDisplayType?: import("ag-grid-community").TreeDataDisplayType | undefined;
    enableGroupEdit?: boolean | undefined;
    initialState?: import("ag-grid-community").GridState | undefined;
    theme?: "legacy" | import("ag-grid-community").Theme | undefined;
    loadThemeGoogleFonts?: boolean | undefined;
    themeCssLayer?: string | undefined;
    styleNonce?: string | undefined;
    themeStyleContainer?: HTMLElement | undefined;
    getContextMenuItems?: import("ag-grid-community").GetContextMenuItems<TData, any> | undefined;
    getMainMenuItems?: import("ag-grid-community").GetMainMenuItems<TData, any> | undefined;
    postProcessPopup?: ((params: import("ag-grid-community").PostProcessPopupParams<TData, any>) => void) | undefined;
    processUnpinnedColumns?: ((params: import("ag-grid-community").ProcessUnpinnedColumnsParams<TData, any>) => import("ag-grid-community").Column<any>[]) | undefined;
    processCellForClipboard?: ((params: import("ag-grid-community").ProcessCellForExportParams<TData, any>) => any) | undefined;
    processHeaderForClipboard?: ((params: import("ag-grid-community").ProcessHeaderForExportParams<TData, any>) => any) | undefined;
    processGroupHeaderForClipboard?: ((params: import("ag-grid-community").ProcessGroupHeaderForExportParams<TData, any>) => any) | undefined;
    processCellFromClipboard?: ((params: import("ag-grid-community").ProcessCellForExportParams<TData, any>) => any) | undefined;
    sendToClipboard?: ((params: import("ag-grid-community").SendToClipboardParams<TData, any>) => void) | undefined;
    processDataFromClipboard?: ((params: import("ag-grid-community").ProcessDataFromClipboardParams<TData, any>) => string[][] | null) | undefined;
    isExternalFilterPresent?: ((params: import("ag-grid-community").IsExternalFilterPresentParams<TData, any>) => boolean) | undefined;
    doesExternalFilterPass?: ((node: IRowNode<TData>) => boolean) | undefined;
    getChartToolbarItems?: import("ag-grid-community").GetChartToolbarItems | undefined;
    createChartContainer?: ((params: import("ag-grid-community").ChartRefParams<TData>) => void) | undefined;
    focusGridInnerElement?: ((params: import("ag-grid-community").FocusGridInnerElementParams<TData, any>) => boolean) | undefined;
    navigateToNextHeader?: ((params: import("ag-grid-community").NavigateToNextHeaderParams<TData, any>) => import("ag-grid-community").HeaderPosition | null) | undefined;
    tabToNextHeader?: ((params: import("ag-grid-community").TabToNextHeaderParams<TData, any>) => boolean | import("ag-grid-community").HeaderPosition) | undefined;
    navigateToNextCell?: ((params: import("ag-grid-community").NavigateToNextCellParams<TData, any>) => import("ag-grid-community").CellPosition | null) | undefined;
    tabToNextCell?: ((params: import("ag-grid-community").TabToNextCellParams<TData, any>) => boolean | import("ag-grid-community").CellPosition) | undefined;
    getLocaleText?: ((params: import("ag-grid-community").GetLocaleTextParams<TData, any>) => string) | undefined;
    getDocument?: (() => Document) | undefined;
    paginationNumberFormatter?: ((params: import("ag-grid-community").PaginationNumberFormatterParams<TData, any>) => string) | undefined;
    getGroupRowAgg?: ((params: import("ag-grid-community").GetGroupRowAggParams<TData, any>) => any) | undefined;
    isGroupOpenByDefault?: ((params: import("ag-grid-community").IsGroupOpenByDefaultParams<TData, any>) => boolean) | undefined;
    initialGroupOrderComparator?: ((params: import("ag-grid-community").InitialGroupOrderComparatorParams<TData, any>) => number) | undefined;
    processPivotResultColDef?: ((colDef: ColDef<TData, any>) => void) | undefined;
    processPivotResultColGroupDef?: ((colGroupDef: import("ag-grid-community").ColGroupDef<TData>) => void) | undefined;
    getDataPath?: import("ag-grid-community").GetDataPath<TData> | undefined;
    getChildCount?: ((dataItem: any) => number) | undefined;
    getServerSideGroupLevelParams?: ((params: import("ag-grid-community").GetServerSideGroupLevelParamsParams) => import("ag-grid-community").ServerSideGroupLevelParams) | undefined;
    isServerSideGroupOpenByDefault?: ((params: import("ag-grid-community").IsServerSideGroupOpenByDefaultParams) => boolean) | undefined;
    isApplyServerSideTransaction?: import("ag-grid-community").IsApplyServerSideTransaction | undefined;
    isServerSideGroup?: import("ag-grid-community").IsServerSideGroup | undefined;
    getServerSideGroupKey?: import("ag-grid-community").GetServerSideGroupKey | undefined;
    getBusinessKeyForNode?: ((node: IRowNode<TData>) => string) | undefined;
    getRowId?: import("ag-grid-community").GetRowIdFunc<TData> | undefined;
    resetRowDataOnUpdate?: boolean | undefined;
    processRowPostCreate?: ((params: import("ag-grid-community").ProcessRowParams<TData, any>) => void) | undefined;
    isRowSelectable?: import("ag-grid-community").IsRowSelectable<TData> | undefined;
    isRowMaster?: import("ag-grid-community").IsRowMaster<TData> | undefined;
    fillOperation?: ((params: import("ag-grid-community").FillOperationParams<TData, any>) => any) | undefined;
    postSortRows?: ((params: import("ag-grid-community").PostSortRowsParams<TData, any>) => void) | undefined;
    getRowStyle?: ((params: import("ag-grid-community").RowClassParams<TData, any>) => import("ag-grid-community").RowStyle | undefined) | undefined;
    getRowClass?: ((params: import("ag-grid-community").RowClassParams<TData, any>) => string | string[] | undefined) | undefined;
    getRowHeight?: ((params: import("ag-grid-community").RowHeightParams<TData, any>) => number | null | undefined) | undefined;
    isFullWidthRow?: ((params: import("ag-grid-community").IsFullWidthRowParams<TData, any>) => boolean) | undefined;
    'onTool-panel-visible-changed'?: ToolPanelVisibleChangedEvent<TData, any> | undefined;
    'onTool-panel-size-changed'?: ToolPanelSizeChangedEvent<TData, any> | undefined;
    'onColumn-menu-visible-changed'?: ColumnMenuVisibleChangedEvent<TData, any> | undefined;
    'onContext-menu-visible-changed'?: ContextMenuVisibleChangedEvent<TData, any> | undefined;
    'onCut-start'?: CutStartEvent<TData, any> | undefined;
    'onCut-end'?: CutEndEvent<TData, any> | undefined;
    'onPaste-start'?: PasteStartEvent<TData, any> | undefined;
    'onPaste-end'?: PasteEndEvent<TData, any> | undefined;
    'onColumn-visible'?: ColumnVisibleEvent<TData, any> | undefined;
    'onColumn-pinned'?: ColumnPinnedEvent<TData, any> | undefined;
    'onColumn-resized'?: ColumnResizedEvent<TData, any> | undefined;
    'onColumn-moved'?: ColumnMovedEvent<TData, any> | undefined;
    'onColumn-value-changed'?: ColumnValueChangedEvent<TData, any> | undefined;
    'onColumn-pivot-mode-changed'?: ColumnPivotModeChangedEvent<TData, any> | undefined;
    'onColumn-pivot-changed'?: ColumnPivotChangedEvent<TData, any> | undefined;
    'onColumn-group-opened'?: ColumnGroupOpenedEvent<TData, any> | undefined;
    'onNew-columns-loaded'?: NewColumnsLoadedEvent<TData, any> | undefined;
    'onGrid-columns-changed'?: GridColumnsChangedEvent<TData, any> | undefined;
    'onDisplayed-columns-changed'?: DisplayedColumnsChangedEvent<TData, any> | undefined;
    'onVirtual-columns-changed'?: VirtualColumnsChangedEvent<TData, any> | undefined;
    'onColumn-everything-changed'?: ColumnEverythingChangedEvent<TData, any> | undefined;
    'onColumn-header-mouse-over'?: ColumnHeaderMouseOverEvent<TData, any> | undefined;
    'onColumn-header-mouse-leave'?: ColumnHeaderMouseLeaveEvent<TData, any> | undefined;
    'onColumn-header-clicked'?: ColumnHeaderClickedEvent<TData, any> | undefined;
    'onColumn-header-context-menu'?: ColumnHeaderContextMenuEvent<TData, any> | undefined;
    'onComponent-state-changed'?: ComponentStateChangedEvent<TData, any> | undefined;
    'onCell-value-changed'?: CellValueChangedEvent<TData, any> | undefined;
    'onCell-edit-request'?: CellEditRequestEvent<TData, any> | undefined;
    'onRow-value-changed'?: RowValueChangedEvent<TData, any> | undefined;
    'onCell-editing-started'?: CellEditingStartedEvent<TData, any> | undefined;
    'onCell-editing-stopped'?: CellEditingStoppedEvent<TData, any> | undefined;
    'onRow-editing-started'?: RowEditingStartedEvent<TData, any> | undefined;
    'onRow-editing-stopped'?: RowEditingStoppedEvent<TData, any> | undefined;
    'onUndo-started'?: UndoStartedEvent<TData, any> | undefined;
    'onUndo-ended'?: UndoEndedEvent<TData, any> | undefined;
    'onRedo-started'?: RedoStartedEvent<TData, any> | undefined;
    'onRedo-ended'?: RedoEndedEvent<TData, any> | undefined;
    'onCell-selection-delete-start'?: CellSelectionDeleteStartEvent<TData, any> | undefined;
    'onCell-selection-delete-end'?: CellSelectionDeleteEndEvent<TData, any> | undefined;
    'onRange-delete-start'?: RangeDeleteStartEvent<TData, any> | undefined;
    'onRange-delete-end'?: RangeDeleteEndEvent<TData, any> | undefined;
    'onFill-start'?: FillStartEvent<TData, any> | undefined;
    'onFill-end'?: FillEndEvent<TData, any> | undefined;
    'onFilter-opened'?: FilterOpenedEvent<TData, any> | undefined;
    'onFilter-changed'?: FilterChangedEvent<TData, any> | undefined;
    'onFilter-modified'?: FilterModifiedEvent<TData, any> | undefined;
    'onAdvanced-filter-builder-visible-changed'?: AdvancedFilterBuilderVisibleChangedEvent<TData, any> | undefined;
    'onFind-changed'?: FindChangedEvent<TData, any> | undefined;
    'onChart-created'?: ChartCreatedEvent<TData, any> | undefined;
    'onChart-range-selection-changed'?: ChartRangeSelectionChangedEvent<TData, any> | undefined;
    'onChart-options-changed'?: ChartOptionsChangedEvent<TData, any> | undefined;
    'onChart-destroyed'?: ChartDestroyedEvent<TData, any> | undefined;
    'onCell-key-down'?: CellKeyDownEvent<TData, any> | FullWidthCellKeyDownEvent<TData, any> | undefined;
    'onGrid-ready'?: GridReadyEvent<TData, any> | undefined;
    'onGrid-pre-destroyed'?: GridPreDestroyedEvent<TData, any> | undefined;
    'onFirst-data-rendered'?: FirstDataRenderedEvent<TData, any> | undefined;
    'onGrid-size-changed'?: GridSizeChangedEvent<TData, any> | undefined;
    'onModel-updated'?: ModelUpdatedEvent<TData, any> | undefined;
    'onVirtual-row-removed'?: VirtualRowRemovedEvent<TData, any> | undefined;
    'onViewport-changed'?: ViewportChangedEvent<TData, any> | undefined;
    'onBody-scroll'?: BodyScrollEvent<TData, any> | undefined;
    'onBody-scroll-end'?: BodyScrollEndEvent<TData, any> | undefined;
    'onDrag-started'?: DragStartedEvent<TData, any> | undefined;
    'onDrag-stopped'?: DragStoppedEvent<TData, any> | undefined;
    'onDrag-cancelled'?: DragCancelledEvent<TData, any> | undefined;
    'onState-updated'?: StateUpdatedEvent<TData, any> | undefined;
    'onPagination-changed'?: PaginationChangedEvent<TData, any> | undefined;
    'onRow-drag-enter'?: RowDragEnterEvent<TData, any> | undefined;
    'onRow-drag-move'?: RowDragMoveEvent<TData, any> | undefined;
    'onRow-drag-leave'?: RowDragLeaveEvent<TData, any> | undefined;
    'onRow-drag-end'?: RowDragEndEvent<TData, any> | undefined;
    'onRow-drag-cancel'?: RowDragCancelEvent<TData, any> | undefined;
    'onRow-resize-started'?: RowResizeStartedEvent<TData, any> | undefined;
    'onRow-resize-ended'?: RowResizeEndedEvent<any, any> | undefined;
    'onColumn-row-group-changed'?: ColumnRowGroupChangedEvent<TData, any> | undefined;
    'onRow-group-opened'?: RowGroupOpenedEvent<TData, any> | undefined;
    'onExpand-or-collapse-all'?: ExpandOrCollapseAllEvent<TData, any> | undefined;
    'onPivot-max-columns-exceeded'?: PivotMaxColumnsExceededEvent<TData, any> | undefined;
    'onPinned-row-data-changed'?: PinnedRowDataChangedEvent<TData, any> | undefined;
    'onPinned-rows-changed'?: PinnedRowsChangedEvent<TData, any> | undefined;
    'onRow-data-updated'?: RowDataUpdatedEvent<TData, any> | undefined;
    'onAsync-transactions-flushed'?: AsyncTransactionsFlushedEvent<TData, any> | undefined;
    'onStore-refreshed'?: StoreRefreshedEvent<TData, any> | undefined;
    'onHeader-focused'?: HeaderFocusedEvent<TData, any> | undefined;
    'onCell-clicked'?: CellClickedEvent<TData, any> | undefined;
    'onCell-double-clicked'?: CellDoubleClickedEvent<TData, any> | undefined;
    'onCell-focused'?: CellFocusedEvent<TData, any> | undefined;
    'onCell-mouse-over'?: CellMouseOverEvent<TData, any> | undefined;
    'onCell-mouse-out'?: CellMouseOutEvent<TData, any> | undefined;
    'onCell-mouse-down'?: CellMouseDownEvent<TData, any> | undefined;
    'onRow-clicked'?: RowClickedEvent<TData, any> | undefined;
    'onRow-double-clicked'?: RowDoubleClickedEvent<TData, any> | undefined;
    'onRow-selected'?: RowSelectedEvent<TData, any> | undefined;
    'onSelection-changed'?: SelectionChangedEvent<TData, any> | undefined;
    'onCell-context-menu'?: CellContextMenuEvent<TData, any> | undefined;
    'onRange-selection-changed'?: RangeSelectionChangedEvent<TData, any> | undefined;
    'onCell-selection-changed'?: CellSelectionChangedEvent<TData, any> | undefined;
    'onTooltip-show'?: TooltipShowEvent<TData, any> | undefined;
    'onTooltip-hide'?: TooltipHideEvent<TData, any> | undefined;
    'onSort-changed'?: SortChangedEvent<TData, any> | undefined;
} & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, __VLS_ctx?: {
    attrs: any;
    slots: {};
    emit: ((evt: "update:modelValue", event: TData[]) => void) & ((evt: "update:modelValue", value: TData[]) => void);
} | undefined, __VLS_expose?: ((exposed: import("vue").ShallowUnwrapRef<{
    api: Ref<GridApi<any> | undefined, GridApi<any> | undefined>;
}>) => void) | undefined, __VLS_setup?: Promise<{
    props: {
        readonly "onUpdate:modelValue"?: ((...args: unknown[]) => any) | undefined;
        modelValue?: TData[] | undefined;
        gridOptions?: GridOptions<TData> | undefined;
        modules?: import("ag-grid-community").Module[] | undefined;
        statusBar?: {
            statusPanels: import("ag-grid-community").StatusPanelDef[];
        } | undefined;
        sideBar?: string | boolean | string[] | import("ag-grid-community").SideBarDef | null | undefined;
        suppressContextMenu?: boolean | undefined;
        preventDefaultOnContextMenu?: boolean | undefined;
        allowContextMenuWithControlKey?: boolean | undefined;
        columnMenu?: "legacy" | "new" | undefined;
        suppressMenuHide?: boolean | undefined;
        enableBrowserTooltips?: boolean | undefined;
        tooltipTrigger?: "hover" | "focus" | undefined;
        tooltipShowDelay?: number | undefined;
        tooltipHideDelay?: number | undefined;
        tooltipMouseTrack?: boolean | undefined;
        tooltipShowMode?: "standard" | "whenTruncated" | undefined;
        tooltipInteraction?: boolean | undefined;
        popupParent?: HTMLElement | null | undefined;
        copyHeadersToClipboard?: boolean | undefined;
        copyGroupHeadersToClipboard?: boolean | undefined;
        clipboardDelimiter?: string | undefined;
        suppressCopyRowsToClipboard?: boolean | undefined;
        suppressCopySingleCellRanges?: boolean | undefined;
        suppressLastEmptyLineOnPaste?: boolean | undefined;
        suppressClipboardPaste?: boolean | undefined;
        suppressClipboardApi?: boolean | undefined;
        suppressCutToClipboard?: boolean | undefined;
        columnDefs?: (ColDef<any, any> | import("ag-grid-community").ColGroupDef<TData>)[] | null | undefined;
        defaultColDef?: ColDef<any, any> | undefined;
        defaultColGroupDef?: Partial<import("ag-grid-community").ColGroupDef<TData>> | undefined;
        columnTypes?: {
            [key: string]: import("ag-grid-community").ColTypeDef<TData>;
        } | undefined;
        dataTypeDefinitions?: {
            [cellDataType: string]: import("ag-grid-community").DataTypeDefinition<TData>;
        } | undefined;
        maintainColumnOrder?: boolean | undefined;
        enableStrictPivotColumnOrder?: boolean | undefined;
        suppressFieldDotNotation?: boolean | undefined;
        headerHeight?: number | undefined;
        groupHeaderHeight?: number | undefined;
        floatingFiltersHeight?: number | undefined;
        pivotHeaderHeight?: number | undefined;
        pivotGroupHeaderHeight?: number | undefined;
        allowDragFromColumnsToolPanel?: boolean | undefined;
        suppressMovableColumns?: boolean | undefined;
        suppressColumnMoveAnimation?: boolean | undefined;
        suppressMoveWhenColumnDragging?: boolean | undefined;
        suppressDragLeaveHidesColumns?: boolean | undefined;
        suppressGroupChangesColumnVisibility?: boolean | "suppressHideOnGroup" | "suppressShowOnUngroup" | undefined;
        suppressMakeColumnVisibleAfterUnGroup?: boolean | undefined;
        suppressRowGroupHidesColumns?: boolean | undefined;
        colResizeDefault?: "shift" | undefined;
        suppressAutoSize?: boolean | undefined;
        autoSizePadding?: number | undefined;
        skipHeaderOnAutoSize?: boolean | undefined;
        autoSizeStrategy?: import("ag-grid-community").SizeColumnsToFitGridStrategy | import("ag-grid-community").SizeColumnsToFitProvidedWidthStrategy | import("ag-grid-community").SizeColumnsToContentStrategy | undefined;
        components?: {
            [p: string]: any;
        } | undefined;
        editType?: "fullRow" | undefined;
        singleClickEdit?: boolean | undefined;
        suppressClickEdit?: boolean | undefined;
        readOnlyEdit?: boolean | undefined;
        stopEditingWhenCellsLoseFocus?: boolean | undefined;
        enterNavigatesVertically?: boolean | undefined;
        enterNavigatesVerticallyAfterEdit?: boolean | undefined;
        enableCellEditingOnBackspace?: boolean | undefined;
        undoRedoCellEditing?: boolean | undefined;
        undoRedoCellEditingLimit?: number | undefined;
        defaultCsvExportParams?: import("ag-grid-community").CsvExportParams | undefined;
        suppressCsvExport?: boolean | undefined;
        defaultExcelExportParams?: import("ag-grid-community").ExcelExportParams | undefined;
        suppressExcelExport?: boolean | undefined;
        excelStyles?: import("ag-grid-community").ExcelStyle[] | undefined;
        findSearchValue?: string | undefined;
        findOptions?: import("ag-grid-community").FindOptions | undefined;
        quickFilterText?: string | undefined;
        cacheQuickFilter?: boolean | undefined;
        includeHiddenColumnsInQuickFilter?: boolean | undefined;
        quickFilterParser?: ((quickFilter: string) => string[]) | undefined;
        quickFilterMatcher?: ((quickFilterParts: string[], rowQuickFilterAggregateText: string) => boolean) | undefined;
        applyQuickFilterBeforePivotOrAgg?: boolean | undefined;
        excludeChildrenWhenTreeDataFiltering?: boolean | undefined;
        enableAdvancedFilter?: boolean | undefined;
        alwaysPassFilter?: ((rowNode: IRowNode<TData>) => boolean) | undefined;
        includeHiddenColumnsInAdvancedFilter?: boolean | undefined;
        advancedFilterParent?: HTMLElement | null | undefined;
        advancedFilterBuilderParams?: import("ag-grid-community").IAdvancedFilterBuilderParams | undefined;
        suppressAdvancedFilterEval?: boolean | undefined;
        suppressSetFilterByDefault?: boolean | undefined;
        enableCharts?: boolean | undefined;
        chartThemes?: string[] | undefined;
        customChartThemes?: {
            [name: string]: import("ag-charts-types").AgChartTheme;
        } | undefined;
        chartThemeOverrides?: import("ag-charts-types").AgChartThemeOverrides | undefined;
        chartToolPanelsDef?: import("ag-grid-community").ChartToolPanelsDef | undefined;
        chartMenuItems?: (import("ag-grid-community").DefaultChartMenuItem | import("ag-grid-community").MenuItemDef<any, any>)[] | import("ag-grid-community").GetChartMenuItems<TData, any> | undefined;
        loadingCellRenderer?: any;
        loadingCellRendererParams?: any;
        loadingCellRendererSelector?: import("ag-grid-community").LoadingCellRendererSelectorFunc<TData> | undefined;
        localeText?: {
            [key: string]: string;
        } | undefined;
        masterDetail?: boolean | undefined;
        keepDetailRows?: boolean | undefined;
        keepDetailRowsCount?: number | undefined;
        detailCellRenderer?: any;
        detailCellRendererParams?: any;
        detailRowHeight?: number | undefined;
        detailRowAutoHeight?: boolean | undefined;
        context?: any;
        alignedGrids?: import("ag-grid-community").AlignedGrid[] | (() => import("ag-grid-community").AlignedGrid[]) | undefined;
        tabIndex?: number | undefined;
        rowBuffer?: number | undefined;
        valueCache?: boolean | undefined;
        valueCacheNeverExpires?: boolean | undefined;
        enableCellExpressions?: boolean | undefined;
        suppressTouch?: boolean | undefined;
        suppressFocusAfterRefresh?: boolean | undefined;
        suppressBrowserResizeObserver?: boolean | undefined;
        suppressPropertyNamesCheck?: boolean | undefined;
        suppressChangeDetection?: boolean | undefined;
        debug?: boolean | undefined;
        loading?: boolean | undefined;
        overlayLoadingTemplate?: string | undefined;
        loadingOverlayComponent?: any;
        loadingOverlayComponentParams?: any;
        suppressLoadingOverlay?: boolean | undefined;
        overlayNoRowsTemplate?: string | undefined;
        noRowsOverlayComponent?: any;
        noRowsOverlayComponentParams?: any;
        suppressNoRowsOverlay?: boolean | undefined;
        pagination?: boolean | undefined;
        paginationPageSize?: number | undefined;
        paginationPageSizeSelector?: boolean | number[] | undefined;
        paginationAutoPageSize?: boolean | undefined;
        paginateChildRows?: boolean | undefined;
        suppressPaginationPanel?: boolean | undefined;
        pivotMode?: boolean | undefined;
        pivotPanelShow?: "always" | "onlyWhenPivoting" | "never" | undefined;
        pivotMaxGeneratedColumns?: number | undefined;
        pivotDefaultExpanded?: number | undefined;
        pivotColumnGroupTotals?: "before" | "after" | undefined;
        pivotRowTotals?: "before" | "after" | undefined;
        pivotSuppressAutoColumn?: boolean | undefined;
        suppressExpandablePivotGroups?: boolean | undefined;
        functionsReadOnly?: boolean | undefined;
        aggFuncs?: {
            [key: string]: import("ag-grid-community").IAggFunc<TData, any>;
        } | undefined;
        suppressAggFuncInHeader?: boolean | undefined;
        alwaysAggregateAtRootLevel?: boolean | undefined;
        aggregateOnlyChangedColumns?: boolean | undefined;
        suppressAggFilteredOnly?: boolean | undefined;
        removePivotHeaderRowWhenSingleValueColumn?: boolean | undefined;
        animateRows?: boolean | undefined;
        cellFlashDuration?: number | undefined;
        cellFadeDuration?: number | undefined;
        allowShowChangeAfterFilter?: boolean | undefined;
        domLayout?: import("ag-grid-community").DomLayoutType | undefined;
        ensureDomOrder?: boolean | undefined;
        enableCellSpan?: boolean | undefined;
        enableRtl?: boolean | undefined;
        suppressColumnVirtualisation?: boolean | undefined;
        suppressMaxRenderedRowRestriction?: boolean | undefined;
        suppressRowVirtualisation?: boolean | undefined;
        rowDragManaged?: boolean | undefined;
        suppressRowDrag?: boolean | undefined;
        suppressMoveWhenRowDragging?: boolean | undefined;
        rowDragEntireRow?: boolean | undefined;
        rowDragMultiRow?: boolean | undefined;
        rowDragText?: ((params: import("ag-grid-community").IRowDragItem, dragItemCount: number) => string) | undefined;
        dragAndDropImageComponent?: any;
        dragAndDropImageComponentParams?: any;
        fullWidthCellRenderer?: any;
        fullWidthCellRendererParams?: any;
        embedFullWidthRows?: boolean | undefined;
        groupDisplayType?: import("ag-grid-community").RowGroupingDisplayType | undefined;
        groupDefaultExpanded?: number | undefined;
        autoGroupColumnDef?: ColDef<TData, any> | undefined;
        groupMaintainOrder?: boolean | undefined;
        groupSelectsChildren?: boolean | undefined;
        groupLockGroupColumns?: number | undefined;
        groupAggFiltering?: boolean | import("ag-grid-community").IsRowFilterable<TData> | undefined;
        groupTotalRow?: "top" | "bottom" | import("ag-grid-community").UseGroupTotalRow<TData> | undefined;
        grandTotalRow?: "top" | "bottom" | "pinnedTop" | "pinnedBottom" | undefined;
        suppressStickyTotalRow?: boolean | "grand" | "group" | undefined;
        groupSuppressBlankHeader?: boolean | undefined;
        groupSelectsFiltered?: boolean | undefined;
        showOpenedGroup?: boolean | undefined;
        groupHideParentOfSingleChild?: boolean | "leafGroupsOnly" | undefined;
        groupRemoveSingleChildren?: boolean | undefined;
        groupRemoveLowestSingleChildren?: boolean | undefined;
        groupHideOpenParents?: boolean | undefined;
        groupAllowUnbalanced?: boolean | undefined;
        rowGroupPanelShow?: "always" | "never" | "onlyWhenGrouping" | undefined;
        groupRowRenderer?: any;
        groupRowRendererParams?: any;
        treeData?: boolean | undefined;
        treeDataChildrenField?: string | undefined;
        treeDataParentIdField?: string | undefined;
        rowGroupPanelSuppressSort?: boolean | undefined;
        suppressGroupRowsSticky?: boolean | undefined;
        pinnedTopRowData?: any[] | undefined;
        pinnedBottomRowData?: any[] | undefined;
        enableRowPinning?: boolean | "top" | "bottom" | undefined;
        isRowPinnable?: import("ag-grid-community").IsRowPinnable<TData> | undefined;
        isRowPinned?: import("ag-grid-community").IsRowPinned<TData> | undefined;
        rowModelType?: import("ag-grid-community").RowModelType | undefined;
        rowData?: TData[] | null | undefined;
        asyncTransactionWaitMillis?: number | undefined;
        suppressModelUpdateAfterUpdateTransaction?: boolean | undefined;
        datasource?: import("ag-grid-community").IDatasource | undefined;
        cacheOverflowSize?: number | undefined;
        infiniteInitialRowCount?: number | undefined;
        serverSideInitialRowCount?: number | undefined;
        suppressServerSideFullWidthLoadingRow?: boolean | undefined;
        cacheBlockSize?: number | undefined;
        maxBlocksInCache?: number | undefined;
        maxConcurrentDatasourceRequests?: number | undefined;
        blockLoadDebounceMillis?: number | undefined;
        purgeClosedRowNodes?: boolean | undefined;
        serverSideDatasource?: import("ag-grid-community").IServerSideDatasource<any> | undefined;
        serverSideSortAllLevels?: boolean | undefined;
        serverSideEnableClientSideSort?: boolean | undefined;
        serverSideOnlyRefreshFilteredGroups?: boolean | undefined;
        serverSidePivotResultFieldSeparator?: string | undefined;
        viewportDatasource?: import("ag-grid-community").IViewportDatasource | undefined;
        viewportRowModelPageSize?: number | undefined;
        viewportRowModelBufferSize?: number | undefined;
        alwaysShowHorizontalScroll?: boolean | undefined;
        alwaysShowVerticalScroll?: boolean | undefined;
        debounceVerticalScrollbar?: boolean | undefined;
        suppressHorizontalScroll?: boolean | undefined;
        suppressScrollOnNewData?: boolean | undefined;
        suppressScrollWhenPopupsAreOpen?: boolean | undefined;
        suppressAnimationFrame?: boolean | undefined;
        suppressMiddleClickScrolls?: boolean | undefined;
        suppressPreventDefaultOnMouseWheel?: boolean | undefined;
        scrollbarWidth?: number | undefined;
        rowSelection?: "single" | "multiple" | import("ag-grid-community").RowSelectionOptions<TData> | undefined;
        cellSelection?: boolean | import("ag-grid-community").CellSelectionOptions<TData> | undefined;
        rowMultiSelectWithClick?: boolean | undefined;
        suppressRowDeselection?: boolean | undefined;
        suppressRowClickSelection?: boolean | undefined;
        suppressCellFocus?: boolean | undefined;
        suppressHeaderFocus?: boolean | undefined;
        selectionColumnDef?: import("ag-grid-community").SelectionColumnDef | undefined;
        rowNumbers?: boolean | import("ag-grid-community").RowNumbersOptions | undefined;
        suppressMultiRangeSelection?: boolean | undefined;
        enableCellTextSelection?: boolean | undefined;
        enableRangeSelection?: boolean | undefined;
        enableRangeHandle?: boolean | undefined;
        enableFillHandle?: boolean | undefined;
        fillHandleDirection?: "x" | "y" | "xy" | undefined;
        suppressClearOnFillReduction?: boolean | undefined;
        sortingOrder?: import("ag-grid-community").SortDirection[] | undefined;
        accentedSort?: boolean | undefined;
        unSortIcon?: boolean | undefined;
        suppressMultiSort?: boolean | undefined;
        alwaysMultiSort?: boolean | undefined;
        multiSortKey?: "ctrl" | undefined;
        suppressMaintainUnsortedOrder?: boolean | undefined;
        icons?: {
            [key: string]: string | ((...args: any[]) => any);
        } | undefined;
        rowHeight?: number | undefined;
        rowStyle?: import("ag-grid-community").RowStyle | undefined;
        rowClass?: string | string[] | undefined;
        rowClassRules?: import("ag-grid-community").RowClassRules<TData> | undefined;
        suppressRowHoverHighlight?: boolean | undefined;
        suppressRowTransform?: boolean | undefined;
        columnHoverHighlight?: boolean | undefined;
        gridId?: string | undefined;
        deltaSort?: boolean | undefined;
        treeDataDisplayType?: import("ag-grid-community").TreeDataDisplayType | undefined;
        enableGroupEdit?: boolean | undefined;
        initialState?: import("ag-grid-community").GridState | undefined;
        theme?: "legacy" | import("ag-grid-community").Theme | undefined;
        loadThemeGoogleFonts?: boolean | undefined;
        themeCssLayer?: string | undefined;
        styleNonce?: string | undefined;
        themeStyleContainer?: HTMLElement | undefined;
        getContextMenuItems?: import("ag-grid-community").GetContextMenuItems<TData, any> | undefined;
        getMainMenuItems?: import("ag-grid-community").GetMainMenuItems<TData, any> | undefined;
        postProcessPopup?: ((params: import("ag-grid-community").PostProcessPopupParams<TData, any>) => void) | undefined;
        processUnpinnedColumns?: ((params: import("ag-grid-community").ProcessUnpinnedColumnsParams<TData, any>) => import("ag-grid-community").Column<any>[]) | undefined;
        processCellForClipboard?: ((params: import("ag-grid-community").ProcessCellForExportParams<TData, any>) => any) | undefined;
        processHeaderForClipboard?: ((params: import("ag-grid-community").ProcessHeaderForExportParams<TData, any>) => any) | undefined;
        processGroupHeaderForClipboard?: ((params: import("ag-grid-community").ProcessGroupHeaderForExportParams<TData, any>) => any) | undefined;
        processCellFromClipboard?: ((params: import("ag-grid-community").ProcessCellForExportParams<TData, any>) => any) | undefined;
        sendToClipboard?: ((params: import("ag-grid-community").SendToClipboardParams<TData, any>) => void) | undefined;
        processDataFromClipboard?: ((params: import("ag-grid-community").ProcessDataFromClipboardParams<TData, any>) => string[][] | null) | undefined;
        isExternalFilterPresent?: ((params: import("ag-grid-community").IsExternalFilterPresentParams<TData, any>) => boolean) | undefined;
        doesExternalFilterPass?: ((node: IRowNode<TData>) => boolean) | undefined;
        getChartToolbarItems?: import("ag-grid-community").GetChartToolbarItems | undefined;
        createChartContainer?: ((params: import("ag-grid-community").ChartRefParams<TData>) => void) | undefined;
        focusGridInnerElement?: ((params: import("ag-grid-community").FocusGridInnerElementParams<TData, any>) => boolean) | undefined;
        navigateToNextHeader?: ((params: import("ag-grid-community").NavigateToNextHeaderParams<TData, any>) => import("ag-grid-community").HeaderPosition | null) | undefined;
        tabToNextHeader?: ((params: import("ag-grid-community").TabToNextHeaderParams<TData, any>) => boolean | import("ag-grid-community").HeaderPosition) | undefined;
        navigateToNextCell?: ((params: import("ag-grid-community").NavigateToNextCellParams<TData, any>) => import("ag-grid-community").CellPosition | null) | undefined;
        tabToNextCell?: ((params: import("ag-grid-community").TabToNextCellParams<TData, any>) => boolean | import("ag-grid-community").CellPosition) | undefined;
        getLocaleText?: ((params: import("ag-grid-community").GetLocaleTextParams<TData, any>) => string) | undefined;
        getDocument?: (() => Document) | undefined;
        paginationNumberFormatter?: ((params: import("ag-grid-community").PaginationNumberFormatterParams<TData, any>) => string) | undefined;
        getGroupRowAgg?: ((params: import("ag-grid-community").GetGroupRowAggParams<TData, any>) => any) | undefined;
        isGroupOpenByDefault?: ((params: import("ag-grid-community").IsGroupOpenByDefaultParams<TData, any>) => boolean) | undefined;
        initialGroupOrderComparator?: ((params: import("ag-grid-community").InitialGroupOrderComparatorParams<TData, any>) => number) | undefined;
        processPivotResultColDef?: ((colDef: ColDef<TData, any>) => void) | undefined;
        processPivotResultColGroupDef?: ((colGroupDef: import("ag-grid-community").ColGroupDef<TData>) => void) | undefined;
        getDataPath?: import("ag-grid-community").GetDataPath<TData> | undefined;
        getChildCount?: ((dataItem: any) => number) | undefined;
        getServerSideGroupLevelParams?: ((params: import("ag-grid-community").GetServerSideGroupLevelParamsParams) => import("ag-grid-community").ServerSideGroupLevelParams) | undefined;
        isServerSideGroupOpenByDefault?: ((params: import("ag-grid-community").IsServerSideGroupOpenByDefaultParams) => boolean) | undefined;
        isApplyServerSideTransaction?: import("ag-grid-community").IsApplyServerSideTransaction | undefined;
        isServerSideGroup?: import("ag-grid-community").IsServerSideGroup | undefined;
        getServerSideGroupKey?: import("ag-grid-community").GetServerSideGroupKey | undefined;
        getBusinessKeyForNode?: ((node: IRowNode<TData>) => string) | undefined;
        getRowId?: import("ag-grid-community").GetRowIdFunc<TData> | undefined;
        resetRowDataOnUpdate?: boolean | undefined;
        processRowPostCreate?: ((params: import("ag-grid-community").ProcessRowParams<TData, any>) => void) | undefined;
        isRowSelectable?: import("ag-grid-community").IsRowSelectable<TData> | undefined;
        isRowMaster?: import("ag-grid-community").IsRowMaster<TData> | undefined;
        fillOperation?: ((params: import("ag-grid-community").FillOperationParams<TData, any>) => any) | undefined;
        postSortRows?: ((params: import("ag-grid-community").PostSortRowsParams<TData, any>) => void) | undefined;
        getRowStyle?: ((params: import("ag-grid-community").RowClassParams<TData, any>) => import("ag-grid-community").RowStyle | undefined) | undefined;
        getRowClass?: ((params: import("ag-grid-community").RowClassParams<TData, any>) => string | string[] | undefined) | undefined;
        getRowHeight?: ((params: import("ag-grid-community").RowHeightParams<TData, any>) => number | null | undefined) | undefined;
        isFullWidthRow?: ((params: import("ag-grid-community").IsFullWidthRowParams<TData, any>) => boolean) | undefined;
        'onTool-panel-visible-changed'?: ToolPanelVisibleChangedEvent<TData, any> | undefined;
        'onTool-panel-size-changed'?: ToolPanelSizeChangedEvent<TData, any> | undefined;
        'onColumn-menu-visible-changed'?: ColumnMenuVisibleChangedEvent<TData, any> | undefined;
        'onContext-menu-visible-changed'?: ContextMenuVisibleChangedEvent<TData, any> | undefined;
        'onCut-start'?: CutStartEvent<TData, any> | undefined;
        'onCut-end'?: CutEndEvent<TData, any> | undefined;
        'onPaste-start'?: PasteStartEvent<TData, any> | undefined;
        'onPaste-end'?: PasteEndEvent<TData, any> | undefined;
        'onColumn-visible'?: ColumnVisibleEvent<TData, any> | undefined;
        'onColumn-pinned'?: ColumnPinnedEvent<TData, any> | undefined;
        'onColumn-resized'?: ColumnResizedEvent<TData, any> | undefined;
        'onColumn-moved'?: ColumnMovedEvent<TData, any> | undefined;
        'onColumn-value-changed'?: ColumnValueChangedEvent<TData, any> | undefined;
        'onColumn-pivot-mode-changed'?: ColumnPivotModeChangedEvent<TData, any> | undefined;
        'onColumn-pivot-changed'?: ColumnPivotChangedEvent<TData, any> | undefined;
        'onColumn-group-opened'?: ColumnGroupOpenedEvent<TData, any> | undefined;
        'onNew-columns-loaded'?: NewColumnsLoadedEvent<TData, any> | undefined;
        'onGrid-columns-changed'?: GridColumnsChangedEvent<TData, any> | undefined;
        'onDisplayed-columns-changed'?: DisplayedColumnsChangedEvent<TData, any> | undefined;
        'onVirtual-columns-changed'?: VirtualColumnsChangedEvent<TData, any> | undefined;
        'onColumn-everything-changed'?: ColumnEverythingChangedEvent<TData, any> | undefined;
        'onColumn-header-mouse-over'?: ColumnHeaderMouseOverEvent<TData, any> | undefined;
        'onColumn-header-mouse-leave'?: ColumnHeaderMouseLeaveEvent<TData, any> | undefined;
        'onColumn-header-clicked'?: ColumnHeaderClickedEvent<TData, any> | undefined;
        'onColumn-header-context-menu'?: ColumnHeaderContextMenuEvent<TData, any> | undefined;
        'onComponent-state-changed'?: ComponentStateChangedEvent<TData, any> | undefined;
        'onCell-value-changed'?: CellValueChangedEvent<TData, any> | undefined;
        'onCell-edit-request'?: CellEditRequestEvent<TData, any> | undefined;
        'onRow-value-changed'?: RowValueChangedEvent<TData, any> | undefined;
        'onCell-editing-started'?: CellEditingStartedEvent<TData, any> | undefined;
        'onCell-editing-stopped'?: CellEditingStoppedEvent<TData, any> | undefined;
        'onRow-editing-started'?: RowEditingStartedEvent<TData, any> | undefined;
        'onRow-editing-stopped'?: RowEditingStoppedEvent<TData, any> | undefined;
        'onUndo-started'?: UndoStartedEvent<TData, any> | undefined;
        'onUndo-ended'?: UndoEndedEvent<TData, any> | undefined;
        'onRedo-started'?: RedoStartedEvent<TData, any> | undefined;
        'onRedo-ended'?: RedoEndedEvent<TData, any> | undefined;
        'onCell-selection-delete-start'?: CellSelectionDeleteStartEvent<TData, any> | undefined;
        'onCell-selection-delete-end'?: CellSelectionDeleteEndEvent<TData, any> | undefined;
        'onRange-delete-start'?: RangeDeleteStartEvent<TData, any> | undefined;
        'onRange-delete-end'?: RangeDeleteEndEvent<TData, any> | undefined;
        'onFill-start'?: FillStartEvent<TData, any> | undefined;
        'onFill-end'?: FillEndEvent<TData, any> | undefined;
        'onFilter-opened'?: FilterOpenedEvent<TData, any> | undefined;
        'onFilter-changed'?: FilterChangedEvent<TData, any> | undefined;
        'onFilter-modified'?: FilterModifiedEvent<TData, any> | undefined;
        'onAdvanced-filter-builder-visible-changed'?: AdvancedFilterBuilderVisibleChangedEvent<TData, any> | undefined;
        'onFind-changed'?: FindChangedEvent<TData, any> | undefined;
        'onChart-created'?: ChartCreatedEvent<TData, any> | undefined;
        'onChart-range-selection-changed'?: ChartRangeSelectionChangedEvent<TData, any> | undefined;
        'onChart-options-changed'?: ChartOptionsChangedEvent<TData, any> | undefined;
        'onChart-destroyed'?: ChartDestroyedEvent<TData, any> | undefined;
        'onCell-key-down'?: CellKeyDownEvent<TData, any> | FullWidthCellKeyDownEvent<TData, any> | undefined;
        'onGrid-ready'?: GridReadyEvent<TData, any> | undefined;
        'onGrid-pre-destroyed'?: GridPreDestroyedEvent<TData, any> | undefined;
        'onFirst-data-rendered'?: FirstDataRenderedEvent<TData, any> | undefined;
        'onGrid-size-changed'?: GridSizeChangedEvent<TData, any> | undefined;
        'onModel-updated'?: ModelUpdatedEvent<TData, any> | undefined;
        'onVirtual-row-removed'?: VirtualRowRemovedEvent<TData, any> | undefined;
        'onViewport-changed'?: ViewportChangedEvent<TData, any> | undefined;
        'onBody-scroll'?: BodyScrollEvent<TData, any> | undefined;
        'onBody-scroll-end'?: BodyScrollEndEvent<TData, any> | undefined;
        'onDrag-started'?: DragStartedEvent<TData, any> | undefined;
        'onDrag-stopped'?: DragStoppedEvent<TData, any> | undefined;
        'onDrag-cancelled'?: DragCancelledEvent<TData, any> | undefined;
        'onState-updated'?: StateUpdatedEvent<TData, any> | undefined;
        'onPagination-changed'?: PaginationChangedEvent<TData, any> | undefined;
        'onRow-drag-enter'?: RowDragEnterEvent<TData, any> | undefined;
        'onRow-drag-move'?: RowDragMoveEvent<TData, any> | undefined;
        'onRow-drag-leave'?: RowDragLeaveEvent<TData, any> | undefined;
        'onRow-drag-end'?: RowDragEndEvent<TData, any> | undefined;
        'onRow-drag-cancel'?: RowDragCancelEvent<TData, any> | undefined;
        'onRow-resize-started'?: RowResizeStartedEvent<TData, any> | undefined;
        'onRow-resize-ended'?: RowResizeEndedEvent<any, any> | undefined;
        'onColumn-row-group-changed'?: ColumnRowGroupChangedEvent<TData, any> | undefined;
        'onRow-group-opened'?: RowGroupOpenedEvent<TData, any> | undefined;
        'onExpand-or-collapse-all'?: ExpandOrCollapseAllEvent<TData, any> | undefined;
        'onPivot-max-columns-exceeded'?: PivotMaxColumnsExceededEvent<TData, any> | undefined;
        'onPinned-row-data-changed'?: PinnedRowDataChangedEvent<TData, any> | undefined;
        'onPinned-rows-changed'?: PinnedRowsChangedEvent<TData, any> | undefined;
        'onRow-data-updated'?: RowDataUpdatedEvent<TData, any> | undefined;
        'onAsync-transactions-flushed'?: AsyncTransactionsFlushedEvent<TData, any> | undefined;
        'onStore-refreshed'?: StoreRefreshedEvent<TData, any> | undefined;
        'onHeader-focused'?: HeaderFocusedEvent<TData, any> | undefined;
        'onCell-clicked'?: CellClickedEvent<TData, any> | undefined;
        'onCell-double-clicked'?: CellDoubleClickedEvent<TData, any> | undefined;
        'onCell-focused'?: CellFocusedEvent<TData, any> | undefined;
        'onCell-mouse-over'?: CellMouseOverEvent<TData, any> | undefined;
        'onCell-mouse-out'?: CellMouseOutEvent<TData, any> | undefined;
        'onCell-mouse-down'?: CellMouseDownEvent<TData, any> | undefined;
        'onRow-clicked'?: RowClickedEvent<TData, any> | undefined;
        'onRow-double-clicked'?: RowDoubleClickedEvent<TData, any> | undefined;
        'onRow-selected'?: RowSelectedEvent<TData, any> | undefined;
        'onSelection-changed'?: SelectionChangedEvent<TData, any> | undefined;
        'onCell-context-menu'?: CellContextMenuEvent<TData, any> | undefined;
        'onRange-selection-changed'?: RangeSelectionChangedEvent<TData, any> | undefined;
        'onCell-selection-changed'?: CellSelectionChangedEvent<TData, any> | undefined;
        'onTooltip-show'?: TooltipShowEvent<TData, any> | undefined;
        'onTooltip-hide'?: TooltipHideEvent<TData, any> | undefined;
        'onSort-changed'?: SortChangedEvent<TData, any> | undefined;
    } & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps;
    expose(exposed: import("vue").ShallowUnwrapRef<{
        api: Ref<GridApi<any> | undefined, GridApi<any> | undefined>;
    }>): void;
    attrs: any;
    slots: {};
    emit: ((evt: "update:modelValue", event: TData[]) => void) & ((evt: "update:modelValue", value: TData[]) => void);
}>) => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}> & {
    __ctx?: {
        props: {
            readonly "onUpdate:modelValue"?: ((...args: unknown[]) => any) | undefined;
            modelValue?: TData[] | undefined;
            gridOptions?: GridOptions<TData> | undefined;
            modules?: import("ag-grid-community").Module[] | undefined;
            statusBar?: {
                statusPanels: import("ag-grid-community").StatusPanelDef[];
            } | undefined;
            sideBar?: string | boolean | string[] | import("ag-grid-community").SideBarDef | null | undefined;
            suppressContextMenu?: boolean | undefined;
            preventDefaultOnContextMenu?: boolean | undefined;
            allowContextMenuWithControlKey?: boolean | undefined;
            columnMenu?: "legacy" | "new" | undefined;
            suppressMenuHide?: boolean | undefined;
            enableBrowserTooltips?: boolean | undefined;
            tooltipTrigger?: "hover" | "focus" | undefined;
            tooltipShowDelay?: number | undefined;
            tooltipHideDelay?: number | undefined;
            tooltipMouseTrack?: boolean | undefined;
            tooltipShowMode?: "standard" | "whenTruncated" | undefined;
            tooltipInteraction?: boolean | undefined;
            popupParent?: HTMLElement | null | undefined;
            copyHeadersToClipboard?: boolean | undefined;
            copyGroupHeadersToClipboard?: boolean | undefined;
            clipboardDelimiter?: string | undefined;
            suppressCopyRowsToClipboard?: boolean | undefined;
            suppressCopySingleCellRanges?: boolean | undefined;
            suppressLastEmptyLineOnPaste?: boolean | undefined;
            suppressClipboardPaste?: boolean | undefined;
            suppressClipboardApi?: boolean | undefined;
            suppressCutToClipboard?: boolean | undefined;
            columnDefs?: (ColDef<any, any> | import("ag-grid-community").ColGroupDef<TData>)[] | null | undefined;
            defaultColDef?: ColDef<any, any> | undefined;
            defaultColGroupDef?: Partial<import("ag-grid-community").ColGroupDef<TData>> | undefined;
            columnTypes?: {
                [key: string]: import("ag-grid-community").ColTypeDef<TData>;
            } | undefined;
            dataTypeDefinitions?: {
                [cellDataType: string]: import("ag-grid-community").DataTypeDefinition<TData>;
            } | undefined;
            maintainColumnOrder?: boolean | undefined;
            enableStrictPivotColumnOrder?: boolean | undefined;
            suppressFieldDotNotation?: boolean | undefined;
            headerHeight?: number | undefined;
            groupHeaderHeight?: number | undefined;
            floatingFiltersHeight?: number | undefined;
            pivotHeaderHeight?: number | undefined;
            pivotGroupHeaderHeight?: number | undefined;
            allowDragFromColumnsToolPanel?: boolean | undefined;
            suppressMovableColumns?: boolean | undefined;
            suppressColumnMoveAnimation?: boolean | undefined;
            suppressMoveWhenColumnDragging?: boolean | undefined;
            suppressDragLeaveHidesColumns?: boolean | undefined;
            suppressGroupChangesColumnVisibility?: boolean | "suppressHideOnGroup" | "suppressShowOnUngroup" | undefined;
            suppressMakeColumnVisibleAfterUnGroup?: boolean | undefined;
            suppressRowGroupHidesColumns?: boolean | undefined;
            colResizeDefault?: "shift" | undefined;
            suppressAutoSize?: boolean | undefined;
            autoSizePadding?: number | undefined;
            skipHeaderOnAutoSize?: boolean | undefined;
            autoSizeStrategy?: import("ag-grid-community").SizeColumnsToFitGridStrategy | import("ag-grid-community").SizeColumnsToFitProvidedWidthStrategy | import("ag-grid-community").SizeColumnsToContentStrategy | undefined;
            components?: {
                [p: string]: any;
            } | undefined;
            editType?: "fullRow" | undefined;
            singleClickEdit?: boolean | undefined;
            suppressClickEdit?: boolean | undefined;
            readOnlyEdit?: boolean | undefined;
            stopEditingWhenCellsLoseFocus?: boolean | undefined;
            enterNavigatesVertically?: boolean | undefined;
            enterNavigatesVerticallyAfterEdit?: boolean | undefined;
            enableCellEditingOnBackspace?: boolean | undefined;
            undoRedoCellEditing?: boolean | undefined;
            undoRedoCellEditingLimit?: number | undefined;
            defaultCsvExportParams?: import("ag-grid-community").CsvExportParams | undefined;
            suppressCsvExport?: boolean | undefined;
            defaultExcelExportParams?: import("ag-grid-community").ExcelExportParams | undefined;
            suppressExcelExport?: boolean | undefined;
            excelStyles?: import("ag-grid-community").ExcelStyle[] | undefined;
            findSearchValue?: string | undefined;
            findOptions?: import("ag-grid-community").FindOptions | undefined;
            quickFilterText?: string | undefined;
            cacheQuickFilter?: boolean | undefined;
            includeHiddenColumnsInQuickFilter?: boolean | undefined;
            quickFilterParser?: ((quickFilter: string) => string[]) | undefined;
            quickFilterMatcher?: ((quickFilterParts: string[], rowQuickFilterAggregateText: string) => boolean) | undefined;
            applyQuickFilterBeforePivotOrAgg?: boolean | undefined;
            excludeChildrenWhenTreeDataFiltering?: boolean | undefined;
            enableAdvancedFilter?: boolean | undefined;
            alwaysPassFilter?: ((rowNode: IRowNode<TData>) => boolean) | undefined;
            includeHiddenColumnsInAdvancedFilter?: boolean | undefined;
            advancedFilterParent?: HTMLElement | null | undefined;
            advancedFilterBuilderParams?: import("ag-grid-community").IAdvancedFilterBuilderParams | undefined;
            suppressAdvancedFilterEval?: boolean | undefined;
            suppressSetFilterByDefault?: boolean | undefined;
            enableCharts?: boolean | undefined;
            chartThemes?: string[] | undefined;
            customChartThemes?: {
                [name: string]: import("ag-charts-types").AgChartTheme;
            } | undefined;
            chartThemeOverrides?: import("ag-charts-types").AgChartThemeOverrides | undefined;
            chartToolPanelsDef?: import("ag-grid-community").ChartToolPanelsDef | undefined;
            chartMenuItems?: (import("ag-grid-community").DefaultChartMenuItem | import("ag-grid-community").MenuItemDef<any, any>)[] | import("ag-grid-community").GetChartMenuItems<TData, any> | undefined;
            loadingCellRenderer?: any;
            loadingCellRendererParams?: any;
            loadingCellRendererSelector?: import("ag-grid-community").LoadingCellRendererSelectorFunc<TData> | undefined;
            localeText?: {
                [key: string]: string;
            } | undefined;
            masterDetail?: boolean | undefined;
            keepDetailRows?: boolean | undefined;
            keepDetailRowsCount?: number | undefined;
            detailCellRenderer?: any;
            detailCellRendererParams?: any;
            detailRowHeight?: number | undefined;
            detailRowAutoHeight?: boolean | undefined;
            context?: any;
            alignedGrids?: import("ag-grid-community").AlignedGrid[] | (() => import("ag-grid-community").AlignedGrid[]) | undefined;
            tabIndex?: number | undefined;
            rowBuffer?: number | undefined;
            valueCache?: boolean | undefined;
            valueCacheNeverExpires?: boolean | undefined;
            enableCellExpressions?: boolean | undefined;
            suppressTouch?: boolean | undefined;
            suppressFocusAfterRefresh?: boolean | undefined;
            suppressBrowserResizeObserver?: boolean | undefined;
            suppressPropertyNamesCheck?: boolean | undefined;
            suppressChangeDetection?: boolean | undefined;
            debug?: boolean | undefined;
            loading?: boolean | undefined;
            overlayLoadingTemplate?: string | undefined;
            loadingOverlayComponent?: any;
            loadingOverlayComponentParams?: any;
            suppressLoadingOverlay?: boolean | undefined;
            overlayNoRowsTemplate?: string | undefined;
            noRowsOverlayComponent?: any;
            noRowsOverlayComponentParams?: any;
            suppressNoRowsOverlay?: boolean | undefined;
            pagination?: boolean | undefined;
            paginationPageSize?: number | undefined;
            paginationPageSizeSelector?: boolean | number[] | undefined;
            paginationAutoPageSize?: boolean | undefined;
            paginateChildRows?: boolean | undefined;
            suppressPaginationPanel?: boolean | undefined;
            pivotMode?: boolean | undefined;
            pivotPanelShow?: "always" | "onlyWhenPivoting" | "never" | undefined;
            pivotMaxGeneratedColumns?: number | undefined;
            pivotDefaultExpanded?: number | undefined;
            pivotColumnGroupTotals?: "before" | "after" | undefined;
            pivotRowTotals?: "before" | "after" | undefined;
            pivotSuppressAutoColumn?: boolean | undefined;
            suppressExpandablePivotGroups?: boolean | undefined;
            functionsReadOnly?: boolean | undefined;
            aggFuncs?: {
                [key: string]: import("ag-grid-community").IAggFunc<TData, any>;
            } | undefined;
            suppressAggFuncInHeader?: boolean | undefined;
            alwaysAggregateAtRootLevel?: boolean | undefined;
            aggregateOnlyChangedColumns?: boolean | undefined;
            suppressAggFilteredOnly?: boolean | undefined;
            removePivotHeaderRowWhenSingleValueColumn?: boolean | undefined;
            animateRows?: boolean | undefined;
            cellFlashDuration?: number | undefined;
            cellFadeDuration?: number | undefined;
            allowShowChangeAfterFilter?: boolean | undefined;
            domLayout?: import("ag-grid-community").DomLayoutType | undefined;
            ensureDomOrder?: boolean | undefined;
            enableCellSpan?: boolean | undefined;
            enableRtl?: boolean | undefined;
            suppressColumnVirtualisation?: boolean | undefined;
            suppressMaxRenderedRowRestriction?: boolean | undefined;
            suppressRowVirtualisation?: boolean | undefined;
            rowDragManaged?: boolean | undefined;
            suppressRowDrag?: boolean | undefined;
            suppressMoveWhenRowDragging?: boolean | undefined;
            rowDragEntireRow?: boolean | undefined;
            rowDragMultiRow?: boolean | undefined;
            rowDragText?: ((params: import("ag-grid-community").IRowDragItem, dragItemCount: number) => string) | undefined;
            dragAndDropImageComponent?: any;
            dragAndDropImageComponentParams?: any;
            fullWidthCellRenderer?: any;
            fullWidthCellRendererParams?: any;
            embedFullWidthRows?: boolean | undefined;
            groupDisplayType?: import("ag-grid-community").RowGroupingDisplayType | undefined;
            groupDefaultExpanded?: number | undefined;
            autoGroupColumnDef?: ColDef<TData, any> | undefined;
            groupMaintainOrder?: boolean | undefined;
            groupSelectsChildren?: boolean | undefined;
            groupLockGroupColumns?: number | undefined;
            groupAggFiltering?: boolean | import("ag-grid-community").IsRowFilterable<TData> | undefined;
            groupTotalRow?: "top" | "bottom" | import("ag-grid-community").UseGroupTotalRow<TData> | undefined;
            grandTotalRow?: "top" | "bottom" | "pinnedTop" | "pinnedBottom" | undefined;
            suppressStickyTotalRow?: boolean | "grand" | "group" | undefined;
            groupSuppressBlankHeader?: boolean | undefined;
            groupSelectsFiltered?: boolean | undefined;
            showOpenedGroup?: boolean | undefined;
            groupHideParentOfSingleChild?: boolean | "leafGroupsOnly" | undefined;
            groupRemoveSingleChildren?: boolean | undefined;
            groupRemoveLowestSingleChildren?: boolean | undefined;
            groupHideOpenParents?: boolean | undefined;
            groupAllowUnbalanced?: boolean | undefined;
            rowGroupPanelShow?: "always" | "never" | "onlyWhenGrouping" | undefined;
            groupRowRenderer?: any;
            groupRowRendererParams?: any;
            treeData?: boolean | undefined;
            treeDataChildrenField?: string | undefined;
            treeDataParentIdField?: string | undefined;
            rowGroupPanelSuppressSort?: boolean | undefined;
            suppressGroupRowsSticky?: boolean | undefined;
            pinnedTopRowData?: any[] | undefined;
            pinnedBottomRowData?: any[] | undefined;
            enableRowPinning?: boolean | "top" | "bottom" | undefined;
            isRowPinnable?: import("ag-grid-community").IsRowPinnable<TData> | undefined;
            isRowPinned?: import("ag-grid-community").IsRowPinned<TData> | undefined;
            rowModelType?: import("ag-grid-community").RowModelType | undefined;
            rowData?: TData[] | null | undefined;
            asyncTransactionWaitMillis?: number | undefined;
            suppressModelUpdateAfterUpdateTransaction?: boolean | undefined;
            datasource?: import("ag-grid-community").IDatasource | undefined;
            cacheOverflowSize?: number | undefined;
            infiniteInitialRowCount?: number | undefined;
            serverSideInitialRowCount?: number | undefined;
            suppressServerSideFullWidthLoadingRow?: boolean | undefined;
            cacheBlockSize?: number | undefined;
            maxBlocksInCache?: number | undefined;
            maxConcurrentDatasourceRequests?: number | undefined;
            blockLoadDebounceMillis?: number | undefined;
            purgeClosedRowNodes?: boolean | undefined;
            serverSideDatasource?: import("ag-grid-community").IServerSideDatasource<any> | undefined;
            serverSideSortAllLevels?: boolean | undefined;
            serverSideEnableClientSideSort?: boolean | undefined;
            serverSideOnlyRefreshFilteredGroups?: boolean | undefined;
            serverSidePivotResultFieldSeparator?: string | undefined;
            viewportDatasource?: import("ag-grid-community").IViewportDatasource | undefined;
            viewportRowModelPageSize?: number | undefined;
            viewportRowModelBufferSize?: number | undefined;
            alwaysShowHorizontalScroll?: boolean | undefined;
            alwaysShowVerticalScroll?: boolean | undefined;
            debounceVerticalScrollbar?: boolean | undefined;
            suppressHorizontalScroll?: boolean | undefined;
            suppressScrollOnNewData?: boolean | undefined;
            suppressScrollWhenPopupsAreOpen?: boolean | undefined;
            suppressAnimationFrame?: boolean | undefined;
            suppressMiddleClickScrolls?: boolean | undefined;
            suppressPreventDefaultOnMouseWheel?: boolean | undefined;
            scrollbarWidth?: number | undefined;
            rowSelection?: "single" | "multiple" | import("ag-grid-community").RowSelectionOptions<TData> | undefined;
            cellSelection?: boolean | import("ag-grid-community").CellSelectionOptions<TData> | undefined;
            rowMultiSelectWithClick?: boolean | undefined;
            suppressRowDeselection?: boolean | undefined;
            suppressRowClickSelection?: boolean | undefined;
            suppressCellFocus?: boolean | undefined;
            suppressHeaderFocus?: boolean | undefined;
            selectionColumnDef?: import("ag-grid-community").SelectionColumnDef | undefined;
            rowNumbers?: boolean | import("ag-grid-community").RowNumbersOptions | undefined;
            suppressMultiRangeSelection?: boolean | undefined;
            enableCellTextSelection?: boolean | undefined;
            enableRangeSelection?: boolean | undefined;
            enableRangeHandle?: boolean | undefined;
            enableFillHandle?: boolean | undefined;
            fillHandleDirection?: "x" | "y" | "xy" | undefined;
            suppressClearOnFillReduction?: boolean | undefined;
            sortingOrder?: import("ag-grid-community").SortDirection[] | undefined;
            accentedSort?: boolean | undefined;
            unSortIcon?: boolean | undefined;
            suppressMultiSort?: boolean | undefined;
            alwaysMultiSort?: boolean | undefined;
            multiSortKey?: "ctrl" | undefined;
            suppressMaintainUnsortedOrder?: boolean | undefined;
            icons?: {
                [key: string]: string | ((...args: any[]) => any);
            } | undefined;
            rowHeight?: number | undefined;
            rowStyle?: import("ag-grid-community").RowStyle | undefined;
            rowClass?: string | string[] | undefined;
            rowClassRules?: import("ag-grid-community").RowClassRules<TData> | undefined;
            suppressRowHoverHighlight?: boolean | undefined;
            suppressRowTransform?: boolean | undefined;
            columnHoverHighlight?: boolean | undefined;
            gridId?: string | undefined;
            deltaSort?: boolean | undefined;
            treeDataDisplayType?: import("ag-grid-community").TreeDataDisplayType | undefined;
            enableGroupEdit?: boolean | undefined;
            initialState?: import("ag-grid-community").GridState | undefined;
            theme?: "legacy" | import("ag-grid-community").Theme | undefined;
            loadThemeGoogleFonts?: boolean | undefined;
            themeCssLayer?: string | undefined;
            styleNonce?: string | undefined;
            themeStyleContainer?: HTMLElement | undefined;
            getContextMenuItems?: import("ag-grid-community").GetContextMenuItems<TData, any> | undefined;
            getMainMenuItems?: import("ag-grid-community").GetMainMenuItems<TData, any> | undefined;
            postProcessPopup?: ((params: import("ag-grid-community").PostProcessPopupParams<TData, any>) => void) | undefined;
            processUnpinnedColumns?: ((params: import("ag-grid-community").ProcessUnpinnedColumnsParams<TData, any>) => import("ag-grid-community").Column<any>[]) | undefined;
            processCellForClipboard?: ((params: import("ag-grid-community").ProcessCellForExportParams<TData, any>) => any) | undefined;
            processHeaderForClipboard?: ((params: import("ag-grid-community").ProcessHeaderForExportParams<TData, any>) => any) | undefined;
            processGroupHeaderForClipboard?: ((params: import("ag-grid-community").ProcessGroupHeaderForExportParams<TData, any>) => any) | undefined;
            processCellFromClipboard?: ((params: import("ag-grid-community").ProcessCellForExportParams<TData, any>) => any) | undefined;
            sendToClipboard?: ((params: import("ag-grid-community").SendToClipboardParams<TData, any>) => void) | undefined;
            processDataFromClipboard?: ((params: import("ag-grid-community").ProcessDataFromClipboardParams<TData, any>) => string[][] | null) | undefined;
            isExternalFilterPresent?: ((params: import("ag-grid-community").IsExternalFilterPresentParams<TData, any>) => boolean) | undefined;
            doesExternalFilterPass?: ((node: IRowNode<TData>) => boolean) | undefined;
            getChartToolbarItems?: import("ag-grid-community").GetChartToolbarItems | undefined;
            createChartContainer?: ((params: import("ag-grid-community").ChartRefParams<TData>) => void) | undefined;
            focusGridInnerElement?: ((params: import("ag-grid-community").FocusGridInnerElementParams<TData, any>) => boolean) | undefined;
            navigateToNextHeader?: ((params: import("ag-grid-community").NavigateToNextHeaderParams<TData, any>) => import("ag-grid-community").HeaderPosition | null) | undefined;
            tabToNextHeader?: ((params: import("ag-grid-community").TabToNextHeaderParams<TData, any>) => boolean | import("ag-grid-community").HeaderPosition) | undefined;
            navigateToNextCell?: ((params: import("ag-grid-community").NavigateToNextCellParams<TData, any>) => import("ag-grid-community").CellPosition | null) | undefined;
            tabToNextCell?: ((params: import("ag-grid-community").TabToNextCellParams<TData, any>) => boolean | import("ag-grid-community").CellPosition) | undefined;
            getLocaleText?: ((params: import("ag-grid-community").GetLocaleTextParams<TData, any>) => string) | undefined;
            getDocument?: (() => Document) | undefined;
            paginationNumberFormatter?: ((params: import("ag-grid-community").PaginationNumberFormatterParams<TData, any>) => string) | undefined;
            getGroupRowAgg?: ((params: import("ag-grid-community").GetGroupRowAggParams<TData, any>) => any) | undefined;
            isGroupOpenByDefault?: ((params: import("ag-grid-community").IsGroupOpenByDefaultParams<TData, any>) => boolean) | undefined;
            initialGroupOrderComparator?: ((params: import("ag-grid-community").InitialGroupOrderComparatorParams<TData, any>) => number) | undefined;
            processPivotResultColDef?: ((colDef: ColDef<TData, any>) => void) | undefined;
            processPivotResultColGroupDef?: ((colGroupDef: import("ag-grid-community").ColGroupDef<TData>) => void) | undefined;
            getDataPath?: import("ag-grid-community").GetDataPath<TData> | undefined;
            getChildCount?: ((dataItem: any) => number) | undefined;
            getServerSideGroupLevelParams?: ((params: import("ag-grid-community").GetServerSideGroupLevelParamsParams) => import("ag-grid-community").ServerSideGroupLevelParams) | undefined;
            isServerSideGroupOpenByDefault?: ((params: import("ag-grid-community").IsServerSideGroupOpenByDefaultParams) => boolean) | undefined;
            isApplyServerSideTransaction?: import("ag-grid-community").IsApplyServerSideTransaction | undefined;
            isServerSideGroup?: import("ag-grid-community").IsServerSideGroup | undefined;
            getServerSideGroupKey?: import("ag-grid-community").GetServerSideGroupKey | undefined;
            getBusinessKeyForNode?: ((node: IRowNode<TData>) => string) | undefined;
            getRowId?: import("ag-grid-community").GetRowIdFunc<TData> | undefined;
            resetRowDataOnUpdate?: boolean | undefined;
            processRowPostCreate?: ((params: import("ag-grid-community").ProcessRowParams<TData, any>) => void) | undefined;
            isRowSelectable?: import("ag-grid-community").IsRowSelectable<TData> | undefined;
            isRowMaster?: import("ag-grid-community").IsRowMaster<TData> | undefined;
            fillOperation?: ((params: import("ag-grid-community").FillOperationParams<TData, any>) => any) | undefined;
            postSortRows?: ((params: import("ag-grid-community").PostSortRowsParams<TData, any>) => void) | undefined;
            getRowStyle?: ((params: import("ag-grid-community").RowClassParams<TData, any>) => import("ag-grid-community").RowStyle | undefined) | undefined;
            getRowClass?: ((params: import("ag-grid-community").RowClassParams<TData, any>) => string | string[] | undefined) | undefined;
            getRowHeight?: ((params: import("ag-grid-community").RowHeightParams<TData, any>) => number | null | undefined) | undefined;
            isFullWidthRow?: ((params: import("ag-grid-community").IsFullWidthRowParams<TData, any>) => boolean) | undefined;
            'onTool-panel-visible-changed'?: ToolPanelVisibleChangedEvent<TData, any> | undefined;
            'onTool-panel-size-changed'?: ToolPanelSizeChangedEvent<TData, any> | undefined;
            'onColumn-menu-visible-changed'?: ColumnMenuVisibleChangedEvent<TData, any> | undefined;
            'onContext-menu-visible-changed'?: ContextMenuVisibleChangedEvent<TData, any> | undefined;
            'onCut-start'?: CutStartEvent<TData, any> | undefined;
            'onCut-end'?: CutEndEvent<TData, any> | undefined;
            'onPaste-start'?: PasteStartEvent<TData, any> | undefined;
            'onPaste-end'?: PasteEndEvent<TData, any> | undefined;
            'onColumn-visible'?: ColumnVisibleEvent<TData, any> | undefined;
            'onColumn-pinned'?: ColumnPinnedEvent<TData, any> | undefined;
            'onColumn-resized'?: ColumnResizedEvent<TData, any> | undefined;
            'onColumn-moved'?: ColumnMovedEvent<TData, any> | undefined;
            'onColumn-value-changed'?: ColumnValueChangedEvent<TData, any> | undefined;
            'onColumn-pivot-mode-changed'?: ColumnPivotModeChangedEvent<TData, any> | undefined;
            'onColumn-pivot-changed'?: ColumnPivotChangedEvent<TData, any> | undefined;
            'onColumn-group-opened'?: ColumnGroupOpenedEvent<TData, any> | undefined;
            'onNew-columns-loaded'?: NewColumnsLoadedEvent<TData, any> | undefined;
            'onGrid-columns-changed'?: GridColumnsChangedEvent<TData, any> | undefined;
            'onDisplayed-columns-changed'?: DisplayedColumnsChangedEvent<TData, any> | undefined;
            'onVirtual-columns-changed'?: VirtualColumnsChangedEvent<TData, any> | undefined;
            'onColumn-everything-changed'?: ColumnEverythingChangedEvent<TData, any> | undefined;
            'onColumn-header-mouse-over'?: ColumnHeaderMouseOverEvent<TData, any> | undefined;
            'onColumn-header-mouse-leave'?: ColumnHeaderMouseLeaveEvent<TData, any> | undefined;
            'onColumn-header-clicked'?: ColumnHeaderClickedEvent<TData, any> | undefined;
            'onColumn-header-context-menu'?: ColumnHeaderContextMenuEvent<TData, any> | undefined;
            'onComponent-state-changed'?: ComponentStateChangedEvent<TData, any> | undefined;
            'onCell-value-changed'?: CellValueChangedEvent<TData, any> | undefined;
            'onCell-edit-request'?: CellEditRequestEvent<TData, any> | undefined;
            'onRow-value-changed'?: RowValueChangedEvent<TData, any> | undefined;
            'onCell-editing-started'?: CellEditingStartedEvent<TData, any> | undefined;
            'onCell-editing-stopped'?: CellEditingStoppedEvent<TData, any> | undefined;
            'onRow-editing-started'?: RowEditingStartedEvent<TData, any> | undefined;
            'onRow-editing-stopped'?: RowEditingStoppedEvent<TData, any> | undefined;
            'onUndo-started'?: UndoStartedEvent<TData, any> | undefined;
            'onUndo-ended'?: UndoEndedEvent<TData, any> | undefined;
            'onRedo-started'?: RedoStartedEvent<TData, any> | undefined;
            'onRedo-ended'?: RedoEndedEvent<TData, any> | undefined;
            'onCell-selection-delete-start'?: CellSelectionDeleteStartEvent<TData, any> | undefined;
            'onCell-selection-delete-end'?: CellSelectionDeleteEndEvent<TData, any> | undefined;
            'onRange-delete-start'?: RangeDeleteStartEvent<TData, any> | undefined;
            'onRange-delete-end'?: RangeDeleteEndEvent<TData, any> | undefined;
            'onFill-start'?: FillStartEvent<TData, any> | undefined;
            'onFill-end'?: FillEndEvent<TData, any> | undefined;
            'onFilter-opened'?: FilterOpenedEvent<TData, any> | undefined;
            'onFilter-changed'?: FilterChangedEvent<TData, any> | undefined;
            'onFilter-modified'?: FilterModifiedEvent<TData, any> | undefined;
            'onAdvanced-filter-builder-visible-changed'?: AdvancedFilterBuilderVisibleChangedEvent<TData, any> | undefined;
            'onFind-changed'?: FindChangedEvent<TData, any> | undefined;
            'onChart-created'?: ChartCreatedEvent<TData, any> | undefined;
            'onChart-range-selection-changed'?: ChartRangeSelectionChangedEvent<TData, any> | undefined;
            'onChart-options-changed'?: ChartOptionsChangedEvent<TData, any> | undefined;
            'onChart-destroyed'?: ChartDestroyedEvent<TData, any> | undefined;
            'onCell-key-down'?: CellKeyDownEvent<TData, any> | FullWidthCellKeyDownEvent<TData, any> | undefined;
            'onGrid-ready'?: GridReadyEvent<TData, any> | undefined;
            'onGrid-pre-destroyed'?: GridPreDestroyedEvent<TData, any> | undefined;
            'onFirst-data-rendered'?: FirstDataRenderedEvent<TData, any> | undefined;
            'onGrid-size-changed'?: GridSizeChangedEvent<TData, any> | undefined;
            'onModel-updated'?: ModelUpdatedEvent<TData, any> | undefined;
            'onVirtual-row-removed'?: VirtualRowRemovedEvent<TData, any> | undefined;
            'onViewport-changed'?: ViewportChangedEvent<TData, any> | undefined;
            'onBody-scroll'?: BodyScrollEvent<TData, any> | undefined;
            'onBody-scroll-end'?: BodyScrollEndEvent<TData, any> | undefined;
            'onDrag-started'?: DragStartedEvent<TData, any> | undefined;
            'onDrag-stopped'?: DragStoppedEvent<TData, any> | undefined;
            'onDrag-cancelled'?: DragCancelledEvent<TData, any> | undefined;
            'onState-updated'?: StateUpdatedEvent<TData, any> | undefined;
            'onPagination-changed'?: PaginationChangedEvent<TData, any> | undefined;
            'onRow-drag-enter'?: RowDragEnterEvent<TData, any> | undefined;
            'onRow-drag-move'?: RowDragMoveEvent<TData, any> | undefined;
            'onRow-drag-leave'?: RowDragLeaveEvent<TData, any> | undefined;
            'onRow-drag-end'?: RowDragEndEvent<TData, any> | undefined;
            'onRow-drag-cancel'?: RowDragCancelEvent<TData, any> | undefined;
            'onRow-resize-started'?: RowResizeStartedEvent<TData, any> | undefined;
            'onRow-resize-ended'?: RowResizeEndedEvent<any, any> | undefined;
            'onColumn-row-group-changed'?: ColumnRowGroupChangedEvent<TData, any> | undefined;
            'onRow-group-opened'?: RowGroupOpenedEvent<TData, any> | undefined;
            'onExpand-or-collapse-all'?: ExpandOrCollapseAllEvent<TData, any> | undefined;
            'onPivot-max-columns-exceeded'?: PivotMaxColumnsExceededEvent<TData, any> | undefined;
            'onPinned-row-data-changed'?: PinnedRowDataChangedEvent<TData, any> | undefined;
            'onPinned-rows-changed'?: PinnedRowsChangedEvent<TData, any> | undefined;
            'onRow-data-updated'?: RowDataUpdatedEvent<TData, any> | undefined;
            'onAsync-transactions-flushed'?: AsyncTransactionsFlushedEvent<TData, any> | undefined;
            'onStore-refreshed'?: StoreRefreshedEvent<TData, any> | undefined;
            'onHeader-focused'?: HeaderFocusedEvent<TData, any> | undefined;
            'onCell-clicked'?: CellClickedEvent<TData, any> | undefined;
            'onCell-double-clicked'?: CellDoubleClickedEvent<TData, any> | undefined;
            'onCell-focused'?: CellFocusedEvent<TData, any> | undefined;
            'onCell-mouse-over'?: CellMouseOverEvent<TData, any> | undefined;
            'onCell-mouse-out'?: CellMouseOutEvent<TData, any> | undefined;
            'onCell-mouse-down'?: CellMouseDownEvent<TData, any> | undefined;
            'onRow-clicked'?: RowClickedEvent<TData, any> | undefined;
            'onRow-double-clicked'?: RowDoubleClickedEvent<TData, any> | undefined;
            'onRow-selected'?: RowSelectedEvent<TData, any> | undefined;
            'onSelection-changed'?: SelectionChangedEvent<TData, any> | undefined;
            'onCell-context-menu'?: CellContextMenuEvent<TData, any> | undefined;
            'onRange-selection-changed'?: RangeSelectionChangedEvent<TData, any> | undefined;
            'onCell-selection-changed'?: CellSelectionChangedEvent<TData, any> | undefined;
            'onTooltip-show'?: TooltipShowEvent<TData, any> | undefined;
            'onTooltip-hide'?: TooltipHideEvent<TData, any> | undefined;
            'onSort-changed'?: SortChangedEvent<TData, any> | undefined;
        } & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps;
        expose(exposed: import("vue").ShallowUnwrapRef<{
            api: Ref<GridApi<any> | undefined, GridApi<any> | undefined>;
        }>): void;
        attrs: any;
        slots: {};
        emit: ((evt: "update:modelValue", event: TData[]) => void) & ((evt: "update:modelValue", value: TData[]) => void);
    } | undefined;
};
export default _default;
type __VLS_PrettifyLocal<T> = {
    [K in keyof T]: T[K];
} & {};
