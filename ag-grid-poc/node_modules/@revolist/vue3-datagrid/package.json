{"name": "@revolist/vue3-datagrid", "version": "4.15.8", "description": "Vue 3 DataGrid Spreadsheet component with native Vue 3 cell render support", "main": "./dist/vue3-datagrid.umd.cjs", "module": "./dist/vue3-datagrid.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/vue3-datagrid.js", "require": "./dist/vue3-datagrid.umd.cjs", "types": "./dist/index.d.ts"}}, "type": "module", "files": ["dist"], "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "release": "npm run build && npm publish --public --tag pre-release"}, "keywords": ["revo-grid", "revolist", "csv", "datagrid", "datalist", "datamanager", "editable", "excel", "excel-grid", "export", "fast-grid", "filtering", "grid", "gridjs", "grouping", "infinity-grid", "rangeedit", "reactive", "spreadsheet", "stenciljs", "storybook", "treeview", "virtualgrid", "virtual", "virtual-scroll", "vue", "vue-grid", "vue-datagrid", "vue3", "vue3-datagrid"], "author": "revolist", "repository": {"type": "git", "url": "git+https://github.com/revolist/vue3-datagrid.git"}, "bugs": {"url": "https://github.com/revolist/revogrid/issues"}, "homepage": "https://github.com/revolist/revogrid#readme", "license": "MIT", "dependencies": {"@revolist/revogrid": "4.15.8"}, "devDependencies": {"@stencil/core": "^4.19.2", "@vitejs/plugin-vue": "^5.0.5", "typescript": "^5.2.2", "vite": "^5.3.1", "vite-plugin-dts": "^3.9.1", "vue": "^3.4.29", "vue-tsc": "^2.0.14"}, "publishConfig": {"access": "public"}}