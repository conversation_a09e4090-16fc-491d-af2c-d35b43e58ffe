(function(i,a){typeof exports=="object"&&typeof module<"u"?a(exports,require("vue"),require("@revolist/revogrid/loader"),require("@revolist/revogrid")):typeof define=="function"&&define.amd?define(["exports","vue","@revolist/revogrid/loader","@revolist/revogrid"],a):(i=typeof globalThis<"u"?globalThis:i||self,a(i.Vue3Datagrid={},i.Vue,i.RevogridLoader,i.Revogrid))})(this,function(i,a,h,R){"use strict";var A;const p="routerLink",w="navManager",j="router",D="aria",m=Symbol(),V={default:m},P=e=>(e==null?void 0:e.split(" "))||[],L=(e,t,s=[])=>{var c;return[...Array.from(((c=e.value)==null?void 0:c.classList)||[]),...s].filter((n,o,r)=>!t.has(n)&&r.indexOf(n)===o)},z=((e,t,s=[],c,n)=>{const o=a.defineComponent((r,{attrs:u,slots:f,emit:q})=>{var S;r[c];const _=a.ref(),k=new Set(P(u.class)),v=a.getCurrentInstance(),I=((S=v==null?void 0:v.appContext)==null?void 0:S.provides[w])?a.inject(w):void 0,F=g=>{const{routerLink:C}=r;if(C!==m)if(I!==void 0){g.preventDefault();let l={event:g};for(const y in r){const d=r[y];r.hasOwnProperty(y)&&y.startsWith(j)&&d!==m&&(l[y]=d)}I.navigate(l)}else console.warn("Tried to navigate, but no router was found. Make sure you have mounted Vue Router.")};return()=>{r[c],P(u.class).forEach(d=>{k.add(d)});const g=r.onClick,C=d=>{g!==void 0&&g(d),d.defaultPrevented||F(d)};let l={ref:_,class:L(_,k),onClick:C};for(const d in r){const M=r[d];(r.hasOwnProperty(d)&&M!==m||d.startsWith(D))&&(l[d]=M)}return r[p]!==m&&(l={...l,href:r[p]}),a.h(e,l,f.default&&f.default())}});return typeof o!="function"&&(o.name=e,o.props={[p]:V},s.forEach(r=>{o.props[r]=V})),o})("revo-grid",void 0,["rowHeaders","frameSize","rowSize","colSize","range","readonly","resize","canFocus","useClipboard","columns","source","pinnedTopSource","pinnedBottomSource","rowDefinitions","editors","applyOnClose","plugins","columnTypes","theme","rowClass","autoSizeColumn","filter","sorting","focusTemplate","canMoveColumns","trimmedRows","exporting","grouping","stretch","additionalData","disableVirtualX","disableVirtualY","hideAttribution","jobsBeforeRender","registerVNode","accessible","canDrag","contentsizechanged","beforeedit","beforerangeedit","afteredit","beforeautofill","beforerange","afterfocus","roworderchanged","beforesorting","beforesourcesortingapply","beforesortingapply","rowdragstart","headerclick","beforecellfocus","beforefocuslost","beforesourceset","beforeanysource","aftersourceset","afteranysource","beforecolumnsset","beforecolumnapplied","aftercolumnsset","beforefilterapply","beforefiltertrimmed","beforetrimmed","aftertrimmed","viewportscroll","beforeexport","beforeeditstart","aftercolumnresize","beforerowdefinition","filterconfigchanged","sortingconfigchanged","rowheaderschanged","beforegridrender","aftergridrender","aftergridinit","additionaldatachanged","afterthemechanged","created"]);function O(e,t){t!=null&&t.appContext&&(e.appContext=t.appContext)}const b=(e,t,s,c,n=null)=>{var u;if(!t)return(u=n==null?void 0:n.destroy)==null||u.call(n),{vueInstance:null,destroy:()=>null,el:null};let o=t._vnode;if(o){O(o,c);for(const f in s)o.component.props[f]=s[f]}else o=a.createVNode(e,s),O(o,c),a.render(o,t);return{vueInstance:o,destroy:()=>a.render(null,t),el:t}},G=(e,t)=>{const s=a.getCurrentInstance();return(c,n,o)=>{const r=t?{...t,...n}:n;r.addition=o;let u=null;return c("span",{key:`${n.prop}-${n.rowIndex||0}`,ref:f=>{u=b(e,f,r,s,u)}})}};class N{constructor(t,s,c,n,o){this.VueEditorConstructor=t,this.column=s,this.save=c,this.close=n,this.vInstance=o,this.element=null}componentDidRender(){}disconnectedCallback(){var t;(t=this.vueEl)==null||t.destroy(),this.vueEl=void 0}render(t){var s;return t("span",{key:`${this.column.prop}-${((s=this.editCell)==null?void 0:s.rowIndex)||0}`,ref:c=>{const n={...this.editCell,column:this.column,save:this.save,close:this.close};this.vueEl=b(this.VueEditorConstructor,c,n,this.vInstance)}})}}const U=e=>{const t=a.getCurrentInstance();return function(s,c,n){return new N(e,s,c,n,t)}};function x(e){return!!e&&(typeof e=="object"||typeof e=="function")&&typeof e.then=="function"}const E=(A=h.defineCustomElements)==null?void 0:A.call(h),T=a.defineAsyncComponent(async()=>(x(E)&&await E,z));i.VGrid=T,i.VGridVueEditor=U,i.VGridVueTemplate=G,i.VGridVueTemplateConstructor=b,i.default=T,Object.keys(h).forEach(e=>{e!=="default"&&!Object.prototype.hasOwnProperty.call(i,e)&&Object.defineProperty(i,e,{enumerable:!0,get:()=>h[e]})}),Object.keys(R).forEach(e=>{e!=="default"&&!Object.prototype.hasOwnProperty.call(i,e)&&Object.defineProperty(i,e,{enumerable:!0,get:()=>R[e]})}),Object.defineProperties(i,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
