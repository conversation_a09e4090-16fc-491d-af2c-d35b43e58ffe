import { ColumnDataSchemaModel } from '@revolist/revogrid';
import { ColumnTemplateProp } from '@revolist/revogrid';
import { ComponentInternalInstance } from 'vue';
import { ComponentPublicInstance } from 'vue';
import { createVNode } from 'vue';
import { DefineSetupFnComponent } from 'vue';
import { EditCell } from '@revolist/revogrid';
import { EditorCtr } from '@revolist/revogrid';
import { HyperFunc } from '@revolist/revogrid';
import { JSX } from '@revolist/revogrid';
import { PublicProps } from 'vue';
import { VNode } from '@revolist/revogrid';

declare type CreateVNodeParameters = (Parameters<typeof createVNode>)[0];

/**
 * Data passed to editor
 */
export declare interface EditorType extends Partial<EditCell> {
    column: ColumnDataSchemaModel;
    save: (value: any, preventFocus?: boolean) => void;
    close: (focusNext?: boolean) => void;
}

declare interface InputProps<T> {
    modelValue?: T;
}

declare const VGrid: DefineSetupFnComponent<JSX.RevoGrid & InputProps<string | number | boolean>, {}, {}, JSX.RevoGrid & InputProps<string | number | boolean> & {}, PublicProps>;
export { VGrid }
export default VGrid;

/**
 * Create editor constructor.
 * This function creates editor constructor by wrapping it with VueEditorAdapter
 * which is responsible for connecting editor with Vue lifecycle events
 */
export declare const VGridVueEditor: (vueConstructor: any) => EditorCtr;

/**
 * Render Vue component in Grid column template.
 */
export declare const VGridVueTemplate: (vueConstructor: CreateVNodeParameters, customProps?: any) => (h: HyperFunc<VNode>, p: ColumnDataSchemaModel | ColumnTemplateProp, addition?: any) => VNode;

/**
 * Convert Vue component to VNode and render it into HTMLElement.
 *
 * @param vueConstructor Vue component constructor
 * @param el HTMLElement where VNode will be rendered
 * @param p Props to pass to component
 * @param vueInstance Vue 3 app instance
 * @param lastEl Previous rendered VNode or null
 * @returns Vue instance, destroy method and HTMLElement
 */
export declare const VGridVueTemplateConstructor: (vueConstructor: CreateVNodeParameters, el: VueElement | null, p: Record<string, any>, vueInstance: ComponentInternalInstance | null, lastEl?: {
    destroy: () => void;
} | null) => {
    vueInstance: ComponentPublicInstance<any>;
    destroy: () => void;
    el: VueElement | null;
};

declare interface VueElement extends Element {
    _vnode?: ComponentPublicInstance<any>;
}


export * from "@revolist/revogrid";
export * from "@revolist/revogrid/loader";

export { }
