import { defineComponent as I, ref as S, getCurrentInstance as v, inject as z, h as D, createVNode as L, render as V, defineAsyncComponent as M } from "vue";
import { defineCustomElements as G } from "@revolist/revogrid/loader";
export * from "@revolist/revogrid/loader";
export * from "@revolist/revogrid";
const p = "routerLink", k = "navManager", N = "router", U = "aria", m = Symbol(), E = { default: m }, P = (t) => (t == null ? void 0 : t.split(" ")) || [], F = (t, e, i = []) => {
  var s;
  return [...Array.from(((s = t.value) == null ? void 0 : s.classList) || []), ...i].filter(
    (o, r, n) => !e.has(o) && n.indexOf(o) === r
  );
}, $ = (t, e, i = [], s, o) => {
  const r = I((n, { attrs: c, slots: d, emit: B }) => {
    var R;
    n[s];
    const C = S(), y = new Set(P(c.class)), h = v(), b = ((R = h == null ? void 0 : h.appContext) == null ? void 0 : R.provides[k]) ? z(k) : void 0, O = (f) => {
      const { routerLink: g } = n;
      if (g !== m)
        if (b !== void 0) {
          f.preventDefault();
          let u = { event: f };
          for (const l in n) {
            const a = n[l];
            n.hasOwnProperty(l) && l.startsWith(N) && a !== m && (u[l] = a);
          }
          b.navigate(u);
        } else
          console.warn("Tried to navigate, but no router was found. Make sure you have mounted Vue Router.");
    };
    return () => {
      n[s], P(c.class).forEach((a) => {
        y.add(a);
      });
      const f = n.onClick, g = (a) => {
        f !== void 0 && f(a), a.defaultPrevented || O(a);
      };
      let u = {
        ref: C,
        class: F(C, y),
        onClick: g
      };
      for (const a in n) {
        const w = n[a];
        (n.hasOwnProperty(a) && w !== m || a.startsWith(U)) && (u[a] = w);
      }
      return n[p] !== m && (u = {
        ...u,
        href: n[p]
      }), D(t, u, d.default && d.default());
    };
  });
  return typeof r != "function" && (r.name = t, r.props = {
    [p]: E
  }, i.forEach((n) => {
    r.props[n] = E;
  })), r;
}, W = /* @__PURE__ */ $("revo-grid", void 0, [
  "rowHeaders",
  "frameSize",
  "rowSize",
  "colSize",
  "range",
  "readonly",
  "resize",
  "canFocus",
  "useClipboard",
  "columns",
  "source",
  "pinnedTopSource",
  "pinnedBottomSource",
  "rowDefinitions",
  "editors",
  "applyOnClose",
  "plugins",
  "columnTypes",
  "theme",
  "rowClass",
  "autoSizeColumn",
  "filter",
  "sorting",
  "focusTemplate",
  "canMoveColumns",
  "trimmedRows",
  "exporting",
  "grouping",
  "stretch",
  "additionalData",
  "disableVirtualX",
  "disableVirtualY",
  "hideAttribution",
  "jobsBeforeRender",
  "registerVNode",
  "accessible",
  "canDrag",
  "contentsizechanged",
  "beforeedit",
  "beforerangeedit",
  "afteredit",
  "beforeautofill",
  "beforerange",
  "afterfocus",
  "roworderchanged",
  "beforesorting",
  "beforesourcesortingapply",
  "beforesortingapply",
  "rowdragstart",
  "headerclick",
  "beforecellfocus",
  "beforefocuslost",
  "beforesourceset",
  "beforeanysource",
  "aftersourceset",
  "afteranysource",
  "beforecolumnsset",
  "beforecolumnapplied",
  "aftercolumnsset",
  "beforefilterapply",
  "beforefiltertrimmed",
  "beforetrimmed",
  "aftertrimmed",
  "viewportscroll",
  "beforeexport",
  "beforeeditstart",
  "aftercolumnresize",
  "beforerowdefinition",
  "filterconfigchanged",
  "sortingconfigchanged",
  "rowheaderschanged",
  "beforegridrender",
  "aftergridrender",
  "aftergridinit",
  "additionaldatachanged",
  "afterthemechanged",
  "created"
]);
function T(t, e) {
  e != null && e.appContext && (t.appContext = e.appContext);
}
const _ = (t, e, i, s, o = null) => {
  var c;
  if (!e)
    return (c = o == null ? void 0 : o.destroy) == null || c.call(o), { vueInstance: null, destroy: () => null, el: null };
  let r = e._vnode;
  if (r) {
    T(r, s);
    for (const d in i)
      r.component.props[d] = i[d];
  } else
    r = L(t, i), T(r, s), V(r, e);
  return { vueInstance: r, destroy: () => V(null, e), el: e };
}, q = (t, e) => {
  const i = v();
  return (s, o, r) => {
    const n = e ? { ...e, ...o } : o;
    n.addition = r;
    let c = null;
    return s("span", {
      key: `${o.prop}-${o.rowIndex || 0}`,
      ref: (d) => {
        c = _(t, d, n, i, c);
      }
    });
  };
};
class X {
  constructor(e, i, s, o, r) {
    this.VueEditorConstructor = e, this.column = i, this.save = s, this.close = o, this.vInstance = r, this.element = null;
  }
  // optional, called after editor rendered
  componentDidRender() {
  }
  // optional, called after editor destroyed
  disconnectedCallback() {
    var e;
    (e = this.vueEl) == null || e.destroy(), this.vueEl = void 0;
  }
  render(e) {
    var i;
    return e("span", {
      key: `${this.column.prop}-${((i = this.editCell) == null ? void 0 : i.rowIndex) || 0}`,
      ref: (s) => {
        const o = {
          ...this.editCell,
          column: this.column,
          save: this.save,
          close: this.close
        };
        this.vueEl = _(
          this.VueEditorConstructor,
          s,
          o,
          this.vInstance
        );
      }
    });
  }
}
const J = (t) => {
  const e = v();
  return function(i, s, o) {
    return new X(
      t,
      i,
      s,
      o,
      e
    );
  };
};
function Y(t) {
  return !!t && (typeof t == "object" || typeof t == "function") && typeof t.then == "function";
}
var A;
const x = (A = G) == null ? void 0 : A(), Q = M(async () => (Y(x) && await x, W));
export {
  Q as VGrid,
  J as VGridVueEditor,
  q as VGridVueTemplate,
  _ as VGridVueTemplateConstructor,
  Q as default
};
