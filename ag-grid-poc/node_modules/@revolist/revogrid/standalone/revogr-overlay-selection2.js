/*!
 * Built by Revolist OU ❤️
 */
import { h, proxyCustomElement, HTMLElement, createEvent, Host } from '@stencil/core/internal/client';
import { g as getRange, Q as ColumnService, A as getCellData, R as getCellEditor, d as isRangeSingleCell } from './column.service.js';
import { c as codesLetter } from './platform.js';
import { R as RESIZE_INTERVAL, i as MOBILE_CLASS, j as CELL_HANDLER_CLASS, S as SELECTION_BORDER_CLASS } from './consts.js';
import { b as getCoordinate, i as isAfterLast, c as isBeforeFirst, a as getCell, g as getPropertyFromEvent, d as getCurrentCell, e as collectModelsOfRange, s as styleByCellProps, f as getFocusCellBasedOnEvent, v as verifyTouchTarget } from './selection.utils.js';
import { c as isClear, d as isTab, f as isEnterKeyValue, h as isCopy, g as isCut, j as isPaste, k as isAll, l as isEditInput, n as defineCustomElement$2 } from './revogr-edit2.js';
import { t as timeout } from './index2.js';
import { d as debounce } from './debounce.js';
import { d as defineCustomElement$3 } from './revogr-clipboard2.js';
import { d as defineCustomElement$1 } from './revogr-order-editor2.js';

const DIRECTION_CODES = [
    codesLetter.TAB,
    codesLetter.ARROW_UP,
    codesLetter.ARROW_DOWN,
    codesLetter.ARROW_LEFT,
    codesLetter.ARROW_RIGHT,
];
class KeyboardService {
    constructor(sv) {
        this.sv = sv;
    }
    async keyDown(e, canRange, isEditMode, { range, focus }) {
        // IF EDIT MODE
        if (isEditMode) {
            switch (e.code) {
                case codesLetter.ESCAPE:
                    this.sv.cancel();
                    break;
                case codesLetter.TAB:
                    this.keyChangeSelection(e, canRange);
                    break;
            }
            return;
        }
        // IF NOT EDIT MODE
        // pressed clear key
        if (range && isClear(e.code)) {
            this.sv.clearCell();
            return;
        }
        // below works with focus only
        if (!focus) {
            return;
        }
        // tab key means same as arrow right
        if (isTab(e.code)) {
            this.keyChangeSelection(e, canRange);
            return;
        }
        // pressed enter
        if (isEnterKeyValue(e.key)) {
            this.sv.change();
            return;
        }
        // copy operation
        if (isCopy(e)) {
            return;
        }
        // cut operation
        if (isCut(e)) {
            return;
        }
        // paste operation
        if (isPaste(e)) {
            this.sv.internalPaste();
            return;
        }
        // select all
        if (isAll(e)) {
            if (canRange) {
                this.selectAll(e);
            }
            return;
        }
        // pressed letter key
        if (e.key.length === 1) {
            this.sv.change(e.key);
            return;
        }
        // pressed arrow, change selection position
        if (await this.keyChangeSelection(e, canRange)) {
            return;
        }
    }
    selectAll(e) {
        const range = this.sv.selectionStore.get('range');
        const focus = this.sv.selectionStore.get('focus');
        // if no range or focus - do nothing
        if (!range || !focus) {
            return;
        }
        e.preventDefault();
        this.sv.selectAll();
    }
    async keyChangeSelection(e, canRange) {
        const data = this.changeDirectionKey(e, canRange);
        if (!data) {
            return false;
        }
        // this interval needed for several cases
        // grid could be resized before next click
        // at this case to avoid screen jump we use this interval
        await timeout(RESIZE_INTERVAL + 30);
        const range = this.sv.selectionStore.get('range');
        const focus = this.sv.selectionStore.get('focus');
        return this.keyPositionChange(data.changes, range, focus, data.isMulti);
    }
    keyPositionChange(changes, range, focus, isMulti = false) {
        if (!range || !focus) {
            return false;
        }
        const data = getCoordinate(range, focus, changes, isMulti);
        if (!data) {
            return false;
        }
        const eData = this.sv.getData();
        if (isMulti) {
            if (isAfterLast(data.end, eData.lastCell) || isBeforeFirst(data.start)) {
                return false;
            }
            const range = getRange(data.start, data.end);
            return this.sv.range(range);
        }
        return this.sv.focus(data.start, changes, isAfterLast(data.start, eData.lastCell)
            ? 1
            : isBeforeFirst(data.start)
                ? -1
                : 0);
    }
    /** Monitor key direction changes */
    changeDirectionKey(e, canRange) {
        const isMulti = canRange && e.shiftKey;
        if (DIRECTION_CODES.includes(e.code)) {
            e.preventDefault();
        }
        if (e.shiftKey) {
            switch (e.code) {
                case codesLetter.TAB:
                    return { changes: { x: -1 }, isMulti: false };
            }
        }
        switch (e.code) {
            case codesLetter.ARROW_UP:
                return { changes: { y: -1 }, isMulti };
            case codesLetter.ARROW_DOWN:
                return { changes: { y: 1 }, isMulti };
            case codesLetter.ARROW_LEFT:
                return { changes: { x: -1 }, isMulti };
            case codesLetter.TAB:
            case codesLetter.ARROW_RIGHT:
                return { changes: { x: 1 }, isMulti };
        }
    }
}

class AutoFillService {
    constructor(sv) {
        this.sv = sv;
        this.autoFillType = null;
        this.autoFillInitial = null;
        this.autoFillStart = null;
        this.autoFillLast = null;
    }
    /**
     * Render autofill box
     * @param range
     * @param selectionFocus
     */
    renderAutofill(range, selectionFocus, isMobile = false) {
        let handlerStyle;
        if (range) {
            handlerStyle = getCell(range, this.sv.dimensionRow.state, this.sv.dimensionCol.state);
        }
        else {
            handlerStyle = getCell(Object.assign(Object.assign({}, selectionFocus), { x1: selectionFocus.x, y1: selectionFocus.y }), this.sv.dimensionRow.state, this.sv.dimensionCol.state);
        }
        return (h("div", { class: {
                [CELL_HANDLER_CLASS]: true,
                [MOBILE_CLASS]: isMobile,
            }, style: {
                left: `${handlerStyle.right}px`,
                top: `${handlerStyle.bottom}px`,
            }, onMouseDown: (e) => this.autoFillHandler(e), onTouchStart: (e) => this.autoFillHandler(e) }));
    }
    autoFillHandler(e, type = "AutoFill" /* AutoFillType.autoFill */) {
        let target = null;
        if (e.target instanceof Element) {
            target = e.target;
        }
        if (!target) {
            return;
        }
        this.selectionStart(target, this.sv.getData(), type);
        e.preventDefault();
    }
    get isAutoFill() {
        return !!this.autoFillType;
    }
    /**
     * Process mouse move events
     */
    selectionMouseMove(e) {
        // initiate mouse move debounce if not present
        if (!this.onMouseMoveAutofill) {
            this.onMouseMoveAutofill = debounce((e, data) => this.doAutofillMouseMove(e, data), 5);
        }
        if (this.isAutoFill) {
            this.onMouseMoveAutofill(e, this.sv.getData());
        }
    }
    getFocus(focus, range) {
        // there was an issue that it was taking last cell from range but focus was out
        if (!focus && range) {
            focus = { x: range.x, y: range.y };
        }
        return focus || null;
    }
    /**
     * Autofill logic:
     * on mouse move apply based on previous direction (if present)
     */
    doAutofillMouseMove(event, data) {
        // if no initial - not started
        if (!this.autoFillInitial) {
            return;
        }
        const x = getPropertyFromEvent(event, 'clientX', MOBILE_CLASS);
        const y = getPropertyFromEvent(event, 'clientY', MOBILE_CLASS);
        // skip touch
        if (x === null || y === null) {
            return;
        }
        const current = getCurrentCell({ x, y }, data);
        // first time or direction equal to start(same as first time)
        if (!this.autoFillLast) {
            if (!this.autoFillLast) {
                this.autoFillLast = this.autoFillStart;
            }
        }
        // check if not the latest, if latest - do nothing
        if (isAfterLast(current, data.lastCell)) {
            return;
        }
        this.autoFillLast = current;
        const isSame = current.x === this.autoFillInitial.x &&
            current.y === this.autoFillInitial.y;
        // if same as initial - clear
        if (isSame) {
            this.sv.setTempRange(null);
        }
        else {
            const area = getRange(this.autoFillInitial, this.autoFillLast);
            this.sv.setTempRange({
                area,
                type: this.autoFillType,
            });
        }
    }
    /**
     * Range selection started
     * Mode @param type:
     * Can be triggered from MouseDown selection on element
     * Or can be triggered on corner square drag
     */
    selectionStart(target, data, type = "Selection" /* AutoFillType.selection */) {
        /** Get cell by autofill element */
        const { top, left } = target.getBoundingClientRect();
        this.autoFillInitial = this.getFocus(data.focus, data.range);
        this.autoFillType = type;
        this.autoFillStart = getCurrentCell({ x: left, y: top }, data);
    }
    /**
     * Clear current range selection on mouse up and mouse leave events
     */
    clearAutoFillSelection(focus, oldRange) {
        // If autofill was active, apply autofill values
        if (this.autoFillInitial) {
            // Fetch latest focus
            this.autoFillInitial = this.getFocus(focus, oldRange);
            // Apply range data if autofill mode is active
            if (this.autoFillType === "AutoFill" /* AutoFillType.autoFill */) {
                const range = getRange(this.autoFillInitial, this.autoFillLast);
                // If range is present, apply data
                if (range) {
                    const { defaultPrevented: stopApply, detail: { range: newRange }, } = this.sv.clearRangeDataApply({
                        range,
                    });
                    // If data apply was not prevented, apply new range
                    if (!stopApply && oldRange) {
                        this.applyRangeWithData(newRange, oldRange);
                    }
                    else {
                        // If data apply was prevented, clear temporary range
                        this.sv.setTempRange(null);
                    }
                }
            }
            else {
                // If not autofill mode, apply range only
                this.applyRangeOnly(this.autoFillInitial, this.autoFillLast);
            }
        }
        // Reset autofill state
        this.resetAutoFillState();
    }
    /**
     * Reset autofill state
     */
    resetAutoFillState() {
        this.autoFillType = null;
        this.autoFillInitial = null;
        this.autoFillLast = null;
        this.autoFillStart = null;
    }
    /**
     * Trigger range apply events and handle responses
     */
    onRangeApply(newData, newRange, oldRange) {
        this.sv.rangeDataApply({
            data: newData,
            models: collectModelsOfRange(newData, this.sv.dataStore),
            type: this.sv.dataStore.get('type'),
            oldRange,
            newRange
        });
        this.sv.setRange(newRange);
    }
    /** Apply range and copy data during range application */
    applyRangeWithData(newRange, rangeToCopy) {
        const rangeData = {
            type: this.sv.dataStore.get('type'),
            colType: this.sv.columnService.type,
            newData: {},
            mapping: {},
            newRange,
            oldRange: rangeToCopy,
        };
        const { mapping, changed } = this.sv.columnService.getRangeData(rangeData, this.sv.columnService.columns);
        rangeData.newData = changed;
        rangeData.mapping = mapping;
        let e = this.sv.selectionChanged(rangeData);
        // if default prevented - clear range
        if (e.defaultPrevented) {
            this.sv.setTempRange(null);
            return;
        }
        e = this.sv.rangeCopy(rangeData);
        if (e.defaultPrevented) {
            this.sv.setRange(newRange);
            return;
        }
        this.onRangeApply(rangeData.newData, newRange, rangeToCopy);
    }
    /**
     * Update range selection only,
     * no data change (mouse selection)
     */
    applyRangeOnly(start, end) {
        // no changes to apply
        if (!start || !end) {
            return;
        }
        const newRange = getRange(start, end);
        this.sv.setRange(newRange);
    }
}

const revogrOverlayStyleCss = ".revo-drag-icon{width:11px;opacity:0.8}.revo-drag-icon::before{content:\"::\"}.revo-alt-icon{-webkit-mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");width:11px;height:11px;background-size:cover;background-repeat:no-repeat}.arrow-down{position:absolute;right:5px;top:0}.arrow-down svg{width:8px;margin-top:5px;margin-left:5px;opacity:0.4}.cell-value-wrapper{margin-right:10px;overflow:hidden;text-overflow:ellipsis}.revo-button{position:relative;overflow:hidden;color:#fff;background-color:#4545ff;height:32px;line-height:32px;padding:0 15px;outline:0;border:0;border-radius:7px;box-sizing:border-box;cursor:pointer}.revo-button.green{background-color:#009037}.revo-button.red{background-color:#E0662E}.revo-button:disabled,.revo-button[disabled]{cursor:not-allowed !important;filter:opacity(0.35) !important}.revo-button.outline{border:1px solid #dbdbdb;line-height:30px;background:none;color:#000;box-shadow:none}revo-grid[theme^=dark] .revo-button.outline{border:1px solid #404040;color:#d8d8d8}revogr-overlay-selection{display:block;position:relative;width:100%}revogr-overlay-selection .autofill-handle{position:absolute;width:14px;height:14px;margin-left:-13px;margin-top:-13px;z-index:10;cursor:crosshair}revogr-overlay-selection .autofill-handle::before{content:\"\";position:absolute;right:0;bottom:0;width:10px;height:10px;background:#0d63e8;border:1px solid white;box-sizing:border-box}revogr-overlay-selection.mobile .autofill-handle{position:absolute;width:30px;height:30px;margin-left:-29px;margin-top:-29px;z-index:10;cursor:crosshair}revogr-overlay-selection.mobile .autofill-handle::before{content:\"\";position:absolute;right:0;bottom:0;width:12px;height:12px;background:#0d63e8;border:1px solid white;box-sizing:border-box}revogr-overlay-selection .selection-border-range{position:absolute;pointer-events:none;z-index:9;box-shadow:-1px 0 0 #0d63e8 inset, 1px 0 0 #0d63e8 inset, 0 -1px 0 #0d63e8 inset, 0 1px 0 #0d63e8 inset}revogr-overlay-selection .selection-border-range .range-handlers{height:100%;background-color:transparent;width:75%;max-width:50px;min-width:20px;left:50%;transform:translateX(-50%);position:absolute}revogr-overlay-selection .selection-border-range .range-handlers>span{pointer-events:auto;height:20px;width:20px;position:absolute;left:50%;transform:translateX(-50%)}revogr-overlay-selection .selection-border-range .range-handlers>span:before,revogr-overlay-selection .selection-border-range .range-handlers>span:after{position:absolute;border-radius:5px;width:15px;height:5px;left:50%;transform:translateX(-50%);background-color:rgba(0, 0, 0, 0.2)}revogr-overlay-selection .selection-border-range .range-handlers>span:first-child{top:-7px}revogr-overlay-selection .selection-border-range .range-handlers>span:first-child:before{content:\"\";top:0}revogr-overlay-selection .selection-border-range .range-handlers>span:last-child{bottom:-7px}revogr-overlay-selection .selection-border-range .range-handlers>span:last-child:after{content:\"\";bottom:0}revogr-overlay-selection revogr-edit{z-index:10}";

const OverlaySelection = /*@__PURE__*/ proxyCustomElement(class OverlaySelection extends HTMLElement {
    constructor() {
        super();
        this.__registerHost();
        this.beforeCopyRegion = createEvent(this, "beforecopyregion", 7);
        this.beforeRegionPaste = createEvent(this, "beforepasteregion", 7);
        this.cellEditApply = createEvent(this, "celleditapply", 7);
        this.beforeFocusCell = createEvent(this, "beforecellfocusinit", 7);
        this.beforeNextViewportFocus = createEvent(this, "beforenextvpfocus", 7);
        this.setEdit = createEvent(this, "setedit", 7);
        this.beforeApplyRange = createEvent(this, "beforeapplyrange", 7);
        this.beforeSetRange = createEvent(this, "beforesetrange", 7);
        this.setRange = createEvent(this, "setrange", 7);
        this.beforeEditRender = createEvent(this, "beforeeditrender", 7);
        this.selectAll = createEvent(this, "selectall", 7);
        this.cancelEdit = createEvent(this, "canceledit", 7);
        this.setTempRange = createEvent(this, "settemprange", 7);
        this.beforeSetTempRange = createEvent(this, "beforesettemprange", 7);
        this.applyFocus = createEvent(this, "applyfocus", 7);
        this.focusCell = createEvent(this, "focuscell", 7);
        this.beforeRangeDataApply = createEvent(this, "beforerangedataapply", 7);
        this.selectionChange = createEvent(this, "selectionchangeinit", 7);
        this.beforeRangeCopyApply = createEvent(this, "beforerangecopyapply", 7);
        this.rangeEditApply = createEvent(this, "rangeeditapply", 7);
        this.rangeClipboardCopy = createEvent(this, "clipboardrangecopy", 7);
        this.rangeClipboardPaste = createEvent(this, "clipboardrangepaste", 7);
        this.beforeKeyDown = createEvent(this, "beforekeydown", 7);
        this.beforeKeyUp = createEvent(this, "beforekeyup", 7);
        this.beforeCellSave = createEvent(this, "beforecellsave", 7);
        this.cellEditDone = createEvent(this, "celledit", 7);
        /**
         * If true applys changes when cell closes if not Escape.
         */
        this.applyChangesOnClose = false;
        this.keyboardService = null;
        this.autoFillService = null;
        this.unsubscribeSelectionStore = [];
    }
    // #endregion
    // #region Listeners
    onMouseMove(e) {
        var _a;
        if (this.selectionStore.get('focus')) {
            (_a = this.autoFillService) === null || _a === void 0 ? void 0 : _a.selectionMouseMove(e);
        }
    }
    /**
     * Action finished inside the document.
     * Pointer left document, clear any active operation.
     */
    onMouseUp() {
        var _a;
        // Clear autofill selection
        // when pointer left document,
        // clear any active operation.
        (_a = this.autoFillService) === null || _a === void 0 ? void 0 : _a.clearAutoFillSelection(this.selectionStore.get('focus'), this.selectionStore.get('range'));
    }
    /**
     * Row drag started.
     * This event is fired when drag action started on cell.
     */
    onCellDrag(e) {
        var _a;
        // Invoke drag start on order editor.
        (_a = this.orderEditor) === null || _a === void 0 ? void 0 : _a.dragStart(e.detail);
    }
    /**
     * Get keyboard down from element.
     * This event is fired when keyboard key is released.
     */
    onKeyUp(e) {
        // Emit before key up event.
        this.beforeKeyUp.emit(Object.assign({ original: e }, this.getData()));
    }
    /**
     * Get keyboard down from element.
     * This event is fired when keyboard key is pressed.
     */
    onKeyDown(e) {
        var _a;
        // Emit before key down event and check if default prevention is set.
        const proxy = this.beforeKeyDown.emit(Object.assign({ original: e }, this.getData()));
        if (e.defaultPrevented || proxy.defaultPrevented) {
            return;
        }
        // Invoke key down on keyboard service.
        (_a = this.keyboardService) === null || _a === void 0 ? void 0 : _a.keyDown(e, this.range, !!this.selectionStore.get('edit'), {
            focus: this.selectionStore.get('focus'),
            range: this.selectionStore.get('range'),
        });
    }
    // #endregion
    /**
     * Selection & Keyboard
     */
    selectionServiceSet(selectionStore) {
        // clear subscriptions
        this.unsubscribeSelectionStore.forEach(v => v());
        this.unsubscribeSelectionStore.length = 0;
        this.unsubscribeSelectionStore.push(selectionStore.onChange('nextFocus', v => v && this.doFocus(v, v)));
        this.keyboardService = new KeyboardService({
            selectionStore,
            range: r => !!r && this.triggerRangeEvent(r),
            focus: (f, changes, focusNextViewport) => {
                if (focusNextViewport) {
                    this.beforeNextViewportFocus.emit(f);
                    return false;
                }
                else {
                    return this.doFocus(f, f, changes);
                }
            },
            change: val => {
                if (this.readonly) {
                    return;
                }
                this.doEdit(val);
            },
            cancel: async () => {
                var _a;
                await ((_a = this.revogrEdit) === null || _a === void 0 ? void 0 : _a.cancelChanges());
                this.closeEdit();
            },
            clearCell: () => !this.readonly && this.clearCell(),
            internalPaste: () => !this.readonly && this.beforeRegionPaste.emit(),
            getData: () => this.getData(),
            selectAll: () => this.selectAll.emit(),
        });
        this.createAutoFillService();
    }
    /** Autofill */
    createAutoFillService() {
        this.autoFillService = new AutoFillService({
            dimensionRow: this.dimensionRow,
            dimensionCol: this.dimensionCol,
            columnService: this.columnService,
            dataStore: this.dataStore,
            clearRangeDataApply: e => this.beforeRangeDataApply.emit(Object.assign(Object.assign(Object.assign({}, e), this.types), { rowDimension: Object.assign({}, this.dimensionRow.state), colDimension: Object.assign({}, this.dimensionCol.state) })),
            setTempRange: e => {
                const tempRangeEvent = this.beforeSetTempRange.emit(Object.assign(Object.assign({ tempRange: e }, this.getData()), this.types));
                if (tempRangeEvent.defaultPrevented) {
                    return null;
                }
                return this.setTempRange.emit(tempRangeEvent.detail.tempRange);
            },
            selectionChanged: e => this.selectionChange.emit(e),
            rangeCopy: e => this.beforeRangeCopyApply.emit(e),
            rangeDataApply: e => this.rangeEditApply.emit(e),
            setRange: e => !!e && this.triggerRangeEvent(e),
            getData: () => this.getData(),
        });
    }
    /** Columns */
    columnServiceSet() {
        var _a;
        (_a = this.columnService) === null || _a === void 0 ? void 0 : _a.destroy();
        this.columnService = new ColumnService(this.dataStore, this.colData);
        this.createAutoFillService();
    }
    connectedCallback() {
        this.columnServiceSet();
        this.selectionServiceSet(this.selectionStore);
    }
    disconnectedCallback() {
        var _a;
        // clear subscriptions
        this.unsubscribeSelectionStore.forEach(v => v());
        this.unsubscribeSelectionStore.length = 0;
        (_a = this.columnService) === null || _a === void 0 ? void 0 : _a.destroy();
    }
    async componentWillRender() {
        var _a, _b;
        const editCell = this.selectionStore.get('edit');
        if (!editCell) {
            await ((_b = (_a = this.revogrEdit) === null || _a === void 0 ? void 0 : _a.beforeDisconnect) === null || _b === void 0 ? void 0 : _b.call(_a));
        }
    }
    renderRange(range) {
        const cell = getCell(range, this.dimensionRow.state, this.dimensionCol.state);
        const styles = styleByCellProps(cell);
        return [
            h("div", { class: SELECTION_BORDER_CLASS, style: styles }, this.isMobileDevice && (h("div", { class: "range-handlers" }, h("span", { class: MOBILE_CLASS }), h("span", { class: MOBILE_CLASS })))),
        ];
    }
    renderEditor() {
        // Check if edit access
        const editCell = this.selectionStore.get('edit');
        // Readonly or Editor closed
        if (this.readonly || !editCell) {
            return null;
        }
        const enteredOrModelValue = editCell.val ||
            getCellData(this.columnService.rowDataModel(editCell.y, editCell.x).value);
        const editable = Object.assign(Object.assign({}, editCell), this.columnService.getSaveData(editCell.y, editCell.x, enteredOrModelValue));
        const renderEvent = this.beforeEditRender.emit(Object.assign(Object.assign({ range: Object.assign(Object.assign({}, editCell), { x1: editCell.x, y1: editCell.y }) }, this.types), { rowDimension: Object.assign({}, this.dimensionRow.state), colDimension: Object.assign({}, this.dimensionCol.state) }));
        // Render prevented
        if (renderEvent.defaultPrevented) {
            return null;
        }
        const cell = getCell(renderEvent.detail.range, renderEvent.detail.rowDimension, renderEvent.detail.colDimension);
        const styles = styleByCellProps(cell);
        return (h("revogr-edit", { style: styles, ref: el => (this.revogrEdit = el), additionalData: this.additionalData, editCell: editable, saveOnClose: this.applyChangesOnClose, onCelleditinit: (e) => {
                this.cellEditDone.emit(e.detail);
            }, column: this.columnService.rowDataModel(editCell.y, editCell.x), editor: getCellEditor(this.columnService.columns[editCell.x], this.editors) }));
    }
    onEditCell(e) {
        if (e.defaultPrevented) {
            return;
        }
        const saveEv = this.beforeCellSave.emit(e.detail);
        if (!saveEv.defaultPrevented) {
            this.cellEdit(saveEv.detail);
        }
        // if not clear navigate to next cell after edit
        if (!saveEv.detail.preventFocus) {
            this.focusNext();
        }
    }
    render() {
        var _a;
        const nodes = [];
        const editCell = this.renderEditor();
        // Editor
        if (editCell) {
            nodes.push(editCell);
        }
        else {
            const range = this.selectionStore.get('range');
            const focus = this.selectionStore.get('focus');
            // Clipboard
            if ((range || focus) && this.useClipboard) {
                nodes.push(h("revogr-clipboard", { readonly: this.readonly, onCopyregion: e => this.onCopy(e.detail), onClearregion: () => !this.readonly && this.clearCell(), ref: e => (this.clipboard = e), onPasteregion: e => this.onPaste(e.detail) }));
            }
            // Range
            if (range) {
                nodes.push(...this.renderRange(range));
            }
            // Autofill
            if (focus && !this.readonly && this.range) {
                nodes.push((_a = this.autoFillService) === null || _a === void 0 ? void 0 : _a.renderAutofill(range, focus, this.isMobileDevice));
            }
            // Order
            if (this.canDrag) {
                nodes.push(h("revogr-order-editor", { ref: e => (this.orderEditor = e), dataStore: this.dataStore, dimensionRow: this.dimensionRow, dimensionCol: this.dimensionCol, parent: this.element, rowType: this.types.rowType, onRowdragstartinit: e => this.rowDragStart(e) }));
            }
        }
        return (h(Host, { key: 'd936e8452e84c7a25ecd6502e929f1a5af69467f', class: { mobile: this.isMobileDevice }, onDblClick: (e) => this.onElementDblClick(e), onMouseDown: (e) => this.onElementMouseDown(e), onTouchStart: (e) => this.onElementMouseDown(e, true), onCloseedit: (e) => this.closeEdit(e),
            // it's done to be able to throw events from different levels, not just from editor
            onCelledit: (e) => this.onEditCell(e) }, nodes, h("slot", { key: 'cd3525d404aa44fd8d06e7fc459777acb8a9d585', name: "data" })));
    }
    /**
     * Executes the focus operation on the specified range of cells.
     */
    doFocus(focus, end, changes) {
        // 1. Trigger beforeFocus event
        const { defaultPrevented } = this.beforeFocusCell.emit(this.columnService.getSaveData(focus.y, focus.x));
        if (defaultPrevented) {
            return false;
        }
        const evData = Object.assign(Object.assign({ range: Object.assign(Object.assign({}, focus), { x1: end.x, y1: end.y }), next: changes }, this.types), { rowDimension: Object.assign({}, this.dimensionRow.state), colDimension: Object.assign({}, this.dimensionCol.state) });
        // 2. Trigger apply focus event
        const applyEvent = this.applyFocus.emit(evData);
        if (applyEvent.defaultPrevented) {
            return false;
        }
        const { range } = applyEvent.detail;
        // 3. Trigger focus event
        return !this.focusCell.emit(Object.assign({ focus: {
                x: range.x,
                y: range.y,
            }, end: {
                x: range.x1,
                y: range.y1,
            } }, applyEvent.detail)).defaultPrevented;
    }
    triggerRangeEvent(range) {
        const type = this.types.rowType;
        // 1. Apply range
        const applyEvent = this.beforeApplyRange.emit(Object.assign(Object.assign({ range: Object.assign({}, range) }, this.types), { rowDimension: Object.assign({}, this.dimensionRow.state), colDimension: Object.assign({}, this.dimensionCol.state) }));
        if (applyEvent.defaultPrevented) {
            return false;
        }
        const data = this.columnService.getRangeTransformedToProps(applyEvent.detail.range, this.dataStore);
        // 2. Before set range
        let e = this.beforeSetRange.emit(data);
        if (e.defaultPrevented) {
            return false;
        }
        // 3. Set range
        e = this.setRange.emit(Object.assign(Object.assign({}, applyEvent.detail.range), { type }));
        if (e.defaultPrevented) {
            return false;
        }
        return !e.defaultPrevented;
    }
    /**
     * Open Editor on DblClick
     */
    onElementDblClick(e) {
        // DblClick prevented outside - Editor will not open
        if (e.defaultPrevented) {
            return;
        }
        // Get data from the component
        const data = this.getData();
        const focusCell = getFocusCellBasedOnEvent(e, data);
        if (!focusCell) {
            return;
        }
        this.doEdit();
    }
    /**
     * Handle mouse down event on Host element
     */
    onElementMouseDown(e, touch = false) {
        var _a;
        // Get the target element from the event object
        const targetElement = e.target;
        // Ignore focus if clicked input
        if (isEditInput(targetElement) || e.defaultPrevented) {
            return;
        }
        // Get data from the component
        const data = this.getData();
        const focusCell = getFocusCellBasedOnEvent(e, data);
        if (!focusCell) {
            return;
        }
        // Set focus on the current cell
        this.focus(focusCell, this.range && e.shiftKey);
        // Initiate autofill selection
        if (this.range) {
            targetElement &&
                ((_a = this.autoFillService) === null || _a === void 0 ? void 0 : _a.selectionStart(targetElement, this.getData()));
            // Prevent default behavior for mouse events,
            // but only if target element is not a mobile input
            if (!touch) {
                e.preventDefault();
            }
            else if (verifyTouchTarget(e.touches[0], MOBILE_CLASS)) {
                // Prevent default behavior for touch events
                // if target element is a mobile input
                e.preventDefault();
            }
        }
    }
    /**
     * Start cell editing
     */
    doEdit(val = '') {
        var _a;
        if (this.canEdit()) {
            const focus = this.selectionStore.get('focus');
            if (!focus) {
                return;
            }
            const data = this.columnService.getSaveData(focus.y, focus.x);
            (_a = this.setEdit) === null || _a === void 0 ? void 0 : _a.emit(Object.assign(Object.assign({}, data), { val }));
        }
    }
    /**
     * Close editor event triggered
     * @param details - if it requires focus next
     */
    async closeEdit(e) {
        this.cancelEdit.emit();
        if (e === null || e === void 0 ? void 0 : e.detail) {
            await this.focusNext();
        }
    }
    /**
     * Edit finished.
     * Close Editor and save.
     */
    cellEdit(e) {
        const dataToSave = this.columnService.getSaveData(e.rgRow, e.rgCol, e.val);
        this.cellEditApply.emit(dataToSave);
    }
    getRegion() {
        const focus = this.selectionStore.get('focus');
        let range = this.selectionStore.get('range');
        if (!range) {
            range = getRange(focus, focus);
        }
        return range;
    }
    onCopy(e) {
        var _a;
        const range = this.getRegion();
        const canCopyEvent = this.beforeCopyRegion.emit(range);
        if (canCopyEvent.defaultPrevented) {
            return false;
        }
        let rangeData;
        if (range) {
            const { data, mapping } = this.columnService.copyRangeArray(range, this.dataStore);
            const event = this.rangeClipboardCopy.emit(Object.assign({ range,
                data,
                mapping }, this.types));
            if (!event.defaultPrevented) {
                rangeData = event.detail.data;
            }
        }
        (_a = this.clipboard) === null || _a === void 0 ? void 0 : _a.doCopy(e, rangeData);
        return true;
    }
    onPaste(data) {
        var _a;
        const focus = this.selectionStore.get('focus');
        const isEditing = this.selectionStore.get('edit') !== null;
        if (!focus || isEditing) {
            return;
        }
        let { changed, range } = this.columnService.getTransformedDataToApply(focus, data);
        const { defaultPrevented: canPaste } = this.rangeClipboardPaste.emit(Object.assign({ data: changed, models: collectModelsOfRange(changed, this.dataStore), range }, this.types));
        if (canPaste) {
            return;
        }
        (_a = this.autoFillService) === null || _a === void 0 ? void 0 : _a.onRangeApply(changed, range, range);
    }
    async focusNext() {
        var _a;
        const canFocus = await ((_a = this.keyboardService) === null || _a === void 0 ? void 0 : _a.keyChangeSelection(new KeyboardEvent('keydown', {
            code: codesLetter.ARROW_DOWN,
        }), this.range));
        if (!canFocus) {
            this.closeEdit();
        }
    }
    clearCell() {
        var _a;
        const range = this.selectionStore.get('range');
        if (range && !isRangeSingleCell(range)) {
            const data = this.columnService.getRangeStaticData(range, '');
            (_a = this.autoFillService) === null || _a === void 0 ? void 0 : _a.onRangeApply(data, range, range);
        }
        else if (this.canEdit()) {
            const focused = this.selectionStore.get('focus');
            if (!focused) {
                return;
            }
            const cell = this.columnService.getSaveData(focused.y, focused.x);
            this.cellEdit({
                rgRow: focused.y,
                rgCol: focused.x,
                val: '',
                type: cell.type,
                prop: cell.prop,
            });
        }
    }
    rowDragStart({ detail }) {
        detail.text = getCellData(this.columnService.rowDataModel(detail.cell.y, detail.cell.x).value);
    }
    /**
     * Verify if edit allowed.
     */
    canEdit() {
        var _a;
        if (this.readonly) {
            return false;
        }
        const focus = this.selectionStore.get('focus');
        return focus && !((_a = this.columnService) === null || _a === void 0 ? void 0 : _a.isReadOnly(focus.y, focus.x));
    }
    get edited() {
        return this.selectionStore.get('edit');
    }
    /**
     * Sets the focus on a cell and optionally edits a range.
     */
    focus(cell, isRangeEdit = false) {
        if (!cell)
            return false;
        const end = cell;
        const start = this.selectionStore.get('focus');
        if (isRangeEdit && start) {
            const range = getRange(start, end);
            if (range) {
                return this.triggerRangeEvent(range);
            }
        }
        return this.doFocus(cell, end);
    }
    get types() {
        return {
            rowType: this.dataStore.get('type'),
            colType: this.columnService.type,
        };
    }
    /**
     * Collect data
     */
    getData() {
        return {
            el: this.element,
            rows: this.dimensionRow.state,
            cols: this.dimensionCol.state,
            lastCell: this.lastCell,
            focus: this.selectionStore.get('focus'),
            range: this.selectionStore.get('range'),
            edit: this.selectionStore.get('edit'),
        };
    }
    get element() { return this; }
    static get watchers() { return {
        "selectionStore": ["selectionServiceSet"],
        "dimensionRow": ["createAutoFillService"],
        "dimensionCol": ["createAutoFillService"],
        "dataStore": ["columnServiceSet"],
        "colData": ["columnServiceSet"]
    }; }
    static get style() { return revogrOverlayStyleCss; }
}, [4, "revogr-overlay-selection", {
        "readonly": [4],
        "range": [4],
        "canDrag": [4, "can-drag"],
        "useClipboard": [4, "use-clipboard"],
        "selectionStore": [16, "selection-store"],
        "dimensionRow": [16, "dimension-row"],
        "dimensionCol": [16, "dimension-col"],
        "dataStore": [16, "data-store"],
        "colData": [16, "col-data"],
        "lastCell": [16, "last-cell"],
        "editors": [16],
        "applyChangesOnClose": [4, "apply-changes-on-close"],
        "additionalData": [8, "additional-data"],
        "isMobileDevice": [4, "is-mobile-device"]
    }, [[5, "touchmove", "onMouseMove"], [5, "mousemove", "onMouseMove"], [5, "touchend", "onMouseUp"], [5, "mouseup", "onMouseUp"], [5, "mouseleave", "onMouseUp"], [0, "dragstartcell", "onCellDrag"], [4, "keyup", "onKeyUp"], [4, "keydown", "onKeyDown"]], {
        "selectionStore": ["selectionServiceSet"],
        "dimensionRow": ["createAutoFillService"],
        "dimensionCol": ["createAutoFillService"],
        "dataStore": ["columnServiceSet"],
        "colData": ["columnServiceSet"]
    }]);
function defineCustomElement() {
    if (typeof customElements === "undefined") {
        return;
    }
    const components = ["revogr-overlay-selection", "revogr-clipboard", "revogr-edit", "revogr-order-editor"];
    components.forEach(tagName => { switch (tagName) {
        case "revogr-overlay-selection":
            if (!customElements.get(tagName)) {
                customElements.define(tagName, OverlaySelection);
            }
            break;
        case "revogr-clipboard":
            if (!customElements.get(tagName)) {
                defineCustomElement$3();
            }
            break;
        case "revogr-edit":
            if (!customElements.get(tagName)) {
                defineCustomElement$2();
            }
            break;
        case "revogr-order-editor":
            if (!customElements.get(tagName)) {
                defineCustomElement$1();
            }
            break;
    } });
}

export { OverlaySelection as O, defineCustomElement as d };
//# sourceMappingURL=revogr-overlay-selection2.js.map

//# sourceMappingURL=revogr-overlay-selection2.js.map