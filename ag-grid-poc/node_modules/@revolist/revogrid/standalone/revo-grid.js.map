{"file": "revo-grid.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAEa,YAAY,CAAA;AAAzB,IAAA,WAAA,GAAA;QACE,IAAc,CAAA,cAAA,GAAG,EAAE;;AACpB;;MCFY,YAAY,CAAA;AAAzB,IAAA,WAAA,GAAA;QACE,IAAc,CAAA,cAAA,GAAG,EAAE;;AACpB;;MCFY,aAAa,CAAA;AAA1B,IAAA,WAAA,GAAA;QACE,IAAc,CAAA,cAAA,GAAG,EAAE;;AACpB;;ACCM,MAAM,aAAa,GAAG,SAAS;AAE/B,MAAM,aAAa,GAAY;IACpC,aAAa;IACb,UAAU;IACV,SAAS;IACT,cAAc;IACd,aAAa;CACd;AACa,MAAO,YAAY,CAAA;AAI/B,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,YAAY;;AAG1B,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc;;IAG/D,IAAI,OAAO,CAAC,IAAY,EAAA;AACtB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;AAG3B,IAAA,WAAA,CAAY,GAAgB,EAAA;QAdpB,IAAa,CAAA,aAAA,GAAG,CAAC;AAevB,QAAA,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC,OAAO;AAChC,QAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;;AAG1B,IAAA,QAAQ,CAAC,KAAY,EAAA;AACnB,QAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC;QACnC,QAAQ,WAAW;AACjB,YAAA,KAAK,UAAU;AACf,YAAA,KAAK,cAAc;AACjB,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,aAAa,EAAE;gBACvC;AACF,YAAA,KAAK,SAAS;AACd,YAAA,KAAK,aAAa;AAChB,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE;gBACtC;AACF,YAAA;AACE,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE;gBACtC;;;AAGP;AAGK,SAAU,QAAQ,CAAC,KAAqB,EAAA;AAC5C,IAAA,IAAI,KAAK,IAAI,aAAa,CAAC,OAAO,CAAC,KAAc,CAAC,GAAG,EAAE,EAAE;AACvD,QAAA,OAAO,KAAc;;AAEvB,IAAA,OAAO,aAAa;AACtB;;ACxDA;;AAEG;AACI,MAAM,yBAAyB,GAAG,CAAC,YAGzC,KAA6C;AAC5C;;AAEG;IACH,OAAO;AACL;;AAEG;AACH,QAAA,GAAG,CAAC,CAAC,EAAA;YACH,QAAQ,CAAC;AACP,gBAAA,KAAK,OAAO;AACZ,gBAAA,KAAK,OAAO;gBACZ,KAAK,gBAAgB,EAAE;;oBAErB,IAAI,QAAQ,GAAG,CAAC;oBAChB,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;AAC7C,oBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;wBAC9B,QAAQ;4BACN,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAClC,gCAAA,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC;;AAE5C,oBAAA,YAAY,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,CAAC;oBACnC;;;SAGL;KACF;AACH,CAAC;;AClCD;;;;;;AAMG;AACI,MAAM,aAAa,GAAG,CAAC,YAG7B,KAA6C;IAC5C,IAAI,cAAc,GAA2C,IAAI;IACjE,IAAI,oBAAoB,GAA2C,IAAI;IAEvE,OAAO;QACL,GAAG,CAAC,GAAG,EAAE,GAAG,EAAA;YACV,QAAQ,GAAG;gBACT,KAAK,OAAO,EAAE;;AAEZ,oBAAA,IAAI,cAAc,IAAI,cAAc,KAAK,GAAG,EAAE;wBAC5C,cAAc,GAAG,IAAI;wBACrB;;oBAEF,oBAAoB,GAAG,IAAI;oBAC3B;;gBAEF,KAAK,SAAS,EAAE;oBACd,MAAM,IAAI,GAAG,GAAwC;oBACrD,IAAI,CAAC,oBAAoB,EAAE;wBACzB,oBAAoB,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;;oBAGxD,cAAc,GAAG,sBAAsB,CACrC,oBAAoB,EACpB,IAAI,IAAI,EAAE,CACX;;AAED,oBAAA,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC;oBACrC;;;SAGL;KACF;AACH,CAAC;AAED,SAAS,sBAAsB,CAC7B,KAAqC,EACrC,QAAwC,EAAA;IAExC,MAAM,QAAQ,GAAmC,EAAE;IACnD,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;SAC1C,GAAG,CAAC,MAAM;AACV,SAAA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACxB,MAAM,SAAS,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IACzD,IAAI,KAAK,GAAG,CAAC;AACb,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE;AACnC,QAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;AAC7B,YAAA,KAAK,EAAE;;AAGP,YAAA,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC1B;;;AAGJ,QAAA,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YAC1B,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;;;AAGlC,IAAA,OAAO,QAAQ;AACjB;;ACvEA;;;AAGG;AAqBH,SAAS,WAAW,GAAA;IAClB,OAAO;AACL,QAAA,OAAO,EAAE,EAAE;AACX,QAAA,KAAK,EAAE,CAAC;;AAGR,QAAA,OAAO,EAAE,IAAI;;AAGb,QAAA,KAAK,EAAE,EAAE;;AAET,QAAA,mBAAmB,EAAE,EAAE;;AAEvB,QAAA,WAAW,EAAE,EAAE;AACf,QAAA,eAAe,EAAE,EAAE;KACpB;AACH;AAEA,SAAS,YAAY,GAAA;AACnB,IAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACK,WAAW,EAAE,CAAA,EAAA;;AAEhB,QAAA,QAAQ,EAAE,CAAC;;QAGX,cAAc,EAAE,CAAC,EACjB,CAAA;AACJ;MAEa,cAAc,CAAA;AAEzB,IAAA,WAAA,CAA4B,IAAwB,EAAA;QAAxB,IAAI,CAAA,IAAA,GAAJ,IAAI;QAC9B,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;AACxC,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC;YAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;AAC3C,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,yBAAyB,CAAC;YACvC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACnC,SAAA,CAAC,CAAC;;IAGL,eAAe,GAAA;AACb,QAAA,MAAM,KAAK,GAAG,YAAY,EAAE;QAC5B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;QAC/B,OAAO,MAAM,CACX,IAAI,EACJ,CAAC,CAAyB,EAAE,CAAO,KAAI;YACrC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9B,YAAA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAa;AACpB,YAAA,OAAO,CAAC;SACT,EACD,KAAK,CACN;;IAGH,OAAO,GAAA;QACL,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAE,CAAC;;AAGtC,IAAA,QAAQ,CAAgC,IAAgB,EAAA;AACtD,QAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC;;IAG5B,IAAI,GAAA;QACF,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE,CAAC;;AAGrC;;;;AAIG;IACH,gBAAgB,CAAC,QAA6B,EAAE,EAAA;AAC9C,QAAA,MAAM,aAAa,GAAG,sBAAsB,CAC1C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAChC,KAAK,CACN;QACD,QAAQ,CAAC,IAAI,CAAC,KAAK,kCACd,aAAa,CAAA,EAAA,EAChB,KAAK,EAAA,CAAA,CACL;;AAGJ,IAAA,4BAA4B,CAAC,aAAuB,EAAE,cAAA,GAA2B,EAAE,EAAA;;QAEjF,MAAM,WAAW,GAAO,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE;YACpC;;;QAGF,MAAM,eAAe,GAA6B,EAAE;QACpD,cAAc,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,SAAS,KAAI;AAC9C,YAAA,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE;AAC/B,gBAAA,eAAe,CAAC,SAAS,CAAC,GAAG,EAAE;;YAEjC,eAAe,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7C,SAAC,CAAC;;QAGF,MAAM,QAAQ,GAA2B,EAAE;QAE3C,aAAa,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,SAAS,KAAI;YAC7C,MAAM,OAAO,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;YAE3C,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjC,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;AAEtC,gBAAA,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,SAAS,IAAI,WAAW,CAAC,aAAa,CAAC,EAAE;oBAC5F,QAAQ,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC;AAChD,oBAAA,OAAO,WAAW,CAAC,aAAa,CAAC;;;AAGvC,SAAC,CAAC;;QAGF,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE;AAChC,YAAA,IAAI,CAAC,gBAAgB,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAChB,WAAW,CACX,EAAA,QAAQ,EACX;;;AAGP;;AC9ID;;;;AAIG;MACU,UAAU,CAAA;IAGrB,WAAmB,CAAA,QAA6B,EAAS,SAA0B,EAAA;QAAhE,IAAQ,CAAA,QAAA,GAAR,QAAQ;QAA8B,IAAS,CAAA,SAAA,GAAT,SAAS;QAFzD,IAAC,CAAA,CAAA,GAAG,CAAC;QACL,IAAa,CAAA,aAAA,GAA6C,EAAE;;AAErE;;;;AAIG;IACH,gBAAgB,CACd,SAAY,EACZ,QAAoF,EAAA;QAEpF,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAmB,EAAE,QAAQ,CAAC;AAC7D,QAAA,IAAI,CAAC,aAAa,CAAC,SAAmB,CAAC,GAAG,QAAQ;;AAGpD;;;;;;;AAOG;AACH,IAAA,KAAK,CACH,IAAY,EACZ,QAAoC,EACpC,EAAE,SAAS,EAAA,GAA2B,EAAE,SAAS,EAAE,KAAK,EAAE,EAAA;QAE1D,MAAM,eAAe,GACnB,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;AACpD,YAAA,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC;;QAG5E,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE;AACzC,YAAA,GAAG,CAAC,GAAM,EAAA;;AACR,gBAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC;AACjC,gBAAA,IAAI,WAAW,KAAK,KAAK,EAAE;oBACzB;;;AAGF,gBAAA,OAAO,CAAA,EAAA,GAAA,eAAe,KAAf,IAAA,IAAA,eAAe,uBAAf,eAAe,CAAE,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC;aAC7C;YACD,GAAG,GAAA;;;AAED,gBAAA,OAAO,CAAA,EAAA,GAAA,eAAe,KAAf,IAAA,IAAA,eAAe,KAAf,MAAA,GAAA,MAAA,GAAA,eAAe,CAAE,GAAG,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAI,CAAC,IAAI,CAAC;aACxC;AACF,SAAA,CAAC;QACF,IAAI,SAAS,EAAE;YACb,QAAQ,CAAC,eAAe,KAAf,IAAA,IAAA,eAAe,uBAAf,eAAe,CAAE,KAAK,CAAC;;;AAIpC;;;AAGG;AACH,IAAA,mBAAmB,CAAC,SAAiB,EAAA;AACnC,QAAA,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3E,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;;AAGtC;;;AAGG;IACH,IAAI,CAAU,SAAiB,EAAE,MAAU,EAAA;AACzC,QAAA,MAAM,KAAK,GAAG,IAAI,WAAW,CAAI,SAAS,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;AACzE,QAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAClC,QAAA,OAAO,KAAK;;AAGd;;AAEG;IACH,kBAAkB,GAAA;AAChB,QAAA,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;AACnC,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;;;AAIlC;;AAEG;IACH,OAAO,GAAA;QACL,IAAI,CAAC,kBAAkB,EAAE;;AAE5B;;ACnGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE;AACpC,EAAE,IAAI,KAAK,GAAG,EAAE;AAChB,MAAM,MAAM,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM;;AAE/C,EAAE,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE;AAC3B,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,KAAK,EAAE;AACxD,MAAM;AACN;AACA;AACA,EAAE,OAAO,KAAK;AACd;;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,EAAE,OAAO,OAAO,KAAK,IAAI,UAAU,GAAG,KAAK,GAAG,QAAQ;AACtD;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE;AACvC,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,SAAS,GAAG,QAAQ;AACvD,EAAE,OAAO,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;AACjD;;ACtCA;;;AAGG;AAiDH,MAAM,iBAAiB,GAAG,CAAC;AAWrB,MAAO,oBAAqB,SAAQ,UAAU,CAAA;AAWlD,IAAA,WAAA,CACE,QAA6B,EACtB,SAA0B,EAC1B,MAA6B,EAAA;AAEpC,QAAA,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;QAHnB,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAM,CAAA,MAAA,GAAN,MAAM;QAbf,IAAe,CAAA,eAAA,GAAoC,IAAI;;QAOvD,IAAW,CAAA,WAAA,GAAmB,IAAI;QAClC,IAAU,CAAA,UAAA,GAAkB,IAAI;AAQ9B,QAAA,IAAI,CAAC,eAAe,GAAG,CAAA,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,MAAA,GAAA,MAAA,GAAN,MAAM,CAAE,eAAe,KAAI,iBAAiB;;QAGnE,IAAI,MAAM,aAAN,MAAM,KAAA,MAAA,GAAA,MAAA,GAAN,MAAM,CAAE,WAAW,EAAE;AACvB,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,sBAAsB,EAAE;AAC5D,YAAA,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC;;QAGpD,MAAM,cAAc,GAAG,CAAC,EACtB,MAAM,EAAE,EAAE,MAAM,EAAE,GACU,KAAI;AAChC,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AACxB,SAAC;QACD,MAAM,gBAAgB,GAAG,CAAC,EACxB,MAAM,EAAE,EAAE,OAAO,EAAE,GACW,KAAI;AAClC,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AACzB,SAAC;AACD,QAAA,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;QAC3D,QAAQ,MAAM,aAAN,MAAM,KAAA,MAAA,GAAA,MAAA,GAAN,MAAM,CAAE,IAAI;AAClB,YAAA,KAAA,uBAAA;AACE,gBAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,cAAc,CAAC;gBACvD,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAG,CAAC,EAAE,MAAM,EAA0B,KAAI;AACzE,oBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AACxB,iBAAC,CAAC;gBACF;AACF,YAAA,KAAA,aAAA;AACE,gBAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,cAAc,CAAC;gBACvD,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,EAAE,MAAM,EAAE,KAAI;AAChD,oBAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;AAC3B,iBAAC,CAAC;gBACF;AACF,YAAA;gBACE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,EAAE,MAAM,EAAmC,KAAI;oBACtF,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC;AACzC,oBAAA,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC;oBACnD,IAAI,IAAI,EAAE;wBACR,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,CACrC,IAAI,EACJ;AACE,4BAAA,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI;yBACrB,EACD,IAAI,CACL;;AAEL,iBAAC,CAAC;gBACF;;;IAIN,MAAM,SAAS,CAAC,MAAkB,EAAA;AAChC,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe;AACnC,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,YAAY,EAAE;;;QAIrB,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,OAAgB,EAAE,MAAc,KAAI;AAC/D,gBAAA,IAAI,CAAC,WAAW,GAAG,OAAO;AAC1B,gBAAA,IAAI,CAAC,UAAU,GAAG,MAAM;AAC1B,aAAC,CAAC;AACF,YAAA,IAAI;gBACF,QAAQ,GAAG,MAAM,OAAO;;YACxB,OAAO,CAAC,EAAE;gBACV;;;;QAKJA,OAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,IAAmB,KAAI;YACzC,MAAM,KAAK,GAAwB,EAAE;YACrCA,OAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,IAAG;;gBAE3B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAC7C,CAAC,IAAI,EAAE,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAClE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,CACjC;AACH,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;AAC5D,SAAC,CAAC;;AAGJ,IAAA,SAAS,CAAC,GAAS,EAAA;;QACjB,MAAM,OAAO,GAAG,EAAE;QAClB,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,OAAO,CAAC;;AAEV,QAAA,IAAI;AACF,YAAA,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE;;AAG1B,YAAA,IAAI,MAAA,IAAI,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,WAAW,EAAE;AAC5B,gBAAA,IAAI,CAAC,uBAAuB,CAAC,SAAS,GAAG,GAAG;gBAC5C,OAAO,IAAI,CAAC,uBAAuB,CAAC,WAAW,GAAG,OAAO,GAAG,CAAC;;YAE/D,OAAO,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,GAAG,OAAO,GAAG,CAAC;;QACtD,OAAO,CAAC,EAAE;AACV,YAAA,OAAO,CAAC;;;AAIZ,IAAA,SAAS,CAAC,CAAY,EAAA;AACpB,QAAA,IAAI,IAA8B;AAClC,QAAA,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;AACvB,YAAA,IAAI,GAAG,CAAC,CAAC,IAAI;;aACR;AACL,YAAA,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;;QAEnCA,OAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,EAAE,IAAmB,KAAI;YAC1D,MAAM,KAAK,GAAwB,EAAE;AAErC,YAAAA,OAAI,CAAC,OAAO,EAAE,KAAK,IAAG;;;gBAEpB,MAAM,IAAI,GAAG,MAAM,CACjB,IAAI,EACJ,CAAC,IAAwB,EAAE,KAAK,KAAI;oBAClC,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE;AAC5C,wBAAA,OAAO,IAAI;;oBAEb,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;iBAC9D,EACD,SAAS,CACV;AAED,gBAAA,IAAI,IAAI,IAAI,CAAC,CAAA,EAAA,GAAA,KAAK,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAI,CAAC,IAAI,IAAI,EAAE;oBACpC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI;;AAE1C,aAAC,CAAC;AAEF,YAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;AAC5D,SAAC,CAAC;;AAGJ,IAAA,YAAY,CAAC,CAAY,EAAA;QACvB,MAAM,KAAK,GAAsB,EAAE;AACnC,QAAA,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;AACvB,YAAAA,OAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAIA,OAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;;aACnD;AACL,YAAA,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI;;QAEtBA,OAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,EAAE,IAAmB,KAAI;YAC1D,MAAM,KAAK,GAAwB,EAAE;AAErC,YAAAA,OAAI,CAAC,OAAO,EAAE,KAAK,IAAG;AACpB,gBAAA,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AACrB,oBAAA,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC;oBAClD,IAAI,IAAI,EAAE;AACR,wBAAA,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI;;;AAG/B,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;AAC5D,SAAC,CAAC;;IAGJ,aAAa,CAAC,KAAa,EAAE,IAAmB,EAAA;;AAC9C,QAAA,MAAM,KAAK,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAG,IAAI,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK,CAAC;QACnD,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,OAAO,CAAC;;AAEV,QAAA,OAAO,MAAM,CACX,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAC1B,CAAC,CAAC,EAAE,CAAC,KAAI;YACP,MAAM,QAAQ,GAAG,MAAM,CACrB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EACpB,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,KAAI;gBAChB,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;gBACtC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;aAC/D,EACD,CAAC,CACF;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC;AAC9B,SAAC,EACD,KAAK,CAAC,IAAI,IAAI,CAAC,CAChB;;AAGH,IAAA,SAAS,CAAC,OAA+C,EAAA;;AACvD,QAAA,KAAK,IAAI,CAAC,IAAI,WAAW,EAAE;YACzB,MAAM,IAAI,GAAG,CAAkB;AAC/B,YAAA,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAE1B,YAAA,KAAK,IAAI,CAAC,IAAI,IAAI,EAAE;AAClB,gBAAA,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAI,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,UAAU,CAAA,EAAE;AAC/C,oBAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AACzB,wBAAA,IAAI,CAAC,eAAe,GAAG,EAAE;;oBAE3B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;AAC/B,wBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE;;oBAEjC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACxB,IAAI,CAAC,CAAC,CAAC,CAAA,EAAA,EACV,KAAK,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,EAAA,CACvB;;;;AAKP,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,CAAC;YAC5C,IAAI,CAAC,YAAY,EAAE;;;IAIvB,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;AAGxB,IAAA,WAAW,CAAC,CAAY,EAAA;AACtB,QAAA,OAAO,CAAC,CAAE,CAAgC,CAAC,IAAI;;IAGjD,sBAAsB,GAAA;;AACpB,QAAA,MAAM,gBAAgB,GAAiC;AACrD,YAAA,QAAQ,EAAE,UAAU;AACpB,YAAA,QAAQ,EAAE,MAAM;AAChB,YAAA,MAAM,EAAE,GAAG;AACX,YAAA,KAAK,EAAE,GAAG;AACV,YAAA,UAAU,EAAE,QAAQ;AACpB,YAAA,GAAG,EAAE,GAAG;AACR,YAAA,SAAS,EAAE,QAAQ;AACnB,YAAA,OAAO,EAAE,OAAO;SACjB;QAED,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACxC,QAAA,KAAK,IAAI,CAAC,IAAI,gBAAgB,EAAE;AAC9B,YAAA,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAA,EAAA,GAAA,gBAAgB,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAI,EAAE;;AAEzC,QAAA,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC;AACvC,QAAA,OAAO,EAAE;;IAGX,OAAO,GAAA;;QACL,KAAK,CAAC,OAAO,EAAE;AACf,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,uBAAuB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,EAAE;;AAEzC;;ACzSK,MAAO,aAAc,SAAQ,UAAU,CAAA;IAG3C,WACE,CAAA,QAA6B,EACtB,SAA0B,EAAA;AAEjC,QAAA,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;QAFnB,IAAS,CAAA,SAAA,GAAT,SAAS;QAJV,IAAe,CAAA,eAAA,GAAyB,IAAI;;AASlD,QAAA,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,QAAQ,CAAC;;AAG5C,QAAA,MAAM,mBAAmB,GAAG,CAAC,EAC3B,MAAM,EAAE,EAAE,OAAO,EAAE,GACW,KAAK,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AAC/D,QAAA,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,mBAAmB,CAAC;;AAG3D,IAAA,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,EAAgB,EAAA;;QACjD,IACE,IAAI,KAAK,OAAO;AAChB,YAAA,IAAI,CAAC,eAAe;AACpB,YAAA,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,WAAW,MAAK,IAAI,CAAC,eAAe,CAAC,IAAI,EAC/D;YACA,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU;gBAC5C,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,CAAC,WAAW,EAAE;;;;IAKhB,eAAe,GAAA;AACrB,QAAA,MAAM,SAAS,GAAG,CAAC,EAAE,MAAM,EAA6B,KACtD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AACxB,QAAA,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,SAAS,CAAC;;IAG1C,WAAW,GAAA;AACjB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI;AAC3B,QAAA,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC;;IAGlC,KAAK,GAAA;AACX,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB;;QAEF,MAAM,IAAI,GAAkB,OAAO;AACnC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;QACtE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,CACrC,IAAI,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAEC,KAAK,CAAA,EAAA,EACR,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAA,CAAA,EAEzD,IAAI,CACL;;AAGH;;AAEG;AACH,IAAA,YAAY,CAAC,OAA+C,EAAA;;QAE1D,IAAI,CAAC,WAAW,EAAE;;QAElB,IAAI,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,CAAC;QAClDA,OAAI,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,IAAmB,KAAI;AACvC,YAAA,MAAM,QAAQ,GACZ,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC;YAC7D,cAAc,IAAI,QAAQ;AAC5B,SAAC,CAAC;AACF,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;YAC5B,MAAM,WAAW,GACf,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM;AAC7D,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU;AACvC,YAAA,MAAM,aAAa,GAAG,sBAAsB,CAC1C,WAAW,EACX,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,SAAS,CAChD;YACD,IAAI,aAAa,EAAE;gBACjB,cAAc,IAAI,aAAa;;;AAGnC,QAAA,IAAI,cAAc,GAAG,CAAC,EAAE;;YAEtB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;YACtC,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;AACjC;;;;AAIG;AACH,YAAA,MAAM,OAAO,GAAG,CAAA,IAAI,KAAJ,IAAA,IAAA,IAAI,uBAAJ,IAAI,CAAE,IAAI,KAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC;AACxD,YAAA,MAAM,IAAI,GAAG,cAAc,GAAG,OAAO,GAAG,CAAC;YAEzC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,OAAO,GAAG,IAAI,EAAE;gBAC5C,IAAI,CAAC,eAAe,GAAG;AACrB,oBAAA,WAAW,EAAE,IAAI;oBACjB,KAAK;oBACL,IAAI;iBACL;gBACD,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,CAAC,eAAe,EAAE;;;;AAI7B;AAED;;AAEG;AACG,SAAU,eAAe,CAC7B,MAA2C,EAAA;AAE3C,IAAA,OAAO,CAAC,CAAE,MAAwB,CAAC,YAAY;AACjD;;AC3IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE;AACzC,EAAE,IAAI,MAAM,KAAK,MAAM,EAAE;AACzB,IAA6B;AAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,KAAK,GAAG,MAAM,GAAG,KAAK;AAC/C;AACA,IAA6B;AAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,KAAK,GAAG,MAAM,GAAG,KAAK;AAC/C;AACA;AACA,EAAE,OAAO,MAAM;AACf;;AChBA;AACA,IAAI,gBAAgB,GAAG,UAAU;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,EAAE,OAAO,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,GAAG,CAAC;AACrE;;AChCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE;AAC5C,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM;;AAE3B,EAAE,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;AAC1B,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE;AACjB,IAAI,KAAK,GAAG,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC;AAClD;AACA,EAAE,GAAG,GAAG,CAAC,GAAG,KAAK,SAAS,IAAI,GAAG,GAAG,MAAM,IAAI,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC;AACrE,EAAE,IAAI,GAAG,GAAG,CAAC,EAAE;AACf,IAAI,GAAG,IAAI,MAAM;AACjB;AACA,EAAE,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC;AACvC,EAAE,OAAO,KAAK,GAAG,GAAG,EAAE;AACtB,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK;AAC1B;AACA,EAAE,OAAO,KAAK;AACd;;AC1BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE;AACxC,EAAE,IAAI,MAAM,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM;AAC/C,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,EAAE;AACb;AAKA,EAAE,OAAO,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC;AAC3C;;ACvCA,MAAM,OAAO,GAAc;AACzB,IAAA,IAAI,EAAE,UAAU;AAChB,IAAA,QAAQ,EAAE,KAAK;;AAEf,IAAA,GAAG,EAAE,IAAI;AACT,IAAA,eAAe,EAAE,GAAG;AACpB,IAAA,YAAY,EAAE,MAAM;AACpB,IAAA,QAAQ,EAAE,EAAE;CACb;AAID;AACA,MAAM,eAAe,GAAG,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;AAC/C;AACA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;AACzC,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;AACzC,MAAM,cAAc,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAClD,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC;MAE3B,SAAS,CAAA;AAEpB,IAAA,WAAA,CAAY,UAA8B,EAAE,EAAA;AAC1C,QAAA,IAAI,CAAC,OAAO,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,OAAO,CAAK,EAAA,OAAO,CAAE;;AAG3C,IAAA,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAa,EAAA;AAC1C,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,cAAc,GAAG,EAAE;;QAGnD,IAAI,CAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,MAAA,GAAA,MAAA,GAAP,OAAO,CAAE,MAAM,IAAG,CAAC,EAAE;AACvB,YAAA,OAAO,CAAC,OAAO,CAAC,MAAM,IAAG;;AAEvB,gBAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;oBAClB;;AAEF,gBAAA,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;AAClE,gBAAA,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY;AACrC,aAAC,CAAC;;QAGJ,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;AAC5B,YAAA,IAAI,KAAK,GAAG,CAAC,EAAE;AACb,gBAAA,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY;;;AAGrC,YAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;AACrB,gBAAA,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;gBAC9E;;AAEF,YAAA,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;AACrH,SAAC,CAAC;AAEF,QAAA,OAAO,MAAM;;IAGP,aAAa,CAAC,aAAuB,EAAE,eAAuB,EAAA;QACpE,IAAI,MAAM,GAAG,EAAE;QACf,MAAM,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;AACzF,QAAA,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC;AAChD,QAAA,OAAO,MAAM;;AAGP,IAAA,SAAS,CAAC,KAAU,EAAE,eAAuB,EAAE,KAAK,GAAG,KAAK,EAAA;QAClE,IAAI,MAAM,GAAG,KAAK;AAClB,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;QAEhC,MAAM,QAAQ,GAAG,CAAC,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,CAAC;AACzE,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACjC,YAAA,OAAO,EAAE;;QAEX,IAAI,MAAM,KAAK,EAAE,KAAK,KAAK,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YAC1E,OAAO,CAAA,CAAA,EAAI,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA,CAAA,CAAG;;AAGjD,QAAA,OAAO,MAAM;;AAEhB;;ACrED,IAAK,WAEJ;AAFD,CAAA,UAAK,WAAW,EAAA;AACd,IAAA,WAAA,CAAA,KAAA,CAAA,GAAA,KAAW;AACb,CAAC,EAFI,WAAW,KAAX,WAAW,GAEf,EAAA,CAAA,CAAA;AAIK,MAAO,gBAAiB,SAAQ,UAAU,CAAA;;IAE9C,MAAM,YAAY,CAAC,OAAA,GAAwB,EAAE,EAAE,CAAA,GAAiB,WAAW,CAAC,GAAG,EAAA;AAC7E,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE;QACtC,IAAI,CAAC,IAAI,EAAE;AACT,YAAA,OAAO,IAAI;;AAEb,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;;;IAIlD,MAAM,UAAU,CAAC,OAAA,GAAwB,EAAE,EAAE,CAAA,GAAiB,WAAW,CAAC,GAAG,EAAA;AAC3E,QAAA,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;;;IAIvD,MAAM,UAAU,CAAC,OAAA,GAAwB,EAAE,EAAE,CAAA,GAAiB,WAAW,CAAC,GAAG,EAAA;QAC3E,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC;;QAG5C,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,SAAS;QAE1C,MAAM,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;QACrC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC,OAAO;AAChD,QAAA,MAAM,IAAI,GAAG,CAAA,EAAG,QAAQ,CAAI,CAAA,EAAA,QAAQ,EAAE;QAEtC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;AAC1C,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE;AAEjD,QAAA,CAAC,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM;AACxB,QAAA,CAAC,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC;AAC3B,QAAA,CAAC,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;AAChC,QAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;;AAG5B,QAAA,MAAM,OAAO,CAAC,GAAG,CAAC;AAClB,QAAA,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC;;;IAI1B,MAAM,OAAO,CAAC,SAAoB,EAAA;AAChC,QAAA,MAAM,IAAI,GAAG,CAAG,EAAA,SAAS,CAAC,OAAO,CAAC,IAAI,CAAA,SAAA,EAAY,SAAS,CAAC,OAAO,CAAC,QAAQ,EAAE;AAC9E,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE;YACtC,IAAI,CAAC,IAAI,EAAE;AACT,gBAAA,OAAO,IAAI;;AAEb,YAAA,OAAO,IAAI,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC;;AAEvD,QAAA,OAAO,IAAI;;;AAIL,IAAA,MAAM,YAAY,GAAA;AACxB,QAAA,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;AAC/B,QAAA,MAAM,KAAK,GAAqC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,CAAC;AACnF,QAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;AAC1B,YAAA,OAAO,IAAI;;AAEb,QAAA,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI;;AAGlB,IAAA,MAAM,OAAO,GAAA;AACnB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE;QACnC,MAAM,SAAS,GAAgB,EAAE;QACjC,MAAM,WAAW,GAAyB,EAAE;QAC5C,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;YAC3B,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACzE,SAAC,CAAC;AACF,QAAA,MAAM,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;AAC9B,QAAA,MAAM,OAAO,GAAc;AACzB,YAAA,OAAO,EAAE,EAAE;AACX,YAAA,KAAK,EAAE,EAAE;SACV;AACD,QAAA,KAAK,IAAI,MAAM,IAAI,SAAS,EAAE;YAC5B,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;gBAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AACvB,oBAAA,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE;;gBAEzB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/B,aAAC,CAAC;YACF,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;;QAErC,OACE,MAAA,CAAA,MAAA,CAAA,EAAA,IAAI,EACD,EAAA,OAAO,CACV;;IAGI,MAAM,eAAe,CAAC,CAAgB,EAAA;QAC5C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;QACnD,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;QAClC,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;QACzC,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC;QACxC,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;QAClC,MAAM,QAAQ,GAAa,EAAE;QAC7B,MAAM,QAAQ,GAAiB,EAAE;AACjC,QAAA,cAAc,CAAC,OAAO,CAAC,CAAC,CAAS,KAAI;YACnC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI;AAC3B,YAAA,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;AACnC,YAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACrB,SAAC,CAAC;AACF,QAAA,MAAM,IAAI,GAAe,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,cAAc,CAAC;AAC5E,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACnB,OAAO;AACL,YAAA,OAAO,EAAE,IAAI;AACb,YAAA,KAAK,EAAE,QAAQ;SAChB;;AAGK,IAAA,eAAe,CAAC,KAAa,EAAE,MAAc,EAAE,KAAe,EAAA;QACpE,MAAM,IAAI,GAAe,EAAE;AAC3B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;AAClD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;AAC9B,YAAA,MAAM,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;AAC3B,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AAChB,YAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBACd;;AAEF,YAAA,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC;;AAG7B,YAAA,WAAW,CAAC,OAAO,CAAC,CAAC,KAAY,KAAI;gBACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACjC,gBAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AAChC,oBAAA,KAAK,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,IAAI;;AAEhC,aAAC,CAAC;;AAEJ,QAAA,OAAO,IAAI;;AAGL,IAAA,MAAM,SAAS,GAAA;QACrB,MAAM,IAAI,GAAiB,EAAE;QAC7B,MAAM,YAAY,GAAsB,EAAE;AAC1C,QAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAG;YACnB,MAAM,QAAQ,GAAe,EAAE;AAC/B,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;YACnB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAa,KAAK,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9F,YAAA,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5B,SAAC,CAAC;AACF,QAAA,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;AAC1B,YAAA,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,YAAA,OAAO,CAAC;SACT,EAAE,EAAE,CAAC;;;AAIA,IAAA,SAAS,CAAC,IAAiB,EAAE,OAAA,GAAwB,EAAE,EAAA;QAC7D,QAAQ,IAAI;YACV,KAAK,WAAW,CAAC,GAAG;AAClB,gBAAA,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC;AAC/B,YAAA;AACE,gBAAA,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC;;;AAGxC;;AC/KD,MAAM,EAAE,GAAkB,CAAC,KAAyB,EAAE,KAA+B,KAAI;AACvF,IAAA,IAAI,OAAO,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;AAC9D,QAAA,OAAO,IAAI;;AAEb,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;AAG/B,IAAA,MAAM,SAAS,GAAG,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,MAAA,GAAA,MAAA,GAAL,KAAK,CAAE,QAAQ,EAAA,CAAG,iBAAiB,EAAE;IACvD,IAAI,CAAA,SAAS,KAAA,IAAA,IAAT,SAAS,KAAA,MAAA,GAAA,MAAA,GAAT,SAAS,CAAE,MAAM,MAAK,CAAC,EAAE;AAC3B,QAAA,OAAO,IAAI;;AAGb,IAAA,OAAO,KAAK,CAAC,iBAAiB,EAAE,KAAK,SAAS;AAChD,CAAC;AAEM,MAAM,KAAK,GAAkB,CAAC,KAAyB,EAAE,KAA+B,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;AACrH,KAAK,CAAC,KAAK,GAAG,OAAqB;AACnC,EAAE,CAAC,KAAK,GAAG,OAAqB;;AClBhC,MAAM,MAAM,GAAkB,UAAU,KAAyB,EAAE,KAA+B,EAAA;AAChG,IAAA,IAAI,cAAsB;AAE1B,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,IAAI,EAAE;AAC/E,QAAA,cAAc,GAAG,UAAU,CAAC,KAAK,KAAL,IAAA,IAAA,KAAK,KAAL,MAAA,GAAA,MAAA,GAAA,KAAK,CAAE,QAAQ,EAAE,CAAC;QAC9C,OAAO,KAAK,GAAG,cAAc;;AAE/B,IAAA,OAAO,KAAK;AACd,CAAC;AAED,MAAM,CAAC,KAAK,GAAG,OAAO;;ACRtB,MAAM,QAAQ,GAAkB,UAAU,KAAyB,EAAE,KAA+B,EAAA;AAClG,IAAA,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,IAAIC,MAAE,CAAC,KAAK,EAAE,KAAK,CAAC;AAC7C,CAAC;AAED,QAAQ,CAAC,KAAK,GAAG,OAAO;;ACNxB,MAAM,EAAE,GAAkB,UAAU,KAAyB,EAAE,KAA+B,EAAA;AAC5F,IAAA,IAAI,cAAsB;AAC1B,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,IAAI,EAAE;QAC/E,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC7C,OAAO,KAAK,GAAG,cAAc;;SACxB;AACL,QAAA,OAAO,KAAK;;AAEhB,CAAC;AAED,EAAE,CAAC,KAAK,GAAG,OAAO;;ACRlB,MAAM,IAAI,GAAkB,UAAU,KAAyB,EAAE,KAA+B,EAAA;AAC9F,IAAA,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;AAC7C,CAAC;AAED,IAAI,CAAC,KAAK,GAAG,OAAO;;ACNpB,MAAM,GAAG,GAAkB,CAAC,KAAyB,KAAK,EAAE,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,MAAM,CAAC;AACxG,MAAM,MAAM,GAAkB,CAAC,KAAyB,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;;ACD/E,MAAM,UAAU,GAAkB,CAAC,KAAyB,EAAE,KAA+B,KAAI;IAC/F,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,OAAO,KAAK;;IAEd,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,OAAO,IAAI;;AAEb,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;AAE/B,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;AAE/B,IAAA,OAAO,KAAK,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC;AAC3E,CAAC;AAED,UAAU,CAAC,KAAK,GAAG,OAAO;;AChB1B,MAAM,QAAQ,GAAkB,CAAC,KAAyB,EAAE,KAA+B,KAAI;IAC7F,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,OAAO,IAAI;;IAEb,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,OAAO,KAAK;;IAEd,IAAI,KAAK,EAAE;AACT,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;AAE/B,QAAA,OAAO,KAAK,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE;;AAE/E,IAAA,OAAO,IAAI;AACb,CAAC;AAEM,MAAM,WAAW,GAAkB,CAAC,KAAyB,EAAE,KAA+B,KAAI;AACvG,IAAA,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;AAChC,CAAC;AACD,WAAW,CAAC,KAAK,GAAG,OAAO;AAC3B,QAAQ,CAAC,KAAK,GAAG,OAAO;;ACtBxB;AAaa,MAAA,gCAAgC,GAAsC;AACjF,IAAA,IAAI,EAAE,MAAM,IAAI;AAChB,IAAA,KAAK,EAAE,MAAM;AACb,IAAA,QAAQ,EAAE,GAAG;AACb,IAAA,EAAE,EAAE,EAAE;AACN,IAAA,KAAK,EAAE,KAAK;AACZ,IAAA,MAAM,EAAE,UAAU;AAClB,IAAA,QAAQ,EAAE,QAAQ;AAClB,IAAA,WAAW,EAAE,WAAW;AAExB,IAAA,GAAG,EAAE,EAAE;AACP,IAAA,IAAI,EAAE,KAAK;AACX,IAAA,EAAE,EAAE,MAAM;AACV,IAAA,GAAG,EAAE,QAAQ;AACb,IAAA,EAAE,EAAE,EAAE;AACN,IAAA,GAAG,EAAE,IAAI;;AAGE,MAAA,WAAW,GAAiC;AACvD,IAAA,MAAM,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,CAAC;AACjF,IAAA,MAAM,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;;AAG3D,MAAA,WAAW,GAAG;AACzB,IAAA,IAAI,EAAE,MAAM;AACZ,IAAA,KAAK,EAAE,SAAS;AAChB,IAAA,QAAQ,EAAE,KAAK;AAEf,IAAA,EAAE,EAAE,OAAO;AACX,IAAA,KAAK,EAAE,WAAW;AAClB,IAAA,MAAM,EAAE,aAAa;AACrB,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,WAAW,EAAE,kBAAkB;AAE/B,IAAA,GAAG,EAAE,GAAG;AACR,IAAA,IAAI,EAAE,IAAI;AACV,IAAA,EAAE,EAAE,GAAG;AACP,IAAA,GAAG,EAAE,IAAI;AACT,IAAA,EAAE,EAAE,GAAG;AACP,IAAA,GAAG,EAAE,IAAI;;;ACpDX;AAiCO,MAAM,mBAAmB,GAAG;AAC5B,MAAM,2BAA2B,GAAG;AACpC,MAAM,WAAW,GAAG;AAE3B;;;;;;;;;;;;;;;AAeG;AACH;;AAEG;AAEG,MAAO,YAAa,SAAQ,UAAU,CAAA;AAyB1C,IAAA,WAAA,CACS,QAA6B,EACpC,SAA0B,EACnB,MAA2B,EAAA;;AAElC,QAAA,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;QAJnB,IAAQ,CAAA,QAAA,GAAR,QAAQ;QAER,IAAM,CAAA,MAAA,GAAN,MAAM;QA1Bf,IAAgB,CAAA,gBAAA,GAA6C,EAAE;QAC/D,IAAgB,CAAA,gBAAA,GAAoB,EAAE;AAEtC;;;;;;;AAOG;QACH,IAAY,CAAA,YAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAkC,WAAW,CAAG;QAC5D,IAAqB,CAAA,qBAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAChB,WAAW,CACd;QACF,IAA4B,CAAA,4BAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACvB,gCAAgC,CACnC;QAEF,IAAU,CAAA,UAAA,GAAG,WAAW;QAUtB,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;;QAGzB,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CACtD,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,KAAK,WAAW,CACtD;AACD,QAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG;AAC5B,YAAA,GAAG,aAAa;AAChB,YAAA,CAAA,CAAA,qBAAA,EAAA,EACE,WAAW,EAAE,IAAI,CAAC,qBAAqB,EACvC,cAAc,EAAE,IAAI,CAAC,4BAA4B,EACjD,cAAc,EAAE,MAAA,MAAM,KAAA,IAAA,IAAN,MAAM,KAAN,MAAA,GAAA,MAAA,GAAA,MAAM,CAAE,YAAY,0CAAE,QAAQ,EAC9C,cAAc,EAAE,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,EAClD,aAAa,EAAE,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,EAChD,uBAAuB,EAAE,MAAM,KAAN,IAAA,IAAA,MAAM,uBAAN,MAAM,CAAE,uBAAuB,EACxD,mBAAmB,EAAE,MAAM,aAAN,MAAM,KAAA,MAAA,GAAA,MAAA,GAAN,MAAM,CAAE,8BAA8B,EAC3D,GAAG,EAAE,CAAC,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,EAAA;gBAEvB,GAAG;gBACH,IAAI,CAAC,YAAY,EAAE,CACA;SACvB;AAED,QAAA,MAAM,cAAc,GAAG,YAAW;YAChC,MAAM,qBAAqB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAChE,YAAA,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE;;gBAEpC,qBAAqB,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;oBAC5C,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAChC,wBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG;AAC5B,4BAAA;AACE,gCAAA,EAAE,EAAE,KAAK;gCACT,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI;gCACtC,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,KAAK;AACxC,gCAAA,QAAQ,EAAE,KAAK;AAChB,6BAAA;yBACF;;AAEL,iBAAC,CAAC;;AAEJ,YAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;gBACnD;;YAEF,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAChD,SAAC;AACD,QAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,gBAAgB,CACnB,2BAA2B,EAC3B,CAAC,EAAE,MAAM,EAA6C,KAAI;AACxD,YAAA,IACE,CAAC,MAAM;iBACN,OAAO,MAAM,KAAK,QAAQ;qBACxB,CAAC,MAAM,CAAC,gBAAgB;AACvB,wBAAA,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,EAClD;gBACA,IAAI,CAAC,cAAc,EAAE;gBACrB;;AAEF,YAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,gBAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;;AAEzB,YAAA,cAAc,EAAE;AAClB,SAAC,CACF;AACD,QAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,cAAc,CAAC;AACvD,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAe,KACtD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAC5B;;AAGH,IAAA,UAAU,CAAC,CAAW,EAAA;;;IAItB,YAAY,GAAA;AACV,QAAA,OAAO,IAAI;;AAGb,IAAA,UAAU,CAAC,MAA0B,EAAA;AACnC,QAAA,IAAI,MAAM,CAAC,gBAAgB,EAAE;AAC3B,YAAA,IAAI,CAAC,gBAAgB,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,MAAM,CAAC,gBAAgB,CAAE;;aACjD;AACL,YAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE;;;AAG5B,QAAA,IAAI,MAAM,CAAC,aAAa,EAAE;AACxB,YAAA,KAAK,IAAI,gBAAgB,IAAI,MAAM,CAAC,aAAa,EAAE;gBACjD,MAAM,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC;gBACtD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;oBAChD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,EAAE;;;AAGlD,gBAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;;gBAElE,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,GAAG,OAAO,CAAC,IAAI;;gBAElE,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,GAAG,OAAO,CAAC,IAAI;;;;AAK/D,QAAA,IAAI,MAAM,CAAC,UAAU,EAAE;AACrB,YAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU;;AAGrC;;;AAGG;AACH,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO;QACjC,IAAI,UAAU,EAAE;YACd,MAAM,OAAO,GAA6B,EAAE;AAE5C,YAAA,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE;;gBAE/B,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAC1C,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,CAChC;AACD,gBAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnB,oBAAA,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ;;;;YAIzB,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AACnC,gBAAA,IAAI,CAAC,YAAY,GAAG,OAAO;;;AAI/B,QAAA,IAAI,MAAM,CAAC,UAAU,EAAE;AACrB,YAAA,MAAM,gCAAgC,GAAG,MAAM,CAAC,OAAO,CACrD,MAAM,CAAC,UAAU,CAClB,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpE,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,WAAW,CACxC,gCAAgC,CACjC;;aACI;AACL,YAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE;;AAG5B,QAAA,IAAI,MAAM,CAAC,YAAY,EAAE;AACvB,YAAA,IAAI,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE;AACnC,gBAAA,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI;oBACjE,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;AAC3C,wBAAA,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC;;AAErC,iBAAC,CAAC;;;;IAKR,MAAM,WAAW,CAAC,CAA6B,EAAA;;QAC7C,MAAM,EAAE,GAAG,CAAA,EAAA,GAAA,CAAC,CAAC,MAAM,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAqB;AACxD,QAAA,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE;YACpB;;QAEF,CAAC,CAAC,cAAc,EAAE;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACb;;;QAIF,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE;AACrD,QAAA,MAAM,SAAS,GAAG,EAAE,CAAC,qBAAqB,EAAE;AAC5C,QAAA,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI;AAE1B,QAAA,MAAM,IAAI,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACL,CAAC,CAAC,MAAM,CAAA,EACR,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAC9B,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,EAC1B,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,EAC7C,WAAW,EAAE,IAAI,EACjB,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAClD,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAClC,YAAY,EAAE,IAAI,CAAC,iBAAiB,GACrC;AACD,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;;AAGrB,IAAA,eAAe,CACb,IAAkC,EAAA;QAElC,IAAI,UAAU,GAAG,QAAQ;QACzB,IAAI,CAAC,IAAI,EAAE;AACT,YAAA,OAAO,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;;;AAIxD,QAAA,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;YAC1B,UAAU,GAAG,IAAI;;;aAGZ,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE;YAClD,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAA2B,EAAE,SAAS,KAAI;AAC5D,gBAAA,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE;oBAC/B,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;;AAE7C,gBAAA,OAAO,CAAC;aACT,EAAE,EAAE,CAAC;;AAER,QAAA,OAAO,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;;AAGxD,IAAA,WAAW,CAAC,IAAS,EAAA;AACnB,QAAA,OAAO,CAAC,EAAE,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;;AAGhE;;AAEG;IACH,MAAM,cAAc,CAAC,WAA4B,EAAA;;AAE/C,QAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW;;AAGnC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;;AAG1C,IAAA,aAAa,CAAC,IAAiB,EAAA;AAC7B,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAA,MAAA,GAAJ,IAAI,GAAI,EAAE,CAAC;AACxC,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC;;AAG5C;;AAEG;IACH,MAAM,WAAW,CACf,UAAoD,EACpD,MAAkB,EAClB,OAAwB,EACxB,WAA4B,EAAA;QAE5B,MAAM,eAAe,GAAoB,EAAE;AAE3C;;AAEG;QACH,MAAM,YAAY,GAAkC,EAAE;AACtD,QAAA,OAAO,CAAC,OAAO,CAAC,KAAK,IAAG;AACtB,YAAA,MAAM,MAAM,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,KAAK,CAAE;YAC3B,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC;AAC1C,YAAA,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM;AAElC;;AAEG;YACH,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE;AACzC,gBAAA,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9B,gBAAA,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC;;AAG9B;;AAEG;YAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,SAAS,EAAE;AACzC,gBAAA,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC;AAC5B,gBAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI;;AAElC,SAAC,CAAC;AACF,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,WAAW,EAAE,YAAY,CAAC;;QAExE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACpE,UAAU;AACV,YAAA,aAAa,EAAE,WAAW;YAC1B,MAAM;YACN,WAAW;AACZ,SAAA,CAAC;QACF,IAAI,gBAAgB,EAAE;YACpB;;AAGF,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,mBAAmB,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;;QAG/E,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC;AACpD,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;AAC5B,YAAA,gBAAgB,EAAE,WAAW;YAC7B,MAAM;YACN,UAAU;AACX,SAAA,CAAC;;AAGJ,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE;QAC1B,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;;IAGhD,MAAM,YAAY,CAAC,gBAAiC,EAAA;QAClD,MAAM,UAAU,GAA6C,EAAE;;QAG/D,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAEjD,QAAA,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;;YAE9B,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrC,MAAM,eAAe,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACjD,UAAU,CAAC,IAAI,CAAC,GAAG;oBACjB,IAAI,EAAE,eAAe,CAAC,IAAI;oBAC1B,KAAK,EAAE,eAAe,CAAC,KAAK;iBAC7B;;;AAIL,QAAA,IAAI,CAAC,gBAAgB,GAAG,UAAU;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,EAAE;;AAElD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;QAEtE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAClE,UAAU,EAAE,IAAI,CAAC,gBAAgB;YACjC,MAAM;YACN,OAAO;YACP,WAAW,EAAE,IAAI,CAAC,gBAAgB;AACnC,SAAA,CAAC;QACF,IAAI,gBAAgB,EAAE;YACpB;;AAEF,QAAA,IAAI,CAAC,WAAW,CACd,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,OAAO,EACd,MAAM,CAAC,WAAW,CACnB;;AAGH;;AAEG;AACH,IAAA,YAAY,CACV,IAAgB,EAChB,WAA4B,EAC5B,YAA2C,EAAA;QAE3C,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;QAEzC,MAAM,OAAO,GAAkB,EAAE;;AAGjC,QAAA,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;;AAEzD,YAAA,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;;gBAE3B,IACE,IAAI,CAAC,aAAa,CAChB,WAAW,CAAC,IAAI,CAAC,EACjB,IAAI,EACJ,YAAY,CAAC,IAAI,CAAC,EAClB,IAAI,CAAC,QAAQ,CAAC,CACf,EACD;AACA,oBAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;;AAE5B,aAAC;;AAEH,QAAA,OAAO,OAAO;;IAGR,aAAa,CACnB,WAAyB,EACzB,IAAgB,EAChB,MAAsB,EACtB,QAAkB,EAAE,EAAA;;QAGpB,IAAI,wBAAwB,GAAG,CAAC;;QAEhC,IAAI,iBAAiB,GAAc,EAAE;;AAGrC,QAAA,KAAK,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE;;YAE7D,MAAM,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,IAAI,CAAC;;;AAIrE,YAAA,MAAM,KAAK,GAAG,MAAM,GAAG,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;;AAErE,YAAA,IAAI,UAAU,CAAC,QAAQ,KAAK,IAAI,EAAE;;gBAEhC,iBAAiB,GAAG,EAAE;;gBAEtB,IAAI,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE;oBACvC;;;AAGF,gBAAA,wBAAwB,EAAE;;;iBAGrB;;;AAGL,gBAAA,iBAAiB,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;AAE5D,gBAAA,IAAI,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;;AAE9C,oBAAA,IAAI,yBAAyB,CAAC,iBAAiB,CAAC,EAAE;;wBAEhD,iBAAiB,GAAG,EAAE;wBACtB;;;AAIF,oBAAA,wBAAwB,IAAI,iBAAiB,CAAC,MAAM;;oBAEpD,iBAAiB,GAAG,EAAE;;;AAG5B,SAAC;AACD,QAAA,OAAO,wBAAwB,KAAK,WAAW,CAAC,MAAM;;AAEzD;AACD;;;;;AAKG;AACH,SAAS,gBAAgB,CAAC,KAAa,EAAE,OAAgC,EAAA;IACvE,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;;AAEtC,IAAA,OAAO,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,KAAK,KAAK,CAAC;AAChF;AAEA;;;;AAIG;AACH,SAAS,yBAAyB,CAAC,cAAyB,EAAA;;AAE1D,IAAA,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC;AACvC;;ACtgBA;AACA,IAAI,SAAS,GAAG,iBAAiB;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,EAAE,OAAO,OAAO,KAAK,IAAI,QAAQ;AACjC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC;AAC9E;;ACzBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,GAAG,YAAY,CAAC,QAAQ,CAAC;;ACTtC;AACA,IAAIC,eAAa,GAAG,iBAAiB;AACrC,IAAIC,mBAAiB,GAAG,iBAAiB;AACzC,IAAIC,uBAAqB,GAAG,iBAAiB;AAC7C,IAAIC,qBAAmB,GAAG,iBAAiB;AAC3C,IAAIC,cAAY,GAAGH,mBAAiB,GAAGC,uBAAqB,GAAGC,qBAAmB;AAClF,IAAIE,YAAU,GAAG,gBAAgB;;AAEjC;AACA,IAAIC,OAAK,GAAG,SAAS;;AAErB;AACA,IAAI,YAAY,GAAG,MAAM,CAAC,GAAG,GAAGA,OAAK,GAAGN,eAAa,IAAII,cAAY,GAAGC,YAAU,GAAG,GAAG,CAAC;;AAEzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,MAAM,EAAE;AAC5B,EAAE,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;AAClC;;ACvBA;AACA,IAAI,aAAa,GAAG,iBAAiB;AACrC,IAAI,iBAAiB,GAAG,iBAAiB;AACzC,IAAI,qBAAqB,GAAG,iBAAiB;AAC7C,IAAI,mBAAmB,GAAG,iBAAiB;AAC3C,IAAI,YAAY,GAAG,iBAAiB,GAAG,qBAAqB,GAAG,mBAAmB;AAClF,IAAI,UAAU,GAAG,gBAAgB;;AAEjC;AACA,IAAI,QAAQ,GAAG,GAAG,GAAG,aAAa,GAAG,GAAG;AACxC,IAAI,OAAO,GAAG,GAAG,GAAG,YAAY,GAAG,GAAG;AACtC,IAAI,MAAM,GAAG,0BAA0B;AACvC,IAAI,UAAU,GAAG,KAAK,GAAG,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG;AACrD,IAAI,WAAW,GAAG,IAAI,GAAG,aAAa,GAAG,GAAG;AAC5C,IAAI,UAAU,GAAG,iCAAiC;AAClD,IAAI,UAAU,GAAG,oCAAoC;AACrD,IAAI,KAAK,GAAG,SAAS;;AAErB;AACA,IAAI,QAAQ,GAAG,UAAU,GAAG,GAAG;AAC/B,IAAI,QAAQ,GAAG,GAAG,GAAG,UAAU,GAAG,IAAI;AACtC,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ,GAAG,IAAI;AAC1H,IAAI,KAAK,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS;AAC3C,IAAI,QAAQ,GAAG,KAAK,GAAG,CAAC,WAAW,GAAG,OAAO,GAAG,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;;AAE/G;AACA,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,IAAI,GAAG,QAAQ,GAAG,KAAK,EAAE,GAAG,CAAC;;AAE9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,MAAM,EAAE;AAC7B,EAAE,IAAI,MAAM,GAAG,SAAS,CAAC,SAAS,GAAG,CAAC;AACtC,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACjC,IAAI,EAAE,MAAM;AACZ;AACA,EAAE,OAAO,MAAM;AACf;;ACrCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,MAAM,EAAE;AAC5B,EAAE,OAAO,UAAU,CAAC,MAAM;AAC1B,MAAM,WAAW,CAAC,MAAM;AACxB,MAAM,SAAS,CAAC,MAAM,CAAC;AACvB;;ACTA;AACA,IAAI,MAAM,GAAG,cAAc;AAC3B,IAAI,MAAM,GAAG,cAAc;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI,CAAC,UAAU,EAAE;AAC1B,EAAE,IAAI,UAAU,IAAI,IAAI,EAAE;AAC1B,IAAI,OAAO,CAAC;AACZ;AACA,EAAE,IAAI,WAAW,CAAC,UAAU,CAAC,EAAE;AAC/B,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,MAAM;AAC5E;AACA,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC;AAC9B,EAAE,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,EAAE;AACtC,IAAI,OAAO,UAAU,CAAC,IAAI;AAC1B;AACA,EAAE,OAAO,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM;AACpC;;ACtCM,SAAU,gBAAgB,CAC9B,OAAiB,EACjB,MAAkB,EAClB,cAAoC,EAAE,EAAA;;IAGtC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;;AAE5C,QAAA,OAAO,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;;;AAG1C;;;AAGG;IACH,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;AAC3B,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC;AACvB,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC;AACvB,QAAA,KAAK,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AACrD,YAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;AACrB,gBAAA,IAAI,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;AACjC,oBAAA,OAAO,CAAC;;;AAGZ,YAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;AACrB,gBAAA,IAAI,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;AACjC,oBAAA,OAAO,CAAC;;;AAGZ;;;AAGG;AACH,YAAA,MAAM,MAAM,GAAG,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAG,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;YACxC,IAAI,MAAM,EAAE;AACV,gBAAA,OAAO,MAAM;;;AAGjB,QAAA,OAAO,CAAC;AACV,KAAC,CAAC;AACJ;SAEgB,kBAAkB,CAAmC,IAAgB,EAAE,CAAW,EAAE,CAAW,EAAA;IAC7G,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAA,IAAA,IAAD,CAAC,KAAA,MAAA,GAAA,MAAA,GAAD,CAAC,CAAG,IAAI,CAAC;IACjE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAA,IAAA,IAAD,CAAC,KAAA,MAAA,GAAA,MAAA,GAAD,CAAC,CAAG,IAAI,CAAC;AACjE,IAAA,MAAM,EAAE,GAAG,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAA,MAAA,GAAA,MAAA,GAAJ,IAAI,CAAE,QAAQ,EAAA,CAAG,WAAW,EAAE;AACzC,IAAA,MAAM,EAAE,GAAG,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAA,MAAA,GAAA,MAAA,GAAJ,IAAI,CAAE,QAAQ,EAAA,CAAG,WAAW,EAAE;AAEzC,IAAA,IAAI,EAAE,KAAK,EAAE,EAAE;AACb,QAAA,OAAO,CAAC;;AAEV,IAAA,IAAI,EAAE,GAAG,EAAE,EAAE;AACX,QAAA,OAAO,CAAC;;IAEV,OAAO,EAAE;AACX;AAEM,SAAU,eAAe,CAAC,GAAoB,EAAA;AAClD,IAAA,OAAO,CAAC,IAAgB,EAAE,CAAW,EAAE,CAAW,KAAY;QAC5D,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,KAAC;AACH;AAEM,SAAU,YAAY,CAAC,YAAmB,EAAA;IAC9C,QAAQ,YAAY;AAClB,QAAA,KAAK,SAAS;AACZ,YAAA,OAAO,KAAK;AACd,QAAA,KAAK,KAAK;AACR,YAAA,OAAO,MAAM;AACf,QAAA,KAAK,MAAM;AACT,YAAA,OAAO,SAAS;;AAEtB;AAGgB,SAAA,WAAW,CAAC,MAA0C,EAAE,KAAY,EAAA;;AAClF,IAAA,MAAM,OAAO,GACX,CAAA,CAAA,EAAA,GAAA,MAAM,KAAN,IAAA,IAAA,MAAM,KAAN,MAAA,GAAA,MAAA,GAAA,MAAM,CAAE,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,MAAI,kBAAkB,KAAA,IAAA,IAAlB,kBAAkB,KAAA,MAAA,GAAA,MAAA,GAAlB,kBAAkB,CAAE,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAA;AACrF,IAAA,IAAI,KAAK,IAAI,KAAK,EAAE;AAClB,QAAA,OAAO,OAAO;;AAEhB,IAAA,IAAI,KAAK,IAAI,MAAM,EAAE;AACnB,QAAA,OAAO,eAAe,CAAC,OAAO,CAAC;;AAEjC,IAAA,OAAO,SAAS;AAClB;;ACvEA;;;;;;;;AAQG;AAEG,MAAO,aAAc,SAAQ,UAAU,CAAA;AAoB3C,IAAA,WAAA,CACS,QAA6B,EACpC,SAA0B,EAC1B,MAAsB,EAAA;AAEtB,QAAA,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;QAJnB,IAAQ,CAAA,QAAA,GAAR,QAAQ;AAfjB;;AAEG;QACH,IAAc,CAAA,cAAA,GAAwB,IAAI;AAE1C;;AAEG;QACH,IAAY,CAAA,YAAA,GAAG,QAAQ,CACrB,CAAC,KAAoB,EAAE,UAAiC,EAAE,oBAA8B,KACtF,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,EAAE,oBAAoB,CAAC,EAC1D,EAAE,CACH;AASC,QAAA,MAAM,SAAS,GAAG,CAAC,GAAmB,KAAI;;YACxC,IAAI,GAAG,EAAE;gBACP,MAAM,WAAW,GAAyB,EAAE;gBAC5C,MAAM,KAAK,GAAiB,EAAE;gBAC9B,CAAA,EAAA,GAAA,GAAG,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,OAAO,CAAC,GAAG,IAAG;AACzB,oBAAA,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC;oBACnD,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK;AAC7B,iBAAC,CAAC;AAEF,gBAAA,IAAI,GAAG,CAAC,QAAQ,EAAE;oBAChB,IAAI,CAAC,OAAO,GACP,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CAAC,OAAO,CAAA,EACZ,KAAK,CACT;oBACD,IAAI,CAAC,WAAW,GACX,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CAAC,WAAW,CAAA,EAChB,WAAW,CACf;;qBACI;;AAEL,oBAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,oBAAA,IAAI,CAAC,WAAW,GAAG,WAAW;;;AAGpC,SAAC;QAED,SAAS,CAAC,MAAM,CAAC;QAEjB,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,CAAC,EAAE,MAAM,EAAE,KAAI;YAC3D,MAAM,GAAG,MAAM;YACf,SAAS,CAAC,MAAM,CAAC;YACjB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC;AACnD,SAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,EAC3C,MAAM,GACP,KAAI;;AACH,YAAA,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM;AAC/B,YAAA,IAAI,MAAM,CAAC,QAAQ,EAAE;AACnB,gBAAA,MAAM,CAAC,IAAI,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACN,MAAM,CAAA,EAAA,EACT,KAAK,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,CAAC,IAAI,CAAC,GACnC;;AAEL,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,EACxC,MAAM,EAAE,EAAE,IAAI,EAAE,GACjB,KAAI;;YAEH,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,WAAW,EAAE;AACtC,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AACpF,gBAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;oBAC1B;;gBAEF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC;;AAErD,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,EACxC,MAAM,EAAE,EAAE,KAAK,EAAE,GAClB,KAAI;;YAEH,IAAI,MAAM,EAAE;gBACV;;YAGF,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,EAAE;YAClD,MAAM,WAAW,GAAyB,EAAE;AAE5C,YAAA,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;AACtB,gBAAA,MAAM,GAAG,GAAG,WAAW,CACrB,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,EAC9B,KAAK,CAAC,IAAI,CAAC,CACZ;AACD,gBAAA,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG;;;AAIzB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,IAAI,WAAW;AACzC,SAAC,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,CAAC,KAAI;;AAC/C,YAAA,IAAI,CAAC,CAAC,gBAAgB,EAAE;gBACtB;;AAGF,YAAA,IAAI,EAAC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,CAAC,CAAC,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,QAAQ,CAAA,EAAE;gBAC/B;;AAGF,YAAA,IAAI,CAAC,WAAW,CACd,CAAC,CAAC,MAAM,CAAC,MAAM,EACf,MAAA,CAAA,EAAA,GAAA,CAAC,CAAC,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,QAAQ,CAClC;AACH,SAAC,CAAC;;AAGJ;;AAEG;AACH,IAAA,YAAY,CAAC,KAAoB,EAAE,WAAkC,EAAE,oBAA8B,EAAA;AACnG,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;;AAExB,YAAA,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CACjC,IAAI,OAAO,CAAO,OAAO,IAAG;AAC1B,gBAAA,IAAI,CAAC,cAAc,GAAG,OAAO;aAC9B,CAAC,CACH;;QAEH,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,EAAE,oBAAoB,CAAC;;AAI7D;;;AAGG;IACH,WAAW,CAAC,MAAqB,EAAE,QAAiB,EAAA;;AAClD,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI;AAC9B,QAAA,IAAI,KAAK,GAAU,YAAY,CAAC,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,UAAU,CAAC,CAAC;AAC3D,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;AAC3E,QAAA,IAAI,WAAW,CAAC,gBAAgB,EAAE;YAChC;;AAEF,QAAA,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK;;AAGhC,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AACvD,YAAA,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,MAAM;YACjC,KAAK;YACL,QAAQ;AACT,SAAA,CAAC;AACF,QAAA,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;YACrC;;AAEF,QAAA,MAAM,GAAG,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC;QAEtF,IAAI,gBAAgB,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE;YACpD,MAAM,OAAO,GAAiB,EAAE;YAChC,MAAM,WAAW,GAAyB,EAAE;YAE5C,IAAI,CAAC,OAAO,GACP,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CAAC,OAAO,CAAA,EACZ,OAAO,CACX;;YAED,IAAI,CAAC,WAAW,GACX,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CAAC,WAAW,CAAA,EAChB,WAAW,CACf;AAED,YAAA,IAAI,UAAU,IAAI,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,SAAS,EAAE;AACrE,gBAAA,OAAO,OAAO,CAAC,UAAU,CAAC;AAC1B,gBAAA,OAAO,WAAW,CAAC,UAAU,CAAC;;iBACzB;AACL,gBAAA,OAAO,CAAC,UAAU,CAAC,GAAG,KAAK;AAC3B,gBAAA,WAAW,CAAC,UAAU,CAAC,GAAG,GAAG;;;aAE1B;YACL,IAAI,KAAK,EAAE;;gBAET,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,UAAU,GAAG,KAAK,EAAE;gBACtC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,UAAU,GAAG,GAAG,EAAE;;iBACnC;AACE,gBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,IAAA,GAAA,OAAA,EAAA,CAAA,UAAU,CAAC;AAC1B,gBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,WAAW,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,IAAA,GAAA,OAAA,EAAA,CAAA,UAAU,CAAC;;;QAIzC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC;;AAGnD,IAAA,UAAU,CACR,KAAoB,EACpB,UAAiC,EACjC,oBAA8B,EAAA;;QAE9B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,oBAAoB,CAAC;AAC7D,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAI;AACvB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;AAG5B;;;;;;;AAOG;IACH,IAAI,CACF,OAAsB,EACtB,WAAkC,EAClC,QAAyB,QAAQ,EACjC,oBAAoB,GAAG,KAAK,EAAA;;AAG5B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;AACtC,YAAA,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;AACtB,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;;gBAErD,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;;gBAE/C,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC;;gBAEvD,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACzE,gBAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,mCAAmC,CAAC,IAAI,EAAE,aAAa,EAAE,UAAU,CAAC;AAC7F,gBAAA,YAAY,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;;;aAEtE;AACL,YAAA,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;AACtB,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;;gBAErD,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;;gBAE/C,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC;AAEvD,gBAAA,MAAM,aAAa,GAAG,gBAAgB,CACpC,CAAC,GAAG,UAAU,CAAC,EACf,MAAM,EACN,WAAW,CACZ;;gBAGD,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;gBACjD,YAAY,CAAC,OAAO,CAAC;AACnB,oBAAA,UAAU,EAAE,aAAa;AACzB,oBAAA,MAAM,EAAE,CAAC,GAAG,MAAM,CAAC;AACpB,iBAAA,CAAC;;gBAEF,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;gBAChD,IAAI,CAAC,oBAAoB,EAAE;oBACzB,IAAI,CAAC,SAAS,CAAC;AACZ,yBAAA,mCAAmC,CAAC,IAAI,EAAE,QAAQ,EAAE,SAAS,CAAC;;;;;AAKvE,QAAA,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC3B,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE;AACnD,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC;;AAEjC;;ACvSD;AACgB,SAAA,UAAU,CAAC,MAAc,EAAE,MAAkB,EAAA;AAC3D,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;AAC5B,IAAA,MAAM,aAAa,GAAG,KAAK,CAAC,uBAAuB,CAAC;IACpD,MAAM,OAAO,GAA4B,EAAE;AAC3C,IAAA,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC;AAClB,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM;AAC3B,IAAA,OAAO,CAAC,GAAG,KAAK,EAAE;AAChB,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC;AAC9B,QAAA,IAAI,UAAU,CAAC,YAAY,CAAC,EAAE;AAC5B,YAAA,MAAM,YAAY,GAAG,YAAY,CAAC,uBAAuB,CAAC;AAC1D,YAAA,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,aAAa,GAAG,GAAG,CAAC,EAAE;gBACzE;;AAEF,YAAA,YAAY,CAAC,cAAc,CAAC,GAAG,KAAK;;AAEtC,QAAA,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI;;AAErB,IAAA,KAAK,CAAC,cAAc,CAAC,GAAG,KAAK;IAC7B,OAAO,EAAE,OAAO,EAAE;AACpB;AAEA;;;;;;AAMG;SACa,QAAQ,CAAC,MAAc,EAAE,MAAkB,EAAE,eAAyB,EAAA;AACpF,IAAA,MAAM,aAAa,GAAG,eAAe,CAAC,MAAM,CAAC;AAC7C,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,aAAa,CAAC;IACnC,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAChE,MAAM,OAAO,GAA4B,EAAE;;IAG3C,IAAI,CAAC,YAAY,EAAE;QACjB,OAAO,EAAE,OAAO,EAAE;;IAGpB,MAAM,UAAU,GAAa,EAAE;AAC/B,IAAA,KAAK,CAAC,cAAc,CAAC,GAAG,IAAI;AAC5B,IAAA,IAAI,CAAC,GAAG,aAAa,GAAG,CAAC;AACzB,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM;IAC3B,IAAI,cAAc,GAAG,CAAC;;AAGtB,IAAA,OAAO,CAAC,GAAG,KAAK,EAAE;AAChB,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC;AAC9B,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC;;QAExC,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE;gBACnD;;iBACK,IAAI,CAAC,cAAc,EAAE;;AAE1B,gBAAA,cAAc,GAAG,YAAY,CAAC,WAAW,CAAC;;;;AAI9C,QAAA,IAAI,CAAC,cAAc,KAAK,OAAO,IAAI,cAAc,KAAK,YAAY,CAAC,WAAW,CAAC,CAAC,EAAE;AAChF,YAAA,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;AAClB,YAAA,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;;AAEpB,QAAA,CAAC,EAAE;;AAEL,IAAA,MAAM,MAAM,GAGR;QACF,OAAO;KACR;AACD,IAAA,IAAI,UAAU,CAAC,MAAM,EAAE;AACrB,QAAA,MAAM,KAAK,GAAG,CAAC,GAAG,eAAe,CAAC;AAClC,QAAA,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC;AAC1C,QAAA,MAAM,CAAC,KAAK,GAAG,KAAK;;AAEtB,IAAA,OAAO,MAAM;AACf;;AChFO,MAAM,gBAAgB,GAAG,UAAU;AAE1C;;;;;AAKG;SACa,8BAA8B,CAAC,eAAwB,EAAE,aAAqC,EAAE,cAAuC,EAAA;IACrJ,MAAM,wBAAwB,GAAY,EAAE;AAC5C;;AAEG;AACH,IAAA,KAAK,IAAI,IAAI,IAAI,eAAe,EAAE;AAChC,QAAA,IAAI,IAAI,KAAK,gBAAgB,EAAE;YAC7B;;AAEF,QAAA,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC;QACnC,MAAM,QAAQ,GAAkB,EAAE;AAElC,QAAA,KAAK,IAAI,YAAY,IAAI,KAAK,EAAE;AAC9B;;;AAGG;AACH,YAAA,IAAI,kBAAkB,GAAG,aAAa,CAAC,YAAY,CAAC;YACpD,IAAI,cAAc,EAAE;AAClB,gBAAA,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;;AAGzD;;;AAGG;AACH,YAAA,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE;AACvB,gBAAA,QAAQ,CAAC,kBAAkB,CAAC,GAAG,IAAI;AACnC;;AAEG;gBACH,IAAI,kBAAkB,KAAK,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE;AACrD,oBAAA,wBAAwB,CAAC,IAAI,CAAC,GAAG,QAAQ;;;;;AAKjD,IAAA,OAAO,wBAAwB;AACjC;;ACGM,MAAO,iBAAkB,SAAQ,UAAU,CAAA;IAG/C,QAAQ,CACN,OAAsB,iBAAiB,EAAA;AAEvC,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK;;IAG/C,WACE,CAAA,QAA6B,EAC7B,SAA0B,EAAA;AAE1B,QAAA,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;;;AAIpB,IAAA,OAAO,CAAC,CAAqC,EAAA;QACnD,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAC9B,CAAC,CAAC,cAAc,EAAE;;;;IAKd,QAAQ,CAAC,EAAE,YAAY,EAAiB,EAAA;QAC9C,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS,CAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAClC;AACD,QAAA,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC;QAEjE,IAAI,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,YAAY,CAAC;QAClD,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,UAAU,EAAE;YACf,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,QAAQ,CACjC,YAAY,EACZ,MAAM,EACN,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAC7B;AACD,YAAA,UAAU,GAAQ,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,UAAU,CAAK,EAAA,OAAO,CAAE;YAC1C,IAAI,KAAK,EAAE;gBACT,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;;;aAE7B;YACL,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC;AACzC,YAAA,UAAU,GAAQ,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,UAAU,CAAK,EAAA,OAAO,CAAE;AAC1C,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;;QAG5B,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC;QACrC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,gBAAgB,CAAC;;AAGhD,IAAA,iBAAiB,CAAC,IAAsB,EAAA;;QAE9C,IAAI,IAAI,aAAJ,IAAI,KAAA,MAAA,GAAA,MAAA,GAAJ,IAAI,CAAE,MAAM,EAAE;YAChB,IAAI,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,GAAG,IAAI;AACnC,YAAA,OAAO,IAAI;;AAEb,QAAA,OAAO,KAAK;;IAGN,UAAU,CAAC,EAAE,OAAO,EAAoB,EAAA;AAC9C,QAAA,KAAK,IAAI,IAAI,IAAI,WAAW,EAAE;YAC5B,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE;gBACzC;;;;;AAME,IAAA,MAAM,CAAC,CAA4C,EAAA;QACzD,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM;AAC7B,QAAA,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC;QAC7B,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS,CAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAClC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC;QAC1C,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI,GAAG,EAAE;QAC1B,MAAM,GAAG,GAAG,MAAM,GAAG,EAAE,GAAG,IAAI;AAC9B,QAAA,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YACnB,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9B,YAAA,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC;YACjC,IAAI,OAAO,EAAE;gBACX,CAAC,CAAC,cAAc,EAAE;gBAClB;;;;IAKE,kBAAkB,CAAC,OAAgC,EAAE,IAAY,EAAA;;AAEvE,QAAA,IAAI,IAAI,KAAK,mBAAmB,EAAE;YAChC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC;AAC5C,YAAA,KAAK,IAAI,KAAK,IAAI,OAAO,EAAE;AACzB,gBAAA,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;AAC/C,oBAAA,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK;;;;;IAMtB,gBAAgB,GAAA;AACtB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC;QACtE,OAAO,CAAC,EAAC,aAAa,KAAA,IAAA,IAAb,aAAa,KAAA,MAAA,GAAA,MAAA,GAAb,aAAa,CAAE,cAAc,CAAA;;AAGxC;;;AAGG;AACK,IAAA,cAAc,CAAC,OAAyB,EAAA;;AAC9C;;;AAGG;AACH,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;QAC7B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,SAAS,CACvD,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EACnB,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,EACvB,IAAI,CACL;AACD,QAAA,MAAM,QAAQ,GACZ,MAAA,CAAA,MAAA,CAAA,EAAA,YAAY,EACT,EAAA,OAAO,CACX;AACD;;;AAGG;QACH,MAAM,EACJ,gBAAgB,EAChB,KAAK,EACL,OAAO,EACP,cAAc,GACf,GAAG,cAAc,CAAC,MAAM,EAAE,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,KAAK,KAAI,EAAE,EAAE,QAAQ,CAAC;QAE/D,MAAM,cAAc,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,MAAA,GAAA,MAAA,GAAA,OAAO,CAAE,kBAAkB;;QAGlD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CACzB,gBAAgB,EAChB,iBAAiB,EACjB,IAAI,CAAC,QAAQ,CAAC,eAAe,EAC7B,EAAE,KAAK,EAAE,cAAc,EAAE,EACzB,IAAI,CACL;AACD,QAAA,IAAI,CAAC,aAAa,CAChB,OAAO,EACP,aAAa,KAAA,IAAA,IAAb,aAAa,KAAA,MAAA,GAAb,aAAa,GAAI,EAAE,EACnB,cAAc,CACf;;AAGH;;;;AAIG;AACK,IAAA,SAAS,CAAC,IAA0B,EAAA;;QAC1C,IAAI,iBAAiB,GAAoC,EAAE;QAC3D,IAAI,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,0CAAE,wBAAwB,MAAK,KAAK,EAAE;AACpD,YAAA,IAAI,EAAE,YAAY,EAAE,GAAG,SAAS,CAC9B,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EACjC,IAAI,CACL;YACD,iBAAiB,GAAG,YAAY;;AAElC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACtD,QAAA,MAAM,OAAO,GACR,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,GAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,EAAE,EAChC,EAAA,EAAA,YAAY,EAAE,iBAAiB,GAChC;QACD,MAAM,EACJ,gBAAgB,EAChB,KAAK,EACL,OAAO,EACP,cAAc,GACf,GAAG,cAAc,CAAC,MAAM,EAAE,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,KAAK,KAAI,EAAE,EAAE,OAAO,CAAC;AAC9D,QAAA,IAAI,CAAC,MAAM,GAAG,gBAAgB;QAC9B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,cAAc,CAAC;;AAG7C;;AAEG;AACH,IAAA,WAAW,CAAC,OAAwB,EAAA;;;QAElC,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;;AAEtB,QAAA,IAAI,EAAC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,MAAM,CAAA,EAAE;YAChC,IAAI,CAAC,aAAa,EAAE;YACpB;;;AAGF,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;QAC7B,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS,CAC1B,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EACnB,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CACxB;AACD,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,YAAA,IAAI,CAAC,cAAc,CAAM,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,EAAG;;;AAGrC,QAAA,KAAK,IAAI,CAAC,IAAI,WAAW,EAAE;AACzB,YAAA,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC/D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;gBACtC;;;;;QAMJ,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,EAAE,MAAM,EAAE,KAAI;;YACtD,IAAI,EAAE,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,MAAM,MAAI,MAAA,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,MAAA,GAAA,MAAA,GAAN,MAAM,CAAE,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,MAAM,CAAA,CAAC,EAAE;gBAC5D;;;AAGF,YAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;gBAC3B;;AAEF,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AACxB,SAAC,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,MAAM,EAAE,KAAI;AACvD,YAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;AACzB,SAAC,CAAC;AAEF;;;AAGG;QACH,IAAI,CAAC,gBAAgB,CACnB,eAAe,EACf,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,EAAE,KACnC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,WAAW,CAAC,CAChD;AACD;;;AAGG;AACH,QAAA,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,MAAK;;AAC9C,YAAA,IAAI,EAAC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,MAAM,CAAA,EAAE;gBAChC;;AAEF,YAAA,IAAI,CAAC,cAAc,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAM,IAAI,CAAC,OAAO,EAAG;AAC1C,SAAC,CAAC;AAEF;;;AAGG;AACH,QAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC9D;;AAEG;AACH,QAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAE7D;;AAEG;AACH,QAAA,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;;;IAIzE,aAAa,GAAA;;AAEX,QAAA,WAAW,CAAC,OAAO,CAAC,CAAC,IAAG;AACtB,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;YAChD,IAAI,OAAO,GAAG,KAAK;AACnB,YAAA,IAAI,CAAC,OAAO,CAAC,CAAC,IAAG;AACf,gBAAA,IAAI,gBAAgB,CAAC,CAAC,CAAC,EAAE;AACvB,oBAAA,OAAO,CAAC,CAAC,mBAAmB,CAAC;oBAC7B,OAAO,GAAG,IAAI;;AAElB,aAAC,CAAC;;YAEF,IAAI,OAAO,EAAE;gBACX,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;;AAE1C,SAAC,CAAC;;AAEF,QAAA,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,SAAS,CACzC,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EACjC,IAAI,CACL;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CACzB,MAAM,EACN,iBAAiB,EACjB,IAAI,CAAC,QAAQ,CAAC,eAAe,EAC7B,SAAS,EACT,IAAI,CACL;QACD,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,EAAE,aAAa,CAAC;;AAGjD,IAAA,aAAa,CACnB,YAA8B,GAAA,EAAE,EAChC,aAAwC,GAAA,EAAE,EAC1C,cAAuC,EAAA;;AAGvC,QAAA,MAAM,wBAAwB,GAAG,8BAA8B,CAC7D,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,EAC9B,aAAa,EACb,cAAc,CACf;AACD,QAAA,KAAK,IAAI,IAAI,IAAI,wBAAwB,EAAE;AACzC,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;;;;QAMhE,IAAI,CAAC,QAAQ,CAAC,UAAU,mBAAM,YAAY,CAAA,EAAI,gBAAgB,CAAC;;AAElE;;ACnXD,MAAM,iBAAiB,GAAG,mBAAmB;MAEhC,kBAAkB,CAAA;AAA/B,IAAA,WAAA,GAAA;QAGU,IAAM,CAAA,MAAA,GAAG,CAAC;;IAElB,gBAAgB,CAAC,CAAa,EAAE,MAA0B,EAAA;QACxD,IAAI,CAAC,MAAM,EAAE;YACX;;QAEF,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;QACjD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,CAAC;AACrD,QAAA,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;;AAGvC,IAAA,UAAU,CAAC,GAAW,EAAE,iBAAyB,EAAE,SAAS,GAAG,YAAY,EAAA;AACzE,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB;;QAEF,MAAM,YAAY,GAAG,EAAE;;;AAGvB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,YAAY,EAAE,iBAAiB,GAAG,CAAC,CAAC;AAErE,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,GAAG,CAAA,EAAG,SAAS,CAAA,CAAA,EAAI,SAAS,CAAA,GAAA,CAAK;AAClE,QAAA,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;AAC/B,YAAA,KAAK,EAAE,SAAS;AAChB,YAAA,MAAM,EAAE,SAAS;AAClB,SAAA,CAAC;;AAGJ,IAAA,KAAK,CAAC,CAAa,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAKxD,EAAE,MAAuB,MAAM,EAAA;AAC9B,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC;AACvC,QAAA,MAAM,mBAAmB,GAAG,QAAQ,CAAC,qBAAqB,EAAE;QAC5D,IAAI,mBAAmB,EAAE;AACvB,YAAA,IAAI,CAAC,MAAM,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC;;AAExD,QAAA,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,MAAM,CAAC;;AAGlC,IAAA,IAAI,CAAC,MAAe,EAAA;;AAClB,QAAA,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC;AAC1C,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI;;AAE5B,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC;AACf,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,EAAE;AAC3B,QAAA,IAAI,CAAC,YAAY,GAAG,SAAS;;AAG/B,IAAA,WAAW,CAAC,GAAW,EAAE,IAAY,EAAE,SAAS,GAAG,YAAY,EAAA;AAC7D,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB;;;AAGF,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC;;;QAGlC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,CAAA,EAAG,SAAS,CAAA,CAAA,EAAI,GAAG,CAAA,GAAA,CAAK;AACvD,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK;;IAG7B,MAAM,GAAA;AACJ,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACvD,QAAA,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC;AACnC,QAAA,EAAE,CAAC,MAAM,GAAG,IAAI;AAChB,QAAA,OAAO,EAAE;;AAEZ;;AC5ED;;AAEG;AAWH,MAAM,YAAY,GAAG,eAAe;AACpC,MAAM,IAAI,GAAG,qBAAqB;AAClC,MAAM,QAAQ,GAAG,eAAe;AAChC,MAAM,eAAe,GAAG,qBAAqB;AAE7C;AACA,MAAM,UAAU,GAAG,iBAAiB;AA4B9B,MAAO,gBAAiB,SAAQ,UAAU,CAAA;IAM9C,WAAmB,CAAA,QAA6B,EAAS,SAA0B,EAAA;AACjF,QAAA,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;QADT,IAAQ,CAAA,QAAA,GAAR,QAAQ;QAA8B,IAAS,CAAA,SAAA,GAAT,SAAS;AAL1D,QAAA,IAAA,CAAA,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAa,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACzD,IAAc,CAAA,cAAA,GAAsB,IAAI;QACxC,IAAQ,CAAA,QAAA,GAA+B,IAAI;QAEhC,IAAkB,CAAA,kBAAA,GAAuB,EAAE;AAG5D,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,kBAAkB,EAAE;QACvC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;AAC3C,QAAA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC;;AAG1C,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,GAAG;AACtC,YAAA,MAAM,EAAE,QAAQ;YAChB,QAAQ,EAAE,CAAC,CAAa,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;SAChD;AACD,QAAA,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,GAAG;AACnC,YAAA,MAAM,EAAE,QAAQ;YAChB,QAAQ,EAAE,CAAC,CAAa,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;SAC/C;AAED,QAAA,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,GAAG;AACrC,YAAA,MAAM,EAAE,QAAQ;YAChB,QAAQ,EAAE,CAAC,CAAa,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SAC1C;AAED,QAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;;AAG7E,IAAA,SAAS,CAAC,EAAE,KAAK,EAAE,IAAI,EAAyB,EAAA;AAC9C,QAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;YAC1B;;AAEF,QAAA,MAAM,EAAE,gBAAgB,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC;;QAEtE,IAAI,gBAAgB,EAAE;YACpB;;QAEF,IAAI,CAAC,UAAU,EAAE;QACjB,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,kBAAkB;QAClE,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,CAAC,QAAQ,CAAC;QACrE,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,QAAQ,CAAC;QAE5D,MAAM,MAAM,GAAI,KAAK,CAAC,MAAsB,CAAC,OAAO,CAAC,eAAe,CAAC;QACrE,MAAM,QAAQ,GAAI,KAAK,CAAC,MAAsB,CAAC,OAAO,CAAC,wBAAwB,CAAC;AAChF,QAAA,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE;YACxB;;;AAIF,QAAA,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,YAAY,EAAE;YAC/D;;AAGF,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,OAAO,CAAC;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE;AACtD,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,qBAAqB,EAAE;QAC7C,MAAM,SAAS,GAAG,iBAAiB,CACjC,IAAI,EACJ,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvE,IAAI,CAAC,cAAc,GAAG;YACpB,QAAQ,EAAE,KAAK,CAAC,CAAC;YACjB,SAAS;YACT,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM,EAAE,IAAI,CAAC,QAAQ;YACrB,IAAI;SACL;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC;QACjD,SAAS,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,QAAQ,CAAC;AAClE,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACnB,IAAI,CAAC,QAAQ,CACb,EAAA,IAAI,CAAC,cAAc,EACtB;;AAGJ,IAAA,MAAM,CAAC,CAAa,EAAA;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB;;AAGF,QAAA,MAAM,QAAQ,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpE,IAAI,CAAC,QAAQ,EAAE;YACb;;AAEF,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ;AAC1C,QAAA,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE;YAC9B,MAAM,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;AACvF,YAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5D,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;;AAGjD,YAAA,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE;gBACrD;;AAEF,YAAA,IAAI,CAAC,OAAO,CAAC,WAAW,CACtB,KAAK,CAAC,GAAG,GAAG,QAAQ,CAAC,YAAY,EACjC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CACxB;;;AAIL,IAAA,IAAI,CAAC,CAAa,EAAA;QAChB,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;;AAEhC,QAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;;AAElB,IAAA,UAAU,CAAC,CAAa,EAAA;QACtB,IAAI,CAAC,UAAU,EAAE;;AAEnB,IAAA,SAAS,CAAC,CAAa,EAAA;;QAErB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE;YACxC,IAAI,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;AAC/F,YAAA,IAAI,WAAW,GAAG,CAAC,EAAE;gBACnB,WAAW,GAAG,CAAC;;AAEjB,YAAA,MAAM,WAAW,GAAG,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,CAAC;AAE5E,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK;YACpE,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;;YAGxC,MAAM,EAAE,gBAAgB,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,eAAe,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACzE,IAAI,CAAC,cAAc,KACtB,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,SAAS,EAC5C,WAAW,EACX,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAA,CAAA,CAC/E;YACF,IAAI,CAAC,QAAQ,EAAE;AACb,gBAAA,MAAM,SAAS,GAAG,CAAC,GAAG,QAAQ,CAAC;;AAE/B,gBAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC;AAC1E,gBAAA,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC;AACpD,gBAAA,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC5B,gBAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,mCAAmC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,SAAS,CAAC;;YAEvG,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC;;QAElD,IAAI,CAAC,UAAU,EAAE;;IAGX,uBAAuB,GAAA;QAC7BP,OAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,GAAG,KAAK,MAAM,CAAC,mBAAmB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;;IAGzG,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;AAC1B,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC,uBAAuB,EAAE;QAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAElC;;AAEG;IACH,kBAAkB,GAAA;QAChB,KAAK,CAAC,kBAAkB,EAAE;QAC1B,IAAI,CAAC,uBAAuB,EAAE;;AAGxB,IAAA,OAAO,CAAC,EACd,MAAM,EACN,MAAM,EACN,IAAI,GACO,EAAA;AACX,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,qBAAqB,EAAE;AAC/C,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,qBAAqB,EAAE;QAC7C,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;QAChD,OAAO;YACL,MAAM;YACN,QAAQ;AACR,YAAA,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,OAAO;YACzB,YAAY;SACb;;AAEK,IAAA,YAAY,CAAC,IAAwB,EAAA;AAC3C,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE;;AAEjE;SAEe,eAAe,CAC7B,SAAiB,EACjB,OAAe,EACf,MAAc,EAAA;AAEd,IAAA,OAAO,SAAS,GAAG,OAAO,GAAG,MAAM;AACrC;;ACtNc,MAAO,kBAAkB,CAAA;AAIrC,IAAA,IAAI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,WAAW;;AAEzB,IAAA,WAAA,GAAA;QALA,IAAU,CAAA,UAAA,GAA4B,IAAI;AAMxC,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,MAAM,CACnC,CAAC,OAA0B,EAAE,CAAgB,KAAI;YAC/C,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,SAAS,CAAC,CAAC,CAAC;AAC7B,YAAA,OAAO,OAAO;SACf,EACD,EAAuB,CACxB;;AAGH,IAAA,MAAM,CAAC,CAAS,EAAE,IAAA,GAAsB,OAAO,EAAA;QAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC;;IAGhC,SAAS,CACP,YAAoB,EACpB,IAAmB,EAAA;AAEnB,QAAA,OAAO,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,YAAY,CAAC;;IAGlE,aAAa,GAAA;AACX,QAAA,OAAO,MAAM,CACX,IAAI,CAAC,WAAW,EAChB,CACE,MAA8C,EAC9C,IAAI,EACJ,IAAmB,KACjB;AACF,YAAA,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;AACvC,YAAA,OAAO,MAAM;AACf,SAAC,EACD;AACE,YAAA,KAAK,EAAE,EAAE;AACT,YAAA,WAAW,EAAE,EAAE;AACf,YAAA,SAAS,EAAE,EAAE;AACd,SAAA,CACF;;IAGH,UAAU,CAAC,OAA8B,KAAK,EAAA;AAC5C,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE;AAC1C,QAAA,IAAI,IAAI,KAAK,KAAK,EAAE;AAClB,YAAA,OAAO,aAAa,CAAC,IAAI,CAAC;;QAE5B,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,CAAkB,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;IAGvF,oBAAoB,CAAC,IAAgB,EAAE,IAAmB,EAAA;AACxD,QAAA,OAAO,+BAA+B,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC;;AAG5E,IAAA,eAAe,CAAC,IAAgB,EAAA;;QAC9B,OAAO,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,0CAAE,YAAY,CAAC,IAAI,CAAC;;AAG5C,IAAA,aAAa,CAAC,IAAmB,EAAA;QAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE;;AAGlC;;AAEG;AACH,IAAA,UAAU,CAAC,IAAsB,EAAA;AAC/B,QAAA,WAAW,CAAC,OAAO,CAAC,CAAC,IAAG;;AAEtB,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;;gBAE9C,KAAK,EAAE,IAAI,CAAC,QAAQ;;AAGpB,gBAAA,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,CAAC,KAAI;oBACvD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AACjB,wBAAA,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;;oBAEnB,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACpB,oBAAA,OAAO,GAAG;iBACX,EAAE,EAAE,CAAC;AACP,aAAA,CAAC;AACJ,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;AACtB,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACH,IAAA,aAAa,CAAC,cAA+B,EAAA;;QAE3C,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,CACvC,CACE,GAAsE,EACtE,CAAC,KACC;AACF,YAAA,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC;AAC7B,YAAA,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACd,gBAAA,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE;;YAEhB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AACrB,YAAA,OAAO,GAAG;SACX,EACD,EAAE,CACH;;QAGD,MAAM,UAAU,GAEZ,EAAE;AACN,QAAA,KAAK,MAAM,CAAC,IAAI,WAAW,EAAE;YAC3B,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;gBAClC;;YAEF,MAAM,IAAI,GAAG,CAAkB;AAC/B,YAAA,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC;AACtC,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;AAC9D,YAAA,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE;AACrB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,gBAAA,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;AAC7B,gBAAA,MAAM,mBAAmB,GAAG,YAAY,KAAA,IAAA,IAAZ,YAAY,KAAA,MAAA,GAAA,MAAA,GAAZ,YAAY,CAAG,MAAM,CAAC,IAAI,CAAC;;gBAGvD,IAAI,mBAAmB,EAAE;oBACvB,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,mBAAmB;;;;AAI/C,QAAA,KAAK,MAAM,CAAC,IAAI,UAAU,EAAE;YAC1B,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;gBACjC;;YAEF,MAAM,IAAI,GAAG,CAAkB;AAC/B,YAAA,wBAAwB,CACtB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,EAC5B,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CACvB;;;IAIL,YAAY,CAAC,MAAqB,EAAE,KAAa,EAAA;AAC/C,QAAA,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC;AAClC,QAAA,uBAAuB,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,MAAM,EAAE,CAAC;;AAE7E;;ACpJD;;;;AAIG;MAEU,YAAY,CAAA;AAEvB,IAAA,WAAA,CAAoB,iBAAoC,EAAA;QAApC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;AACnC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAClB,QAAQ,EACR,CAAC,OAAgC,EAAE,CAAgB,KAAI;YACrD,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,SAAS,CAAC,CAAC,CAAC;AAC7B,YAAA,OAAO,OAAO;SACf,EACD,EAAE,CACe;;AAGrB,IAAA,OAAO,CACL,IAAgB,EAChB,IAAA,GAAsB,OAAO,EAC7B,kBAAkB,GAAG,KAAK,EAC1B,QAIC,EACD,MAAM,GAAG,KAAK,EAAA;;AAGd,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC;;AAGzD,QAAA,MAAM,SAAS,GAAG,IAAI,KAAK,OAAO,IAAI,kBAAkB;AACxD,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,CAAC;AAC5D,QAAA,OAAO,IAAI;;AAGb,IAAA,QAAQ,CAAC,YAAoB,EAAE,IAAA,GAAsB,OAAO,EAAA;QAC1D,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK;AACrC,QAAA,OAAO,aAAa,CAAC,KAAK,EAAE,YAAY,CAAC;;IAG3C,WAAW,CAAC,EAAE,OAAO,GAAG,OAAO,EAAE,IAAI,EAAE,EAAE,EAAwD,EAAA;QAC/F,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;;AAGzC,QAAA,MAAM,aAAa,GAAG,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;;AAGjD,QAAA,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CACjC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACtC,QAAA,CAAC,CACF;;AAED,QAAA,aAAa,CAAC,MAAM,CAClB,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AACpC,QAAA,CAAC,EACD,GAAG,MAAM,CACV;QACD,YAAY,CAAC,OAAO,CAAC;AACnB,YAAA,UAAU,EAAE,aAAa;AAC1B,SAAA,CAAC;;QAGF,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;QAChD,IAAI,CAAC,iBAAiB,CAAC,mCAAmC,CACxD,OAAO,EACP,QAAQ,EACR,SAAS,CACV;;AAGH,IAAA,WAAW,CACT,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAqE,EAChG,MAAM,GAAG,IAAI,EAAA;QAEb,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC;AAC3C,QAAA,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG;AACjB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,QAAQ,GAAG,KAAK,EAAE,EAAE,MAAM,CAAC;;IAGhE,YAAY,CAAC,IAAgB,EAAE,IAAmB,EAAA;QAChD,MAAM,KAAK,GAA6B,EAAE;AAC1C,QAAA,KAAK,IAAI,QAAQ,IAAI,IAAI,EAAE;YACzB,MAAM,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,aAAa,CAC/C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EACvB,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CACvB,CAAC;YACF,IAAI,CAAC,QAAQ,EAAE;gBACb;;YAEF,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC/B,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;;;QAGzC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC;;IAGxC,OAAO,CAAC,OAA8B,KAAK,EAAA;AACzC,QAAA,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;;AAEzB,QAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAgB,KAAK,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;IAG9D,YAAY,CAAC,OAAsB,OAAO,EAAA;AACxC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;AAClD,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;;AAGlD,IAAA,WAAW,CAAC,EAAE,KAAK,EAAqB,EAAE,OAAsB,OAAO,EAAA;AACrE,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;;AAGrD,IAAA,UAAU,CAAC,OAAgB,EAAE,IAAA,GAAsB,OAAO,EAAA;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AAC/B,QAAA,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC;QACzB,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC;AAChD,QAAA,IAAI,IAAI,KAAK,OAAO,EAAE;AACpB,YAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAC5B,oBAAoB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,EACxC,IAAI,CACL;;;AAGN;;AC3HD;;;;;AAKG;AACW,MAAO,iBAAiB,CAAA;IAEpC,WACU,CAAA,SAA2B,EACnC,MAAuB,EAAA;QADf,IAAS,CAAA,SAAA,GAAT,SAAS;AAGjB,QAAA,MAAM,WAAW,GAAG,QAAQ,CAC1B,CAAC,CAAqB,KAAK,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,EACpD,eAAe,CAChB;AACD,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAClB,CAAC,GAAG,QAAQ,EAAE,GAAG,WAAW,CAAC,EAC7B,CAAC,OAA0C,EAAE,CAAqB,KAAI;YACpE,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,cAAc,CAAC,CAAC,CAAC;AAClC,YAAA,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,WAAW,CAAC,CAAC,CAAC,CAAC;AAC3D,YAAA,OAAO,OAAO;SACf,EACD,EAAE,CACyB;;AAG/B;;;;AAIG;IACH,SAAS,CAAC,CAAqB,EAAE,KAAa,EAAA;QAC5C,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;;QAErB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CACvC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAC3C;AACD,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;;AAG7B;;;;;AAKG;AACH,IAAA,cAAc,CACZ,IAAwB,EACxB,KAA0B,EAC1B,OAAO,GAAG,KAAK,EAAA;QAEf,IAAI,QAAQ,GAAG,KAAK;QACpB,IAAI,OAAO,EAAE;AACX,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;AACrD,YAAA,QAAQ,GACH,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,QAAQ,CACR,EAAA,KAAK,CACT;;QAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC;QAC5C,IAAI,CAAC,qBAAqB,CAAC;YACzB,IAAI;AACJ,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA,CAAC;;IAGJ,YAAY,CAAC,SAAiB,EAAE,IAAwB,EAAA;AACtD,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,EAAE,SAAS,EAAE,CAAC;AACtD,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;;AAGlD;;;;AAIG;IACH,UAAU,CAAC,OAAgB,EAAE,IAAwB,EAAA;AACnD,QAAA,MAAM,UAAU,GAAG,kBAAkB,CAAC,OAAO,CAAC;QAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACtC,YAAY,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;QAC9C,IAAI,CAAC,qBAAqB,CAAC;YACzB,IAAI;AACJ,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA,CAAC;;AAGJ;;;;;AAKG;AACH,IAAA,OAAO,CAAC,SAAiB,EAAE,IAAwB,EAAE,SAAS,GAAG,KAAK,EAAA;AACpE,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC;;QAGlC,IAAI,SAAS,EAAE;YACb,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE;YACrD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC;gBACtC,WAAW,EAAE,SAAS,CAAC,QAAQ;AAChC,aAAA,CAAC;;QAEJ,IAAI,CAAC,qBAAqB,CAAC;YACzB,IAAI;AACL,SAAA,CAAC;;AAGJ;;;;AAIG;AACH,IAAA,eAAe,CACb,OAA+C,EAC/C,eAAwB,EACxB,OAAO,GAAG,KAAK,EAAA;;AAGf,QAAA,KAAK,IAAI,IAAI,IAAI,WAAW,EAAE;YAC5B,IAAI,CAAC,OAAO,EAAE;;gBAEZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE;;;AAI1B,YAAA,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;;AAG3B,YAAA,MAAM,SAAS,GAAG,IAAI,KAAK,OAAO,IAAI,eAAe;;AAGrD,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;;AAGnD,YAAA,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC;;AAG5C,YAAA,MAAM,QAAQ,GAA2B;;gBAEvC,SAAS,EAAE,KAAK,CAAC,MAAM;aACxB;;YAGD,IAAI,SAAS,EAAE;AACb,gBAAA,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,CAAC,QAAQ;;;AAIrE,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC;YACjD,IAAI,CAAC,qBAAqB,CAAC;gBACzB,IAAI;AACL,aAAA,CAAC;;;AAIN;;;AAGG;IAEH,WAAW,GAAA;;QACT,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,GAAG,CAAC;AACT,QAAA,KAAK,IAAI,IAAI,IAAI,WAAW,EAAE;AAC5B,YAAA,CAAC,IAAI,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,KAAI,CAAC;;AAEpD,QAAA,KAAK,IAAI,IAAI,IAAI,QAAQ,EAAE;AACzB,YAAA,CAAC,IAAI,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,KAAI,CAAC;;AAEpD,QAAA,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE;;IAGjB,qBAAqB,CAAC,EACpB,IAAI,EACJ,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,EACvD,KAAK,GAAG,KAAK,GAKd,EAAA;QACC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE;AACrD,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAC/C,UAAU,EACV,SAAS,EACT,KAAK,CACN;;AAGH,IAAA,cAAc,CAAC,CAAsB,EAAA;AACnC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,eAAe,EAAE;QAC5D,MAAM,IAAI,GAAG,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,UAAU,CAAC;QAEpD,OAAO,IAAI,CAAC,KAAK;;IAGnB,WAAW,CACT,IAAqC,EACrC,aAA4B,EAAA;QAE5B,IAAI,MAAM,GAAyB,EAAE;QACrC,QAAQ,aAAa;AACnB,YAAA,KAAK,OAAO;gBACV,MAAM,GAAG,WAAW;gBACpB;AACF,YAAA,KAAK,OAAO;gBACV,MAAM,GAAG,QAAQ;gBACjB;;AAEJ,QAAA,KAAK,IAAI,CAAC,IAAI,MAAM,EAAE;YACpB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;;;AAIjC,IAAA,mCAAmC,CACjC,IAAwB,EACxB,aAAuB,EACvB,iBAA2B,EAAE,EAAA;;AAG7B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAC5C,aAAa,EACb,cAAc,CACf;QACD,IAAI,CAAC,qBAAqB,CAAC;YACzB,IAAI;AACJ,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA,CAAC;;AAEL;;AC/Pa,MAAO,gBAAgB,CAAA;AAEnC,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAClB,CAAC,GAAG,QAAQ,EAAE,GAAG,WAAW,CAAC,EAC7B,CAAC,OAAyC,EAAE,CAAqB,KAAI;YACnE,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC;AACjC,YAAA,OAAO,OAAO;SACf,EACD,EAAE,CACwB;;IAG9B,WAAW,CAAC,IAAwB,EAAE,IAA4B,EAAA;QAChE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC;;AAEtC;;ACgCD;AACA,SAAS,gBAAgB,CAAC,IAAoB,EAAA;AAC5C,IAAA,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK;IACxD,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC;AAE9C,IAAA,MAAM,IAAI,GAAuB;AAC/B,QAAA,YAAY,EAAE,SAAS;QACvB,KAAK,EAAE,IAAI,CAAC,OAAO;QACnB,aAAa,EAAE,IAAI,CAAC,aAAa;QACjC,GAAG,EAAE,IAAI,CAAC,OAAO;QACjB,OAAO,EAAE,IAAI,CAAC,OAAO;QACrB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;;AAEvC,QAAA,KAAK,EAAE,IAAI,CAAC,QAAQ,GAAG,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAA,EAAA,CAAI,EAAE,GAAG,SAAS;KAClE;AAED,IAAA,MAAM,UAAU,GAAqB;AACnC,QAAA,OAAO,EAAE,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC5C,QAAA,YAAY,EAAE,YAAY;QAC1B,IAAI,EAAE,IAAI,CAAC,OAAO;QAClB,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC;QACnC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC;AACjD,QAAA,aAAa,EAAE,IAAI,CAAC,OAAO,KAAK,WAAW,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS;QAC/D,cAAc,EAAE,IAAI,CAAC,cAAc;KACpC;IAED,OAAO;QACL,IAAI;QACJ,IAAI,EAAE,IAAI,CAAC,OAAO;QAClB,QAAQ,EAAE,IAAI,CAAC,QAAQ;QACvB,UAAU;QACV,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK;KAChD;AACH;AAEc,MAAO,eAAe,CAAA;IAElC,WACU,CAAA,MAAc,EACtB,aAAqB,EAAA;;QADb,IAAM,CAAA,MAAA,GAAN,MAAM;QAGd,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,CAAC,uBAAuB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,YAAY,EAAE;;;QAKnD,MAAM,OAAO,GAAoB,EAAE;AACnC,QAAA,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,QAAA,WAAW,CAAC,OAAO,CAAC,GAAG,IAAG;AACxB,YAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK;;YAExD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;gBACjC;;AAEF,YAAA,MAAM,MAAM,GAAmB;AAC7B,gBAAA,OAAO,EAAE,GAAG;AACZ,gBAAA,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBAErB,aAAa;;gBAEb,QAAQ,EAAE,GAAG,KAAK,OAAO;AAEzB,gBAAA,SAAS,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM;AACzC,gBAAA,UAAU,EAAE,MAAM,CAAC,iBAAiB,CAAC,MAAM;AAC3C,gBAAA,SAAS,EAAE,MAAM,CAAC,YAAY,CAAC,MAAM;gBAErC,QAAQ;AACR,gBAAA,cAAc,EAAE,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,QAAQ,CAAC;aAC3D;AACD,YAAA,IAAI,GAAG,KAAK,OAAO,EAAE;AACnB,gBAAA,MAAM,CAAC,gBAAgB,GAAG,CAAC,CAAmC,KAAI;;AAChE,oBAAA,MAAM,OAAO,GAA2B;AACtC,wBAAA,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI;qBAC1B;;AAGD,oBAAA,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe;AACvD,4BAAC,CAAC,CAAC,MAAM,CAAC,SAAS,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE;wBAC9D,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI;;AAEzC,oBAAA,CAAA,EAAA,GAAA,MAAM,CAAC,gBAAgB,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC;AACnE,iBAAC;;AAEH,YAAA,MAAM,OAAO,GAAG,gBAAgB,CAAC,MAAM,CAAC;AACxC,YAAA,MAAM,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;AAGtE,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,MAAM,CAChD,CAAC,CAAC,EAAE,KAAK,KAAI;;gBAEX,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC7D,gBAAA,gBAAgB,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC;;AAG5C,gBAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,CACxC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAChB,KAAK,CAAC,IAAI,CACX;AACD,gBAAA,MAAM,MAAM,GACV,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,OAAO,EAAE,GAAG,IACT,KAAK,CAAA,EAAA,EACR,iBAAiB,EACjB,cAAc,EAAE,gBAAgB,CAAC,KAAK,EACtC,GAAG,EAAE,CAAC,CAAC,KACL,MAAM,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC,CAAC,EACnD,UAAU,EAAE,CAAC,IAAG;AACd,wBAAA,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC;qBACxC,EACD,cAAc,EAAE,CAAC,IAAI,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,EAC3D,WAAW,EAAE,CAAC,IAAG;;wBAEf,gBAAgB,CAAC,UAAU,EAAE;wBAC7B,MAAM,CAAC,uBAAuB,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,MAAM,CAAC;AAClE,qBAAC,GACF;AACD,gBAAA,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AACd,gBAAA,OAAO,CAAC;aACT,EACD,EAAE,CACH;AACD,YAAA,OAAO,CAAC,IAAI,CACP,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,KACV,oBAAoB;AACpB,gBAAA,SAAS,IACT;AACF,YAAA,CAAC,EAAE;AACL,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;;QAGtB,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,CAAC,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,UAAU,EAAE;;AAGpC,IAAA,cAAc,CACpB,IAAmB,EACnB,EAAE,MAAM,EAAoC,EAC5C,KAA6D,EAAA;;;AAG7D,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;;QAGjE,MAAM,YAAY,GAAkB,EAAE;AACtC,QAAA,KAAK,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,EAAE;YACpD,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,EAAE,YAAY,CAAC;YAC/C,IAAI,IAAI,EAAE;gBACR,YAAY,CAAC,YAAY,CAAC,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,IAAI,CAAE,EAAA,EAAA,IAAI,GAAE;;;AAGlD,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;;;AAI1B,IAAA,eAAe,CAAC,QAAc,EAAA;QACpC,OAAO,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,QAAQ,CAAC,QAAQ,CAAC;;;IAIvD,WAAW,CAAC,CAAS,EAAE,IAAmB,EAAA;AAChD,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK;;;IAI/D,WAAW,CAAC,CAAS,EAAE,IAAmB,EAAA;AAChD,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK;;;AAIlE,IAAA,YAAY,CAAC,IAAoB,EAAA;AACvC,QAAA,MAAM,KAAK,GAAyC;AAClD,YAAA,WAAW,EAAE,WAAW;AACxB,YAAA,KAAK,EAAE,YAAY;AACnB,YAAA,SAAS,EAAE,WAAW;SACvB;;QAGD,IAAI,CAAC,GAAG,CAAC;QACT,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAqB,EAAE,IAAI,KAAI;;AAErD,YAAA,MAAM,SAAS,GACb,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,IAAI,KAAK,OAAO;YACjE,MAAM,KAAK,mCACN,IAAI,CAAA,EAAA,EACP,QAAQ,EAAO,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CAAC,QAAQ,CAAE,EAAA,EAAA,CAAC,EAAE,SAAS,GAAG,CAAC,GAAG,WAAW,EAAA,CAAA,EAAA,CAC7D;AACD,YAAA,MAAM,SAAS,GAAG,qBAAqB,CACrC,KAAK,EACL,IAAI,EACJ,KAAK,CAAC,IAAI,CAAC,EACX,IAAI,KAAK,OAAO,CACjB;AACD,YAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACtB,IAAI,SAAS,EAAE;AACb,gBAAA,CAAC,EAAE;;AAEL,YAAA,OAAO,MAAM;SACd,EAAE,EAAE,CAAC;;AAGR,IAAA,YAAY,CAAC,IAAmB,EAAA;AAC9B,QAAA,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;AACpB,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,GAAiB,CAAC;AAC1C,YAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AAClC,gBAAA,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC;oBACvC,SAAS,EAAE,GAAG,KAAK,GAAG,GAAG,OAAO,GAAG,OAAO;oBAC1C,UAAU;AACX,iBAAA,CAAC;;;;AAKR;;AAEG;IACH,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,QAAQ,EAAE;;IAGhD,SAAS,GAAA;QACP,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,OAAO,CAAC,KAAK,CAAC;;AAGpD;;AAEG;IACH,UAAU,GAAA;QACR,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,YAAY;QAChE,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,OAAO,IAAI;;;AAGb,QAAA,MAAM,OAAO,GACX,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvE,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CACjD,OAAO,CAAC,IAAI,CAAC,CAAC,EACd,OAAO,CACR;;AAGD,QAAA,MAAM,OAAO,GACX,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvE,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;QACxE,OAAO;YACL,MAAM;YACN,KAAK;YACL,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,OAAO;YACP,OAAO;SACR;;IAGH,wBAAwB,CAAC,OAAsB,EAAE,OAAsB,EAAA;QACrE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,YAAY;AAC/D,QAAA,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,WAAW,EAAE;YACpF;;QAEF,OAAO;AACL,YAAA,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC;AAClB,YAAA,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC;SACnB;;AAGH,IAAA,QAAQ,CAAC,OAAe,EAAE,OAAe,EAAE,KAAW,EAAE,GAAS,EAAA;;QAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAwB,EAAE,OAAwB,CAAC;QACpG,IAAI,UAAU,EAAE;AACd,YAAA,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,CAAC,uBAAuB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,WAAW,CAC9C,UAAU,EACV,KAAK,EACL,GAAG,CACJ;;;IAIL,gBAAgB,GAAA;QAEd,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,YAAY;QAChE,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,OAAO,IAAI;;;AAGb,QAAA,MAAM,OAAO,GACX,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;;AAGvE,QAAA,MAAM,OAAO,GACX,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AAEvE,QAAA,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;QAC/C,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,OAAO,IAAI;;QAEb,OACK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAK,KACR,OAAO;AACP,YAAA,OAAO,EACR,CAAA;;AAGH,IAAA,OAAO,CACL,QAAgB,EAChB,QAAgB,EAChB,OAAsB,EACtB,OAAsB,EAAA;;QAEtB,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAwB,EAAE,OAAwB,CAAC;QACpG,IAAI,UAAU,EAAE;YACd,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,CAAC,uBAAuB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,aAAa,CAChD,UAAU,EACV,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAC7B;;;AAGN;;ACtWa,MAAO,oBAAoB,CAAA;AAEvC,IAAA,WAAA,CAAoB,WAA6C,EAAA;QAA7C,IAAW,CAAA,WAAA,GAAX,WAAW;QADvB,IAAQ,CAAA,QAAA,GAAmB,EAAE;;AAGrC,IAAA,MAAM,WAAW,CAAC,CAAsB,EAAE,GAA8B,EAAA;;AACtE,QAAA,IAAI,eAAqE;QACzE,IAAI,KAAK,GAAG,CAAC;AACb,QAAA,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;;YAE/B,IAAI,CAAC,CAAC,SAAS,KAAK,OAAO,IAAI,KAAK,KAAK,WAAW,EAAE;gBACpD;;;AAEK,iBAAA,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,OAAO,EAAE;gBAC9D,IAAI,KAAK,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE;oBAC7B;;gBAEF,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACnC,oBAAA,IAAI,EAAE,CAAC,YAAY,EAAE;AACnB,wBAAA,eAAe,GAAG,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;;;;iBAGnC;gBACL,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;oBACnC,OAAM,MAAA,EAAE,CAAC,SAAS,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,EAAA,CAAC,CAAC,CAAA;;;;AAI7B,QAAA,MAAM,QAAQ,GAAG,MAAM,eAAe;QACtC,IAAI,QAAQ,EAAE;YACZ,KAAK,GAAG,QAAQ;;AAElB,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;AAGzB;;AAEG;AACH,IAAA,MAAM,mBAAmB,CACvB,CAAsB,EACtB,GAA8B,EAAA;;AAE9B,QAAA,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;;AAE/B,YAAA,IAAI,KAAK,KAAK,GAAG,EAAE;gBACjB;;AAEF,YAAA,IACE,WAAW,CAAC,QAAQ,CAAC,GAAsB,CAAC;iBAC3C,KAAK,KAAK,WAAW;AACpB,oBAAA,WAAW,CAAC,QAAQ,CAAC,KAAwB,CAAC,CAAC,EACjD;gBACA,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;oBACnC,OAAM,CAAA,EAAA,GAAA,EAAE,CAAC,YAAY,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,EAAA,CAAC,EAAE,IAAI,CAAC,CAAA;;gBAElC;;;;AAKE,IAAA,cAAc,CACpB,GAA8B,EAAA;AAE9B,QAAA,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE;;AAGhE,IAAA,gBAAgB,CAAC,GAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG;;AAGrB;;;;AAIG;IACH,eAAe,CAAC,EAAoC,EAAE,GAAW,EAAA;QAC/D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACvB,YAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE;;;QAGzB,IAAI,EAAE,EAAE;YACN,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;AACtB,aAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;;AAE7B,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;;;IAI7B,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAErB;;AC5FD;;AAEG;MACU,cAAc,CAAA;AAA3B,IAAA,WAAA,GAAA;QACU,IAAO,CAAA,OAAA,GAAG,CAAC;;IAMnB,KAAK,CAAC,MAAmB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAA0D,EAAA;;QACrG,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,qBAAqB,EAAE;AAC9C,QAAA,IAAI,CAAC,OAAO,GAAG,GAAG;AAClB,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI;;AAE5B,QAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;AACd,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QACxC,CAAA,EAAA,GAAA,IAAI,CAAC,EAAE,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;;IAErC,GAAG,GAAA;;QACD,CAAA,EAAA,GAAA,IAAI,CAAC,EAAE,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;;AAElC,IAAA,IAAI,CAAC,GAAiB,EAAA;QACpB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;;AAE1C,IAAA,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAQ,EAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB;;QAEF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,CAAA,EAAG,CAAC,CAAA,EAAA,CAAI;QACpC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,CAAA,EAAG,CAAC,CAAA,EAAA,CAAI;;AAG7B,IAAA,WAAW,CAAC,CAAS,EAAA;AAC3B,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf;;QAEF,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,CAAA,WAAA,EAAc,CAAC,CAAA,GAAA,CAAK;;AAEpD;AAED,MAAM,aAAa,GAAG,CAAC,EAAE,GAAG,EAAS,KAAa;AAChD,IAAA,MAAM,OAAO,GAAG,IAAI,cAAc,EAAE;IACpC,GAAG,CAAC,OAAO,CAAC;AACZ,IAAA,QACE,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,0BAA0B,EAAC,GAAG,EAAE,CAAC,KAAK,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,EAAA;AAC9D,QAAA,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,WAAW,EAAC,GAAG,EAAE,EAAE,KAAK,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC,EAAA;YACxD,CAAM,CAAA,MAAA,EAAA,EAAA,KAAK,EAAC,eAAe,EAAG,CAAA;AAC9B,YAAA,CAAA,CAAA,MAAA,EAAA,EAAM,GAAG,EAAE,CAAC,KAAK,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,GAAI,CAClC;QACN,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,eAAe,EAAC,GAAG,EAAE,CAAC,KAAK,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,EAAI,CAAA,CACxD;AAEV,CAAC;;ACpDM,MAAM,mBAAmB,GAAG,CAAC,MAA0B,GAAA,EAAE,KAAI;IAClE,MAAM,MAAM,GAAW,EAAE;AACzB,IAAA,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE;QACtB,IAAI,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5B,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;;AAE/B,QAAA,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AAClB,gBAAA,OAAO,CAAC,KAAK,GAAG,EAAE;;YAEpB,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI;;;AAGnC,IAAA,OAAO,MAAM;AACf,CAAC;AAEM,MAAM,yBAAyB,GAAG,CAAC,MAA0B,GAAA,EAAE,KAAI;IACxE,MAAM,MAAM,GAAiB,EAAE;AAC/B,IAAA,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE;QACtB,IAAI,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5B,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;;AAE/B,QAAA,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,YAAA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;;;AAGzB,IAAA,OAAO,MAAM;AACf,CAAC;;SCrCe,cAAc,GAAA;IAC1B,OAAO,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,cAAc,GAAG,CAAC;AACpH;;ACEA;;;;;;;;;;;;;;;;;;;;AAoBG;AACG,MAAO,UAAW,SAAQ,UAAU,CAAA;IACxC,WAAY,CAAA,QAA6B,EAAE,SAA0B,EAAA;AACnE,QAAA,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;AAE1B,QAAA,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC;AACnC,QAAA,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;AACzC,QAAA,QAAQ,CAAC,YAAY,CAAC,mBAAmB,EAAE,OAAO,CAAC;AACnD,QAAA,QAAQ,CAAC,YAAY,CAAC,sBAAsB,EAAE,MAAM,CAAC;AACrD,QAAA,QAAQ,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC;AAEtC;;AAEG;QACH,IAAI,CAAC,gBAAgB,CACnB,kBAAkB,EAClB,CAAC,EAAE,MAAM,EAAiC,KAAI;AAC5C,YAAA,MAAM,OAAO,GAAG;AACd,gBAAA,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW;AAC7B,gBAAA,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK;AACvB,gBAAA,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS;aAC5B;YAED,QAAQ,CAAC,YAAY,CAAC,eAAe,EAAE,CAAG,EAAA,OAAO,CAAC,MAAM,CAAE,CAAA,CAAC;YAE3D,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,KAAI;AAChC,gBAAA,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,MAAM;AAEnD,gBAAA,MAAM,CAAC,gBAAgB,GAAG,CAAC,GAAG,IAAI,KAAI;AACpC,oBAAA,MAAM,MAAM,GAAG,CAAA,gBAAgB,aAAhB,gBAAgB,KAAA,MAAA,GAAA,MAAA,GAAhB,gBAAgB,CAAG,GAAG,IAAI,CAAC,KAAI,EAAE;AAEhD,oBAAA,MAAM,CAAC,IAAI,GAAG,cAAc;AAC5B,oBAAA,MAAM,CAAC,eAAe,CAAC,GAAG,CAAG,EAAA,KAAK,EAAE;AAEpC,oBAAA,OAAO,MAAM;AACf,iBAAC;AAED,gBAAA,MAAM,CAAC,cAAc,GAAG,CAAC,GAAG,IAAI,KAAI;AAClC,oBAAA,MAAM,SAAS,GAAc;wBAC3B,CAAC,MAAM,GAAG,UAAU;AACpB,wBAAA,CAAC,eAAe,GAAG,CAAA,EAAG,KAAK,CAAE,CAAA;wBAC7B,CAAC,eAAe,GAAG,CAAG,EAAA,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAE,CAAA;AACxC,wBAAA,CAAC,UAAU,GAAG,EAAE;qBACjB;AACD,oBAAA,MAAM,WAAW,GAAc,CAAA,cAAc,aAAd,cAAc,KAAA,MAAA,GAAA,MAAA,GAAd,cAAc,CAAG,GAAG,IAAI,CAAC,KAAI,EAAE;oBAE9D,OACK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,SAAS,CACT,EAAA,WAAW,CACd;AACJ,iBAAC;AACH,aAAC,CAAC;AACJ,SAAC,CACF;AAED;;AAEG;QACH,IAAI,CAAC,gBAAgB,CACnB,iBAAiB,EACjB,CAAC,EACC,MAAM,GACsD,KAAI;AAChE,YAAA,QAAQ,CAAC,YAAY,CAAC,eAAe,EAAE,CAAA,EAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAA,CAAE,CAAC;AACnE,SAAC,CACF;QACD,IAAI,CAAC,gBAAgB,CACnB,iBAAiB,EACjB,CAAC,EACC,MAAM,GACwD,KAAI;YAClE,MAAM,CAAC,IAAI,CAAC,OAAO,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACd,MAAM,CAAC,IAAI,CAAC,OAAO,CACtB,EAAA,EAAA,IAAI,EAAE,KAAK,EACX,CAAC,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,EAAA,CACzC;AACH,SAAC,CACF;;QAGD,IAAI,CAAC,gBAAgB,CACnB,YAAY,EACZ,OACE,CAA4D,KAC1D;AACF,YAAA,IAAI,CAAC,CAAC,gBAAgB,EAAE;gBACtB;;AAEF,YAAA,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CACpC,CAAA,kBAAA,EAAqB,CAAC,CAAC,MAAM,CAAC,OAAO,gBAAgB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAA,gBAAA,EAAmB,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAkB,eAAA,EAAA,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAA,EAAA,CAAI,CACjJ;AACD,YAAA,IAAI,EAAE,YAAY,WAAW,EAAE;gBAC7B,EAAE,CAAC,KAAK,EAAE;;AAEd,SAAC,CACF;;AAEJ;;ACtHD;;;AAGG;MACU,aAAa,CAAA;AAA1B,IAAA,WAAA,GAAA;AACE;;;AAGG;QACH,IAAe,CAAA,eAAA,GAA0B,EAAE;;AAE3C;;AAEG;IACH,GAAG,GAAA;AACD,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;;AAGlC;;AAEG;AACH,IAAA,GAAG,CAAC,MAA2B,EAAA;AAC7B,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC;;AAGnC;;AAEG;IACH,uBAAuB,CACrB,OAA4B,EAC5B,OAAA,GAAwB,EAAE,EAC1B,WAA0B,EAC1B,UAA4B,EAAA;QAE5B,IAAI,CAAC,UAAU,EAAE;YACf;;;AAIF,QAAA,MAAM,eAAe,GACnB,CAAA,WAAW,KAAX,IAAA,IAAA,WAAW,KAAX,MAAA,GAAA,MAAA,GAAA,WAAW,CAAE,MAAM,CACjB,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,UAAU,KAAK,UAAU,CAAC,CACrE,KAAI,EAAE;;AAGT,QAAA,eAAe,CAAC,OAAO,CAAC,MAAM,IAAG;;AAC/B,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAC1C,aAAa,IAAI,aAAa,YAAY,MAAM,CACjD;AACD,YAAA,IAAI,KAAK,KAAK,EAAE,EAAE;gBAChB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAI;gBACvC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;;AAE1C,SAAC,CAAC;;QAGF,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,MAAA,GAAA,MAAA,GAAA,OAAO,CAAE,OAAO,CAAC,UAAU,IAAG;;AAE5B,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAC9C,aAAa,IAAI,aAAa,YAAY,UAAU,CACrD;YACD,IAAI,cAAc,EAAE;gBAClB;;YAEF,IAAI,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AAC/C,SAAC,CAAC;;AAGJ;;AAEG;AACH,IAAA,UAAU,CACR,WAAsC,EAAA;AAEtC,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,WAAW,CAEjD;;AAGf;;AAEG;AACH,IAAA,MAAM,CAAC,MAA2B,EAAA;;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC;AAClD,QAAA,IAAI,KAAK,GAAG,EAAE,EAAE;YACd,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAI;YACvC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;;AAIzC;;AAEG;IAEH,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,IAAG,EAAA,IAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,CAAC,CAAC,OAAO,iDAAI,CAAA,EAAA,CAAC;AAChD,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE;;AAE5B;;ACrGD,MAAM,gBAAgB,GAAG,qqkBAAqqkB;;MCoHjrkB,iBAAiB,iBAAAS,kBAAA,CAAA,MAAA,iBAAA,SAAAC,aAAA,CAAA;AAJ9B,IAAA,WAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYE;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAG,CAAC;AAErB;;;;;AAKG;AACK,QAAA,IAAO,CAAA,OAAA,GAAG,CAAC;;AAGX,QAAA,IAAO,CAAA,OAAA,GAAG,GAAG;;AAGb,QAAA,IAAK,CAAA,KAAA,GAAG,KAAK;;AAGb,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;;AAGhB,QAAA,IAAM,CAAA,MAAA,GAAG,KAAK;;AAGd,QAAA,IAAQ,CAAA,QAAA,GAAG,IAAI;;AAGf,QAAA,IAAY,CAAA,YAAA,GAAG,IAAI;AAE3B;;;AAGG;AACK,QAAA,IAAO,CAAA,OAAA,GAAuC,EAAE;AACxD;;;;AAIG;AACK,QAAA,IAAM,CAAA,MAAA,GAAe,EAAE;;AAGvB,QAAA,IAAe,CAAA,eAAA,GAAe,EAAE;;AAGhC,QAAA,IAAkB,CAAA,kBAAA,GAAe,EAAE;;AAGnC,QAAA,IAAc,CAAA,cAAA,GAAoB,EAAE;;AAGpC,QAAA,IAAO,CAAA,OAAA,GAAY,EAAE;AAE7B;;;;AAIG;AACK,QAAA,IAAY,CAAA,YAAA,GAAG,KAAK;AAE5B;;;;;AAKG;AACK,QAAA,IAAO,CAAA,OAAA,GAAiB,EAAE;AAElC;;;;;AAKG;AACK,QAAA,IAAW,CAAA,WAAA,GAAmC,EAAE;;AAGhB,QAAA,IAAK,CAAA,KAAA,GAAU,SAAS;AAEhE;;;;AAIG;AACsB,QAAA,IAAQ,CAAA,QAAA,GAAG,EAAE;AAEtC;;;;;;;AAOG;AACK,QAAA,IAAc,CAAA,cAAA,GAAmC,KAAK;AAE9D;;;;AAIG;AACK,QAAA,IAAM,CAAA,MAAA,GAAiC,KAAK;AAgBpD;;AAEG;AACK,QAAA,IAAc,CAAA,cAAA,GAAG,KAAK;AAC9B;;;;AAIG;AACK,QAAA,IAAW,CAAA,WAAA,GAA4B,EAAE;AAEjD;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAG,KAAK;AAQzB;;;AAGG;AACK,QAAA,IAAO,CAAA,OAAA,GAAqB,KAAK;AAEzC;;;AAGG;AACK,QAAA,IAAc,CAAA,cAAA,GAAmB,EAAE;AAE3C;;;;AAIG;AACK,QAAA,IAAe,CAAA,eAAA,GAAG,KAAK;AAC/B;;;;AAIG;AACK,QAAA,IAAe,CAAA,eAAA,GAAG,KAAK;AAE/B;;AAEG;AACK,QAAA,IAAe,CAAA,eAAA,GAAG,KAAK;AAE/B;;;;AAIG;AACK,QAAA,IAAgB,CAAA,gBAAA,GAAmB,EAAE;AAE7C;;;;;;AAMG;AACK,QAAA,IAAa,CAAA,aAAA,GAGf,EAAE;AAGR;;;AAGG;AACK,QAAA,IAAU,CAAA,UAAA,GAAG,IAAI;AAIzB;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAG,IAAI;AA6vBtB,QAAA,IAAa,CAAA,aAAA,GAAoC,EAAE;AAYnD,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,aAAa,EAAE;AACnC,QAAA,IAAQ,CAAA,QAAA,GAA2B,IAAI;AACvC,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;AAmqBjB;;;AAjpCC;;;AAGG;AACO,IAAA,MAAM,OAAO,CAAC,IAAA,GAA8B,KAAK,EAAA;AACzD,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAElC,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;;AAGjC;;;;;;;;AAQG;IACO,MAAM,SAAS,CACvB,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,GAAG,OAAO,EAAE,OAAO,GAAG,OAAO,EAAE,GAAG,EAAE,cAAc,GAAG,KAAK,EAKzD,EAAA;;QAEpB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,cAAc,EAAE;AAC/D,YAAA,MAAM,UAAU,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,IAAI;AACpE,YAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACrC,gBAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;AAC5B,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,QAAQ,EAAE,GAAG;AACb,oBAAA,IAAI,EAAE,UAAU;oBAChB,GAAG;iBACJ,EAAE,KAAK,CAAC;;;AAGb,QAAA,MAAM,WAAW,GACf,IAAI,CAAC,OAAO,CAAC,aAAa,CACxB,CAAA,kBAAA,EAAqB,OAAO,CAAA,aAAA,EAAgB,OAAO,CAAA,EAAA,CAAI,CACxD;AACH,QAAA,OAAO,WAAW,KAAX,IAAA,IAAA,WAAW,uBAAX,WAAW,CAAE,UAAU,CAAC;YAC7B,GAAG;YACH,GAAG;AACJ,SAAA,CAAC;;AAGJ;;AAEG;AACO,IAAA,MAAM,WAAW,CAAC,UAAU,GAAG,CAAC,EAAA;AACxC,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;AAC3B,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAElC,QAAA,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;YAC9C,UAAU;AACV,YAAA,SAAS,EAAE,OAAO;AACnB,SAAA,CAAC;QACF,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC;;AAGtC;;AAEG;AACO,IAAA,MAAM,mBAAmB,CAAC,UAAU,GAAG,CAAC,EAAA;AAChD,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;AAC3B,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAElC,QAAA,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;YAC9C,UAAU;AACV,YAAA,SAAS,EAAE,OAAO;AACnB,SAAA,CAAC;QACF,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC;;AAGtC;;AAEG;AACO,IAAA,MAAM,kBAAkB,CAChC,IAAgB,EAChB,YAA8B,OAAO,EAAA;QAErC,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACnD,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAGlC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,oBAAoB,CACzD,IAAI,EACJ,SAAS,CACV;AACD,QAAA,IAAI,UAAU,GAAG,CAAC,EAAE;;YAElB;;AAEF,QAAA,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;YAC9C,UAAU;YACV,SAAS;AACV,SAAA,CAAC;QACF,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC;;;IAI5B,MAAM,aAAa,CAAC,IAAqB,EAAA;;QACjD,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,aAAa,CAAC,IAAI,CAAC;;;IAIhC,MAAM,UAAU,CACxB,OAAgC,EAChC,WAAW,GAAG,UAAU,EACxB,IAAA,GAAsB,OAAO,EAAA;AAE7B,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAElC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACpC,OAAO;YACP,WAAW;YACX,IAAI;AACL,SAAA,CAAC;AACF,QAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;AAC1B,YAAA,OAAO,KAAK;;AAEd,QAAA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC;AAC3E,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AACxB,QAAA,OAAO,KAAK;;;IAIJ,MAAM,kBAAkB,CAAC,IAAmB,EAAA;;QACpD,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,YAAY,CAAC,IAAI,CAAC;;;IAIzB,MAAM,WAAW,CACzB,KAAa,EACb,IAAgB,EAChB,YAA2B,OAAO,EAAA;;QAElC,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;QACjD,IAAI,CAAC,KAAK,EAAE;YACV;;QAEF,MAAM,OAAO,EAAE;AACf,QAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAElC,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,OAAO,CACpB,KAAK,EACL,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EACxD,QAAQ,EACR,SAAS,CACV;;;AAIO,IAAA,MAAM,aAAa,CAC3B,SAAA,GAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAChC,OAAA,GAAgB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAC9B,OAAO,GAAG,OAAO,EACjB,OAAO,GAAG,OAAO,EAAA;;AAEjB,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC;;;AAIrD,IAAA,MAAM,SAAS,CAAC,IAAA,GAAsB,OAAO,EAAA;AACrD,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAElC,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;;AAG3D;;;;AAIG;AACO,IAAA,MAAM,gBAAgB,CAAC,IAAA,GAAsB,OAAO,EAAA;AAC5D,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAElC,QAAA,OAAO,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;;AAGnE;;;;AAIG;AACO,IAAA,MAAM,cAAc,CAC5B,IAAA,GAAsB,OAAO,EAAA;AAE7B,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;QAElC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK;;AAE7C;;;;AAIG;AACO,IAAA,MAAM,cAAc,CAC5B,IAAA,GAAsB,OAAO,EAAA;AAE7B,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;QAElC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK;;AAG/C;;;;;;;AAOG;AACO,IAAA,MAAM,mBAAmB,CACjC,MAAmD,EACnD,KAAiC,EACjC,QAAiB,EAAA;AAEjB,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;AAC7B,YAAA,OAAO,EAAE,CAAC;oBACR,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,KAAK;oBACL,WAAW,EAAE,MAAM,CAAC,WAAW;iBAChC,CAAC;YACF,QAAQ;AACT,SAAA,CAAC;;AAGJ;;AAEG;AACO,IAAA,MAAM,YAAY,GAAA;AAC1B,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;AAC7B,YAAA,OAAO,EAAE,EAAE;AACZ,SAAA,CAAC;;AAGJ;;AAEG;AACO,IAAA,MAAM,UAAU,GAAA;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAElC,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;;AAGzC;;AAEG;AACO,IAAA,MAAM,UAAU,GAAA;;AACxB,QAAA,MAAM,OAAO,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,UAAU,EAAE;QAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC;AAChD,QAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;YAC1B;;QAEF,CAAA,EAAA,GAAA,IAAI,CAAC,uBAAuB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,QAAQ,EAAE;;AAG1C;;AAEG;AACO,IAAA,MAAM,UAAU,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE;;AAGjC;;AAEG;AACO,IAAA,MAAM,UAAU,GAAA;;QACxB,OAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,0CAAE,UAAU,EAAE,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,IAAI;;AAG5C;;;AAGG;AACO,IAAA,MAAM,cAAc,GAAA;;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;AAC3B,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;QAElC,OAAO,MAAA,IAAI,CAAC,iBAAiB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,WAAW,EAAE;;AAE9C;;AAEG;AACO,IAAA,MAAM,gBAAgB,GAAA;;QAC9B,OAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,0CAAE,gBAAgB,EAAE,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,IAAI;;AAGlD;;;;AAIG;AACO,IAAA,MAAM,oBAAoB,GAAA;;QAClC,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,OAAO,EAAE;;AAG9B;;;AAGG;AACO,IAAA,MAAM,YAAY,GAAA;AAC1B,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE;;AAS7B,IAAA,eAAe,CAAC,KAA8B,EAAA;QAC5C,MAAM,OAAO,GAAG,oBAAoB,CAAC,KAAK,EAAE,SAAS,CAAC;QACtD,MAAM,OAAO,GAAG,oBAAoB,CAAC,KAAK,EAAE,SAAS,CAAC;QACtD,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,EAAE;YACxC;;AAGF,QAAA,IAAI,CAAC,uBAAuB,GAAG,OAAO,GAAG,OAAO;;AAElD;;AAEG;IAGH,MAAM,aAAa,CAAC,KAA8B,EAAA;;QAChD,MAAM,OAAO,GAAG,oBAAoB,CAAC,KAAK,EAAE,SAAS,CAAC;QACtD,MAAM,OAAO,GAAG,oBAAoB,CAAC,KAAK,EAAE,SAAS,CAAC;QACtD,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,EAAE;YACxC;;AAGF,QAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;YAC1B;;AAEF,QAAA,MAAM,GAAG,GAAG,OAAO,GAAG,OAAO;;AAE7B,QAAA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA,EAAA,GAAA,IAAI,CAAC,uBAAuB,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,CAAC,IAAI,GAAG,CAAC,GAAG,EAAE,EAAE;YAC5D;;;;AAKF,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,YAAY,EAAE;QACjC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5B,YAAA,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EACpE;;AAEF,YAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;;;;;AAOC,IAAA,gBAAgB,CAC1C,CAAwE,EAAA;;AAExE,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;AAClD,QAAA,IAAI,SAAS,CAAC,gBAAgB,EAAE;YAC9B,CAAC,CAAC,cAAc,EAAE;YAClB;;QAEF,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,0CAAE,KAAK,CAAC,IAAI,CAAC,OAAO,EAChC,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,CAAC,CAAC,MAAM,CAAA,EACR,SAAS,CAAC,MAAM,EACnB;;IAGsB,YAAY,GAAA;;QACpC,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,GAAG,EAAE;;AAGA,IAAA,gBAAgB,CAAC,CAAsE,EAAA;;AAC/G,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;;IAGf,SAAS,CAAC,EAAE,MAAM,EAA6B,EAAA;;QACxE,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,IAAI,CAAC,MAAM,CAAC;;AAGL,IAAA,cAAc,CAAC,CAAoB,EAAA;;AAC7D,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;;IAGb,MAAM,UAAU,CACvC,CAAqC,EAAA;;AAErC,QAAA,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;QACnE,MAAM,OAAO,EAAE;;QAEf,IAAI,CAAC,gBAAgB,EAAE;YACrB,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,WAAW,CAAC,MAAM,CAAC;;;;;;;;;AAUtC,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;;;AAIL,IAAA,WAAW,CACnC,CAA2E,EAAA;AAE3E,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAElC,QAAA,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;QACxE,IAAI,gBAAgB,EAAE;YACpB,CAAC,CAAC,cAAc,EAAE;YAClB;;AAEF,QAAA,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;AACxD,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;;AAGE,IAAA,cAAc,CAC3C,CAAgF,EAAA;AAChF,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;AAClD,QAAA,IAAI,UAAU,CAAC,gBAAgB,EAAE;YAC/B,CAAC,CAAC,cAAc,EAAE;;AAEpB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;AAC9D,QAAA,IAAI,UAAU,CAAC,gBAAgB,EAAE;YAC/B,CAAC,CAAC,cAAc,EAAE;;;AAIC,IAAA,YAAY,CACjC,CAA4C,EAAA;;AAG5C,QAAA,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;QAChE,IAAI,gBAAgB,EAAE;YACpB,CAAC,CAAC,cAAc,EAAE;;;AAIO,IAAA,aAAa,CACxC,CAAkC,EAAA;AAElC,QAAA,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAC7C,CAAC,CAAC,MAAM,CAAC,MAAM,CAClB,EAAA,EAAA,aAAa,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,EAAA,CAAA,CACrC;QACF,IAAI,gBAAgB,EAAE;YACpB,CAAC,CAAC,cAAc,EAAE;;;AAIS,IAAA,WAAW,CACxC,CAAqC,EAAA;AAErC,QAAA,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;AAChE,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,gBAAgB,EAAE;YACtC,CAAC,CAAC,cAAc,EAAE;;;;;IA+BA,kBAAkB,GAAA;;AAEtC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;;AAGhB,IAAA,aAAa,CAC7B,MAAA,GAA6C,EAAE,EAC/C,QAA2D,GAAA,SAAS,EACpE,WAAA,GAAsB,SAAS,EAC/B,IAAI,GAAG,KAAK,EAAA;QAEZ,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACnD;;AAEF,QAAA,MAAM,YAAY,GAAG,UAAU,CAC7B,MAAM,EACN,CAAC,EACD,IAAI,CAAC,WAAW,CACjB;QACD,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC;AAC/D,QAAA,IAAI,cAAc,CAAC,gBAAgB,EAAE;YACnC;;AAEF,QAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CACpC,cAAc,CAAC,MAAM,CAAC,OAAO,EAC7B,IAAI,CAAC,eAAe,EACpB,IAAI,CACL;QACD,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC;AACpE,QAAA,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;YACrC;;AAEF,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACvE,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACxB,OAAO;YACP,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAiB,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,KAAI;AAC/F,gBAAA,GAAG,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK;AACxB,gBAAA,OAAO,GAAG;aACX,EAAE,EAAE,CAAC;AACP,SAAA,CAAC;;AAGsB,IAAA,sBAAsB,CAC9C,MAAM,GAAG,KAAK,EACd,OAAO,GAAG,KAAK,EAAA;AAEf,QAAA,IAAI,MAAM,KAAK,OAAO,EAAE;YACtB;;AAEF,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;;AAGhB,IAAA,cAAc,CAAC,CAAS,EAAA;AACxC,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B;;;AAGF,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC;AAClE,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,SAAS,EAAE,IAAI,CAAC;;IAG/D,YAAY,CAC1B,CAAQ,EACR,CAAS,EACT,EAAE,GAAG,OAAO,EACZ,IAAI,GAAG,KAAK,EAAA;AAEZ,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B;;AAEF,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAChC,EAAE,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAC7C,OAAO,CACR;AACD,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAChC,EAAE,cAAc,EAAE,IAAI,CAAC,OAAO,EAAE,EAChC,OAAO,CACR;;QAED,IAAI,CAAC,IAAI,EAAE;;AAET,YAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAChC,EAAE,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAC7C,OAAO,CACR;AACD,YAAA,IAAI,CAAC,aAAa;;YAEhB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,EACnB,OAAO,EACP,IAAI,CACL;;AAEH,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;;AAMhC,IAAA,iBAAiB,CACf,MAAc,GAAA,EAAE,EAChB,CAAkB,EAClB,SAAiB,EAAA;AAEjB,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB;;QAEF,IAAI,IAAI,GAAkB,OAAO;QACjC,QAAQ,SAAS;AACf,YAAA,KAAK,oBAAoB;gBACvB,IAAI,GAAG,WAAW;gBAClB;AACF,YAAA,KAAK,iBAAiB;gBACpB,IAAI,GAAG,aAAa;gBACpB;AACF,YAAA,KAAK,QAAQ;gBACX,IAAI,GAAG,OAAO;AACd;;AAEG;AACH,gBAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;oBAChD,IAAI;AACJ,oBAAA,MAAM,EAAE,MAAM;AACf,iBAAA,CAAC;AACF,gBAAA,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,MAAa;gBAC7C;;AAEJ,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAChD,IAAI;AACJ,YAAA,MAAM,EAAE,MAAM;AACf,SAAA,CAAC;QACF,MAAM,SAAS,GAAG,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC;AACpD,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC;AAEhE;;AAEG;AACH,QAAA,IAAI,SAAS,KAAK,QAAQ,EAAE;AAC1B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBACvB,IAAI;AACJ,gBAAA,MAAM,EAAE,MAAM;AACf,aAAA,CAAC;;AAEJ,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACvB,IAAI;AACJ,YAAA,MAAM,EAAE,MAAM;AACf,SAAA,CAAC;;AAGsB,IAAA,sBAAsB,CAC9C,MAAM,GAAG,KAAK,EACd,OAAO,GAAG,KAAK,EAAA;AAEf,QAAA,IAAI,MAAM,KAAK,OAAO,EAAE;YACtB;;AAEF,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC;;IAGnC,aAAa,CACpC,KAAsB,EACtB,MAAwB,EACxB,UAAmB,EACnB,WAAW,GAAG,IAAI,EAAA;;QAGlB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACjD;;AAEF,QAAA,MAAM,EACJ,MAAM,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAC1C,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;AAChC,YAAA,IAAI,EAAE,KAAK;AACX,YAAA,OAAO,EAAE,MAAM;AAChB,SAAA,CAAC;;AAEF,QAAA,MAAM,OAAO,GAAG,mBAAmB,CAAC,MAAM,CAAC;;QAE3C,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,MAAM,GAAG,yBAAyB,CAAC,MAAM,CAAC;;AAEhD,YAAA,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE;AACtB,gBAAA,IAAI,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;oBAC5B,MAAM,IAAI,GAAG,CAAkB;oBAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC;AAC5C,oBAAA,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM;oBACrD,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC;;;;;AAK1D,QAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;;AACrB,YAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC;;AAE3B,YAAA,IAAI,QAAQ,IAAI,WAAW,EAAE;gBAC3B,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,0CAAE,cAAc,CAAC,CAAC,EAAE,CAAA,QAAQ,KAAR,IAAA,IAAA,QAAQ,uBAAR,QAAQ,CAAE,KAAK,KAAI,EAAE,CAAC;;AAEpE,SAAC,CAAC;;IAGkB,kBAAkB,CACtC,SAAkC,EAAE,EAAA;AAEpC,QAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;;AAEzB;;AAEG;IACgB,eAAe,CAAC,SAA0B,EAAE,EAAA;;AAC7D,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,WAAW,CAAC,MAAM,IAAI,EAAE,CAAC;;AAE7E;;AAEG;AACe,IAAA,YAAY,CAAC,SAA2B,EAAA;QACxD,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACnG;;AAEF,QAAA,IAAI,SAAS,KAAK,OAAO,EAAE;YACzB,SAAS,GAAG,KAAK;;AAGnB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE;QACvC,IAAI,CAAC,UAAU,EAAE;YACf;;QAEF,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,aAAa,CAAC;AAC5D,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS,KAAK,MAAM,EAAE;YACzE,IAAI,CAAC,OAAO,EAAE;AACZ,gBAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;;AAC9D,iBAAA,IAAI,eAAe,CAAC,OAAO,CAAC,EAAE;gBACnC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;;;aAEtD,IAAI,OAAO,EAAE;AAClB,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC;;;AAIrB,IAAA,WAAW,CAAC,GAAiC,EAAA;AAC5D,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC;;AAGlB,IAAA,YAAY,CAAC,GAAmB,EAAA;AAChD,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC;;AAGhB,IAAA,gBAAgB,CAAC,UAAiC,EAAA;AACrE,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;;AAGzC;;AAEG;IACqB,qBAAqB,CAAC,WAA4C,EAAE,EAAA;AAC1F,QAAA,IAAI,CAAC,aAAa,GAAG,QAAQ;;AAGN,IAAA,qBAAqB,CAAC,IAAS,EAAA;AACtD,QAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;;AAGvC;;AAEG;AACe,IAAA,cAAc,CAAC,OAAA,GAAwB,EAAE,EAAE,WAA0B,EAAA;AACrF,QAAA,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;;;;IAM9F,UAAU,GAAA;;QAEhB,IAAI,CAAC,aAAa,EAAE;;AAEpB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE;QACvC,IAAI,CAAC,UAAU,EAAE;YACf;;;AAIF,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;;AAE/B,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC;;AAG3B,IAAA,cAAc,CAAC,UAA2B,EAAA;AAChD,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;;;AAIlE,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAC7C,IAAI,CAAC,OAAO,EACZ,UAAU,EACV,OAAO,IAAI,CAAC,cAAc,KAAK;kBAC3B,IAAI,CAAC;AACP,kBAAE,SAAS,CACd,CAAC;;;AAIJ,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CACpB,IAAI,YAAY,CACd,IAAI,CAAC,OAAO,EACZ,UAAU,EACV,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,SAAS,CAC1D,CACF;;;AAIH,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;;;AAIxE,QAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;;AAGnE,QAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AACvE,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;;;IAI1E,aAAa,GAAA;QACX,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACpI;;;AAIF,QAAA,MAAM,UAAU,GAAoB;YAClC,IAAI,EAAE,IAAI,CAAC,YAAY;YACvB,MAAM,EAAE,IAAI,CAAC,cAAc;YAC3B,SAAS,EAAE,IAAI,CAAC,iBAAiB;YACjC,QAAQ,EAAE,IAAI,CAAC,gBAAgB;YAC/B,SAAS,EAAE,IAAI,CAAC,uBAAuB;YACvC,OAAO,EAAE,IAAI,CAAC,aAAa;SAC5B;AAED,QAAA,OAAO,UAAU;;IAGX,aAAa,GAAA;AACnB,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;;;;IAM9B,iBAAiB,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,UAAU,EAAE;;AAEnB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;;AAGrB;;;;;AAKG;IACH,iBAAiB,GAAA;;;AAEf,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,EAAE;AAC9C,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,SAAA,CAAC;QACF,IAAI,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,EAAE;AACpE,YAAA,eAAe,EAAE,CAAC,CAAqB,KACrC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;AAClC,SAAA,CAAC;AACF,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,kBAAkB,EAAE;AAC9C,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,uBAAuB,EAAE;QAC5D,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC;;AAG5D,QAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,aAAa,CAAC;;QAG9C,IAAI,CAAC,UAAU,EAAE;;AAGjB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC;AAC/B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC;AACzD,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC;QAC5D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;QACxD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,EAAE,iBAAiB,CAAC;QAC1E,IAAI,CAAC,iBAAiB,CACpB,IAAI,CAAC,kBAAkB,EACvB,SAAS,EACT,oBAAoB,CACrB;AACD,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,CAAA,EAAA,GAAA,IAAI,CAAC,WAAW,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AAClD,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC;;AAE3C,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC;;AAGvC,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC;;;QAIrC,IAAI,CAAC,gBAAgB,GAAG,IAAI,oBAAoB,CAC9C,CAAC,CAAsB,KAAI;;YACzB,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,qBAAqB,CAAC;gBAC5C,UAAU,EAAE,CAAC,CAAC,UAAU;gBACxB,IAAI,EAAE,CAAC,CAAC,SAAS;AAClB,aAAA,CAAC;AACF,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7B,SAAC,CACF;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;;AAEzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;IAGtB,mBAAmB,GAAA;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE;AAC1C,QAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;AAC1B,YAAA,OAAO,KAAK;;QAEd,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC;;IAG3C,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;;IAG7B,MAAM,GAAA;QACJ,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACpI;;AAEF,QAAA,MAAM,aAAa,GACjB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC;;AAG9D,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,eAAe,CACjC;YACE,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;YACrD,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,eAAe,EAAE,IAAI,CAAC,eAAe;AACrC,YAAA,MAAM,EAAE,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;SAC5C,EACD,aAAa,CACd;;AAGD;;;;;AAKG;QAEH,MAAM,gBAAgB,GAAwB,EAAE;;AAGhD,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE;YACnD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;YACxC,gBAAgB,CAAC,IAAI,CACnB,CACE,CAAA,oBAAA,EAAA,EAAA,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,MAAM,EAAE,aAAa,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,SAAS,EAAE,OAAO,CAAC,SAAS,EAC5B,UAAU,EAAE,OAAO,CAAC,UAAU,EAC9B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EACvC,eAAe,EACb,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,SAAS,EAEnE,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAe,KACvC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,EAEnD,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAe,KAChC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,EAAE,WAAW,CAAC,EAEvD,CAAA,CACH;;;AAIH,QAAA,MAAM,QAAQ,GAAG,cAAc,EAAE;QACjC,MAAM,YAAY,GAAY,EAAE;;QAGhC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACtC,YAAA,MAAM,gBAAgB,mCACjB,IAAI,CAAC,UAAU,CAClB,EAAA,EAAA,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,cAAc,EAAE,IAAI,CAAC,oBAAoB,EACzC,SAAS,EAAE,IAAI,CAAC,MAAM,EACtB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,EAAA,CAC5B;;AAED,YAAA,MAAM,SAAS,GAAY;AACzB,gBAAA,CAAA,CAAA,eAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAmB,gBAAgB,EAAA,EAAE,IAAI,EAAE,WAAW,EAAI,CAAA,CAAA;aAC3D;;AAGD,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,IAAG;gBAC5B,MAAM,GAAG,GAAG,CAAA,EAAG,IAAI,CAAC,IAAI,CAAA,CAAA,EAAI,IAAI,CAAC,IAAI,CAAA,CAAE;gBACvC,MAAM,QAAQ,IACZ,CAAA,CAAA,0BAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACM,IAAI,EAAA,EACR,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,EACrC,cAAc,EAAE,QAAQ,EACxB,WAAW,EAAE,MAAK,EAAC,IAAA,EAAA,CAAA,CAAA,OAAA,CAAA,EAAA,GAAA,IAAI,CAAC,uBAAuB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,SAAS,EAAE,CAAA,EAAA,EAC5D,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,YAAY,EAAE,IAAI,CAAC,YAAY,EAC/B,mBAAmB,EAAE,IAAI,CAAC,YAAY,EACtC,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,mBAAmB,EAAE,CAAC,CAAC,KAAI,EAAC,IAAA,EAAA,CAAA,CAAA,OAAA,CAAA,EAAA,GAAA,IAAI,CAAC,uBAAuB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA,EAAA,EACvF,YAAY,EAAE,QAAM,IAAA,EAAA,CAAA,CAAA,OAAA,CAAA,EAAA,GAAA,IAAI,CAAC,uBAAuB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,OAAO,CAAC,KAAK,CAAC,CAAA,EAAA,EAChE,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,KAAI;;wBACxB,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/C,wBAAA,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;AAC3B,4BAAA,CAAA,EAAA,GAAA,IAAI,CAAC,uBAAuB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC;;qBAEpD,EAAA,CAAA,EAED,CACM,CAAA,aAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,IACR,OAAO,EAAE,IAAI,CAAC,IAAI,EAClB,GAAG,EAAE,GAAG,EACR,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EACzC,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EACvC,IAAI,EAAE,SAAS,EAAA,CAAA,EAEf,CAAA,CAAA,MAAA,EAAA,EAAM,IAAI,EAAE,CAAA,KAAA,EAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAE,CAAA,EAAA,CAAI,CACpC,EACd,CAAA,CAAA,mBAAA,EAAA,EACE,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,YAAY,EAAE,IAAI,CAAC,YAAY,EAC/B,YAAY,EAAE,IAAI,CAAC,YAAY,EAC/B,CAAA,EACF,CACE,CAAA,cAAA,EAAA,EAAA,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,aAAa,EAAE,IAAI,CAAC,aAAa,EACjC,OAAO,EAAE,IAAI,CAAC,IAAI,EAClB,OAAO,EAAE,IAAI,CAAC,IAAI,EAClB,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,YAAY,EAAE,IAAI,CAAC,YAAY,EAC/B,YAAY,EAAE,IAAI,CAAC,YAAY,EAAA,EAE/B,CAAA,CAAA,MAAA,EAAA,EAAM,IAAI,EAAE,CAAA,MAAA,EAAS,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAE,CAAA,EAAA,CAAI,CACpC,CACU,CAC5B;AAED,gBAAA,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC1B,aAAC,CAAC;;YAGF,YAAY,CAAC,IAAI,CACf,8CACM,IAAI,CAAC,IAAI,EACb,EAAA,GAAG,EAAE,EAAE,IACL,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAE/D,gBAAgB,EAAE,CAAC,IACjB,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAEjE,sBAAsB,EAAE,CAAC,IACvB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CACvC,CAAC,CAAC,MAAM,EACR,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CACnB,KAGF,SAAS,CACa,CAC1B;;AAGH,QAAA,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC;;QAGnC,MAAM,OAAO,GAAkB,OAAO;QACtC,MAAM,OAAO,GAAkB,OAAO;AAEtC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM;AAC9C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM;QAEhD,MAAM,cAAc,IAClB,6BACE,KAAK,EAAC,UAAU,EAChB,SAAS,EAAE,OAAO,EAClB,UAAU,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,EACtD,WAAW,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,EACxD,QAAQ,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,EACnD,GAAG,EAAE,EAAE,IAAI,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,EAAE,WAAW,CAAC,EACjE,eAAe,EAAE,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,EAAA,CACjE,CACH;QAED,MAAM,gBAAgB,IACpB,6BACI,KAAK,EAAC,YAAY,EAClB,SAAS,EAAE,OAAO,EAClB,UAAU,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,EACtD,WAAW,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,EACxD,QAAQ,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,EACnD,GAAG,EAAE,EAAE,IAAI,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,EAAE,WAAW,CAAC,EACjE,eAAe,EAAE,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,EAAA,CACjE,CACL;QAED,QACE,EAAC,IAAI,EAAA,IAAA,EACF,IAAI,CAAC,eAAe,GAAG,IAAI,IAC1B,CAAoB,CAAA,oBAAA,EAAA,EAAA,KAAK,EAAC,aAAa,GAAG,CAC3C,EACD,CAAM,CAAA,MAAA,EAAA,EAAA,IAAI,EAAC,QAAQ,EAAG,CAAA,EACtB,CACE,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,eAAe,EACrB,OAAO,EAAE,CAAC,CAAa,KAAI;;gBACzB,IAAI,CAAC,CAAC,aAAa,KAAK,CAAC,CAAC,MAAM,EAAE;oBAChC,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,SAAS,EAAE;;AAE9B,aAAC,EAAA,EAED,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,WAAW,EAAA,EACpB,CAAM,CAAA,MAAA,EAAA,EAAA,IAAI,EAAC,UAAU,EAAG,CAAA,EACvB,gBAAgB,EAChB,cAAc,EACf,CAAA,CAAC,aAAa,EAAC,EAAA,GAAG,EAAE,CAAC,KAAK,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,EAAA,CAAI,CAChD,CACF,EACL,gBAAgB,EACjB,CAAA,CAAA,cAAA,EAAA,EAAc,GAAG,EAAE,EAAE,KAAK,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAI,CAAA,EAChF,CAAA,CAAA,MAAA,EAAA,EAAM,IAAI,EAAC,QAAQ,EAAG,CAAA,CACjB;;IAIX,oBAAoB,GAAA;;;QAGlB,IAAI,CAAC,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["each", "gt", "rsAstralRange", "rsComboMarksRange", "reComboHalfMarksRange", "rsComboSymbolsRange", "rsComboRange", "rsVarRange", "rsZWJ", "__stencil_proxyCustomElement", "HTMLElement"], "sources": ["src/themeManager/theme.compact.ts", "src/themeManager/theme.default.ts", "src/themeManager/theme.material.ts", "src/themeManager/theme.service.ts", "src/store/dimension/dimension.recalculate.plugin.ts", "src/store/dimension/dimension.trim.plugin.ts", "src/store/dimension/dimension.store.ts", "src/plugins/base.plugin.ts", "node_modules/lodash/_arrayEach.js", "node_modules/lodash/_castFunction.js", "node_modules/lodash/forEach.js", "src/plugins/column.auto-size.plugin.ts", "src/plugins/column.stretch.plugin.ts", "node_modules/lodash/_baseClamp.js", "node_modules/lodash/toLength.js", "node_modules/lodash/_baseFill.js", "node_modules/lodash/fill.js", "src/plugins/export/csv.ts", "src/plugins/export/export.plugin.ts", "src/plugins/filter/conditions/equal.ts", "src/plugins/filter/conditions/number/greaterThan.ts", "src/plugins/filter/conditions/number/greaterThanOrEqual.ts", "src/plugins/filter/conditions/number/lessThan.ts", "src/plugins/filter/conditions/number/lessThanOrEqual.ts", "src/plugins/filter/conditions/set.ts", "src/plugins/filter/conditions/string/beginswith.ts", "src/plugins/filter/conditions/string/contains.ts", "src/plugins/filter/filter.indexed.ts", "src/plugins/filter/filter.plugin.tsx", "node_modules/lodash/isString.js", "node_modules/lodash/_asciiSize.js", "node_modules/lodash/_hasUnicode.js", "node_modules/lodash/_unicodeSize.js", "node_modules/lodash/_stringSize.js", "node_modules/lodash/size.js", "src/plugins/sorting/sorting.func.ts", "src/plugins/sorting/sorting.plugin.ts", "src/plugins/groupingRow/grouping.row.expand.service.ts", "src/plugins/groupingRow/grouping.trimmed.service.ts", "src/plugins/groupingRow/grouping.row.plugin.ts", "src/plugins/moveColumn/order-column.handler.tsx", "src/plugins/moveColumn/column.drag.plugin.ts", "src/services/column.data.provider.ts", "src/services/data.provider.ts", "src/services/dimension.provider.ts", "src/services/viewport.provider.ts", "src/components/revoGrid/viewport.service.ts", "src/components/revoGrid/viewport.scrolling.service.ts", "src/components/order/order-renderer.tsx", "src/components/revoGrid/grid.helpers.ts", "src/utils/mobile.ts", "src/plugins/wcag/index.ts", "src/components/revoGrid/plugin.service.ts", "src/components/revoGrid/revo-grid-style.scss?tag=revo-grid", "src/components/revoGrid/revo-grid.tsx"], "sourcesContent": ["import { ThemePackage } from '../types/theme';\n\nexport class ThemeCompact implements ThemePackage {\n  defaultRowSize = 32;\n}\n", "import { ThemePackage } from '../types/theme';\n\nexport class ThemeDefault implements ThemePackage {\n  defaultRowSize = 27;\n}\n", "import { ThemePackage } from '../types/theme';\n\nexport class ThemeMaterial implements ThemePackage {\n  defaultRowSize = 42;\n}\n", "import type { Theme, ThemeConfig, ThemePackage } from '../types/theme';\nimport { ThemeCompact } from './theme.compact';\nimport { ThemeDefault } from './theme.default';\nimport { ThemeMaterial } from './theme.material';\n\nexport const DEFAULT_THEME = 'default';\n\nexport const allowedThemes: Theme[] = [\n  DEFAULT_THEME,\n  'material',\n  'compact',\n  'darkMaterial',\n  'darkCompact',\n];\nexport default class ThemeService {\n  private currentTheme: ThemePackage;\n  private customRowSize = 0;\n\n  get theme() {\n    return this.currentTheme;\n  }\n\n  get rowSize() {\n    return this.customRowSize || this.currentTheme.defaultRowSize;\n  }\n\n  set rowSize(size: number) {\n    this.customRowSize = size;\n  }\n\n  constructor(cfg: ThemeConfig) {\n    this.customRowSize = cfg.rowSize;\n    this.register('default');\n  }\n\n  register(theme: Theme) {\n    const parsedTheme = getTheme(theme);\n    switch (parsedTheme) {\n      case 'material':\n      case 'darkMaterial':\n        this.currentTheme = new ThemeMaterial();\n        break;\n      case 'compact':\n      case 'darkCompact':\n        this.currentTheme = new ThemeCompact();\n        break;\n      default:\n        this.currentTheme = new ThemeDefault();\n        break;\n    }\n  }\n}\n\n\nexport function getTheme(theme?: string | null): Theme {\n  if (theme && allowedThemes.indexOf(theme as Theme) > -1) {\n    return theme as Theme;\n  }\n  return DEFAULT_THEME;\n}", "import { DimensionSettingsState } from '@type';\nimport type { Observable, PluginSubscribe } from '../../utils';\n\n/**\n * Plugin which recalculates realSize on changes of sizes, originItemSize and count\n */\nexport const recalculateRealSizePlugin = (storeService: {\n  store: Observable<DimensionSettingsState>;\n  setStore: (k: Partial<DimensionSettingsState>) => void;\n}): PluginSubscribe<DimensionSettingsState> => {\n  /**\n   * Recalculates realSize if size, origin size or count changes\n   */\n  return {\n    /**\n     * Reacts on changes of count, sizes and originItemSize\n     */\n    set(k) {\n      switch (k) {\n        case 'count':\n        case 'sizes':\n        case 'originItemSize': {\n          // recalculate realSize\n          let realSize = 0;\n          const count = storeService.store.get('count');\n          for (let i = 0; i < count; i++) {\n            realSize +=\n              storeService.store.get('sizes')[i] ||\n              storeService.store.get('originItemSize');\n          }\n          storeService.setStore({ realSize });\n          break;\n        }\n      }\n    },\n  };\n};\n", "import { DimensionSettingsState } from '@type';\nimport type { Observable, PluginSubscribe } from '../../utils';\n/**\n * Plugin for trimming\n *\n * 1.a. Retrieves the previous sizes value. Saves the resulting trimmed data as a new sizes value.\n * 1.b. Stores a reference to the trimmed data to prevent further changes.\n * 2. Removes multiple and shifts the data based on the trimmed value.\n */\nexport const trimmedPlugin = (storeService: {\n  store: Observable<DimensionSettingsState>;\n  setSizes: (k: DimensionSettingsState['sizes']) => void;\n}): PluginSubscribe<DimensionSettingsState> => {\n  let trimmingObject: DimensionSettingsState['sizes'] | null = null;\n  let trimmedPreviousSizes: DimensionSettingsState['sizes'] | null = null;\n\n  return {\n    set(key, val) {\n      switch (key) {\n        case 'sizes': {\n          // prevent changes after trimming\n          if (trimmingObject && trimmingObject === val) {\n            trimmingObject = null;\n            return;\n          }\n          trimmedPreviousSizes = null;\n          break;\n        }\n        case 'trimmed': {\n          const trim = val as DimensionSettingsState['trimmed'];\n          if (!trimmedPreviousSizes) {\n            trimmedPreviousSizes = storeService.store.get('sizes');\n          }\n\n          trimmingObject = removeMultipleAndShift(\n            trimmedPreviousSizes,\n            trim || {},\n          );\n          // save a reference to the trimmed object to prevent changes after trimming\n          storeService.setSizes(trimmingObject);\n          break;\n        }\n      }\n    },\n  };\n};\n\nfunction removeMultipleAndShift<T1, T2>(\n  items: { [virtialIndex: number]: T1 },\n  toRemove: { [virtialIndex: number]: T2 },\n): { [virtialIndex: number]: T1 } {\n  const newItems: { [virtialIndex: number]: T1 } = {};\n  const sortedIndexes = Object.keys(items || {})\n    .map(Number)\n    .sort((a, b) => a - b);\n  const lastIndex = sortedIndexes[sortedIndexes.length - 1];\n  let shift = 0;\n  for (let i = 0; i <= lastIndex; i++) {\n    if (toRemove[i] !== undefined) {\n      shift++;\n\n      // skip already removed\n      if (items[i] !== undefined) {\n        continue;\n      }\n    }\n    if (items[i] !== undefined) {\n      newItems[i - shift] = items[i];\n    }\n  }\n  return newItems;\n}\n", "/**\n * Storing pre-calculated\n * Dimension information and sizes\n */\nimport reduce from 'lodash/reduce';\nimport { createStore } from '@stencil/store';\n\nimport { setStore, Observable } from '../../utils';\nimport { calculateDimensionData } from './dimension.helpers';\nimport {\n  DimensionCalc,\n  DimensionSettingsState,\n  ViewSettingSizeProp,\n  MultiDimensionType,\n} from '@type';\nimport { recalculateRealSizePlugin } from './dimension.recalculate.plugin';\nimport { trimmedPlugin } from './dimension.trim.plugin';\n\nexport type DimensionStoreCollection = {\n  [T in MultiDimensionType]: DimensionStore;\n};\n\ntype Item = keyof DimensionSettingsState;\n\nfunction initialBase(): DimensionCalc {\n  return {\n    indexes: [],\n    count: 0,\n\n    // hidden items\n    trimmed: null,\n\n    // virtual item index to size\n    sizes: {},\n    // order in indexes[] to coordinate\n    positionIndexToItem: {},\n    // initial element to coordinate ^\n    indexToItem: {},\n    positionIndexes: [],\n  };\n}\n\nfunction initialState(): DimensionSettingsState {\n  return {\n    ...initialBase(),\n    // size which all items can take\n    realSize: 0,\n\n    // initial item size if it wasn't changed\n    originItemSize: 0,\n  };\n}\n\nexport class DimensionStore {\n  readonly store: Observable<DimensionSettingsState>;\n  constructor(public readonly type: MultiDimensionType) {\n    this.store = createStore(initialState());\n    this.store.use(trimmedPlugin({\n      store: this.store,\n      setSizes: this.setDimensionSize.bind(this),\n    }));\n    this.store.use(recalculateRealSizePlugin({\n      store: this.store,\n      setStore: this.setStore.bind(this),\n    }));\n  }\n\n  getCurrentState(): DimensionSettingsState {\n    const state = initialState();\n    const keys = Object.keys(state);\n    return reduce(\n      keys,\n      (r: DimensionSettingsState, k: Item) => {\n        const data = this.store.get(k);\n        r[k] = data as never;\n        return r;\n      },\n      state,\n    );\n  }\n\n  dispose() {\n    setStore(this.store, initialState());\n  }\n\n  setStore<T extends Record<string, any>>(data: Partial<T>) {\n    setStore(this.store, data);\n  }\n\n  drop() {\n    setStore(this.store, initialBase());\n  }\n\n  /**\n   * Set custom dimension sizes and overwrite old\n   * Generates new indexes based on sizes\n   * @param sizes - sizes to set\n   */\n  setDimensionSize(sizes: ViewSettingSizeProp = {}) {\n    const dimensionData = calculateDimensionData(\n      this.store.get('originItemSize'),\n      sizes,\n    );\n    setStore(this.store, {\n      ...dimensionData,\n      sizes,\n    });\n  }\n\n  updateSizesPositionByIndexes(newItemsOrder: number[], prevItemsOrder: number[] = []) {\n    // Move custom sizes to new order\n    const customSizes = {...this.store.get('sizes')};\n    if (!Object.keys(customSizes).length) {\n      return;\n    }\n    // Step 1: Create a map of original indices, but allow duplicates by storing arrays of indices\n    const originalIndices: Record<number, number[]> = {};\n    prevItemsOrder.forEach((physIndex, virtIndex) => {\n      if (!originalIndices[physIndex]) {\n        originalIndices[physIndex] = [];\n      }\n      originalIndices[physIndex].push(virtIndex); // Store all indices for each value\n    });\n\n    // Step 2: Create new sizes based on new item order\n    const newSizes: Record<number, number> = {};\n\n    newItemsOrder.forEach((physIndex, virtIndex) => {\n      const indices = originalIndices[physIndex]; // Get all original indices for this value\n      \n      if (indices && indices.length > 0) {\n        const originalIndex = indices.shift(); // Get the first available original index\n\n        if (originalIndex !== undefined && originalIndex !== virtIndex && customSizes[originalIndex]) {\n          newSizes[virtIndex] = customSizes[originalIndex];\n          delete customSizes[originalIndex];\n        }\n      }\n    });\n\n    // Step 3: Set new sizes if there are changes\n    if (Object.keys(newSizes).length) {\n      this.setDimensionSize({\n        ...customSizes,\n        ...newSizes,\n      });\n    }\n  }\n}\n", "import { h } from '@stencil/core';\nimport type { PluginProviders, PluginBaseComponent } from '../types';\n\n\nexport type WatchConfig = { immediate: boolean };\n\n/**\n * Base layer for plugins\n * Provide minimal starting core for plugins to work\n * Extend this class to create plugin\n */\nexport class BasePlugin implements PluginBaseComponent {\n  readonly h = h;\n  readonly subscriptions: Record<string, (...args: any[]) => void> = {};\n  constructor(public revogrid: HTMLRevoGridElement, public providers: PluginProviders) {}\n  /**\n   *\n   * @param eventName - event name to subscribe to in revo-grid component (e.g. 'beforeheaderclick')\n   * @param callback - callback function for event\n   */\n  addEventListener<K extends keyof HTMLRevoGridElementEventMap>(\n    eventName: K,\n    callback: (this: BasePlugin, e: CustomEvent<HTMLRevoGridElementEventMap[K]>) => void,\n  ) {\n    this.revogrid.addEventListener(eventName as string, callback);\n    this.subscriptions[eventName as string] = callback;\n  }\n\n  /**\n   * Subscribe to property change in revo-grid component\n   * You can return false in callback to prevent default value set\n   *\n   * @param prop - property name\n   * @param callback - callback function\n   * @param immediate - trigger callback immediately with current value\n   */\n  watch<T extends any>(\n    prop: string,\n    callback: (arg: T) => boolean | void,\n    { immediate }: Partial<WatchConfig> = { immediate: false },\n  ) {\n    const nativeValueDesc =\n      Object.getOwnPropertyDescriptor(this.revogrid, prop) ||\n      Object.getOwnPropertyDescriptor(this.revogrid.constructor.prototype, prop);\n\n    // Overwrite property descriptor for this instance\n    Object.defineProperty(this.revogrid, prop, {\n      set(val: T) {\n        const keepDefault = callback(val);\n        if (keepDefault === false) {\n          return;\n        }\n        // Continue with native behavior\n        return nativeValueDesc?.set?.call(this, val);\n      },\n      get() {\n        // Continue with native behavior\n        return nativeValueDesc?.get?.call(this);\n      },\n    });\n    if (immediate) {\n      callback(nativeValueDesc?.value);\n    }\n  }\n\n  /**\n   * Remove event listener\n   * @param eventName\n   */\n  removeEventListener(eventName: string) {\n    this.revogrid.removeEventListener(eventName, this.subscriptions[eventName]);\n    delete this.subscriptions[eventName];\n  }\n\n  /**\n   * Emit event from revo-grid component\n   * Event can be cancelled by calling event.preventDefault() in callback\n   */\n  emit<T = any>(eventName: string, detail?: T) {\n    const event = new CustomEvent<T>(eventName, { detail, cancelable: true });\n    this.revogrid.dispatchEvent(event);\n    return event;\n  }\n\n  /**\n   * Clear all subscriptions\n   */\n  clearSubscriptions() {\n    for (let type in this.subscriptions) {\n      this.removeEventListener(type);\n    }\n  }\n\n  /**\n   * Destroy plugin and clear all subscriptions\n   */\n  destroy() {\n    this.clearSubscriptions();\n  }\n}\n\nexport type GridPlugin = (typeof BasePlugin);\n", "/**\n * A specialized version of `_.forEach` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns `array`.\n */\nfunction arrayEach(array, iteratee) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    if (iteratee(array[index], index, array) === false) {\n      break;\n    }\n  }\n  return array;\n}\n\nexport default arrayEach;\n", "import identity from './identity.js';\n\n/**\n * Casts `value` to `identity` if it's not a function.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {Function} Returns cast function.\n */\nfunction castFunction(value) {\n  return typeof value == 'function' ? value : identity;\n}\n\nexport default castFunction;\n", "import arrayEach from './_arrayEach.js';\nimport baseEach from './_baseEach.js';\nimport castFunction from './_castFunction.js';\nimport isArray from './isArray.js';\n\n/**\n * Iterates over elements of `collection` and invokes `iteratee` for each element.\n * The iteratee is invoked with three arguments: (value, index|key, collection).\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * **Note:** As with other \"Collections\" methods, objects with a \"length\"\n * property are iterated like arrays. To avoid this behavior use `_.forIn`\n * or `_.forOwn` for object iteration.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @alias each\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n * @see _.forEachRight\n * @example\n *\n * _.forEach([1, 2], function(value) {\n *   console.log(value);\n * });\n * // => Logs `1` then `2`.\n *\n * _.forEach({ 'a': 1, 'b': 2 }, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a' then 'b' (iteration order is not guaranteed).\n */\nfunction forEach(collection, iteratee) {\n  var func = isArray(collection) ? arrayEach : baseEach;\n  return func(collection, castFunction(iteratee));\n}\n\nexport default forEach;\n", "/**\n * Plugin module for revo-grid grid system\n * Add support for automatic column resize\n */\nimport each from 'lodash/each';\nimport reduce from 'lodash/reduce';\n\nimport { BasePlugin } from './base.plugin';\nimport { getSourceItem, columnTypes } from '@store';\nimport type {\n  DimensionCols,\n  DimensionRows,\n  ColumnRegular,\n  DataType,\n  InitialHeaderClick,\n  ViewSettingSizeProp,\n  BeforeSaveDataDetails,\n  BeforeRangeSaveDataDetails,\n  PluginProviders,\n} from '@type';\nimport { ColumnCollection, getColumnType } from '../utils/column.utils';\n\ninterface Column extends ColumnRegular {\n  index: number;\n}\n\ntype AutoSizeColumns = Record<DimensionCols, ColumnRecords>;\ntype ColumnRecords = Record<any, Column>;\ntype SourceSetEvent = { type: DimensionRows; source: DataType[] };\ntype EditEvent = BeforeSaveDataDetails | BeforeRangeSaveDataDetails;\ntype Resolve = (cols: Partial<AutoSizeColumns>) => void;\ntype Reject = () => void;\n\nexport type AutoSizeColumnConfig = {\n  // ui behavior mode\n  mode?: ColumnAutoSizeMode;\n  /**\n   * autoSize for all columns\n   * if allColumnes true all columns treated as autoSize, worse for performance\n   * false by default\n   */\n  allColumns?: boolean;\n  /**\n   * assumption per characted size\n   * by default defined as 8, can be changed in this config\n   */\n  letterBlockSize?: number;\n  /** make size calculation exact\n   * by default it based on assumption each character takes some space defined in letterBlockSize */\n  preciseSize?: boolean;\n};\n\nconst LETTER_BLOCK_SIZE = 7;\n\nexport const enum ColumnAutoSizeMode {\n  // increases column width on header click according the largest text value\n  headerClickAutosize = 'headerClickAutoSize',\n  // increases column width on data set and text edit, decreases performance\n  autoSizeOnTextOverlap = 'autoSizeOnTextOverlap',\n  // increases and decreases column width based on all items sizes, worst for performance\n  autoSizeAll = 'autoSizeAll',\n}\n\nexport class AutoSizeColumnPlugin extends BasePlugin {\n  autoSizeColumns: Partial<AutoSizeColumns> | null = null;\n  readonly letterBlockSize: number;\n\n  /** for config option when preciseSize enabled */\n  readonly precsizeCalculationArea: HTMLElement;\n\n  /** for edge case when no columns defined before data */\n  dataResolve: Resolve | null = null;\n  dataReject: Reject | null = null;\n\n  constructor(\n    revogrid: HTMLRevoGridElement,\n    public providers: PluginProviders,\n    public config?: AutoSizeColumnConfig,\n  ) {\n    super(revogrid, providers);\n    this.letterBlockSize = config?.letterBlockSize || LETTER_BLOCK_SIZE;\n\n    // create test container to check text width\n    if (config?.preciseSize) {\n      this.precsizeCalculationArea = this.initiatePresizeElement();\n      revogrid.appendChild(this.precsizeCalculationArea);\n    }\n\n    const aftersourceset = ({\n      detail: { source },\n    }: CustomEvent<SourceSetEvent>) => {\n      this.setSource(source);\n    };\n    const beforecolumnsset = ({\n      detail: { columns },\n    }: CustomEvent<ColumnCollection>) => {\n      this.columnSet(columns);\n    };\n    this.addEventListener('beforecolumnsset', beforecolumnsset);\n    switch (config?.mode) {\n      case ColumnAutoSizeMode.autoSizeOnTextOverlap:\n        this.addEventListener('aftersourceset', aftersourceset);\n        this.addEventListener('afteredit',  ({ detail }: CustomEvent<EditEvent>) => {\n          this.afteredit(detail);\n        });\n        break;\n      case ColumnAutoSizeMode.autoSizeAll:\n        this.addEventListener('aftersourceset', aftersourceset);\n        this.addEventListener('afteredit', ({ detail }) => {\n          this.afterEditAll(detail);\n        });\n        break;\n      default:\n        this.addEventListener('headerdblclick', ({ detail }: CustomEvent<InitialHeaderClick>) => {\n          const type = getColumnType(detail.column);\n          const size = this.getColumnSize(detail.index, type);\n          if (size) {\n            this.providers.dimension.setCustomSizes(\n              type,\n              {\n                [detail.index]: size,\n              },\n              true,\n            );\n          }\n        });\n        break;\n    }\n  }\n\n  async setSource(source: DataType[]): Promise<void> {\n    let autoSize = this.autoSizeColumns;\n    if (this.dataReject) {\n      this.dataReject();\n      this.clearPromise();\n    }\n\n    /** If data set first and no column provided await until get one */\n    if (!autoSize) {\n      const request = new Promise((resolve: Resolve, reject: Reject) => {\n        this.dataResolve = resolve;\n        this.dataReject = reject;\n      });\n      try {\n        autoSize = await request;\n      } catch (e) {\n        return;\n      }\n    }\n\n    // calculate sizes\n    each(autoSize, (_v, type: DimensionCols) => {\n      const sizes: ViewSettingSizeProp = {};\n      each(autoSize[type], rgCol => {\n        // calculate size\n        rgCol.size = sizes[rgCol.index] = source.reduce(\n          (prev, rgRow) => Math.max(prev, this.getLength(rgRow[rgCol.prop])),\n          this.getLength(rgCol.name || ''),\n        );\n      });\n      this.providers.dimension.setCustomSizes(type, sizes, true);\n    });\n  }\n\n  getLength(len?: any): number {\n    const padding = 15;\n    if (!len) {\n      return 0;\n    }\n    try {\n      const str = len.toString();\n\n      /**if exact calculation required proxy with html element, slow operation */\n      if (this.config?.preciseSize) {\n        this.precsizeCalculationArea.innerText = str;\n        return this.precsizeCalculationArea.scrollWidth + padding * 2;\n      }\n      return str.length * this.letterBlockSize + padding * 2;\n    } catch (e) {\n      return 0;\n    }\n  }\n\n  afteredit(e: EditEvent) {\n    let data: Record<string, DataType>;\n    if (this.isRangeEdit(e)) {\n      data = e.data;\n    } else {\n      data = { 0: { [e.prop]: e.val } };\n    }\n    each(this.autoSizeColumns, (columns, type: DimensionCols) => {\n      const sizes: ViewSettingSizeProp = {};\n\n      each(columns, rgCol => {\n        // calculate size\n        const size = reduce(\n          data,\n          (prev: number | undefined, rgRow) => {\n            if (typeof rgRow[rgCol.prop] === 'undefined') {\n              return prev;\n            }\n            return Math.max(prev || 0, this.getLength(rgRow[rgCol.prop]));\n          },\n          undefined,\n        );\n\n        if (size && (rgCol.size ?? 0) < size) {\n          rgCol.size = sizes[rgCol.index] = size;\n        }\n      });\n\n      this.providers.dimension.setCustomSizes(type, sizes, true);\n    });\n  }\n\n  afterEditAll(e: EditEvent) {\n    const props: Record<any, true> = {};\n    if (this.isRangeEdit(e)) {\n      each(e.data, r => each(r, (_v, p) => (props[p] = true)));\n    } else {\n      props[e.prop] = true;\n    }\n    each(this.autoSizeColumns, (columns, type: DimensionCols) => {\n      const sizes: ViewSettingSizeProp = {};\n\n      each(columns, rgCol => {\n        if (props[rgCol.prop]) {\n          const size = this.getColumnSize(rgCol.index, type);\n          if (size) {\n            sizes[rgCol.index] = size;\n          }\n        }\n      });\n      this.providers.dimension.setCustomSizes(type, sizes, true);\n    });\n  }\n\n  getColumnSize(index: number, type: DimensionCols): number {\n    const rgCol = this.autoSizeColumns?.[type]?.[index];\n    if (!rgCol) {\n      return 0;\n    }\n    return reduce(\n      this.providers.data.stores,\n      (r, s) => {\n        const perStore = reduce(\n          s.store.get('items'),\n          (prev, _row, i) => {\n            const item = getSourceItem(s.store, i);\n            return Math.max(prev || 0, this.getLength(item?.[rgCol.prop]));\n          },\n          0,\n        );\n        return Math.max(r, perStore);\n      },\n      rgCol.size || 0,\n    );\n  }\n\n  columnSet(columns: Record<DimensionCols, ColumnRegular[]>) {\n    for (let t of columnTypes) {\n      const type = t as DimensionCols;\n      const cols = columns[type];\n\n      for (let i in cols) {\n        if (cols[i].autoSize || this.config?.allColumns) {\n          if (!this.autoSizeColumns) {\n            this.autoSizeColumns = {};\n          }\n          if (!this.autoSizeColumns[type]) {\n            this.autoSizeColumns[type] = {};\n          }\n          this.autoSizeColumns[type][i] = {\n            ...cols[i],\n            index: parseInt(i, 10),\n          };\n        }\n      }\n    }\n\n    if (this.dataResolve) {\n      this.dataResolve(this.autoSizeColumns || {});\n      this.clearPromise();\n    }\n  }\n\n  clearPromise() {\n    this.dataResolve = null;\n    this.dataReject = null;\n  }\n\n  isRangeEdit(e: EditEvent): e is BeforeRangeSaveDataDetails {\n    return !!(e as BeforeRangeSaveDataDetails).data;\n  }\n\n  initiatePresizeElement(): HTMLElement {\n    const styleForFontTest: Partial<CSSStyleDeclaration> = {\n      position: 'absolute',\n      fontSize: '14px',\n      height: '0',\n      width: '0',\n      whiteSpace: 'nowrap',\n      top: '0',\n      overflowX: 'scroll',\n      display: 'block',\n    };\n\n    const el = document.createElement('div');\n    for (let s in styleForFontTest) {\n      el.style[s] = styleForFontTest[s] ?? '';\n    }\n    el.classList.add('revo-test-container');\n    return el;\n  }\n\n  destroy() {\n    super.destroy();\n    this.precsizeCalculationArea?.remove();\n  }\n}\n", "import each from 'lodash/each';\nimport { calculateRowHeaderSize } from '../utils/row-header-utils';\nimport { getScrollbarSize } from '../utils';\nimport { BasePlugin } from './base.plugin';\nimport { DimensionCols, DimensionType, PluginBaseComponent, PluginProviders, ColumnRegular } from '@type';\nimport { ColumnCollection } from '../utils/column.utils';\n\n/**\n * This plugin serves to recalculate columns initially\n * Base on empty space if there is any\n * Currently plugin supports only increasing last column\n */\ntype ScrollChange = {\n  type: DimensionType;\n  hasScroll: boolean;\n};\ntype StretchedData = {\n  initialSize: number;\n  size: number;\n  index: number;\n};\n\nexport class StretchColumn extends BasePlugin {\n  private stretchedColumn: StretchedData | null = null;\n  private readonly scrollSize;\n  constructor(\n    revogrid: HTMLRevoGridElement,\n    public providers: PluginProviders,\n  ) {\n    super(revogrid, providers);\n\n    // calculate scroll bar size for current user session\n    this.scrollSize = getScrollbarSize(document);\n\n    // subscribe to column changes\n    const beforecolumnapplied = ({\n      detail: { columns },\n    }: CustomEvent<ColumnCollection>) => this.applyStretch(columns);\n    this.addEventListener('beforecolumnapplied', beforecolumnapplied);\n  }\n\n  private setScroll({ type, hasScroll }: ScrollChange) {\n    if (\n      type === 'rgRow' &&\n      this.stretchedColumn &&\n      this.stretchedColumn?.initialSize === this.stretchedColumn.size\n    ) {\n      if (hasScroll) {\n        this.stretchedColumn.size -= this.scrollSize;\n        this.apply();\n        this.dropChanges();\n      }\n    }\n  }\n\n  private activateChanges() {\n    const setScroll = ({ detail }: CustomEvent<ScrollChange>) =>\n      this.setScroll(detail);\n    this.addEventListener('scrollchange', setScroll);\n  }\n\n  private dropChanges() {\n    this.stretchedColumn = null;\n    this.removeEventListener('scrollchange');\n  }\n\n  private apply() {\n    if (!this.stretchedColumn) {\n      return;\n    }\n    const type: DimensionCols = 'rgCol';\n    const sizes = this.providers.dimension.stores[type].store.get('sizes');\n    this.providers.dimension.setCustomSizes(\n      type,\n      {\n        ...sizes,\n        [this.stretchedColumn.index]: this.stretchedColumn.size,\n      },\n      true,\n    );\n  }\n\n  /**\n   * Apply stretch changes\n   */\n  applyStretch(columns: Record<DimensionCols, ColumnRegular[]>) {\n    // unsubscribe from all events\n    this.dropChanges();\n    // calculate grid size\n    let sizeDifference = this.revogrid.clientWidth - 1;\n    each(columns, (_, type: DimensionCols) => {\n      const realSize =\n        this.providers.dimension.stores[type].store.get('realSize');\n      sizeDifference -= realSize;\n    });\n    if (this.revogrid.rowHeaders) {\n      const itemsLength =\n        this.providers.data.stores.rgRow.store.get('source').length;\n      const header = this.revogrid.rowHeaders;\n      const rowHeaderSize = calculateRowHeaderSize(\n        itemsLength,\n        typeof header === 'object' ? header : undefined,\n      );\n      if (rowHeaderSize) {\n        sizeDifference -= rowHeaderSize;\n      }\n    }\n    if (sizeDifference > 0) {\n      // currently plugin accepts last column only\n      const index = columns.rgCol.length - 1;\n      const last = columns.rgCol[index];\n      /**\n       * has column\n       * no auto size applied\n       * size for column shouldn't be defined\n       */\n      const colSize = last?.size || this.revogrid.colSize || 0;\n      const size = sizeDifference + colSize - 1;\n\n      if (last && !last.autoSize && colSize < size) {\n        this.stretchedColumn = {\n          initialSize: size,\n          index,\n          size,\n        };\n        this.apply();\n        this.activateChanges();\n      }\n    }\n  }\n}\n\n/**\n * Check plugin type is Stretch\n */\nexport function isStretchPlugin(\n  plugin: PluginBaseComponent | StretchColumn,\n): plugin is StretchColumn {\n  return !!(plugin as StretchColumn).applyStretch;\n}\n", "/**\n * The base implementation of `_.clamp` which doesn't coerce arguments.\n *\n * @private\n * @param {number} number The number to clamp.\n * @param {number} [lower] The lower bound.\n * @param {number} upper The upper bound.\n * @returns {number} Returns the clamped number.\n */\nfunction baseClamp(number, lower, upper) {\n  if (number === number) {\n    if (upper !== undefined) {\n      number = number <= upper ? number : upper;\n    }\n    if (lower !== undefined) {\n      number = number >= lower ? number : lower;\n    }\n  }\n  return number;\n}\n\nexport default baseClamp;\n", "import baseClamp from './_baseClamp.js';\nimport toInteger from './toInteger.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295;\n\n/**\n * Converts `value` to an integer suitable for use as the length of an\n * array-like object.\n *\n * **Note:** This method is based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toLength(3.2);\n * // => 3\n *\n * _.toLength(Number.MIN_VALUE);\n * // => 0\n *\n * _.toLength(Infinity);\n * // => 4294967295\n *\n * _.toLength('3.2');\n * // => 3\n */\nfunction toLength(value) {\n  return value ? baseClamp(toInteger(value), 0, MAX_ARRAY_LENGTH) : 0;\n}\n\nexport default toLength;\n", "import toInteger from './toInteger.js';\nimport toLength from './toLength.js';\n\n/**\n * The base implementation of `_.fill` without an iteratee call guard.\n *\n * @private\n * @param {Array} array The array to fill.\n * @param {*} value The value to fill `array` with.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns `array`.\n */\nfunction baseFill(array, value, start, end) {\n  var length = array.length;\n\n  start = toInteger(start);\n  if (start < 0) {\n    start = -start > length ? 0 : (length + start);\n  }\n  end = (end === undefined || end > length) ? length : toInteger(end);\n  if (end < 0) {\n    end += length;\n  }\n  end = start > end ? 0 : toLength(end);\n  while (start < end) {\n    array[start++] = value;\n  }\n  return array;\n}\n\nexport default baseFill;\n", "import baseFill from './_baseFill.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Fills elements of `array` with `value` from `start` up to, but not\n * including, `end`.\n *\n * **Note:** This method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 3.2.0\n * @category Array\n * @param {Array} array The array to fill.\n * @param {*} value The value to fill `array` with.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = [1, 2, 3];\n *\n * _.fill(array, 'a');\n * console.log(array);\n * // => ['a', 'a', 'a']\n *\n * _.fill(Array(3), 2);\n * // => [2, 2, 2]\n *\n * _.fill([4, 6, 8, 10], '*', 1, 3);\n * // => [4, '*', '*', 10]\n */\nfunction fill(array, value, start, end) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  if (start && typeof start != 'number' && isIterateeCall(array, value, start)) {\n    start = 0;\n    end = length;\n  }\n  return baseFill(array, value, start, end);\n}\n\nexport default fill;\n", "import { getGroupingName, isGrouping } from '../groupingRow/grouping.service';\nimport { CSVFormat, DataInput, Formatter } from './types';\n\nconst INITIAL: CSVFormat = {\n  mime: 'text/csv',\n  fileKind: 'csv',\n  // BOM signature\n  bom: true,\n  columnDelimiter: ',',\n  rowDelimiter: '\\r\\n',\n  encoding: '',\n};\n\nexport type CSVFormatter = (options: Partial<CSVFormat>, data: DataInput) => string;\n\n// The ASCII character code 13 is called a Carriage Return or CR.\nconst CARRIAGE_RETURN = String.fromCharCode(13);\n// Chr(13) followed by a Chr(10) that compose a proper CRLF.\nconst LINE_FEED = String.fromCharCode(10);\nconst DOUBLE_QT = String.fromCharCode(34);\nconst NO_BREAK_SPACE = String.fromCharCode(0xfeff);\nconst escapeRegex = new RegExp('\"', 'g');\n\nexport class ExportCsv implements Formatter {\n  readonly options: Readonly<CSVFormat>;\n  constructor(options: Partial<CSVFormat> = {}) {\n    this.options = { ...INITIAL, ...options };\n  }\n\n  doExport({ data, headers, props }: DataInput) {\n    let result = this.options.bom ? NO_BREAK_SPACE : '';\n\n    // any header\n    if (headers?.length > 0) {\n      headers.forEach(header => {\n        // ignore empty\n        if (!header.length) {\n          return;\n        }\n        result += this.prepareHeader(header, this.options.columnDelimiter);\n        result += this.options.rowDelimiter;\n      });\n    }\n\n    data.forEach((rgRow, index) => {\n      if (index > 0) {\n        result += this.options.rowDelimiter;\n      }\n      // support grouping\n      if (isGrouping(rgRow)) {\n        result += this.parseCell(getGroupingName(rgRow), this.options.columnDelimiter);\n        return;\n      }\n      result += props.map(p => this.parseCell(rgRow[p], this.options.columnDelimiter)).join(this.options.columnDelimiter);\n    });\n\n    return result;\n  }\n\n  private prepareHeader(columnHeaders: string[], columnDelimiter: string) {\n    let result = '';\n    const newColumnHeaders = columnHeaders.map(v => this.parseCell(v, columnDelimiter, true));\n    result += newColumnHeaders.join(columnDelimiter);\n    return result;\n  }\n\n  private parseCell(value: any, columnDelimiter: string, force = false) {\n    let escape = value;\n    if (typeof value !== 'string') {\n      escape = JSON.stringify(value);\n    }\n    const toEscape = [CARRIAGE_RETURN, DOUBLE_QT, LINE_FEED, columnDelimiter];\n    if (typeof escape === 'undefined') {\n      return '';\n    }\n    if (escape !== '' && (force || toEscape.some(i => escape.indexOf(i) >= 0))) {\n      return `\"${escape.replace(escapeRegex, '\"\"')}\"`;\n    }\n\n    return escape;\n  }\n}\n", "import fill from 'lodash/fill';\nimport { columnTypes, rowTypes, Group, Groups } from '@store';\n\nimport { timeout } from '../../utils';\nimport { BasePlugin } from '../base.plugin';\nimport { ExportCsv } from './csv';\nimport type { ColSource, CSVFormat, DataInput, Formatter } from './types';\nimport type { DimensionCols, ColumnProp, DataType } from '@type';\n\nexport * from './csv';\nexport * from './types';\n\nenum ExportTypes {\n  csv = 'csv',\n}\n\nexport type ExportFormat = Partial<CSVFormat>;\n\nexport class ExportFilePlugin extends BasePlugin {\n  /** Exports string */\n  async exportString(options: ExportFormat = {}, t: ExportTypes = ExportTypes.csv) {\n    const data = await this.beforeexport();\n    if (!data) {\n      return null;\n    }\n    return this.formatter(t, options).doExport(data);\n  }\n\n  /** Exports Blob */\n  async exportBlob(options: ExportFormat = {}, t: ExportTypes = ExportTypes.csv) {\n    return await this.getBlob(this.formatter(t, options));\n  }\n\n  /** Export file */\n  async exportFile(options: ExportFormat = {}, t: ExportTypes = ExportTypes.csv) {\n    const formatter = this.formatter(t, options);\n\n    // url\n    const URL = window.URL || window.webkitURL;\n\n    const a = document.createElement('a');\n    const { filename, fileKind } = formatter.options;\n    const name = `${filename}.${fileKind}`;\n\n    const blob = await this.getBlob(formatter);\n    const url = blob ? URL.createObjectURL(blob) : '';\n\n    a.style.display = 'none';\n    a.setAttribute('href', url);\n    a.setAttribute('download', name);\n    this.revogrid.appendChild(a);\n    a.dispatchEvent(new MouseEvent('click'));\n    this.revogrid.removeChild(a);\n\n    // delay for revoke, correct for some browsers\n    await timeout(120);\n    URL.revokeObjectURL(url);\n  }\n\n  /** Blob object */\n  async getBlob(formatter: Formatter) {\n    const type = `${formatter.options.mime};charset=${formatter.options.encoding}`;\n    if (typeof Blob !== 'undefined') {\n      const data = await this.beforeexport();\n      if (!data) {\n        return null;\n      }\n      return new Blob([formatter.doExport(data)], { type });\n    }\n    return null;\n  }\n\n  // before event\n  private async beforeexport() {\n    let data = await this.getData();\n    const event: CustomEvent<{ data: DataInput }> = this.emit('beforeexport', { data });\n    if (event.defaultPrevented) {\n      return null;\n    }\n    return event.detail.data;\n  }\n\n  private async getData(): Promise<DataInput> {\n    const data = await this.getSource();\n    const colSource: ColSource[] = [];\n    const colPromises: Promise<ColSource>[] = [];\n    columnTypes.forEach((t, i) => {\n      colPromises.push(this.getColPerSource(t).then(s => (colSource[i] = s)));\n    });\n    await Promise.all(colPromises);\n    const columns: ColSource = {\n      headers: [],\n      props: [],\n    };\n    for (let source of colSource) {\n      source.headers.forEach((h, i) => {\n        if (!columns.headers[i]) {\n          columns.headers[i] = [];\n        }\n        columns.headers[i].push(...h);\n      });\n      columns.props.push(...source.props);\n    }\n    return {\n      data,\n      ...columns,\n    };\n  }\n\n  private async getColPerSource(t: DimensionCols) {\n    const store = await this.revogrid.getColumnStore(t);\n    const source = store.get('source');\n    const virtualIndexes = store.get('items');\n    const depth = store.get('groupingDepth');\n    const groups = store.get('groups');\n    const colNames: string[] = [];\n    const colProps: ColumnProp[] = [];\n    virtualIndexes.forEach((v: number) => {\n      const prop = source[v].prop;\n      colNames.push(source[v].name || '');\n      colProps.push(prop);\n    });\n    const rows: string[][] = this.getGroupHeaders(depth, groups, virtualIndexes);\n    rows.push(colNames);\n    return {\n      headers: rows,\n      props: colProps,\n    };\n  }\n\n  private getGroupHeaders(depth: number, groups: Groups, items: number[]) {\n    const rows: string[][] = [];\n    const template = fill(new Array(items.length), '');\n    for (let d = 0; d < depth; d++) {\n      const rgRow = [...template];\n      rows.push(rgRow);\n      if (!groups[d]) {\n        continue;\n      }\n      const levelGroups = groups[d];\n\n      // add names of groups\n      levelGroups.forEach((group: Group) => {\n        const minIndex = group.indexes[0];\n        if (typeof minIndex === 'number') {\n          rgRow[minIndex] = group.name;\n        }\n      });\n    }\n    return rows;\n  }\n\n  private async getSource() {\n    const data: DataType[][] = [];\n    const promisesData: Promise<number>[] = [];\n    rowTypes.forEach(t => {\n      const dataPart: DataType[] = [];\n      data.push(dataPart);\n      const promise = this.revogrid.getVisibleSource(t).then((d: DataType[]) => dataPart.push(...d));\n      promisesData.push(promise);\n    });\n    await Promise.all(promisesData);\n    return data.reduce((r, v) => {\n      r.push(...v);\n      return r;\n    }, []);\n  }\n\n  // get correct class for future multiple types support\n  private formatter(type: ExportTypes, options: ExportFormat = {}) {\n    switch (type) {\n      case ExportTypes.csv:\n        return new ExportCsv(options);\n      default:\n        throw new Error('Unknown format');\n    }\n  }\n}\n", "import { LogicFunction, LogicFunctionExtraParam, LogicFunctionParam, ExtraField } from '../filter.types';\n\nconst eq: LogicFunction = (value: LogicFunctionParam, extra?: LogicFunctionExtraParam) => {\n  if (typeof value === 'undefined' || (value === null && !extra)) {\n    return true;\n  }\n  if (typeof value !== 'string') {\n    value = JSON.stringify(value);\n  }\n\n  const filterVal = extra?.toString().toLocaleLowerCase();\n  if (filterVal?.length === 0) {\n    return true;\n  }\n  \n  return value.toLocaleLowerCase() === filterVal;\n};\n\nexport const notEq: LogicFunction = (value: LogicFunctionParam, extra?: LogicFunctionExtraParam) => !eq(value, extra);\nnotEq.extra = 'input' as ExtraField;\neq.extra = 'input' as ExtraField;\nexport default eq;\n", "import { LogicFunction, LogicFunctionExtraParam, LogicFunctionParam } from '../../filter.types';\n\nconst gtThan: LogicFunction = function (value: LogicFunctionParam, extra?: LogicFunctionExtraParam) {\n  let conditionValue: number;\n\n  if (typeof value === 'number' && typeof extra !== 'undefined' && extra !== null) {\n    conditionValue = parseFloat(extra?.toString());\n    return value > conditionValue;\n  }\n  return false;\n};\n\ngtThan.extra = 'input';\nexport default gtThan;\n", "import { LogicFunction, LogicFunctionExtraParam, LogicFunctionParam } from '../../filter.types';\nimport eq from '../equal';\nimport gt from './greaterThan';\n\nconst gtThanEq: LogicFunction = function (value: LogicFunctionParam, extra?: LogicFunctionExtraParam) {\n  return eq(value, extra) || gt(value, extra);\n};\n\ngtThanEq.extra = 'input';\nexport default gtThanEq;\n", "import { LogicFunction, LogicFunctionExtraParam, LogicFunctionParam } from '../../filter.types';\n\nconst lt: LogicFunction = function (value: LogicFunctionParam, extra?: LogicFunctionExtraParam) {\n  let conditionValue: number;\n  if (typeof value === 'number' && typeof extra !== 'undefined' && extra !== null) {\n    conditionValue = parseFloat(extra.toString());\n    return value < conditionValue;\n  } else {\n    return false;\n  }\n};\n\nlt.extra = 'input';\nexport default lt;\n", "import { LogicFunction, LogicFunctionExtraParam, LogicFunctionParam } from '../../filter.types';\nimport eq from '../equal';\nimport lt from './lessThan';\n\nconst lsEq: LogicFunction = function (value: LogicFunctionParam, extra?: LogicFunctionExtraParam) {\n  return eq(value, extra) || lt(value, extra);\n};\n\nlsEq.extra = 'input';\nexport default lsEq;\n", "import { LogicFunction, LogicFunctionParam } from '../filter.types';\n\nconst set: LogicFunction = (value: LogicFunctionParam) => !(value === '' || value === null || value === void 0);\nexport const notSet: LogicFunction = (value: LogicFunctionParam) => !set(value);\nexport default set;\n", "import { LogicFunction, LogicFunctionExtraParam, LogicFunctionParam } from '../../filter.types';\n\nconst beginsWith: LogicFunction = (value: LogicFunctionParam, extra?: LogicFunctionExtraParam) => {\n  if (!value) {\n    return false;\n  }\n  if (!extra) {\n    return true;\n  }\n  if (typeof value !== 'string') {\n    value = JSON.stringify(value);\n  }\n  if (typeof extra !== 'string') {\n    extra = JSON.stringify(extra);\n  }\n  return value.toLocaleLowerCase().indexOf(extra.toLocaleLowerCase()) === 0;\n};\n\nbeginsWith.extra = 'input';\nexport default beginsWith;\n", "import { LogicFunction, LogicFunctionExtraParam, LogicFunctionParam } from '../../filter.types';\n\nconst contains: LogicFunction = (value: LogicFunctionParam, extra?: LogicFunctionExtraParam) => {\n  if (!extra) {\n    return true;\n  }\n  if (!value) {\n    return false;\n  }\n  if (extra) {\n    if (typeof value !== 'string') {\n      value = JSON.stringify(value);\n    }\n    return value.toLocaleLowerCase().indexOf(extra.toString().toLowerCase()) > -1;\n  }\n  return true;\n};\n\nexport const notContains: LogicFunction = (value: LogicFunctionParam, extra?: LogicFunctionExtraParam) => {\n  return !contains(value, extra);\n};\nnotContains.extra = 'input';\ncontains.extra = 'input';\nexport default contains;\n", "// filter.indexed.ts\n\nimport eq, { notEq } from './conditions/equal';\nimport gtThan from './conditions/number/greaterThan';\nimport gtThanEq from './conditions/number/greaterThanOrEqual';\nimport lt from './conditions/number/lessThan';\nimport lsEq from './conditions/number/lessThanOrEqual';\nimport set, { notSet } from './conditions/set';\nimport beginsWith from './conditions/string/beginswith';\nimport contains, { notContains } from './conditions/string/contains';\nimport { LogicFunction } from './filter.types';\n\n\nexport const filterCoreFunctionsIndexedByType: Record<FilterType, LogicFunction> = {\n  none: () => true,\n  empty: notSet,\n  notEmpty: set,\n  eq: eq,\n  notEq: notEq,\n  begins: beginsWith,\n  contains: contains,\n  notContains: notContains,\n\n  eqN: eq,\n  neqN: notEq,\n  gt: gtThan,\n  gte: gtThanEq,\n  lt: lt,\n  lte: lsEq,\n};\n\nexport const filterTypes: Record<string, FilterType[]> = {\n  string: ['notEmpty', 'empty', 'eq', 'notEq', 'begins', 'contains', 'notContains'],\n  number: ['notEmpty', 'empty', 'eqN', 'neqN', 'gt', 'gte', 'lt', 'lte'],\n};\n\nexport const filterNames = {\n  none: 'None',\n  empty: 'Not set',\n  notEmpty: 'Set',\n\n  eq: 'Equal',\n  notEq: 'Not equal',\n  begins: 'Begins with',\n  contains: 'Contains',\n  notContains: 'Does not contain',\n\n  eqN: '=',\n  neqN: '!=',\n  gt: '>',\n  gte: '>=',\n  lt: '<',\n  lte: '<=',\n};\n\nexport type FilterType = keyof typeof filterNames;\n", "// filter.plugin.tsx\nimport { h, type VNode } from '@stencil/core';\n\nimport type {\n  ColumnProp,\n  ColumnRegular,\n  DataType,\n  PluginProviders,\n} from '@type';\nimport { BasePlugin } from '../base.plugin';\nimport { FILTER_PROP, isFilterBtn } from './filter.button';\nimport {\n  filterCoreFunctionsIndexedByType,\n  filterNames,\n  filterTypes,\n} from './filter.indexed';\n\nimport type {\n  ColumnFilterConfig,\n  FilterCollectionItem,\n  FilterData,\n  LogicFunction,\n  MultiFilterItem,\n  ShowData,\n} from './filter.types';\n\nimport { getCellDataParsed } from '../../utils';\nimport { TrimmedEntity } from '@store';\n\nexport * from './filter.types';\nexport * from './filter.indexed';\nexport * from './filter.button';\n\nexport const FILTER_TRIMMED_TYPE = 'filter';\nexport const FILTER_CONFIG_CHANGED_EVENT = 'filterconfigchanged';\nexport const FILTE_PANEL = 'revogr-filter-panel';\n\n/**\n * @typedef ColumnFilterConfig\n * @type {object}\n *\n * @property {MultiFilterItem|undefined} multiFilterItems - data for multi filtering with relation\n *\n * @property {Record<ColumnProp, FilterCollectionItem>|undefined} collection - preserved filter data, relation for filters will be applied as 'and'\n *\n * @property {string[]|undefined} include - filters to be included, if defined everything else out of scope will be ignored\n *\n * @property {Record<string, CustomFilter>|undefined} customFilters - hash map of {FilterType:CustomFilter}.\n *\n * @property {FilterLocalization|undefined} localization - translation for filter popup captions.\n *\n * @property {boolean|undefined} disableDynamicFiltering - disables dynamic filtering. A way to apply filters on Save only.\n */\n/**\n * @internal\n */\n\nexport class FilterPlugin extends BasePlugin {\n  pop?: HTMLRevogrFilterPanelElement;\n  filterCollection: Record<ColumnProp, FilterCollectionItem> = {};\n  multiFilterItems: MultiFilterItem = {};\n\n  /**\n   * Filter types\n   * @example\n   * {\n   *    string: ['contains', 'beginswith'],\n   *    number: ['eqN', 'neqN', 'gt']\n   *  }\n   */\n  filterByType: Record<string, string[]> = { ...filterTypes };\n  filterNameIndexByType: Record<string, string> = {\n    ...filterNames,\n  };\n  filterFunctionsIndexedByType: Record<string, LogicFunction> = {\n    ...filterCoreFunctionsIndexedByType,\n  };\n\n  filterProp = FILTER_PROP;\n\n  extraHyperContent?: (data: ShowData) => VNode | VNode[];\n\n  constructor(\n    public revogrid: HTMLRevoGridElement,\n    providers: PluginProviders,\n    public config?: ColumnFilterConfig,\n  ) {\n    super(revogrid, providers);\n    if (config) {\n      this.initConfig(config);\n    }\n\n    const existingNodes = this.revogrid.registerVNode.filter(\n      n => typeof n === 'object' && n.$tag$ !== FILTE_PANEL,\n    );\n    this.revogrid.registerVNode = [\n      ...existingNodes,\n      <revogr-filter-panel\n        filterNames={this.filterNameIndexByType}\n        filterEntities={this.filterFunctionsIndexedByType}\n        filterCaptions={config?.localization?.captions}\n        onFilterChange={e => this.onFilterChange(e.detail)}\n        onResetChange={e => this.onFilterReset(e.detail)}\n        disableDynamicFiltering={config?.disableDynamicFiltering}\n        closeOnOutsideClick={config?.closeFilterPanelOnOutsideClick}\n        ref={e => (this.pop = e)}\n      >\n        {' '}\n        {this.extraContent()}\n      </revogr-filter-panel>,\n    ];\n\n    const aftersourceset = async () => {\n      const filterCollectionProps = Object.keys(this.filterCollection);\n      if (filterCollectionProps.length > 0) {\n        // handle old way of filtering by reworking FilterCollection to new MultiFilterItem\n        filterCollectionProps.forEach((prop, index) => {\n          if (!this.multiFilterItems[prop]) {\n            this.multiFilterItems[prop] = [\n              {\n                id: index,\n                type: this.filterCollection[prop].type,\n                value: this.filterCollection[prop].value,\n                relation: 'and',\n              },\n            ];\n          }\n        });\n      }\n      if (Object.keys(this.multiFilterItems).length === 0) {\n        return;\n      }\n      await this.runFiltering(this.multiFilterItems);\n    };\n    this.addEventListener('headerclick', e => this.headerclick(e));\n    this.addEventListener(\n      FILTER_CONFIG_CHANGED_EVENT,\n      ({ detail }: CustomEvent<ColumnFilterConfig | boolean>) => {\n        if (\n          !detail ||\n          (typeof detail === 'object' &&\n            (!detail.multiFilterItems ||\n              !Object.keys(detail.multiFilterItems).length))\n        ) {\n          this.clearFiltering();\n          return;\n        }\n        if (typeof detail === 'object') {\n          this.initConfig(detail);\n        }\n        aftersourceset();\n      },\n    );\n    this.addEventListener('aftersourceset', aftersourceset);\n    this.addEventListener('filter', ({ detail }: CustomEvent) =>\n      this.onFilterChange(detail),\n    );\n  }\n\n  beforeshow(_: ShowData) {\n    // used as hook for filter panel\n  }\n\n  extraContent(): any {\n    return null;\n  }\n\n  initConfig(config: ColumnFilterConfig) {\n    if (config.multiFilterItems) {\n      this.multiFilterItems = { ...config.multiFilterItems };\n    } else {\n      this.multiFilterItems = {};\n    }\n    // Add custom filters\n    if (config.customFilters) {\n      for (let customFilterType in config.customFilters) {\n        const cFilter = config.customFilters[customFilterType];\n        if (!this.filterByType[cFilter.columnFilterType]) {\n          this.filterByType[cFilter.columnFilterType] = [];\n        }\n        // add custom filter type\n        this.filterByType[cFilter.columnFilterType].push(customFilterType);\n        // add custom filter function\n        this.filterFunctionsIndexedByType[customFilterType] = cFilter.func;\n        // add custom filter name\n        this.filterNameIndexByType[customFilterType] = cFilter.name;\n      }\n    }\n\n    // Add filterProp if provided in config\n    if (config.filterProp) {\n      this.filterProp = config.filterProp;\n    }\n\n    /**\n     * which filters has to be included/excluded\n     * convenient way to exclude system filters\n     */\n    const cfgInlcude = config.include;\n    if (cfgInlcude) {\n      const filters: Record<string, string[]> = {};\n\n      for (let t in this.filterByType) {\n        // validate filters, if appropriate function present\n        const newTypes = this.filterByType[t].filter(\n          f => cfgInlcude.indexOf(f) > -1,\n        );\n        if (newTypes.length) {\n          filters[t] = newTypes;\n        }\n      }\n      // if any valid filters provided show them\n      if (Object.keys(filters).length > 0) {\n        this.filterByType = filters;\n      }\n    }\n\n    if (config.collection) {\n      const filtersWithFilterFunctionPresent = Object.entries(\n        config.collection,\n      ).filter(([, item]) => this.filterFunctionsIndexedByType[item.type]);\n      this.filterCollection = Object.fromEntries(\n        filtersWithFilterFunctionPresent,\n      );\n    } else {\n      this.filterCollection = {};\n    }\n\n    if (config.localization) {\n      if (config.localization.filterNames) {\n        Object.entries(config.localization.filterNames).forEach(([k, v]) => {\n          if (this.filterNameIndexByType[k] != void 0) {\n            this.filterNameIndexByType[k] = v;\n          }\n        });\n      }\n    }\n  }\n\n  async headerclick(e: CustomEvent<ColumnRegular>) {\n    const el = e.detail.originalEvent?.target as HTMLElement;\n    if (!isFilterBtn(el)) {\n      return;\n    }\n    e.preventDefault();\n    if (!this.pop) {\n      return;\n    }\n\n    // filter button clicked, open filter dialog\n    const gridPos = this.revogrid.getBoundingClientRect();\n    const buttonPos = el.getBoundingClientRect();\n    const prop = e.detail.prop;\n\n    const data: ShowData = {\n      ...e.detail,\n      ...this.filterCollection[prop],\n      x: buttonPos.x - gridPos.x,\n      y: buttonPos.y - gridPos.y + buttonPos.height,\n      autoCorrect: true,\n      filterTypes: this.getColumnFilter(e.detail.filter),\n      filterItems: this.multiFilterItems,\n      extraContent: this.extraHyperContent,\n    };\n    this.beforeshow?.(data);\n    this.pop.show(data);\n  }\n\n  getColumnFilter(\n    type?: boolean | string | string[],\n  ): Record<string, string[]> {\n    let filterType = 'string';\n    if (!type) {\n      return { [filterType]: this.filterByType[filterType] };\n    }\n\n    // if custom column filter\n    if (this.isValidType(type)) {\n      filterType = type;\n\n      // if multiple filters applied\n    } else if (typeof type === 'object' && type.length) {\n      return type.reduce((r: Record<string, string[]>, multiType) => {\n        if (this.isValidType(multiType)) {\n          r[multiType] = this.filterByType[multiType];\n        }\n        return r;\n      }, {});\n    }\n    return { [filterType]: this.filterByType[filterType] };\n  }\n\n  isValidType(type: any): type is string {\n    return !!(typeof type === 'string' && this.filterByType[type]);\n  }\n\n  /**\n   * Called on internal component change\n   */\n  async onFilterChange(filterItems: MultiFilterItem) {\n    // store the filter items\n    this.multiFilterItems = filterItems;\n\n    // run the filtering when the items change\n    this.runFiltering(this.multiFilterItems);\n  }\n\n  onFilterReset(prop?: ColumnProp) {\n    delete this.multiFilterItems[prop ?? ''];\n    this.onFilterChange(this.multiFilterItems);\n  }\n\n  /**\n   * Triggers grid filtering\n   */\n  async doFiltering(\n    collection: Record<ColumnProp, FilterCollectionItem>,\n    source: DataType[],\n    columns: ColumnRegular[],\n    filterItems: MultiFilterItem,\n  ) {\n    const columnsToUpdate: ColumnRegular[] = [];\n\n    /**\n     * Loop through the columns and update the columns that need to be updated with the `hasFilter` property.\n     */\n    const columnByProp: Record<string, ColumnRegular> = {};\n    columns.forEach(rgCol => {\n      const column = { ...rgCol };\n      const hasFilter = filterItems[column.prop];\n      columnByProp[column.prop] = column;\n\n      /**\n       * If the column has a filter and it's not already marked as filtered, update the column.\n       */\n      if (column[this.filterProp] && !hasFilter) {\n        delete column[this.filterProp];\n        columnsToUpdate.push(column);\n      }\n\n      /**\n       * If the column does not have a filter and it's marked as filtered, update the column.\n       */\n\n      if (!column[this.filterProp] && hasFilter) {\n        columnsToUpdate.push(column);\n        column[this.filterProp] = true;\n      }\n    });\n    const itemsToTrim = this.getRowFilter(source, filterItems, columnByProp);\n    // check is filter event prevented\n    const { defaultPrevented, detail } = this.emit('beforefiltertrimmed', {\n      collection,\n      itemsToFilter: itemsToTrim,\n      source,\n      filterItems,\n    });\n    if (defaultPrevented) {\n      return;\n    }\n\n    this.providers.data.setTrimmed({ [FILTER_TRIMMED_TYPE]: detail.itemsToFilter });\n\n    // applies the hasFilter to the columns to show filter icon\n    this.providers.column.updateColumns(columnsToUpdate);\n    this.emit('afterfilterapply', {\n      multiFilterItems: filterItems,\n      source,\n      collection,\n    });\n  }\n\n  async clearFiltering() {\n    this.multiFilterItems = {};\n    await this.runFiltering(this.multiFilterItems);\n  }\n\n  async runFiltering(multiFilterItems: MultiFilterItem) {\n    const collection: Record<ColumnProp, FilterCollectionItem> = {};\n\n    // handle old filterCollection to return the first filter only (if any) from multiFilterItems\n    const filterProps = Object.keys(multiFilterItems);\n\n    for (const prop of filterProps) {\n      // check if we have any filter for a column\n      if (multiFilterItems[prop].length > 0) {\n        const firstFilterItem = multiFilterItems[prop][0];\n        collection[prop] = {\n          type: firstFilterItem.type,\n          value: firstFilterItem.value,\n        };\n      }\n    }\n\n    this.filterCollection = collection;\n    const columns = this.providers.column.getColumns();\n    // run the filtering on the main source only\n    const source = this.providers.data.stores['rgRow'].store.get('source');\n\n    const { defaultPrevented, detail } = this.emit('beforefilterapply', {\n      collection: this.filterCollection,\n      source,\n      columns,\n      filterItems: this.multiFilterItems,\n    });\n    if (defaultPrevented) {\n      return;\n    }\n    this.doFiltering(\n      detail.collection,\n      detail.source,\n      detail.columns,\n      detail.filterItems,\n    );\n  }\n\n  /**\n   * Get trimmed rows based on filter\n   */\n  getRowFilter(\n    rows: DataType[],\n    filterItems: MultiFilterItem,\n    columnByProp: Record<string, ColumnRegular>,\n  ): TrimmedEntity {\n    const propKeys = Object.keys(filterItems);\n\n    const trimmed: TrimmedEntity = {};\n\n    // each rows\n    for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n      // check filter by column properties\n      for (const prop of propKeys) {\n        // add to the list of removed/trimmed rows of filter condition is satisfied\n        if (\n          this.shouldTrimRow(\n            filterItems[prop],\n            prop,\n            columnByProp[prop],\n            rows[rowIndex],\n          )\n        ) {\n          trimmed[rowIndex] = true;\n        }\n      } // end of for-of propKeys\n    }\n    return trimmed;\n  }\n\n  private shouldTrimRow(\n    propFilters: FilterData[],\n    prop: ColumnProp,\n    column?: ColumnRegular,\n    model: DataType = {},\n  ) {\n    // reset the count of satisfied filters\n    let propFilterSatisfiedCount = 0;\n    // reset the array of last filter results\n    let lastFilterResults: boolean[] = [];\n\n    // testing each filter for a prop\n    for (const [filterIndex, filterData] of propFilters.entries()) {\n      // the filter LogicFunction based on the type\n      const filterFunc = this.filterFunctionsIndexedByType[filterData.type];\n\n      // THE MAGIC OF FILTERING IS HERE\n      // If there is no column but user wants to filter by a property\n      const value = column ? getCellDataParsed(model, column) : model[prop];\n      // OR relation\n      if (filterData.relation === 'or') {\n        // reset the array of last filter results\n        lastFilterResults = [];\n        // if the filter is satisfied, continue to the next filter\n        if (filterFunc(value, filterData.value)) {\n          continue;\n        }\n        // if the filter is not satisfied, count it\n        propFilterSatisfiedCount++;\n\n        // AND relation\n      } else {\n        // 'and' relation will need to know the next filter\n        // so we save this current filter to include it in the next filter\n        lastFilterResults.push(!filterFunc(value, filterData.value));\n\n        if (isFinalAndFilter(filterIndex, propFilters)) {\n          // let's just continue since for sure propFilterSatisfiedCount cannot be satisfied\n          if (allAndConditionsSatisfied(lastFilterResults)) {\n            // reset the array of last filter results\n            lastFilterResults = [];\n            continue;\n          }\n\n          // we need to add all of the lastFilterResults since we need to satisfy all\n          propFilterSatisfiedCount += lastFilterResults.length;\n          // reset the array of last filter results\n          lastFilterResults = [];\n        }\n      }\n    } // end of propFilters forEach\n    return propFilterSatisfiedCount === propFilters.length;\n  }\n}\n/**\n * Checks if the current filter is the final one in an AND sequence.\n * @param index - Current filter index in the list.\n * @param filters - Array of filters for the property.\n * @returns True if this is the last AND condition; false otherwise.\n */\nfunction isFinalAndFilter(index: number, filters: MultiFilterItem[string]): boolean {\n  const nextFilter = filters[index + 1]; // Get the next filter in the list.\n  // Return true if there's no next filter or if the next filter defined and is not part of the AND sequence.\n  return !nextFilter || (!!nextFilter.relation && nextFilter.relation !== 'and');\n}\n\n/**\n * Determines if all conditions in an AND sequence are satisfied.\n * @param pendingResults - An array of results from the AND conditions.\n * @returns True if all conditions are satisfied; false otherwise.\n */\nfunction allAndConditionsSatisfied(pendingResults: boolean[]): boolean {\n  // Check if there are any failed conditions in the pending results.\n  return !pendingResults.includes(true);\n}\n", "import baseGetTag from './_baseGetTag.js';\nimport isArray from './isArray.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar stringTag = '[object String]';\n\n/**\n * Checks if `value` is classified as a `String` primitive or object.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a string, else `false`.\n * @example\n *\n * _.isString('abc');\n * // => true\n *\n * _.isString(1);\n * // => false\n */\nfunction isString(value) {\n  return typeof value == 'string' ||\n    (!isArray(value) && isObjectLike(value) && baseGetTag(value) == stringTag);\n}\n\nexport default isString;\n", "import baseProperty from './_baseProperty.js';\n\n/**\n * Gets the size of an ASCII `string`.\n *\n * @private\n * @param {string} string The string inspect.\n * @returns {number} Returns the string size.\n */\nvar asciiSize = baseProperty('length');\n\nexport default asciiSize;\n", "/** Used to compose unicode character classes. */\nvar rsAstralRange = '\\\\ud800-\\\\udfff',\n    rsComboMarksRange = '\\\\u0300-\\\\u036f',\n    reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n    rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,\n    rsVarRange = '\\\\ufe0e\\\\ufe0f';\n\n/** Used to compose unicode capture groups. */\nvar rsZWJ = '\\\\u200d';\n\n/** Used to detect strings with [zero-width joiners or code points from the astral planes](http://eev.ee/blog/2015/09/12/dark-corners-of-unicode/). */\nvar reHasUnicode = RegExp('[' + rsZWJ + rsAstralRange  + rsComboRange + rsVarRange + ']');\n\n/**\n * Checks if `string` contains Unicode symbols.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {boolean} Returns `true` if a symbol is found, else `false`.\n */\nfunction hasUnicode(string) {\n  return reHasUnicode.test(string);\n}\n\nexport default hasUnicode;\n", "/** Used to compose unicode character classes. */\nvar rsAstralRange = '\\\\ud800-\\\\udfff',\n    rsComboMarksRange = '\\\\u0300-\\\\u036f',\n    reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n    rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,\n    rsVarRange = '\\\\ufe0e\\\\ufe0f';\n\n/** Used to compose unicode capture groups. */\nvar rsAstral = '[' + rsAstralRange + ']',\n    rsCombo = '[' + rsComboRange + ']',\n    rsFitz = '\\\\ud83c[\\\\udffb-\\\\udfff]',\n    rsModifier = '(?:' + rsCombo + '|' + rsFitz + ')',\n    rsNonAstral = '[^' + rsAstralRange + ']',\n    rsRegional = '(?:\\\\ud83c[\\\\udde6-\\\\uddff]){2}',\n    rsSurrPair = '[\\\\ud800-\\\\udbff][\\\\udc00-\\\\udfff]',\n    rsZWJ = '\\\\u200d';\n\n/** Used to compose unicode regexes. */\nvar reOptMod = rsModifier + '?',\n    rsOptVar = '[' + rsVarRange + ']?',\n    rsOptJoin = '(?:' + rsZWJ + '(?:' + [rsNonAstral, rsRegional, rsSurrPair].join('|') + ')' + rsOptVar + reOptMod + ')*',\n    rsSeq = rsOptVar + reOptMod + rsOptJoin,\n    rsSymbol = '(?:' + [rsNonAstral + rsCombo + '?', rsCombo, rsRegional, rsSurrPair, rsAstral].join('|') + ')';\n\n/** Used to match [string symbols](https://mathiasbynens.be/notes/javascript-unicode). */\nvar reUnicode = RegExp(rsFitz + '(?=' + rsFitz + ')|' + rsSymbol + rsSeq, 'g');\n\n/**\n * Gets the size of a Unicode `string`.\n *\n * @private\n * @param {string} string The string inspect.\n * @returns {number} Returns the string size.\n */\nfunction unicodeSize(string) {\n  var result = reUnicode.lastIndex = 0;\n  while (reUnicode.test(string)) {\n    ++result;\n  }\n  return result;\n}\n\nexport default unicodeSize;\n", "import asciiSize from './_asciiSize.js';\nimport hasUnicode from './_hasUnicode.js';\nimport unicodeSize from './_unicodeSize.js';\n\n/**\n * Gets the number of symbols in `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the string size.\n */\nfunction stringSize(string) {\n  return hasUnicode(string)\n    ? unicodeSize(string)\n    : asciiSize(string);\n}\n\nexport default stringSize;\n", "import baseKeys from './_baseKeys.js';\nimport getTag from './_getTag.js';\nimport isArrayLike from './isArrayLike.js';\nimport isString from './isString.js';\nimport stringSize from './_stringSize.js';\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    setTag = '[object Set]';\n\n/**\n * Gets the size of `collection` by returning its length for array-like\n * values or the number of own enumerable string keyed properties for objects.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object|string} collection The collection to inspect.\n * @returns {number} Returns the collection size.\n * @example\n *\n * _.size([1, 2, 3]);\n * // => 3\n *\n * _.size({ 'a': 1, 'b': 2 });\n * // => 2\n *\n * _.size('pebbles');\n * // => 7\n */\nfunction size(collection) {\n  if (collection == null) {\n    return 0;\n  }\n  if (isArrayLike(collection)) {\n    return isString(collection) ? stringSize(collection) : collection.length;\n  }\n  var tag = getTag(collection);\n  if (tag == mapTag || tag == setTag) {\n    return collection.size;\n  }\n  return baseKeys(collection).length;\n}\n\nexport default size;\n", "import type { CellCompareFunc, ColumnProp, ColumnRegular, DataType, Order } from '@type';\nimport type { SortingOrderFunction } from './sorting.types';\nimport { isGrouping } from '../groupingRow/grouping.service';\nimport { getCellRaw } from '../../utils/column.utils';\n\nexport function sortIndexByItems(\n  indexes: number[],\n  source: DataType[],\n  sortingFunc: SortingOrderFunction = {},\n): number[] {\n  // if no sorting - return unsorted indexes\n  if (Object.entries(sortingFunc).length === 0) {\n    // Unsorted indexes\n    return [...Array(indexes.length).keys()];\n  }\n  //\n  /**\n   * go through all indexes and align in new order\n   * performs a multi-level sorting by applying multiple comparison functions to determine the order of the items based on different properties.\n   */\n  return indexes.sort((a, b) => {\n    const itemA = source[a];\n    const itemB = source[b];\n    for (const [prop, cmp] of Object.entries(sortingFunc)) {\n      if (isGrouping(itemA)) {\n        if (itemA['__rvgr-prop'] !== prop) {\n          return 0;\n        }\n      }\n      if (isGrouping(itemB)) {\n        if (itemB['__rvgr-prop'] !== prop) {\n          return 0;\n        }\n      }\n      /**\n       * If the comparison function returns a non-zero value (sorted), it means that the items should be sorted based on the given property. In such a case, the function immediately returns the sorted value, indicating the order in which the items should be arranged.\n       * If none of the comparison functions result in a non-zero value, indicating that the items are equal or should remain in the same order, the function eventually returns 0.\n       */\n      const sorted = cmp?.(prop, itemA, itemB);\n      if (sorted) {\n        return sorted;\n      }\n    }\n    return 0;\n  });\n}\n\nexport function defaultCellCompare(this: { column?: ColumnRegular }, prop: ColumnProp, a: DataType, b: DataType) {\n  const aRaw = this.column ? getCellRaw(a, this.column) : a?.[prop];\n  const bRaw = this.column ? getCellRaw(b, this.column) : b?.[prop];\n  const av = aRaw?.toString().toLowerCase();\n  const bv = bRaw?.toString().toLowerCase();\n\n  if (av === bv) {\n    return 0;\n  }\n  if (av > bv) {\n    return 1;\n  }\n  return -1;\n}\n\nexport function descCellCompare(cmp: CellCompareFunc) {\n  return (prop: ColumnProp, a: DataType, b: DataType): number => {\n    return -1 * cmp(prop, a, b);\n  };\n}\n\nexport function getNextOrder(currentOrder: Order): Order {\n  switch (currentOrder) {\n    case undefined:\n      return 'asc';\n    case 'asc':\n      return 'desc';\n    case 'desc':\n      return undefined;\n  }\n}\n\n\nexport function getComparer(column: Partial<ColumnRegular> | undefined, order: Order): CellCompareFunc | undefined {\n  const cellCmp: CellCompareFunc =\n    column?.cellCompare?.bind({ order }) || defaultCellCompare?.bind({ column, order });\n  if (order == 'asc') {\n    return cellCmp;\n  }\n  if (order == 'desc') {\n    return descCellCompare(cellCmp);\n  }\n  return undefined;\n}", "import size from 'lodash/size';\nimport debounce from 'lodash/debounce';\n\nimport { BasePlugin } from '../base.plugin';\nimport type {\n  Order,\n  ColumnRegular,\n  DimensionRows,\n  PluginProviders,\n} from '@type';\nimport type { SortingConfig, SortingOrder, SortingOrderFunction } from './sorting.types';\nimport { getColumnByProp } from '../../utils/column.utils';\nimport { columnTypes, rowTypes } from '@store';\nimport { getComparer, getNextOrder, sortIndexByItems } from './sorting.func';\n\nexport * from './sorting.types';\nexport * from './sorting.func';\nexport * from './sorting.sign';\n\n/**\n * Lifecycle\n * 1. @event `beforesorting` - Triggered when sorting just starts. Nothing has happened yet. This can be triggered from a column or from the source. If the type is from rows, the column will be undefined.\n * 2. @event `beforesourcesortingapply` - Triggered before the sorting data is applied to the data source. You can prevent this event, and the data will not be sorted.\n * 3. @event `beforesortingapply` - Triggered before the sorting data is applied to the data source. You can prevent this event, and the data will not be sorted. This event is only called from a column sorting click.\n * 4. @event `aftersortingapply` - Triggered after sorting has been applied and completed. This event occurs for both row and column sorting.\n *\n * Note: If you prevent an event, it will not proceed to the subsequent steps.\n */\n\nexport class SortingPlugin extends BasePlugin {\n  // Current sorting order per column prop\n  sorting?: SortingOrder;\n\n  // Each column sorting function, multiple columns sorting supported\n  sortingFunc?: SortingOrderFunction;\n  /**\n   * Delayed sorting promise\n   */\n  sortingPromise: (() => void) | null = null;\n\n  /**\n   * We need to sort only so often\n   */\n  postponeSort = debounce(\n    (order?: SortingOrder, comparison?: SortingOrderFunction, ignoreViewportUpdate?: boolean) =>\n      this.runSorting(order, comparison, ignoreViewportUpdate),\n    50,\n  );\n\n  constructor(\n    public revogrid: HTMLRevoGridElement,\n    providers: PluginProviders,\n    config?: SortingConfig,\n  ) {\n    super(revogrid, providers);\n\n    const setConfig = (cfg?: SortingConfig) => {\n      if (cfg) {\n        const sortingFunc: SortingOrderFunction = {};\n        const order: SortingOrder = {};\n        cfg.columns?.forEach(col => {\n          sortingFunc[col.prop] = getComparer(col, col.order);\n          order[col.prop] = col.order;\n        });\n\n        if (cfg.additive) {\n          this.sorting = {\n            ...this.sorting,\n            ...order,\n          };\n          this.sortingFunc = {\n            ...this.sortingFunc,\n            ...sortingFunc,\n          };\n        } else {\n          // // set sorting\n          this.sorting = order;\n          this.sortingFunc = sortingFunc;\n        }\n      }\n    }\n\n    setConfig(config);\n\n    this.addEventListener('sortingconfigchanged', ({ detail }) => {\n      config = detail;\n      setConfig(detail);\n      this.startSorting(this.sorting, this.sortingFunc);\n    });\n\n    this.addEventListener('beforeheaderrender', ({\n      detail,\n    }) => {\n      const { data: column } = detail;\n      if (column.sortable) {\n        detail.data = {\n          ...column,\n          order: this.sorting?.[column.prop],\n        };\n      }\n    });\n\n    this.addEventListener('beforeanysource', ({\n      detail: { type },\n    }) => {\n      // if sorting was provided - sort data\n      if (!!this.sorting && this.sortingFunc) {\n        const event = this.emit('beforesourcesortingapply', { type, sorting: this.sorting });\n        if (event.defaultPrevented) {\n          return;\n        }\n        this.startSorting(this.sorting, this.sortingFunc);\n      }\n    });\n    this.addEventListener('aftercolumnsset', ({\n      detail: { order },\n    }) => {\n      // if config provided - do nothing, read from config\n      if (config) {\n        return;\n      }\n\n      const columns = this.providers.column.getColumns();\n      const sortingFunc: SortingOrderFunction = {};\n\n      for (let prop in order) {\n        const cmp = getComparer(\n          getColumnByProp(columns, prop),\n          order[prop],\n        );\n        sortingFunc[prop] = cmp;\n      }\n\n      // set sorting\n      this.sorting = order;\n      this.sortingFunc = order && sortingFunc;\n    });\n    this.addEventListener('beforeheaderclick', (e) => {\n      if (e.defaultPrevented) {\n        return;\n      }\n\n      if (!e.detail?.column?.sortable) {\n        return;\n      }\n\n      this.headerclick(\n        e.detail.column,\n        e.detail?.originalEvent?.shiftKey,\n      );\n    });\n  }\n\n  /**\n   * Entry point for sorting, waits for all delayes, registers jobs\n   */\n  startSorting(order?: SortingOrder, sortingFunc?: SortingOrderFunction, ignoreViewportUpdate?: boolean) {\n    if (!this.sortingPromise) {\n      // add job before render\n      this.revogrid.jobsBeforeRender.push(\n        new Promise<void>(resolve => {\n          this.sortingPromise = resolve;\n        }),\n      );\n    }\n    this.postponeSort(order, sortingFunc, ignoreViewportUpdate);\n  }\n\n\n  /**\n   * Apply sorting to data on header click\n   * If additive - add to existing sorting, multiple columns can be sorted\n   */\n  headerclick(column: ColumnRegular, additive: boolean) {\n    const columnProp = column.prop;\n    let order: Order = getNextOrder(this.sorting?.[columnProp]);\n    const beforeEvent = this.emit('beforesorting', { column, order, additive });\n    if (beforeEvent.defaultPrevented) {\n      return;\n    }\n    order = beforeEvent.detail.order;\n\n    // apply sort data\n    const beforeApplyEvent = this.emit('beforesortingapply', {\n      column: beforeEvent.detail.column,\n      order,\n      additive,\n    });\n    if (beforeApplyEvent.defaultPrevented) {\n      return;\n    }\n    const cmp = getComparer(beforeApplyEvent.detail.column, beforeApplyEvent.detail.order);\n\n    if (beforeApplyEvent.detail.additive && this.sorting) {\n      const sorting: SortingOrder = {};\n      const sortingFunc: SortingOrderFunction = {};\n\n      this.sorting = {\n        ...this.sorting,\n        ...sorting,\n      };\n      // extend sorting function with new sorting for multiple columns sorting\n      this.sortingFunc = {\n        ...this.sortingFunc,\n        ...sortingFunc,\n      };\n\n      if (columnProp in sorting && size(sorting) > 1 && order === undefined) {\n        delete sorting[columnProp];\n        delete sortingFunc[columnProp];\n      } else {\n        sorting[columnProp] = order;\n        sortingFunc[columnProp] = cmp;\n      }\n    } else {\n      if (order) {\n        // reset sorting\n        this.sorting = { [columnProp]: order };\n        this.sortingFunc = { [columnProp]: cmp };\n      } else {\n        delete this.sorting?.[columnProp];\n        delete this.sortingFunc?.[columnProp];\n      }\n    }\n\n    this.startSorting(this.sorting, this.sortingFunc);\n  }\n\n  runSorting(\n    order?: SortingOrder,\n    comparison?: SortingOrderFunction,\n    ignoreViewportUpdate?: boolean\n  ) {\n    this.sort(order, comparison, undefined, ignoreViewportUpdate);\n    this.sortingPromise?.();\n    this.sortingPromise = null;\n  }\n\n  /**\n   * Sort items by sorting function\n   * @requires proxyItems applied to row store\n   * @requires source applied to row store\n   *\n   * @param sorting - per column sorting\n   * @param data - this.stores['rgRow'].store.get('source')\n   */\n  sort(\n    sorting?: SortingOrder,\n    sortingFunc?: SortingOrderFunction,\n    types: DimensionRows[] = rowTypes,\n    ignoreViewportUpdate = false\n  ) {\n    // if no sorting - reset\n    if (!Object.keys(sorting || {}).length) {\n      for (let type of types) {\n        const storeService = this.providers.data.stores[type];\n        // row data\n        const source = storeService.store.get('source');\n        // row indexes\n        const proxyItems = storeService.store.get('proxyItems');\n        // row indexes\n        const newItemsOrder = Array.from({ length: source.length }, (_, i) => i); // recover indexes range(0, source.length)\n        this.providers.dimension.updateSizesPositionByNewDataIndexes(type, newItemsOrder, proxyItems);\n        storeService.setData({ proxyItems: newItemsOrder, source: [...source], });\n      }\n    } else {\n      for (let type of types) {\n        const storeService = this.providers.data.stores[type];\n        // row data\n        const source = storeService.store.get('source');\n        // row indexes\n        const proxyItems = storeService.store.get('proxyItems');\n\n        const newItemsOrder = sortIndexByItems(\n          [...proxyItems],\n          source,\n          sortingFunc,\n        );\n       \n        // take row indexes before trim applied and proxy items\n        const prevItems = storeService.store.get('items');\n        storeService.setData({\n          proxyItems: newItemsOrder,\n          source: [...source],\n        });\n        // take currently visible row indexes\n        const newItems = storeService.store.get('items');\n        if (!ignoreViewportUpdate) {\n          this.providers.dimension\n            .updateSizesPositionByNewDataIndexes(type, newItems, prevItems);\n        }\n      }\n    }\n    // refresh columns to redraw column headers and show correct icon\n    columnTypes.forEach((type) => {\n      this.providers.column.dataSources[type].refresh();\n    });\n    this.emit('aftersortingapply');\n  }\n}\n\n\n", "import { DataType } from '@type';\nimport { PSEUDO_GROUP_ITEM_ID, PSEUDO_GROUP_ITEM_VALUE, GROUP_EXPANDED, GROUP_DEPTH } from './grouping.const';\nimport { isGrouping, getParsedGroup, isSameGroup } from './grouping.service';\n\n// provide collapse data\nexport function doCollapse(pIndex: number, source: DataType[]) {\n  const model = source[pIndex];\n  const collapseValue = model[PSEUDO_GROUP_ITEM_VALUE];\n  const trimmed: Record<number, boolean> = {};\n  let i = pIndex + 1;\n  const total = source.length;\n  while (i < total) {\n    const currentModel = source[i];\n    if (isGrouping(currentModel)) {\n      const currentValue = currentModel[PSEUDO_GROUP_ITEM_VALUE];\n      if (!currentValue.length || !currentValue.startsWith(collapseValue + ',')) {\n        break;\n      }\n      currentModel[GROUP_EXPANDED] = false;\n    }\n    trimmed[i++] = true;\n  }\n  model[GROUP_EXPANDED] = false;\n  return { trimmed };\n}\n\n/**\n *\n * @param pIndex - physical index\n * @param vIndex - virtual index, need to update item collection\n * @param source - data source\n * @param rowItemsIndexes - rgRow indexes\n */\nexport function doExpand(vIndex: number, source: DataType[], rowItemsIndexes: number[]) {\n  const physicalIndex = rowItemsIndexes[vIndex];\n  const model = source[physicalIndex];\n  const currentGroup = getParsedGroup(model[PSEUDO_GROUP_ITEM_ID]);\n  const trimmed: Record<number, boolean> = {};\n\n  // no group found\n  if (!currentGroup) {\n    return { trimmed };\n  }\n\n  const groupItems: number[] = [];\n  model[GROUP_EXPANDED] = true;\n  let i = physicalIndex + 1;\n  const total = source.length;\n  let groupLevelOnly = 0;\n\n  // go through all rows\n  while (i < total) {\n    const currentModel = source[i];\n    const isGroup = isGrouping(currentModel);\n    // group found\n    if (isGroup) {\n      if (!isSameGroup(currentGroup, model, currentModel)) {\n        break;\n      } else if (!groupLevelOnly) {\n        // if get group first it's group only level\n        groupLevelOnly = currentModel[GROUP_DEPTH];\n      }\n    }\n    // level 0 or same depth\n    if (!groupLevelOnly || (isGroup && groupLevelOnly === currentModel[GROUP_DEPTH])) {\n      trimmed[i] = false;\n      groupItems.push(i);\n    }\n    i++;\n  }\n  const result: {\n    trimmed: Record<number, boolean>;\n    items?: number[];\n  } = {\n    trimmed,\n  };\n  if (groupItems.length) {\n    const items = [...rowItemsIndexes];\n    items.splice(vIndex + 1, 0, ...groupItems);\n    result.items = items;\n  }\n  return result;\n}\n", "import { gatherTrimmedItems, Trimmed, TrimmedEntity } from '@store';\n\nexport const TRIMMED_GROUPING = 'grouping';\n\n/**\n * Prepare trimming updated indexes for grouping\n * @param initiallyTrimed\n * @param firstLevelMap\n * @param secondLevelMap\n */\nexport function processDoubleConversionTrimmed(initiallyTrimed: Trimmed, firstLevelMap: Record<number, number>, secondLevelMap?: Record<number, number>) {\n  const trimemedOptionsToUpgrade: Trimmed = {};\n  /**\n   * go through all groups except grouping\n   */\n  for (let type in initiallyTrimed) {\n    if (type === TRIMMED_GROUPING) {\n      continue;\n    }\n    const items = initiallyTrimed[type];\n    const newItems: TrimmedEntity = {};\n\n    for (let initialIndex in items) {\n      /**\n       * if item exists we find it in collection\n       * we support 2 level of conversions\n       */\n      let newConversionIndex = firstLevelMap[initialIndex];\n      if (secondLevelMap) {\n        newConversionIndex = secondLevelMap[newConversionIndex];\n      }\n\n      /**\n       * if item was trimmed previously\n       * trimming makes sense to apply\n       */\n      if (items[initialIndex]) {\n        newItems[newConversionIndex] = true;\n        /**\n         * If changes present apply changes to new source\n         */\n        if (newConversionIndex !== parseInt(initialIndex, 10)) {\n          trimemedOptionsToUpgrade[type] = newItems;\n        }\n      }\n    }\n  }\n  return trimemedOptionsToUpgrade;\n}\n\nexport function filterOutEmptyGroups(allTrimmedGroups: Trimmed, childrenByGroup: Record<number, number[]> = {}) {\n  const trimmedGroup: TrimmedEntity = {};\n  const allTrimmed = gatherTrimmedItems(allTrimmedGroups);\n  // find is groups are filled\n  for (let groupIndex in childrenByGroup) {\n    const hasChidlren = childrenByGroup[groupIndex].filter(childIndex => !allTrimmed[childIndex]).length > 0;\n    if (!hasChidlren) {\n      trimmedGroup[groupIndex] = true;\n    }\n  }\n  return trimmedGroup;\n}\n", "import {\n  getPhysical,\n  setItems,\n  columnTypes,\n  type TrimmedEntity,\n  type DSourceState,\n} from '@store';\nimport type {\n  BeforeSaveDataDetails,\n  ColumnRegular,\n  DataType,\n  DimensionRows,\n  PluginProviders,\n} from '@type';\n\nimport { BasePlugin } from '../base.plugin';\nimport { FILTER_TRIMMED_TYPE } from '../filter/filter.plugin';\n\nimport type { Observable, ColumnCollection } from '../../utils';\nimport { SortingPlugin } from '../sorting/sorting.plugin';\n\nimport {\n  GROUP_EXPAND_EVENT,\n  GROUPING_ROW_TYPE,\n  PSEUDO_GROUP_COLUMN,\n} from './grouping.const';\nimport { doExpand, doCollapse } from './grouping.row.expand.service';\nimport type {\n  BeforeSourceSetEvent,\n  ExpandedOptions,\n  GroupingOptions,\n  OnExpandEvent,\n} from './grouping.row.types';\nimport {\n  gatherGrouping,\n  getExpanded,\n  getSource,\n  isGrouping,\n  isGroupingColumn,\n} from './grouping.service';\nimport {\n  processDoubleConversionTrimmed,\n  TRIMMED_GROUPING,\n} from './grouping.trimmed.service';\n\nexport * from './grouping.const';\nexport * from './grouping.row.expand.service';\nexport * from './grouping.row.types';\nexport * from './grouping.service';\nexport * from './grouping.row.renderer';\n\nexport class GroupingRowPlugin extends BasePlugin {\n  private options: GroupingOptions | undefined;\n\n  getStore(\n    type: DimensionRows = GROUPING_ROW_TYPE,\n  ): Observable<DSourceState<DataType, DimensionRows>> {\n    return this.providers.data.stores[type].store;\n  }\n\n  constructor(\n    revogrid: HTMLRevoGridElement,\n    providers: PluginProviders,\n  ) {\n    super(revogrid, providers);\n  }\n\n  // befoce cell focus\n  private onFocus(e: CustomEvent<BeforeSaveDataDetails>) {\n    if (isGrouping(e.detail.model)) {\n      e.preventDefault();\n    }\n  }\n\n  // expand event triggered\n  private onExpand({ virtualIndex }: OnExpandEvent) {\n    const { source } = getSource(\n      this.getStore().get('source'),\n      this.getStore().get('proxyItems'),\n    );\n    let newTrimmed = this.getStore().get('trimmed')[TRIMMED_GROUPING];\n\n    let i = getPhysical(this.getStore(), virtualIndex);\n    const isExpanded = getExpanded(source[i]);\n    if (!isExpanded) {\n      const { trimmed, items } = doExpand(\n        virtualIndex,\n        source,\n        this.getStore().get('items'),\n      );\n      newTrimmed = { ...newTrimmed, ...trimmed };\n      if (items) {\n        setItems(this.getStore(), items);\n      }\n    } else {\n      const { trimmed } = doCollapse(i, source);\n      newTrimmed = { ...newTrimmed, ...trimmed };\n      this.revogrid.clearFocus();\n    }\n\n    this.getStore().set('source', source);\n    this.revogrid.addTrimmed(newTrimmed, TRIMMED_GROUPING);\n  }\n\n  private setColumnGrouping(cols?: ColumnRegular[]) {\n    // if 0 column as holder\n    if (cols?.length) {\n      cols[0][PSEUDO_GROUP_COLUMN] = true;\n      return true;\n    }\n    return false;\n  }\n\n  private setColumns({ columns }: ColumnCollection) {\n    for (let type of columnTypes) {\n      if (this.setColumnGrouping(columns[type])) {\n        break;\n      }\n    }\n  }\n\n  // evaluate drag between groups\n  private onDrag(e: CustomEvent<{ from: number; to: number }>) {\n    const { from, to } = e.detail;\n    const isDown = to - from >= 0;\n    const { source } = getSource(\n      this.getStore().get('source'),\n      this.getStore().get('proxyItems'),\n    );\n    const items = this.getStore().get('items');\n    let i = isDown ? from : to;\n    const end = isDown ? to : from;\n    for (; i < end; i++) {\n      const model = source[items[i]];\n      const isGroup = isGrouping(model);\n      if (isGroup) {\n        e.preventDefault();\n        return;\n      }\n    }\n  }\n\n  private beforeTrimmedApply(trimmed: Record<number, boolean>, type: string) {\n    /** Before filter apply remove grouping filtering */\n    if (type === FILTER_TRIMMED_TYPE) {\n      const source = this.getStore().get('source');\n      for (let index in trimmed) {\n        if (trimmed[index] && isGrouping(source[index])) {\n          trimmed[index] = false;\n        }\n      }\n    }\n  }\n\n  private isSortingRunning() {\n    const sortingPlugin = this.providers.plugins.getByClass(SortingPlugin);\n    return !!sortingPlugin?.sortingPromise;\n  }\n\n  /**\n   * Starts global source update with group clearing and applying new one\n   * Initiated when need to reapply grouping\n   */\n  private doSourceUpdate(options?: ExpandedOptions) {\n    /**\n     * Get source without grouping\n     * @param newOldIndexMap - provides us mapping with new indexes vs old indexes, we would use it for trimmed mapping\n     */\n    const store = this.getStore();\n    const { source, prevExpanded, oldNewIndexes } = getSource(\n      store.get('source'),\n      store.get('proxyItems'),\n      true,\n    );\n    const expanded: ExpandedOptions = {\n      prevExpanded,\n      ...options,\n    };\n    /**\n     * Group again\n     * @param oldNewIndexMap - provides us mapping with new indexes vs old indexes\n     */\n    const {\n      sourceWithGroups,\n      depth,\n      trimmed,\n      oldNewIndexMap,\n    } = gatherGrouping(source, this.options?.props || [], expanded);\n\n    const customRenderer = options?.groupLabelTemplate;\n\n    // setup source\n    this.providers.data.setData(\n      sourceWithGroups,\n      GROUPING_ROW_TYPE,\n      this.revogrid.disableVirtualY,\n      { depth, customRenderer },\n      true,\n    );\n    this.updateTrimmed(\n      trimmed,\n      oldNewIndexes ?? {},\n      oldNewIndexMap,\n    );\n  }\n\n  /**\n   * Apply grouping on data set\n   * Clear grouping from source\n   * If source came from other plugin\n   */\n  private onDataSet(data: BeforeSourceSetEvent) {\n    let preservedExpanded: ExpandedOptions['prevExpanded'] = {};\n    if (this.options?.preserveGroupingOnUpdate !== false) {\n      let { prevExpanded } = getSource(\n        this.getStore().get('source'),\n        this.getStore().get('proxyItems'),\n        true,\n      );\n      preservedExpanded = prevExpanded;\n    }\n    const source = data.source.filter(s => !isGrouping(s));\n    const options: ExpandedOptions = {\n      ...(this.revogrid.grouping || {}),\n      prevExpanded: preservedExpanded,\n    };\n    const {\n      sourceWithGroups,\n      depth,\n      trimmed,\n      oldNewIndexMap,\n    } = gatherGrouping(source, this.options?.props || [], options);\n    data.source = sourceWithGroups;\n    this.providers.data.setGrouping({ depth });\n    this.updateTrimmed(trimmed, oldNewIndexMap);\n  }\n\n  /**\n   * External call to apply grouping. Called by revogrid when prop changed.\n   */\n  setGrouping(options: GroupingOptions) {\n    // unsubscribe from all events when group applied\n    this.clearSubscriptions();\n    this.options = options;\n    // clear props, no grouping exists\n    if (!this.options?.props?.length) {\n      this.clearGrouping();\n      return;\n    }\n    // props exist and source initd\n    const store = this.getStore();\n    const { source } = getSource(\n      store.get('source'),\n      store.get('proxyItems'),\n    );\n    if (source.length) {\n      this.doSourceUpdate({ ...options });\n    }\n    // props exist and columns initd\n    for (let t of columnTypes) {\n      if (this.setColumnGrouping(this.providers.column.getColumns(t))) {\n        this.providers.column.refreshByType(t);\n        break;\n      }\n    }\n\n    // if has any grouping subscribe to events again\n    /** if grouping present and new data source arrived */\n    this.addEventListener('beforesourceset', ({ detail }) => {\n      if (!(this.options?.props?.length && detail?.source?.length)) {\n        return;\n      }\n      // if sorting is running don't apply grouping, wait for sorting, then it'll apply in @aftersortingapply\n      if (this.isSortingRunning()) {\n        return;\n      }\n      this.onDataSet(detail);\n    });\n    this.addEventListener('beforecolumnsset', ({ detail }) => {\n      this.setColumns(detail);\n    });\n\n    /**\n     * filter applied need to clear grouping and apply again\n     * based on new results can be new grouping\n     */\n    this.addEventListener(\n      'beforetrimmed',\n      ({ detail: { trimmed, trimmedType } }) =>\n        this.beforeTrimmedApply(trimmed, trimmedType),\n    );\n    /**\n     * sorting applied need to clear grouping and apply again\n     * based on new results whole grouping order will changed\n     */\n    this.addEventListener('aftersortingapply', () => {\n      if (!this.options?.props?.length) {\n        return;\n      }\n      this.doSourceUpdate({ ...this.options });\n    });\n\n    /**\n     * Apply logic for focus inside of grouping\n     * We can't focus on grouping rows, navigation only inside of groups for now\n     */\n    this.addEventListener('beforecellfocus', e => this.onFocus(e));\n    /**\n     * Prevent rgRow drag outside the group\n     */\n    this.addEventListener('roworderchanged', e => this.onDrag(e));\n\n    /**\n     * When grouping expand icon was clicked\n     */\n    this.addEventListener(GROUP_EXPAND_EVENT, e => this.onExpand(e.detail));\n  }\n\n  // clear grouping\n  clearGrouping() {\n    // clear columns\n    columnTypes.forEach(t => {\n      const cols = this.providers.column.getColumns(t);\n      let deleted = false;\n      cols.forEach(c => {\n        if (isGroupingColumn(c)) {\n          delete c[PSEUDO_GROUP_COLUMN];\n          deleted = true;\n        }\n      });\n      // if column store had grouping clear and refresh\n      if (deleted) {\n        this.providers.column.refreshByType(t);\n      }\n    });\n    // clear rows\n    const { source, oldNewIndexes } = getSource(\n      this.getStore().get('source'),\n      this.getStore().get('proxyItems'),\n      true,\n    );\n    this.providers.data.setData(\n      source,\n      GROUPING_ROW_TYPE,\n      this.revogrid.disableVirtualY,\n      undefined,\n      true,\n    );\n    this.updateTrimmed(undefined, undefined, oldNewIndexes);\n  }\n\n  private updateTrimmed(\n    trimmedGroup: TrimmedEntity = {},\n    firstLevelMap: Record<number, number> = {},\n    secondLevelMap?: Record<number, number>,\n  ) {\n    // map previously trimmed data\n    const trimemedOptionsToUpgrade = processDoubleConversionTrimmed(\n      this.getStore().get('trimmed'),\n      firstLevelMap,\n      secondLevelMap,\n    );\n    for (let type in trimemedOptionsToUpgrade) {\n      this.revogrid.addTrimmed(trimemedOptionsToUpgrade[type], type);\n    }\n\n    // const emptyGroups = this.filterOutEmptyGroups(trimemedOptionsToUpgrade, childrenByGroup);\n\n    // setup trimmed data for grouping\n    this.revogrid.addTrimmed({ ...trimmedGroup }, TRIMMED_GROUPING);\n  }\n}\n", "const COLUMN_DRAG_CLASS = 'column-drag-start';\n\nexport class ColumnOrderHandler {\n  private element?: HTMLDivElement;\n  private autoscrollEl?: HTMLElement;\n  private offset = 0;\n\n  renderAutoscroll(_: MouseEvent, parent: HTMLElement | null) {\n    if (!parent) {\n      return;\n    }\n    this.autoscrollEl = document.createElement('div');\n    this.autoscrollEl.classList.add('drag-auto-scroll-y');\n    parent.appendChild(this.autoscrollEl);\n  }\n\n  autoscroll(pos: number, dataContainerSize: number, direction = 'translateX') {\n    if (!this.autoscrollEl) {\n      return;\n    }\n    const helperOffset = 10;\n    // calculate current y position inside of the grid active holder\n    // 3 - size of element + border\n    const maxScroll = Math.min(pos + helperOffset, dataContainerSize - 3);\n\n    this.autoscrollEl.style.transform = `${direction}(${maxScroll}px)`;\n    this.autoscrollEl.scrollIntoView({\n      block: 'nearest',\n      inline: 'nearest',\n    });\n  }\n\n  start(e: MouseEvent, { dataEl, gridRect, scrollEl, gridEl }: {\n    dataEl: HTMLElement;\n    gridRect: DOMRect;\n    scrollEl: Element;\n    gridEl: Element;\n  }, dir: 'top' | 'left'  = 'left') {\n    gridEl.classList.add(COLUMN_DRAG_CLASS);\n    const scrollContainerRect = scrollEl.getBoundingClientRect();\n    if (scrollContainerRect) {\n      this.offset = scrollContainerRect[dir] - gridRect[dir];\n    }\n    this.renderAutoscroll(e, dataEl);\n  }\n\n  stop(gridEl: Element) {\n    gridEl.classList.remove(COLUMN_DRAG_CLASS);\n    if (this.element) {\n      this.element.hidden = true;\n    }\n    this.offset = 0;\n    this.autoscrollEl?.remove();\n    this.autoscrollEl = undefined;\n  }\n\n  showHandler(pos: number, size: number, direction = 'translateX') {\n    if (!this.element) {\n      return;\n    }\n    // do not allow overcross top of the scrollable area, header excluded\n    if (this.offset) {\n      pos = Math.max(pos, this.offset);\n    }\n    // can not be bigger then grid end\n    pos = Math.min(pos, size);\n    this.element.style.transform = `${direction}(${pos}px)`;\n    this.element.hidden = false;\n  }\n\n  render() {\n    const el = this.element = document.createElement('div');\n    el.classList.add('drag-position-y');\n    el.hidden = true;\n    return el;\n  }\n}\n", "/**\n * Plugin for column manual move\n */\nimport debounce from 'lodash/debounce';\nimport each from 'lodash/each';\nimport { getItemByPosition } from '@store';\nimport { BasePlugin } from '../base.plugin';\nimport { ColumnOrderHandler } from './order-column.handler';\nimport { dispatch } from '../dispatcher';\nimport type { ColumnPropProp, ColumnTemplateProp, DimensionSettingsState, PositionItem, DimensionCols, MultiDimensionType, PluginProviders } from '@type';\nimport { ON_COLUMN_CLICK } from '../../components/header/header-cell-renderer';\nimport { isColGrouping } from '../../utils/column.utils';\n\nconst COLUMN_CLICK = ON_COLUMN_CLICK;\nconst MOVE = 'columndragmousemove';\nconst DRAG_END = 'columndragend';\nconst BEFORE_DRAG_END = 'beforecolumndragend';\n\n// use this event subscription to drop D&D for particular columns\nconst DRAG_START = 'columndragstart';\n\nexport type DragStartEventDetails = {\n  event: MouseEvent;\n  data: ColumnPropProp;\n};\n\ntype StaticData = {\n  startPos: number;\n  startItem: PositionItem;\n  data: ColumnTemplateProp;\n  dataEl: HTMLElement;\n  scrollEl: Element;\n  gridEl: HTMLElement;\n  cols: DimensionSettingsState;\n};\n\ntype LocalSubscriptions = Record<string, LocalSubscription>;\ntype LocalSubscription = {\n  target: Element | Document;\n  callback(...params: any[]): void;\n};\nexport type ColumnDragEventData = {\n  elRect: DOMRect;\n  gridRect: DOMRect;\n  scrollOffset: number;\n  type: DimensionCols;\n};\nexport class ColumnMovePlugin extends BasePlugin {\n  private moveFunc = debounce((e: MouseEvent) => this.doMove(e), 5);\n  private staticDragData: StaticData | null = null;\n  private dragData: ColumnDragEventData | null = null;\n  private readonly orderUi: ColumnOrderHandler;\n  protected readonly localSubscriptions: LocalSubscriptions = {};\n  constructor(public revogrid: HTMLRevoGridElement, public providers: PluginProviders) {\n    super(revogrid, providers);\n    this.orderUi = new ColumnOrderHandler();\n    revogrid.appendChild(this.orderUi.render());\n    revogrid.classList.add('column-draggable');\n\n    // Register events\n    this.localSubscriptions['mouseleave'] = {\n      target: document,\n      callback: (e: MouseEvent) => this.onMouseOut(e),\n    };\n    this.localSubscriptions['mouseup'] = {\n      target: document,\n      callback: (e: MouseEvent) => this.onMouseUp(e),\n    };\n\n    this.localSubscriptions['mousemove'] = {\n      target: document,\n      callback: (e: MouseEvent) => this.move(e),\n    };\n\n    this.addEventListener(COLUMN_CLICK, ({ detail }) => this.dragStart(detail));\n  }\n\n  dragStart({ event, data }: DragStartEventDetails) {\n    if (event.defaultPrevented) {\n      return;\n    }\n    const { defaultPrevented } = dispatch(this.revogrid, DRAG_START, data);\n    // check if allowed to drag particulat column\n    if (defaultPrevented) {\n      return;\n    }\n    this.clearOrder();\n    const { mouseleave, mouseup, mousemove } = this.localSubscriptions;\n    mouseleave.target.addEventListener('mouseleave', mouseleave.callback);\n    mouseup.target.addEventListener('mouseup', mouseup.callback);\n\n    const dataEl = (event.target as HTMLElement).closest('revogr-header');\n    const scrollEl = (event.target as HTMLElement).closest('revogr-viewport-scroll');\n    if (!dataEl || !scrollEl) {\n      return;\n    }\n\n    // no grouping drag and no row header column drag\n    if (isColGrouping(data) || data.providers.type === 'rowHeaders') {\n      return;\n    }\n\n    const cols = this.getDimension(data.pin || 'rgCol');\n    const gridRect = this.revogrid.getBoundingClientRect();\n    const elRect = dataEl.getBoundingClientRect();\n    const startItem = getItemByPosition(\n      cols,\n      getLeftRelative(event.x, gridRect.left, elRect.left - gridRect.left));\n  \n    this.staticDragData = {\n      startPos: event.x,\n      startItem,\n      data,\n      dataEl,\n      scrollEl,\n      gridEl: this.revogrid,\n      cols,\n    };\n    this.dragData = this.getData(this.staticDragData);\n    mousemove.target.addEventListener('mousemove', mousemove.callback);\n    this.orderUi.start(event, {\n      ...this.dragData,\n      ...this.staticDragData,\n    });\n  }\n\n  doMove(e: MouseEvent) {\n    if (!this.staticDragData) {\n      return;\n    }\n\n    const dragData = (this.dragData = this.getData(this.staticDragData));\n    if (!dragData) {\n      return;\n    }\n    const start = this.staticDragData.startPos;\n    if (Math.abs(start - e.x) > 10) {\n      const x = getLeftRelative(e.x, this.dragData.gridRect.left, this.dragData.scrollOffset);\n      const rgCol = getItemByPosition(this.staticDragData.cols, x);\n      this.orderUi.autoscroll(x, dragData.elRect.width);\n\n      // prevent position change if out of bounds\n      if (rgCol.itemIndex >= this.staticDragData.cols.count) {\n        return;\n      }\n      this.orderUi.showHandler(\n        rgCol.end + dragData.scrollOffset,\n        dragData.gridRect.width\n      );\n    }\n  }\n\n  move(e: MouseEvent) {\n    dispatch(this.revogrid, MOVE, e);\n    // then do move\n    this.moveFunc(e);\n  }\n  onMouseOut(_: MouseEvent) {\n    this.clearOrder();\n  }\n  onMouseUp(e: MouseEvent) {\n    // apply new positions\n    if (this.dragData && this.staticDragData) {\n      let relativePos = getLeftRelative(e.x, this.dragData.gridRect.left, this.dragData.scrollOffset);\n      if (relativePos < 0) {\n        relativePos = 0;\n      }\n      const newPosition = getItemByPosition(this.staticDragData.cols, relativePos);\n\n      const store = this.providers.column.stores[this.dragData.type].store;\n      const newItems = [...store.get('items')];\n\n      // prevent position change if needed\n      const { defaultPrevented: stopDrag } = dispatch(this.revogrid, BEFORE_DRAG_END, {\n        ...this.staticDragData,\n        startPosition: this.staticDragData.startItem,\n        newPosition,\n        newItem: store.get('source')[newItems[this.staticDragData.startItem.itemIndex]]\n      });\n      if (!stopDrag) {\n        const prevItems = [...newItems];\n        // todo: if move item out of group remove item from group\n        const toMove = newItems.splice(this.staticDragData.startItem.itemIndex, 1);\n        newItems.splice(newPosition.itemIndex, 0, ...toMove);\n        store.set('items', newItems);\n        this.providers.dimension.updateSizesPositionByNewDataIndexes(this.dragData.type, newItems, prevItems);\n      }\n      dispatch(this.revogrid, DRAG_END, this.dragData);\n    }\n    this.clearOrder();\n  }\n\n  private clearLocalSubscriptions() {\n    each(this.localSubscriptions, ({ target, callback }, key) => target.removeEventListener(key, callback));\n  }\n\n  clearOrder() {\n    this.staticDragData = null;\n    this.dragData = null;\n    this.clearLocalSubscriptions();\n    this.orderUi.stop(this.revogrid);\n  }\n  /**\n   * Clearing subscription\n   */\n  clearSubscriptions() {\n    super.clearSubscriptions();\n    this.clearLocalSubscriptions();\n  }\n\n  private getData({\n    gridEl,\n    dataEl,\n    data,\n  }: StaticData): ColumnDragEventData {\n    const gridRect = gridEl.getBoundingClientRect();\n    const elRect = dataEl.getBoundingClientRect();\n    const scrollOffset = elRect.left - gridRect.left;\n    return {\n      elRect,\n      gridRect,\n      type: data.pin || 'rgCol',\n      scrollOffset,\n    };\n  }\n  private getDimension(type: MultiDimensionType) {\n    return this.providers.dimension.stores[type].getCurrentState();\n  }\n}\n\nexport function getLeftRelative(\n  absoluteX: number,\n  gridPos: number,\n  offset: number\n): number {\n  return absoluteX - gridPos - offset;\n}\n", "import reduce from 'lodash/reduce';\nimport {\n  columnTypes,\n  DataStore,\n  getSourceItem,\n  getSourceItemVirtualIndexByProp,\n  Groups,\n  setSourceByPhysicalIndex,\n  setSourceByVirtualIndex,\n} from '@store';\nimport type {\n  ColumnProp,\n  ColumnRegular,\n  DimensionCols,\n} from '@type';\nimport { ColumnCollection, getColumnType } from '../utils/column.utils';\n\nexport type ColumnDataSources = Record<\n  DimensionCols,\n  DataStore<ColumnRegular, DimensionCols>\n>;\n\nexport default class ColumnDataProvider {\n  readonly dataSources: ColumnDataSources;\n  collection: ColumnCollection | null = null;\n\n  get stores() {\n    return this.dataSources;\n  }\n  constructor() {\n    this.dataSources = columnTypes.reduce(\n      (sources: ColumnDataSources, k: DimensionCols) => {\n        sources[k] = new DataStore(k);\n        return sources;\n      },\n      {} as ColumnDataSources,\n    );\n  }\n\n  column(c: number, type: DimensionCols = 'rgCol'): ColumnRegular | undefined {\n    return this.getColumn(c, type);\n  }\n\n  getColumn(\n    virtualIndex: number,\n    type: DimensionCols,\n  ): ColumnRegular | undefined {\n    return getSourceItem(this.dataSources[type].store, virtualIndex);\n  }\n\n  getRawColumns(): Record<DimensionCols, ColumnRegular[]> {\n    return reduce(\n      this.dataSources,\n      (\n        result: Record<DimensionCols, ColumnRegular[]>,\n        item,\n        type: DimensionCols,\n      ) => {\n        result[type] = item.store.get('source');\n        return result;\n      },\n      {\n        rgCol: [],\n        colPinStart: [],\n        colPinEnd: [],\n      },\n    );\n  }\n\n  getColumns(type: DimensionCols | 'all' = 'all'): ColumnRegular[] {\n    const columnsByType = this.getRawColumns();\n    if (type !== 'all') {\n      return columnsByType[type];\n    }\n    return columnTypes.reduce((r: ColumnRegular[], t) => [...r, ...columnsByType[t]], []);\n  }\n\n  getColumnIndexByProp(prop: ColumnProp, type: DimensionCols): number {\n    return getSourceItemVirtualIndexByProp(this.dataSources[type].store, prop);\n  }\n\n  getColumnByProp(prop: ColumnProp) {\n    return this.collection?.columnByProp[prop];\n  }\n\n  refreshByType(type: DimensionCols) {\n    this.dataSources[type].refresh();\n  }\n\n  /**\n   * Main method to set columns\n   */\n  setColumns(data: ColumnCollection): ColumnCollection {\n    columnTypes.forEach(k => {\n      // set columns data\n      this.dataSources[k].updateData(data.columns[k], {\n        // max depth level\n        depth: data.maxLevel,\n\n        // groups\n        groups: data.columnGrouping[k].reduce((res: Groups, g) => {\n          if (!res[g.level]) {\n            res[g.level] = [];\n          }\n          res[g.level].push(g);\n          return res;\n        }, {}),\n      });\n    });\n    this.collection = data;\n    return data;\n  }\n\n  /**\n   * Used in plugins\n   * Modify columns in store\n   */\n  updateColumns(updatedColumns: ColumnRegular[]) {\n    // collect column by type and propert\n    const columnByKey = updatedColumns.reduce(\n      (\n        res: Partial<Record<DimensionCols, Record<ColumnProp, ColumnRegular>>>,\n        c,\n      ) => {\n        const type = getColumnType(c);\n        if (!res[type]) {\n          res[type] = {};\n        }\n        res[type][c.prop] = c;\n        return res;\n      },\n      {},\n    );\n\n    // find indexes in source\n    const colByIndex: Partial<\n      Record<DimensionCols, Record<number, ColumnRegular>>\n    > = {};\n    for (const t in columnByKey) {\n      if (!columnByKey.hasOwnProperty(t)) {\n        continue;\n      }\n      const type = t as DimensionCols;\n      const colsToUpdate = columnByKey[type];\n      const sourceItems = this.dataSources[type].store.get('source');\n      colByIndex[type] = {};\n      for (let i = 0; i < sourceItems.length; i++) {\n        const column = sourceItems[i];\n        const colToUpdateIfExists = colsToUpdate?.[column.prop];\n\n        // update column if exists in source\n        if (colToUpdateIfExists) {\n          colByIndex[type][i] = colToUpdateIfExists;\n        }\n      }\n    }\n    for (const t in colByIndex) {\n      if (!colByIndex.hasOwnProperty(t)) {\n        continue;\n      }\n      const type = t as DimensionCols;\n      setSourceByPhysicalIndex(\n        this.dataSources[type].store,\n        colByIndex[type] || {},\n      );\n    }\n  }\n\n  updateColumn(column: ColumnRegular, index: number) {\n    const type = getColumnType(column);\n    setSourceByVirtualIndex(this.dataSources[type].store, { [index]: column });\n  }\n}\n", "import reduce from 'lodash/reduce';\n\nimport {\n  isRowType,\n  rowTypes,\n  DataStore,\n  getSourceItem,\n  getVisibleSourceItem,\n  Groups,\n  Trimmed,\n} from '@store';\nimport DimensionProvider from './dimension.provider';\nimport type { GroupLabelTemplateFunc } from '../plugins/groupingRow/grouping.row.types';\nimport type {\n  DataLookup,\n  DimensionRows,\n  DataType,\n  BeforeSaveDataDetails,\n} from '@type';\n\nexport type RowDataSources = {\n  [T in DimensionRows]: DataStore<DataType, DimensionRows>;\n};\n\n/**\n * Data source provider\n * \n * @dependsOn DimensionProvider\n */\n\nexport class DataProvider {\n  public readonly stores: RowDataSources;\n  constructor(private dimensionProvider: DimensionProvider) {\n    this.stores = reduce(\n      rowTypes,\n      (sources: Partial<RowDataSources>, k: DimensionRows) => {\n        sources[k] = new DataStore(k);\n        return sources;\n      },\n      {},\n    ) as RowDataSources;\n  }\n\n  setData(\n    data: DataType[],\n    type: DimensionRows = 'rgRow',\n    disableVirtualRows = false,\n    grouping?: {\n      depth: number;\n      groups?: Groups;\n      customRenderer?: GroupLabelTemplateFunc;\n    },\n    silent = false,\n  ): DataType[] {\n    // set rgRow data\n    this.stores[type].updateData([...data], grouping, silent);\n\n    // for pinned row no need virtual data\n    const noVirtual = type !== 'rgRow' || disableVirtualRows;\n    this.dimensionProvider.setData(data.length, type, noVirtual);\n    return data;\n  }\n\n  getModel(virtualIndex: number, type: DimensionRows = 'rgRow') {\n    const store = this.stores[type].store;\n    return getSourceItem(store, virtualIndex);\n  }\n\n  changeOrder({ rowType = 'rgRow', from, to }: { rowType: DimensionRows, from: number; to: number }) {\n    const storeService = this.stores[rowType];\n\n    // take currently visible row indexes\n    const newItemsOrder = [...storeService.store.get('proxyItems')];\n    const prevItems = storeService.store.get('items');\n\n    // take out\n    const toMove = newItemsOrder.splice(\n      newItemsOrder.indexOf(prevItems[from]), // get index in proxy\n      1\n    );\n    // insert before\n    newItemsOrder.splice(\n      newItemsOrder.indexOf(prevItems[to]),  // get index in proxy\n      0,\n      ...toMove\n    );\n    storeService.setData({\n      proxyItems: newItemsOrder,\n    });\n\n    // take currently visible row indexes\n    const newItems = storeService.store.get('items');\n    this.dimensionProvider.updateSizesPositionByNewDataIndexes(\n      rowType,\n      newItems,\n      prevItems\n    );\n  }\n\n  setCellData(\n    { type, rowIndex, prop, val }: Pick<BeforeSaveDataDetails, 'type' | 'rowIndex' | 'prop' | 'val'>, \n    mutate = true,\n  ) {\n    const model = this.getModel(rowIndex, type);\n    model[prop] = val;\n    this.stores[type].setSourceData({ [rowIndex]: model }, mutate);\n  }\n\n  setRangeData(data: DataLookup, type: DimensionRows) {\n    const items: Record<number, DataType> = {};\n    for (let rowIndex in data) {\n      const oldModel = (items[rowIndex] = getSourceItem(\n        this.stores[type].store,\n        parseInt(rowIndex, 10),\n      ));\n      if (!oldModel) {\n        continue;\n      }\n      for (let prop in data[rowIndex]) {\n        oldModel[prop] = data[rowIndex][prop];\n      }\n    }\n    this.stores[type].setSourceData(items);\n  }\n\n  refresh(type: DimensionRows | 'all' = 'all') {\n    if (isRowType(type)) {\n      this.refreshItems(type);\n    }\n    rowTypes.forEach((t: DimensionRows) => this.refreshItems(t));\n  }\n\n  refreshItems(type: DimensionRows = 'rgRow') {\n    const items = this.stores[type].store.get('items');\n    this.stores[type].setData({ items: [...items] });\n  }\n\n  setGrouping({ depth }: { depth: number }, type: DimensionRows = 'rgRow') {\n    this.stores[type].setData({ groupingDepth: depth });\n  }\n\n  setTrimmed(trimmed: Trimmed, type: DimensionRows = 'rgRow') {\n    const store = this.stores[type];\n    store.addTrimmed(trimmed);\n    this.dimensionProvider.setTrimmed(trimmed, type);\n    if (type === 'rgRow') {\n      this.dimensionProvider.setData(\n        getVisibleSourceItem(store.store).length,\n        type,\n      );\n    }\n  }\n}\n", "import reduce from 'lodash/reduce';\nimport debounce from 'lodash/debounce';\nimport ViewportProvider from './viewport.provider';\nimport { RESIZE_INTERVAL } from '../utils/consts';\n\nimport {\n  columnTypes,\n  rowTypes,\n  getItemByIndex,\n  DimensionStore,\n  DimensionStoreCollection,\n  gatherTrimmedItems,\n  Trimmed,\n} from '@store';\nimport type {\n  DimensionCols,\n  DimensionType,\n  MultiDimensionType,\n  ColumnRegular,\n  DimensionSettingsState,\n  ViewPortScrollEvent,\n  ViewSettingSizeProp,\n  ViewportState,\n} from '@type';\nimport { getColumnSizes } from '../utils/column.utils';\n\nexport type DimensionConfig = {\n  realSizeChanged(k: MultiDimensionType): void;\n};\n/**\n * Dimension provider\n * Stores dimension information and custom sizes\n * \n * @dependsOn ViewportProvider\n */\nexport default class DimensionProvider {\n  readonly stores: DimensionStoreCollection;\n  constructor(\n    private viewports: ViewportProvider,\n    config: DimensionConfig,\n  ) {\n    const sizeChanged = debounce(\n      (k: MultiDimensionType) => config.realSizeChanged(k),\n      RESIZE_INTERVAL,\n    );\n    this.stores = reduce(\n      [...rowTypes, ...columnTypes],\n      (sources: Partial<DimensionStoreCollection>, t: MultiDimensionType) => {\n        sources[t] = new DimensionStore(t);\n        sources[t].store.onChange('realSize', () => sizeChanged(t));\n        return sources;\n      },\n      {},\n    ) as DimensionStoreCollection;\n  }\n\n  /**\n   * Clear old sizes from dimension and viewports\n   * @param type - dimension type\n   * @param count - count of items\n   */\n  clearSize(t: MultiDimensionType, count: number) {\n    this.stores[t].drop();\n    // after we done with drop trigger viewport recalculaction\n    this.viewports.stores[t].setOriginalSizes(\n      this.stores[t].store.get('originItemSize'),\n    );\n    this.setItemCount(count, t);\n  }\n\n  /**\n   * Apply new custom sizes to dimension and view port\n   * @param type - dimension type\n   * @param sizes - new custom sizes\n   * @param keepOld - keep old sizes merge new with old\n   */\n  setCustomSizes(\n    type: MultiDimensionType,\n    sizes: ViewSettingSizeProp,\n    keepOld = false,\n  ) {\n    let newSizes = sizes;\n    if (keepOld) {\n      const oldSizes = this.stores[type].store.get('sizes');\n      newSizes = {\n        ...oldSizes,\n        ...sizes,\n      };\n    }\n    this.stores[type].setDimensionSize(newSizes);\n    this.setViewPortCoordinate({\n      type,\n      force: true,\n    });\n  }\n\n  setItemCount(realCount: number, type: MultiDimensionType) {\n    this.viewports.stores[type].setViewport({ realCount });\n    this.stores[type].setStore({ count: realCount });\n  }\n\n  /**\n   * Apply trimmed items\n   * @param trimmed - trimmed items\n   * @param type\n   */\n  setTrimmed(trimmed: Trimmed, type: MultiDimensionType) {\n    const allTrimmed = gatherTrimmedItems(trimmed);\n    const dimStoreType = this.stores[type];\n    dimStoreType.setStore({ trimmed: allTrimmed });\n    this.setViewPortCoordinate({\n      type,\n      force: true,\n    });\n  }\n\n  /**\n   * Sets dimension data and viewport coordinate\n   * @param itemCount\n   * @param type - dimension type\n   * @param noVirtual - disable virtual data\n   */\n  setData(itemCount: number, type: MultiDimensionType, noVirtual = false) {\n    this.setItemCount(itemCount, type);\n\n    // Virtualization will get disabled\n    if (noVirtual) {\n      const dimension = this.stores[type].getCurrentState();\n      this.viewports.stores[type].setViewport({\n        virtualSize: dimension.realSize,\n      });\n    }\n    this.setViewPortCoordinate({\n      type,\n    });\n  }\n\n  /**\n   * Applies new columns to the dimension provider\n   * @param columns - new columns data\n   * @param disableVirtualX - disable virtual data for X axis\n   */\n  applyNewColumns(\n    columns: Record<DimensionCols, ColumnRegular[]>,\n    disableVirtualX: boolean,\n    keepOld = false,\n  ) {\n    // Apply new columns to dimension provider\n    for (let type of columnTypes) {\n      if (!keepOld) {\n        // Clear existing data in the dimension provider\n        this.stores[type].drop();\n      }\n\n      // Get the new columns for the current type\n      const items = columns[type];\n\n      // Determine if virtual data should be disabled for the current type\n      const noVirtual = type !== 'rgCol' || disableVirtualX;\n\n      // Set the items count in the dimension provider\n      this.stores[type].setStore({ count: items.length });\n\n      // Set the custom sizes for the columns\n      const newSizes = getColumnSizes(items);\n      this.stores[type].setDimensionSize(newSizes);\n\n      // Update the viewport with new data\n      const vpUpdate: Partial<ViewportState> = {\n        // This triggers drop on realCount change\n        realCount: items.length,\n      };\n\n      // If virtual data is disabled, set the virtual size to the real size\n      if (noVirtual) {\n        vpUpdate.virtualSize = this.stores[type].getCurrentState().realSize;\n      }\n\n      // Update the viewport\n      this.viewports.stores[type].setViewport(vpUpdate);\n      this.setViewPortCoordinate({\n        type,\n      });\n    }\n  }\n\n  /**\n   * Gets the full size of the grid by summing up the sizes of all dimensions\n   * Goes through all dimensions columnTypes (x) and rowTypes (y) and sums up their sizes\n   */\n\n  getFullSize(): { x: number; y: number } {\n    let x = 0;\n    let y = 0;\n    for (let type of columnTypes) {\n      x += this.stores[type]?.store.get('realSize') || 0;\n    }\n    for (let type of rowTypes) {\n      y += this.stores[type]?.store.get('realSize') || 0;\n    }\n    return { y, x };\n  }\n\n  setViewPortCoordinate({\n    type,\n    coordinate = this.viewports.stores[type].lastCoordinate,\n    force = false,\n  }: {\n    coordinate?: number;\n    type: MultiDimensionType;\n    force?: boolean;\n  }) {\n    const dimension = this.stores[type].getCurrentState();\n    this.viewports.stores[type].setViewPortCoordinate(\n      coordinate,\n      dimension,\n      force,\n    );\n  }\n\n  getViewPortPos(e: ViewPortScrollEvent): number {\n    const dimension = this.stores[e.dimension].getCurrentState();\n    const item = getItemByIndex(dimension, e.coordinate);\n\n    return item.start;\n  }\n\n  setSettings(\n    data: Partial<DimensionSettingsState>,\n    dimensionType: DimensionType,\n  ) {\n    let stores: MultiDimensionType[] = [];\n    switch (dimensionType) {\n      case 'rgCol':\n        stores = columnTypes;\n        break;\n      case 'rgRow':\n        stores = rowTypes;\n        break;\n    }\n    for (let s of stores) {\n      this.stores[s].setStore(data);\n    }\n  }\n\n  updateSizesPositionByNewDataIndexes(\n    type: MultiDimensionType,\n    newItemsOrder: number[],\n    prevItemsOrder: number[] = [],\n  ) {\n    // Move custom sizes to new order\n    this.stores[type].updateSizesPositionByIndexes(\n      newItemsOrder,\n      prevItemsOrder,\n    );\n    this.setViewPortCoordinate({\n      type,\n      force: true,\n    });\n  }\n}\n", "import reduce from 'lodash/reduce';\nimport { columnTypes, rowTypes, type ViewportStoreCollection, ViewportStore } from '@store';\nimport type { MultiDimensionType, ViewportState } from '@type';\n\n\nexport default class ViewportProvider {\n  readonly stores: ViewportStoreCollection;\n  constructor() {\n    this.stores = reduce(\n      [...rowTypes, ...columnTypes],\n      (sources: Partial<ViewportStoreCollection>, k: MultiDimensionType) => {\n        sources[k] = new ViewportStore(k);\n        return sources;\n      },\n      {},\n    ) as ViewportStoreCollection;\n  }\n\n  setViewport(type: MultiDimensionType, data: Partial<ViewportState>) {\n    this.stores[type].setViewport(data);\n  }\n}\n", "import DimensionProvider from '../../services/dimension.provider';\nimport { type SelectionStoreConnector, EMPTY_INDEX } from '../../services/selection.store.connector';\nimport ViewportProvider from '../../services/viewport.provider';\nimport { columnTypes, DSourceState, getSourceItem, getVisibleSourceItem, rowTypes } from '@store';\nimport { OrdererService } from '../order/order-renderer';\nimport GridScrollingService from './viewport.scrolling.service';\nimport { CONTENT_SLOT, FOOTER_SLOT, HEADER_SLOT, viewportDataPartition, VPPartition } from './viewport.helpers';\n\nimport ColumnDataProvider from '../../services/column.data.provider';\nimport { DataProvider } from '../../services/data.provider';\nimport type {\n  AllDimensionType,\n  Cell,\n  ColumnRegular,\n  DimensionCols,\n  DimensionRows,\n  HeaderProperties,\n  RangeArea,\n  SlotType,\n  ViewportColumn,\n  ViewportData,\n  ViewportProperties,\n  ViewportProps,\n  ViewPortResizeEvent,\n  ViewportState,\n  ViewSettingSizeProp,\n} from '@type';\nimport { Observable } from '../../utils';\n\nexport type ResizeDetails = { [index: number]: ColumnRegular };\ntype Config = {\n  columnProvider: ColumnDataProvider;\n  dataProvider: DataProvider;\n  dimensionProvider: DimensionProvider;\n  viewportProvider: ViewportProvider;\n  scrollingService: GridScrollingService;\n  orderService: OrdererService;\n  selectionStoreConnector: SelectionStoreConnector;\n\n  disableVirtualX?: boolean;\n  disableVirtualY?: boolean;\n\n  resize(r: ResizeDetails): void;\n};\n\nexport type FocusedData = {\n  model: any;\n  cell: Cell;\n  colType: DimensionCols;\n  rowType: DimensionRows;\n  column?: ColumnRegular;\n};\n\n/** Collect Column data */\nfunction gatherColumnData(data: ViewportColumn) {\n  const colDimension = data.dimensions[data.colType].store;\n  const realWidth = colDimension.get('realSize');\n\n  const prop: ViewportProperties = {\n    contentWidth: realWidth,\n    class: data.colType,\n    contentHeight: data.contentHeight,\n    key: data.colType,\n    colType: data.colType,\n    onResizeviewport: data.onResizeviewport,\n    // set viewport size to real size\n    style: data.fixWidth ? { minWidth: `${realWidth}px` } : undefined,\n  };\n\n  const headerProp: HeaderProperties = {\n    colData: getVisibleSourceItem(data.colStore),\n    dimensionCol: colDimension,\n    type: data.colType,\n    groups: data.colStore.get('groups'),\n    groupingDepth: data.colStore.get('groupingDepth'),\n    resizeHandler: data.colType === 'colPinEnd' ? ['l'] : undefined,\n    onHeaderresize: data.onHeaderresize,\n  };\n\n  return {\n    prop,\n    type: data.colType,\n    position: data.position,\n    headerProp,\n    viewportCol: data.viewports[data.colType].store,\n  };\n}\n\nexport default class ViewportService {\n  readonly columns: ViewportProps[];\n  constructor(\n    private config: Config,\n    contentHeight: number,\n  ) {\n    this.config.selectionStoreConnector?.beforeUpdate();\n\n    // ----------- Handle columns ----------- //\n\n    // Transform data from stores and apply it to different components\n    const columns: ViewportProps[] = [];\n    let x = 0; // we increase x only if column present\n    columnTypes.forEach(val => {\n      const colStore = config.columnProvider.stores[val].store;\n      // only columns that have data show\n      if (!colStore.get('items').length) {\n        return;\n      }\n      const column: ViewportColumn = {\n        colType: val,\n        position: { x, y: 1 },\n\n        contentHeight,\n        // only central column has dynamic width\n        fixWidth: val !== 'rgCol',\n\n        viewports: config.viewportProvider.stores,\n        dimensions: config.dimensionProvider.stores,\n        rowStores: config.dataProvider.stores,\n\n        colStore,\n        onHeaderresize: e => this.onColumnResize(val, e, colStore),\n      };\n      if (val === 'rgCol') {\n        column.onResizeviewport = (e: CustomEvent<ViewPortResizeEvent>) => {\n          const vpState: Partial<ViewportState> = {\n            clientSize: e.detail.size,\n          };\n\n          // virtual size will be handled by dimension provider if disabled\n          if ((e.detail.dimension === 'rgRow' && !config.disableVirtualY)\n              || (e.detail.dimension === 'rgCol' && !config.disableVirtualX)) {\n                vpState.virtualSize = e.detail.size;\n          }\n          config.viewportProvider?.setViewport(e.detail.dimension, vpState);\n        };\n      }\n      const colData = gatherColumnData(column);\n      const columnSelectionStore = this.registerCol(colData.position.x, val);\n\n      // render per each column data collections vertically\n      const dataPorts = this.dataViewPort(column).reduce<ViewportData[]>(\n        (r, rgRow) => {\n          // register selection store for Segment\n          const segmentSelection = this.registerSegment(rgRow.position);\n          segmentSelection.setLastCell(rgRow.lastCell);\n\n          // register selection store for Row\n          const rowSelectionStore = this.registerRow(\n            rgRow.position.y,\n            rgRow.type,\n          );\n          const rowDef: ViewportData = {\n            colType: val,\n            ...rgRow,\n            rowSelectionStore,\n            selectionStore: segmentSelection.store,\n            ref: (e) =>\n              config.selectionStoreConnector.registerSection(e),\n            onSetrange: e => {\n              segmentSelection.setRangeArea(e.detail);\n            },\n            onSettemprange: e => segmentSelection.setTempArea(e.detail),\n            onFocuscell: e => {\n              // todo: multi focus\n              segmentSelection.clearFocus();\n              config.selectionStoreConnector.focus(segmentSelection, e.detail);\n            },\n          };\n          r.push(rowDef);\n          return r;\n        },\n        [],\n      );\n      columns.push({\n        ...colData,\n        columnSelectionStore,\n        dataPorts,\n      });\n      x++;\n    });\n    this.columns = columns;\n    // ----------- Handle columns end ----------- //\n\n    this.config.scrollingService?.unregister();\n  }\n\n  private onColumnResize(\n    type: DimensionCols,\n    { detail }: CustomEvent<ViewSettingSizeProp>,\n    store: Observable<DSourceState<ColumnRegular, DimensionCols>>,\n  ) {\n    // apply to dimension provider\n    this.config.dimensionProvider?.setCustomSizes(type, detail, true);\n\n    // set resize event\n    const changedItems: ResizeDetails = {};\n    for (const [i, size] of Object.entries(detail || {})) {\n      const virtualIndex = parseInt(i, 10);\n      const item = getSourceItem(store, virtualIndex);\n      if (item) {\n        changedItems[virtualIndex] = { ...item, size };\n      }\n    }\n    this.config.resize(changedItems);\n  }\n\n  /** register selection store for Segment */\n  private registerSegment(position: Cell) {\n    return this.config.selectionStoreConnector.register(position);\n  }\n\n  /** register selection store for Row */\n  private registerRow(y: number, type: DimensionRows) {\n    return this.config.selectionStoreConnector.registerRow(y, type).store;\n  }\n\n  /** register selection store for Column */\n  private registerCol(x: number, type: DimensionCols) {\n    return this.config.selectionStoreConnector.registerColumn(x, type).store;\n  }\n\n  /** Collect Row data */\n  private dataViewPort(data: ViewportColumn) {\n    const slots: { [key in DimensionRows]: SlotType } = {\n      rowPinStart: HEADER_SLOT,\n      rgRow: CONTENT_SLOT,\n      rowPinEnd: FOOTER_SLOT,\n    };\n\n    // y position for selection\n    let y = 0;\n    return rowTypes.reduce((result: VPPartition[], type) => {\n      // filter out empty sources, we still need to return source to keep slot working\n      const isPresent =\n        data.viewports[type].store.get('realCount') || type === 'rgRow';\n      const rgCol = {\n        ...data,\n        position: { ...data.position, y: isPresent ? y : EMPTY_INDEX },\n      };\n      const partition = viewportDataPartition(\n        rgCol,\n        type,\n        slots[type],\n        type !== 'rgRow', // is fixed row\n      );\n      result.push(partition);\n      if (isPresent) {\n        y++;\n      }\n      return result;\n    }, []);\n  }\n\n  scrollToCell(cell: Partial<Cell>) {\n    for (let key in cell) {\n      const coordinate = cell[key as keyof Cell];\n      if (typeof coordinate === 'number') {\n        this.config.scrollingService.proxyScroll({\n          dimension: key === 'x' ? 'rgCol' : 'rgRow',\n          coordinate,\n        });\n      }\n    }\n  }\n\n  /**\n   * Clear current grid focus\n   */\n  clearFocused() {\n    this.config.selectionStoreConnector.clearAll();\n  }\n\n  clearEdit() {\n    this.config.selectionStoreConnector.setEdit(false);\n  }\n\n  /**\n   * Collect focused element data\n   */\n  getFocused(): FocusedData | null {\n    const focused = this.config.selectionStoreConnector.focusedStore;\n    if (!focused) {\n      return null;\n    }\n    // get column data\n    const colType =\n      this.config.selectionStoreConnector.storesXToType[focused.position.x];\n    const column = this.config.columnProvider.getColumn(\n      focused.cell.x,\n      colType,\n    );\n\n    // get row data\n    const rowType =\n      this.config.selectionStoreConnector.storesYToType[focused.position.y];\n    const model = this.config.dataProvider.getModel(focused.cell.y, rowType);\n    return {\n      column,\n      model,\n      cell: focused.cell,\n      colType,\n      rowType,\n    };\n  }\n\n  getStoreCoordinateByType(colType: DimensionCols, rowType: DimensionRows): Cell | undefined {\n    const stores = this.config.selectionStoreConnector.storesByType;\n    if (typeof stores[colType] === 'undefined' || typeof stores[rowType] === 'undefined') {\n      return;\n    }\n    return {\n      x: stores[colType],\n      y: stores[rowType],\n    };\n  }\n\n  setFocus(colType: string, rowType: string, start: Cell, end: Cell) {\n    const coordinate = this.getStoreCoordinateByType(colType as DimensionCols, rowType as DimensionRows);\n    if (coordinate) {\n      this.config.selectionStoreConnector?.focusByCell(\n        coordinate,\n        start,\n        end,\n      );\n    }\n  }\n\n  getSelectedRange(): RangeArea & AllDimensionType | null | undefined {\n\n    const focused = this.config.selectionStoreConnector.focusedStore;\n    if (!focused) {\n      return null;\n    }\n    // get column data\n    const colType =\n      this.config.selectionStoreConnector.storesXToType[focused.position.x];\n\n    // get row data\n    const rowType =\n      this.config.selectionStoreConnector.storesYToType[focused.position.y];\n\n    const range = focused.entity.store.get('range');\n    if (!range) {\n      return null;\n    }\n    return {\n      ...range,\n      colType,\n      rowType,\n    }\n  }\n\n  setEdit(\n    rowIndex: number,\n    colIndex: number,\n    colType: DimensionCols,\n    rowType: DimensionRows,\n  ) {\n    const coordinate = this.getStoreCoordinateByType(colType as DimensionCols, rowType as DimensionRows);\n    if (coordinate) {\n      this.config.selectionStoreConnector?.setEditByCell(\n        coordinate,\n        { x: colIndex, y: rowIndex },\n      );\n    }\n  }\n}\n", "import { columnTypes } from '@store';\nimport {\n  DimensionColPin,\n  ViewPortScrollEvent,\n  ElementsScroll,\n  ElementScroll,\n} from '@type';\n\nexport default class GridScrollingService {\n  private elements: ElementsScroll = {};\n  constructor(private setViewport: (e: ViewPortScrollEvent) => void) {}\n\n  async proxyScroll(e: ViewPortScrollEvent, key?: DimensionColPin | string) {\n    let newEventPromise: Promise<ViewPortScrollEvent | undefined> | undefined;\n    let event = e;\n    for (let elKey in this.elements) {\n      // skip\n      if (e.dimension === 'rgCol' && elKey === 'headerRow') {\n        continue;\n        // pinned column only\n      } else if (this.isPinnedColumn(key) && e.dimension === 'rgCol') {\n        if (elKey === key || !e.delta) {\n          continue;\n        }\n        for (let el of this.elements[elKey]) {\n          if (el.changeScroll) {\n            newEventPromise = el.changeScroll(e);\n          }\n        }\n      } else {\n        for (let el of this.elements[elKey]) {\n          await el.setScroll?.(e);\n        }\n      }\n    }\n    const newEvent = await newEventPromise;\n    if (newEvent) {\n      event = newEvent;\n    }\n    this.setViewport(event);\n  }\n\n  /**\n   * Silent scroll update for mobile devices when we have negative scroll top\n   */\n  async scrollSilentService(\n    e: ViewPortScrollEvent,\n    key?: DimensionColPin | string,\n  ) {\n    for (let elKey in this.elements) {\n      // skip same element update\n      if (elKey === key) {\n        continue;\n      }\n      if (\n        columnTypes.includes(key as DimensionColPin) &&\n        (elKey === 'headerRow' ||\n          columnTypes.includes(elKey as DimensionColPin))\n      ) {\n        for (let el of this.elements[elKey]) {\n          await el.changeScroll?.(e, true);\n        }\n        continue;\n      }\n    }\n  }\n\n  private isPinnedColumn(\n    key?: DimensionColPin | string,\n  ): key is DimensionColPin {\n    return !!key && ['colPinStart', 'colPinEnd'].indexOf(key) > -1;\n  }\n\n  registerElements(els: ElementsScroll) {\n    this.elements = els;\n  }\n\n  /**\n   * Register new element for farther scroll support\n   * @param el - can be null if holder removed\n   * @param key - element key\n   */\n  registerElement(el: ElementScroll | null | undefined, key: string) {\n    if (!this.elements[key]) {\n      this.elements[key] = [];\n    }\n    // new element added\n    if (el) {\n      this.elements[key].push(el);\n    } else if (this.elements[key]) {\n      // element removed\n      delete this.elements[key];\n    }\n  }\n\n  unregister() {\n    this.elements = {};\n  }\n}\n", "import { h, type VNode } from '@stencil/core';\nimport { PositionItem } from '@type';\nimport { Cell } from '@type';\n\ntype Props = { ref: { (e: OrdererService): void } };\n\n/**\n * Draw drag\n */\nexport class OrdererService {\n  private parentY = 0;\n  el?: HTMLElement;\n  rgRow?: HTMLElement;\n  text?: HTMLElement;\n  draggable?: HTMLElement;\n\n  start(parent: HTMLElement, { pos, text, event }: { pos: PositionItem; text: string; event: MouseEvent }) {\n    const { top } = parent.getBoundingClientRect();\n    this.parentY = top;\n    if (this.text) {\n      this.text.innerText = text;\n    }\n    this.move(pos);\n    this.moveTip({ x: event.x, y: event.y });\n    this.el?.classList.remove('hidden');\n  }\n  end() {\n    this.el?.classList.add('hidden');\n  }\n  move(pos: PositionItem) {\n    this.moveElement(pos.end - this.parentY);\n  }\n  moveTip({ x, y }: Cell) {\n    if (!this.draggable) {\n      return;\n    }\n    this.draggable.style.left = `${x}px`;\n    this.draggable.style.top = `${y}px`;\n  }\n\n  private moveElement(y: number) {\n    if (!this.rgRow) {\n      return;\n    }\n    this.rgRow.style.transform = `translateY(${y}px)`;\n  }\n}\n\nconst OrderRenderer = ({ ref }: Props): VNode[] => {\n  const service = new OrdererService();\n  ref(service);\n  return (\n    <div class=\"draggable-wrapper hidden\" ref={e => (service.el = e)}>\n      <div class=\"draggable\" ref={el => (service.draggable = el)}>\n        <span class=\"revo-alt-icon\" />\n        <span ref={e => (service.text = e)} />\n      </div>\n      <div class=\"drag-position\" ref={e => (service.rgRow = e)} />\n    </div>\n  );\n};\nexport default OrderRenderer;\n", "import { RowDefinition, DimensionRows } from '@type';\n\ntype Result = Partial<{\n  [T in DimensionRows]: { sizes?: Record<number, number>; };\n}>;\ntype RemoveResult = Partial<{\n  [T in DimensionRows]: number[];\n}>;\nexport const rowDefinitionByType = (newVal: RowDefinition[] = []) => {\n  const result: Result = {};\n  for (const v of newVal) {\n    let rowDefs = result[v.type];\n    if (!rowDefs) {\n      rowDefs = result[v.type] = {};\n    }\n    if (v.size) {\n      if (!rowDefs.sizes) {\n        rowDefs.sizes = {};\n      }\n      rowDefs.sizes[v.index] = v.size;\n    }\n  }\n  return result;\n};\n\nexport const rowDefinitionRemoveByType = (oldVal: RowDefinition[] = []) => {\n  const result: RemoveResult = {};\n  for (const v of oldVal) {\n    let rowDefs = result[v.type];\n    if (!rowDefs) {\n      rowDefs = result[v.type] = [];\n    }\n    if (v.size) {\n      rowDefs.push(v.index);\n    }\n  }\n  return result;\n};\n", "export function isMobileDevice() {\n    return /Mobi/i.test(navigator.userAgent) || /Android/i.test(navigator.userAgent) || navigator.maxTouchPoints > 0;\n}\n", "import { CellProps, PluginProviders } from '@type';\nimport { BasePlugin } from '../base.plugin';\nimport { ColumnCollection } from 'src/utils';\n\n/**\n * WCAG Plugin is responsible for enhancing the accessibility features of the RevoGrid component.\n * It ensures that the grid is fully compliant with Web Content Accessibility Guidelines (WCAG) 2.1.\n * This plugin should be the last plugin you add, as it modifies the grid's default behavior.\n *\n * The WCAG Plugin performs the following tasks:\n * - Sets the 'dir' attribute to 'ltr' for left-to-right text direction.\n * - Sets the 'role' attribute to 'treegrid' for treelike hierarchical structure.\n * - Sets the 'aria-keyshortcuts' attribute to 'Enter' and 'Esc' for keyboard shortcuts.\n * - Adds event listeners for keyboard navigation and editing.\n *\n * By default, the plugin adds ARIA roles and properties to the grid elements, providing semantic information\n * for assistive technologies. These roles include 'grid', 'row', and 'gridcell'. The plugin also sets\n * ARIA attributes such as 'aria-rowindex', 'aria-colindex', and 'aria-selected'.\n *\n * The WCAG Plugin ensures that the grid is fully functional and usable for users with various disabilities,\n * including visual impairments, deaf-blindness, and cognitive disabilities.\n *\n * Note: The WCAG Plugin should be added as the last plugin in the list of plugins, as it modifies the grid's\n * default behavior and may conflict with other plugins if added earlier.\n */\nexport class WCAGPlugin extends BasePlugin {\n  constructor(revogrid: HTMLRevoGridElement, providers: PluginProviders) {\n    super(revogrid, providers);\n\n    revogrid.setAttribute('dir', 'ltr');\n    revogrid.setAttribute('role', 'treegrid');\n    revogrid.setAttribute('aria-keyshortcuts', 'Enter');\n    revogrid.setAttribute('aria-multiselectable', 'true');\n    revogrid.setAttribute('tabindex', '0');\n\n    /**\n     * Before Columns Set Event\n     */\n    this.addEventListener(\n      'beforecolumnsset',\n      ({ detail }: CustomEvent<ColumnCollection>) => {\n        const columns = [\n          ...detail.columns.colPinStart,\n          ...detail.columns.rgCol,\n          ...detail.columns.colPinEnd,\n        ];\n\n        revogrid.setAttribute('aria-colcount', `${columns.length}`);\n\n        columns.forEach((column, index) => {\n          const { columnProperties, cellProperties } = column;\n\n          column.columnProperties = (...args) => {\n            const result = columnProperties?.(...args) || {};\n\n            result.role = 'columnheader';\n            result['aria-colindex'] = `${index}`;\n\n            return result;\n          };\n\n          column.cellProperties = (...args) => {\n            const wcagProps: CellProps = {\n              ['role']: 'gridcell',\n              ['aria-colindex']: `${index}`,\n              ['aria-rowindex']: `${args[0].rowIndex}`,\n              ['tabindex']: -1,\n            };\n            const columnProps: CellProps = cellProperties?.(...args) || {};\n\n            return {\n              ...wcagProps,\n              ...columnProps,\n            };\n          };\n        });\n      },\n    );\n\n    /**\n     * Before Row Set Event\n     */\n    this.addEventListener(\n      'beforesourceset',\n      ({\n        detail,\n      }: CustomEvent<HTMLRevoGridElementEventMap['beforesourceset']>) => {\n        revogrid.setAttribute('aria-rowcount', `${detail.source.length}`);\n      },\n    );\n    this.addEventListener(\n      'beforerowrender',\n      ({\n        detail,\n      }: CustomEvent<HTMLRevogrDataElementEventMap['beforerowrender']>) => {\n        detail.node.$attrs$ = {\n          ...detail.node.$attrs$,\n          role: 'row',\n          ['aria-rowindex']: detail.item.itemIndex,\n        };\n      },\n    );\n\n    // focuscell\n    this.addEventListener(\n      'afterfocus',\n      async (\n        e: CustomEvent<HTMLRevogrFocusElementEventMap['afterfocus']>,\n      ) => {\n        if (e.defaultPrevented) {\n          return;\n        }\n        const el = this.revogrid.querySelector(\n          `revogr-data[type=\"${e.detail.rowType}\"][col-type=\"${e.detail.colType}\"] [data-rgrow=\"${e.detail.rowIndex}\"][data-rgcol=\"${e.detail.colIndex}\"]`,\n        );\n        if (el instanceof HTMLElement) {\n          el.focus();\n        }\n      },\n    );\n  }\n}\n", "import { PluginBaseComponent, PluginProviders, PluginServiceBase } from '@type';\nimport { GridPlugin } from 'src/plugins';\n\n/**\n * Plugin service\n * Manages plugins\n */\nexport class PluginService implements PluginServiceBase {\n  /**\n   * Plugins\n   * Define plugins collection\n   */\n  internalPlugins: PluginBaseComponent[] = [];\n\n  /**\n   * Get all plugins\n   */\n  get() {\n    return [...this.internalPlugins];\n  }\n\n  /**\n   * Add plugin to collection\n   */\n  add(plugin: PluginBaseComponent) {\n    this.internalPlugins.push(plugin);\n  }\n\n  /**\n   * Add user plugins and create\n   */\n  addUserPluginsAndCreate(\n    element: HTMLRevoGridElement,\n    plugins: GridPlugin[] = [],\n    prevPlugins?: GridPlugin[],\n    pluginData?: PluginProviders,\n  ) {\n    if (!pluginData) {\n      return;\n    }\n\n    // Step 1: Identify plugins to remove, compare new and old plugins\n    const pluginsToRemove =\n      prevPlugins?.filter(\n        prevPlugin => !plugins.some(userPlugin => userPlugin === prevPlugin),\n      ) || [];\n\n    // Step 2: Remove old plugins\n    pluginsToRemove.forEach(plugin => {\n      const index = this.internalPlugins.findIndex(\n        createdPlugin => createdPlugin instanceof plugin,\n      );\n      if (index !== -1) {\n        this.internalPlugins[index].destroy?.();\n        this.internalPlugins.splice(index, 1); // Remove the plugin\n      }\n    });\n\n    // Step 3: Register user plugins\n    plugins?.forEach(userPlugin => {\n      // check if plugin already exists, if so, skip\n      const existingPlugin = this.internalPlugins.find(\n        createdPlugin => createdPlugin instanceof userPlugin,\n      );\n      if (existingPlugin) {\n        return;\n      }\n      this.add(new userPlugin(element, pluginData));\n    });\n  }\n\n  /**\n   * Get plugin by class\n   */\n  getByClass<T extends PluginBaseComponent>(\n    pluginClass: new (...args: any[]) => T,\n  ): T | undefined {\n    return this.internalPlugins.find(p => p instanceof pluginClass) as\n      | T\n      | undefined;\n  }\n\n  /**\n   * Remove plugin\n   */\n  remove(plugin: PluginBaseComponent) {\n    const index = this.internalPlugins.indexOf(plugin);\n    if (index > -1) {\n      this.internalPlugins[index].destroy?.();\n      this.internalPlugins.splice(index, 1);\n    }\n  }\n\n  /**\n   * Remove all plugins\n   */\n\n  destroy() {\n    this.internalPlugins.forEach(p => p.destroy?.());\n    this.internalPlugins = [];\n  }\n}\n", "@import './styles/revo-grid.mixin.scss';\n@import './styles/revo-grid.common.scss';\n@import './styles/revo-grid.default.scss';\n@import './styles/revo-grid.material.scss';\n@import './styles/revo-grid.dark.material.scss';\n@import './styles/revo-grid.dark.compact.scss';\n@import './styles/revo-grid.compact.scss';\n\nrevo-grid {\n  /* Base theme variables */\n  --revo-grid-primary: #266ae8;\n  --revo-grid-primary-transparent: rgba(38, 106, 232, 0.9);\n  --revo-grid-background: #fff;\n  --revo-grid-foreground: black;\n  --revo-grid-divider: gray;\n  --revo-grid-shadow: rgba(0, 0, 0, 0.15);\n  --revo-grid-text: black;\n  --revo-grid-border: rgba(0, 0, 0, 0.2);\n  \n  /* Filter panel variables */\n  --revo-grid-filter-panel-bg: #fff;\n  --revo-grid-filter-panel-border: #d9d9d9;\n  --revo-grid-filter-panel-shadow: rgba(0, 0, 0, 0.15);\n  --revo-grid-filter-panel-input-bg: #eaeaeb;\n  --revo-grid-filter-panel-divider: #d9d9d9;\n  --revo-grid-filter-panel-select-border: transparent;\n  --revo-grid-filter-panel-select-border-hover: transparent;\n\n  /* Grid specific variables */\n  --revo-grid-header-bg: #f8f9fa;\n  --revo-grid-header-color: #000;\n  --revo-grid-header-border: #cecece;\n  --revo-grid-cell-border: #e2e3e3;\n  --revo-grid-focused-bg: rgba(233, 234, 237, 0.5);\n  --revo-grid-row-hover: #f1f1f1;\n  --revo-grid-row-headers-bg: #f7faff;\n  --revo-grid-row-headers-color: #757a82;\n  --revo-grid-cell-disabled-bg: rgba(0, 0, 0, 0.07);\n  \n  /* Dark theme overrides - applied when theme contains 'dark' */\n  &[theme*='dark'] {\n    --revo-grid-background: #212529;\n    --revo-grid-foreground: #fff;\n    --revo-grid-text: rgba(255, 255, 255, 0.9);\n    --revo-grid-divider: #505050;\n    --revo-grid-border: rgba(255, 255, 255, 0.2);\n\n    /* Filter panel variables */\n    --revo-grid-filter-panel-bg: #212529;\n    --revo-grid-filter-panel-border: #505050;\n    --revo-grid-filter-panel-input-bg: #343a40;\n    --revo-grid-filter-panel-divider: #505050;\n\n    --revo-grid-header-bg: #343a40;\n    --revo-grid-header-color: #fff;\n    --revo-grid-header-border: #505050;\n    --revo-grid-cell-border: #424242;\n    --revo-grid-focused-bg: rgba(52, 58, 64, 0.5);\n    --revo-grid-row-hover: rgba(80, 80, 80, 0.5);\n    --revo-grid-row-headers-bg: rgba(52, 58, 64, 0.8);\n    --revo-grid-row-headers-color: rgba(255, 255, 255, 0.8);\n    --revo-grid-cell-disabled-bg: rgba(255, 255, 255, 0.07);\n  }\n  \n  @include revoGridCommon;\n  \n  /*\n  * Grid required to have a flex property to be able to stretch\n  */\n  display: flex !important;\n  height: 100%;\n  /**\n  * In some cases the min-height is not enough for safari\n  * so setting up the grid  height works\n  */\n  min-height: 300px;\n  font-family: Helvetica, Arial, Sans-Serif, serif;\n  font-size: 14px;\n  position: relative;\n  color: var(--revo-grid-text);\n\n  @include noSelect;\n\n  .attribution {\n    $size: 4px;\n    $color: var(--revo-grid-primary-transparent);\n\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    z-index: 1000;\n    width: 0;\n    height: 0;\n    border-left: $size solid $color;\n    border-bottom: $size solid $color;\n    border-top: $size solid transparent;\n    border-right: $size solid transparent;\n    cursor: pointer;\n\n    .value {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      background-color: var(--revo-grid-background);\n      padding: 4px;\n      border-radius: 4px;\n      box-shadow: 0 1px 10px var(--revo-grid-border);\n      white-space: nowrap;\n      text-decoration: none;\n      color: var(--revo-grid-text);\n      letter-spacing: 0.3px;\n      font-size: 11px;\n      opacity: 0;\n      width: $size;\n      overflow: hidden;\n      transition: opacity 0.5s ease-in-out, width 0.3s ease-in-out;\n    }\n\n    &:hover {\n      .value {\n        width: 63px;\n        opacity: 1;\n      }\n    }\n  }\n\n  &.column-draggable.column-drag-start {\n    &, * {\n      &:hover {\n        cursor: grabbing;\n      }\n    }\n  }\n\n  .footer-wrapper,\n  .header-wrapper {\n    width: 100%;\n\n    revogr-data {\n      z-index: 3;\n    }\n  }\n\n  revo-dropdown {\n    width: 100%;\n\n    .rv-dr-root {\n      max-height: 100%;\n    }\n    &.shrink label {\n      opacity: 0;\n    }\n  }\n\n  .viewports {\n    // max-height: 100%; // for safari\n    max-width: 100%;\n    display: flex;\n    flex-direction: row;\n    align-items: flex-start;\n    flex-grow: 1;\n  }\n\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  height: 100%;\n\n  .main-viewport {\n    flex-grow: 1;\n    height: 0;\n    display: flex;\n    justify-content: space-between;\n    flex-direction: row;\n  }\n\n  .draggable {\n    $s: 30px;\n    position: fixed;\n    height: $s;\n    line-height: $s;\n    background: var(--revo-grid-background);\n    border-radius: 3px;\n    display: block;\n    z-index: 100;\n    margin-top: 5px;\n    margin-right: -20px;\n    box-shadow: 0 4px 20px 0 var(--revo-grid-shadow);\n    padding-left: 20px;\n    padding-right: 5px;\n\n    &.hidden {\n      display: none;\n    }\n\n    .revo-alt-icon {\n      $alt-icon-color: var(--revo-grid-foreground);\n      background-color: $alt-icon-color;\n      position: absolute;\n      left: 5px;\n      top: 10px;\n    }\n  }\n\n  .draggable-wrapper {\n    &.hidden {\n      display: none;\n    }\n  }\n\n  .drag-position {\n    position: absolute;\n    left: 0;\n    right: 0;\n    height: 1px;\n    z-index: 2;\n    background: var(--revo-grid-divider);\n    pointer-events: none;\n  }\n\n  .drag-position-y {\n    position: absolute;\n    top: 0;\n    left: 0;\n    bottom: 0;\n    width: 1px;\n    z-index: 2;\n    background: var(--revo-grid-divider);\n    pointer-events: none;\n  }\n\n  .drag-auto-scroll-y {\n    pointer-events: none;\n    position: absolute;\n    left: 0;\n    top: 0;\n    height: 50px;\n    width: 1px;\n  }\n\n  .clipboard {\n    position: absolute;\n    left: 0;\n    top: 0;\n  }\n\n  revogr-scroll-virtual {\n    position: relative;\n    &.vertical,\n    &.horizontal {\n      z-index: 3;\n    }\n  }\n}\n", "import {\n  type VNode,\n  Component,\n  Prop,\n  h,\n  Watch,\n  Element,\n  Listen,\n  Event,\n  EventEmitter,\n  Method,\n  Host,\n} from '@stencil/core';\n\nimport type {\n  MultiDimensionType,\n  DimensionRows,\n  DimensionCols,\n  DimensionType,\n  DimensionTypeCol,\n  RowHeaders,\n  ColumnRegular,\n  ColumnGrouping,\n  DataType,\n  RowDefinition,\n  ColumnType,\n  FocusTemplateFunc,\n  PositionItem,\n  ColumnProp,\n  ViewPortScrollEvent,\n  InitialHeaderClick,\n  AllDimensionType,\n  Editors,\n  BeforeSaveDataDetails,\n  BeforeRangeSaveDataDetails,\n  Cell,\n  ChangedRange,\n  RangeArea,\n  AfterEditEvent,\n  Theme,\n  PluginBaseComponent,\n  HeaderProperties,\n  PluginProviders,\n  FocusAfterRenderEvent,\n  ExtraNodeFuncConfig,\n  RowDragStartDetails,\n  AdditionalData,\n} from '@type';\n\nimport ColumnDataProvider from '../../services/column.data.provider';\nimport { DataProvider } from '../../services/data.provider';\nimport { DSourceState, getVisibleSourceItem, rowTypes } from '@store';\nimport DimensionProvider from '../../services/dimension.provider';\nimport ViewportProvider from '../../services/viewport.provider';\nimport ThemeService from '../../themeManager/theme.service';\nimport { timeout } from '../../utils';\nimport {\n  AutoSizeColumnPlugin,\n  type AutoSizeColumnConfig,\n} from '../../plugins/column.auto-size.plugin';\n\nimport {\n  FilterPlugin,\n} from '../../plugins/filter/filter.plugin';\nimport { SortingPlugin } from '../../plugins/sorting/sorting.plugin';\nimport { ExportFilePlugin } from '../../plugins/export/export.plugin';\nimport { DataInput } from '../../plugins/export/types';\nimport { GroupingRowPlugin } from '../../plugins/groupingRow/grouping.row.plugin';\nimport type { GroupingOptions } from '../../plugins/groupingRow/grouping.row.types';\nimport ViewportService, { FocusedData } from './viewport.service';\nimport { DATA_SLOT, HEADER_SLOT } from './viewport.helpers';\nimport GridScrollingService from './viewport.scrolling.service';\nimport { SelectionStoreConnector } from '../../services/selection.store.connector';\nimport OrderRenderer, { OrdererService } from '../order/order-renderer';\nimport {\n  StretchColumn,\n  isStretchPlugin,\n} from '../../plugins/column.stretch.plugin';\nimport { rowDefinitionByType, rowDefinitionRemoveByType } from './grid.helpers';\nimport { ColumnMovePlugin } from '../../plugins/moveColumn/column.drag.plugin';\nimport { getPropertyFromEvent } from '../../utils/events';\nimport { isMobileDevice } from '../../utils/mobile';\nimport type { Observable } from '../../utils';\nimport type { GridPlugin } from '../../plugins/base.plugin';\nimport { ColumnCollection, getColumnByProp, getColumns } from '../../utils/column.utils';\nimport { WCAGPlugin } from '../../plugins/wcag';\nimport { ColumnFilterConfig, FilterCollectionItem } from '../../plugins/filter/filter.types';\nimport { PluginService } from './plugin.service';\nimport { SortingConfig, SortingOrder } from '../../plugins';\n\n\n/**\n * Revogrid - High-performance, customizable grid library for managing large datasets.\n * ### Events guide\n *\n * For a comprehensive events guide, check the [Events API Page](/guide/api/events).\n * All events propagate to the root level of the grid. [Dependency tree](#Dependencies).\n * \n * ### Type definitions\n *\n * Read [type definition file](https://github.com/revolist/revogrid/blob/master/src/interfaces.d.ts) for the full interface information.\n * \n * All complex property types such as `ColumnRegular`, `ColumnProp`, `ColumnDataSchemaModel` can be found there.\n * \n * ### HTMLRevoGridElement\n *\n * @slot data-{column-type}-{row-type}. @example data-rgCol-rgRow - main data slot. Applies extra elements in <revogr-data />.\n * @slot focus-{column-type}-{row-type}. @example focus-rgCol-rgRow - focus layer for main data. Applies extra elements in <revogr-focus />.\n * @slot viewport - Viewport slot.\n * @slot header - Header slot.\n * @slot footer - Footer slot.\n*/\n@Component({\n  tag: 'revo-grid',\n  styleUrl: 'revo-grid-style.scss',\n})\nexport class RevoGridComponent {\n  // #region Properties\n  /** Excel like functionality.\n   * Show row numbers.\n   * Also can be used for custom row header render if object provided.\n   */\n  @Prop() rowHeaders: RowHeaders | boolean;\n\n  /**\n   * Defines how many rows/columns should be rendered outside visible area.\n   */\n  @Prop() frameSize = 1;\n\n  /**\n   * Indicates default rgRow size.\n   * By default 0, means theme package size will be applied\n   *\n   * Alternatively you can use `rowSize` to reset viewport\n   */\n  @Prop() rowSize = 0;\n\n  /** Indicates default column size. */\n  @Prop() colSize = 100;\n\n  /** When true, user can range selection. */\n  @Prop() range = false;\n\n  /** When true, grid in read only mode. */\n  @Prop() readonly = false;\n\n  /** When true, columns are resizable. */\n  @Prop() resize = false;\n\n  /** When true cell focus appear. */\n  @Prop() canFocus = true;\n\n  /** When true enable clipboard. */\n  @Prop() useClipboard = true;\n\n  /**\n   * Columns - defines an array of grid columns.\n   * Can be column or grouped column.\n   */\n  @Prop() columns: (ColumnRegular | ColumnGrouping)[] = [];\n  /**\n   * Source - defines main data source.\n   * Can be an Object or 2 dimensional array([][]);\n   * Keys/indexes referenced from columns Prop.\n   */\n  @Prop() source: DataType[] = [];\n\n  /** Pinned top Source: {[T in ColumnProp]: any} - defines pinned top rows data source. */\n  @Prop() pinnedTopSource: DataType[] = [];\n\n  /** Pinned bottom Source: {[T in ColumnProp]: any} - defines pinned bottom rows data source. */\n  @Prop() pinnedBottomSource: DataType[] = [];\n\n  /** Custom row properies to be applied. See `RowDefinition` for more info. */\n  @Prop() rowDefinitions: RowDefinition[] = [];\n\n  /** Custom editors register. */\n  @Prop() editors: Editors = {};\n\n  /**\n   * Apply changes in editor when closed except 'Escape' cases.\n   * If custom editor in use method getValue required.\n   * Check interfaces.d.ts `EditorBase` for more info.\n   */\n  @Prop() applyOnClose = false;\n\n  /**\n   * Custom grid plugins. Can be added or removed at runtime.\n   * Every plugin should be inherited from BasePlugin class.\n   * \n   * For more details check [Plugin guide](https://rv-grid.com/guide/plugin/)\n   */\n  @Prop() plugins: GridPlugin[] = [];\n\n  /**\n   * Column Types Format.\n   * Every type represent multiple column properties.\n   * Types will be merged but can be replaced with column properties.\n   * Types were made as separate objects to be reusable per multiple columns.\n   */\n  @Prop() columnTypes: { [name: string]: ColumnType } = {};\n\n  /** Theme name. */\n  @Prop({ reflect: true, mutable: true }) theme: Theme = 'default';\n\n  /**\n   * Row class property mapping.\n   * Map custom classes to rows from row object data.\n   * Define this property in rgRow object and this will be mapped as rgRow class.\n   */\n  @Prop({ reflect: true }) rowClass = '';\n\n  /**\n   * Autosize config.\n   * Enables columns autoSize.\n   * For more details check `autoSizeColumn` plugin.\n   * By default disabled, hence operation is not performance efficient.\n   * `true` to enable with default params (double header separator click for autosize).\n   * Or define config. See `AutoSizeColumnConfig` for more details.\n   */\n  @Prop() autoSizeColumn: boolean | AutoSizeColumnConfig = false;\n\n  /**\n   * Enables filter plugin.\n   * Can be boolean.\n   * Or can be filter collection See `FilterCollection` for more info.\n   */\n  @Prop() filter: boolean | ColumnFilterConfig = false;\n\n  /**\n   * Alternative way to set sorting.\n   * `{columns: [{prop: 'name', order: 'asc'}]}`\n   * Use SortingPlugin to get current sorting state\n   */\n  @Prop() sorting?: SortingConfig;\n\n  /**\n   * Apply changes typed in editor on editor close except Escape cases.\n   * If custom editor in use method `getValue` required.\n   * Check `interfaces.d.ts` `EditorBase` for more info.\n   */\n  @Prop() focusTemplate: FocusTemplateFunc;\n\n  /**\n   * Enable column move plugin.\n   */\n  @Prop() canMoveColumns = false;\n  /**\n   * Trimmed rows.\n   * Functionality which allows to hide rows from main data set.\n   * `trimmedRows` are physical `rgRow` indexes to hide.\n   */\n  @Prop() trimmedRows: Record<number, boolean> = {};\n\n  /**\n   * Enable export plugin.\n   */\n  @Prop() exporting = false;\n\n  /**\n   * Group rows based on this property.\n   * Define properties to be groped by grouping plugin See `GroupingOptions`.\n   */\n  @Prop() grouping: GroupingOptions;\n\n  /**\n   * Stretch strategy for columns by `StretchColumn` plugin.\n   * For example if there are more space on the right last column size would be increased.\n   */\n  @Prop() stretch: boolean | string = false;\n\n  /**\n   * Additional data to be passed to plugins, renders or editors.\n   * For example if you need to pass Vue component instance.\n   */\n  @Prop() additionalData: AdditionalData = {};\n\n  /**\n   * Disable lazy rendering mode for the `X axis`.\n   * Use when not many columns present and you don't need rerenader cells during scroll.\n   * Can be used for initial rendering performance improvement.\n   */\n  @Prop() disableVirtualX = false;\n  /**\n   * Disable lazy rendering mode for the `Y axis`.\n   * Use when not many rows present and you don't need rerenader cells during scroll.\n   * Can be used for initial rendering performance improvement.\n   */\n  @Prop() disableVirtualY = false;\n\n  /**\n   * Please only hide the attribution if you are subscribed to Pro version\n   */\n  @Prop() hideAttribution = false;\n\n  /**\n   * Prevent rendering until job is done.\n   * Can be used for initial rendering performance improvement.\n   * When several plugins require initial rendering this will prevent double initial rendering.\n   */\n  @Prop() jobsBeforeRender: Promise<any>[] = [];\n\n  /**\n   * Register new virtual node inside of grid.\n   * Used for additional items creation such as plugin elements.\n   * Should be set before grid render inside of plugins.\n   * Can return VNode result of h() function or a function that returns VNode.\n   * Function can be used for performance improvement and additional renders.\n   */\n  @Prop() registerVNode: (\n    | VNode\n    | ((c: ExtraNodeFuncConfig) => VNode)\n  )[] = [];\n\n\n  /**\n   * Enable accessibility. If disabled, the grid will not be accessible.\n   * @default true\n   */\n  @Prop() accessible = true;\n\n\n\n  /**\n   * Disable native drag&drop plugin.\n   */\n  @Prop() canDrag = true;\n\n  // #endregion\n\n  // #region Events\n  /**\n   * New content size has been applied. The size excludes the header.\n   * Currently, the event responsible for applying the new content size does not provide the actual size.\n   * To retrieve the actual content size, you can utilize the `getContentSize` function after the event has been triggered.\n   */\n  @Event() contentsizechanged: EventEmitter<MultiDimensionType>;\n\n  /**\n   * Before the data is edited.\n   * To prevent the default behavior of editing data and use your own implementation, call `e.preventDefault()`.\n   * To override the edit result with your own value, set the `e.val` property to your desired value.\n   */\n  @Event() beforeedit: EventEmitter<BeforeSaveDataDetails>;\n\n  /**\n   * Before applying range data, specifically when a range selection occurs.\n   * To customize the data and prevent the default edit data from being set, you can call `e.preventDefault()`.\n   */\n  @Event() beforerangeedit: EventEmitter<BeforeRangeSaveDataDetails>;\n\n  /**\n   * After data applied or range changed.\n   */\n  @Event() afteredit: EventEmitter<AfterEditEvent>;\n\n  /**\n   * Before autofill is applied.\n   * To prevent the default behavior of applying the edit data, you can call `e.preventDefault()`.\n   */\n  @Event() beforeautofill: EventEmitter<ChangedRange>;\n\n  /**\n   * Before autofill is applied. Runs before beforeautofill event.\n   * Use e.preventDefault() to prevent range.\n   */\n  @Event() beforerange: EventEmitter<ChangedRange>;\n\n  /**\n   * After focus render finished.\n   * Can be used to access a focus element through `event.target`.\n   * This is just a duplicate of `afterfocus` from `revogr-focus.tsx`.\n   */\n  @Event() afterfocus: EventEmitter<FocusAfterRenderEvent>;\n\n  /**\n   * Before the order of `rgRow` is applied.\n   * To prevent the default behavior of changing the order of `rgRow`, you can call `e.preventDefault()`.\n   */\n  @Event() roworderchanged: EventEmitter<{ from: number; to: number }>;\n\n  /**\n   * By `SortingPlugin`\n   * <br>Triggered immediately after header click.\n   * <br>First in sorting event sequence. Ff this event stops no other event called.\n   * <br>Use `e.preventDefault()` to prevent sorting.\n   */\n  @Event() beforesorting: EventEmitter<{\n    column: ColumnRegular;\n    order: 'desc' | 'asc';\n    additive: boolean;\n  }>;\n\n  /**\n   * By `SortingPlugin`\n   * <br>Same as `beforesorting` but triggered after `beforeanysource` (when source is changed).\n   * <br>Use `e.preventDefault()` to prevent sorting data change.\n   */\n  @Event() beforesourcesortingapply: EventEmitter<{\n    type: DimensionRows;\n    sorting?: SortingOrder;\n  }>;\n\n  /**\n   * By `SortingPlugin`\n   * <br> After `beforesorting`\n   * <br>Triggered after column data updated with new sorting order.\n   * <br>Use `e.preventDefault()` to prevent sorting data change.\n   */\n  @Event() beforesortingapply: EventEmitter<{\n    column: ColumnRegular;\n    order: 'desc' | 'asc';\n    additive: boolean;\n  }>;\n\n  /**\n   * This event is triggered when the row order change is started.\n   * To prevent the default behavior of changing the row order, you can call `e.preventDefault()`.\n   * To change the item name at the start of the row order change, you can set `e.text` to the desired new name.\n   */\n  @Event() rowdragstart: EventEmitter<RowDragStartDetails>;\n\n  /**\n   * On header click.\n   */\n  @Event() headerclick: EventEmitter<ColumnRegular>;\n\n  /**\n   * Before the cell focus is changed.\n   * To prevent the default behavior of changing the cell focus, you can call `e.preventDefault()`.\n   */\n  @Event() beforecellfocus: EventEmitter<BeforeSaveDataDetails>;\n\n  /**\n   * Before the grid focus is lost.\n   * To prevent the default behavior of changing the cell focus, you can call `e.preventDefault()`.\n   */\n  @Event() beforefocuslost: EventEmitter<FocusedData | null>;\n\n  /**\n   * Before main source/rows data apply.\n   * You can override data source here\n   */\n  @Event() beforesourceset: EventEmitter<{\n    type: DimensionRows;\n    source: DataType[];\n  }>;\n\n  /**\n   * Before data apply on any source type. Can be source from pinned and main viewport.\n   * You can override data source here\n   */\n  @Event() beforeanysource: EventEmitter<{\n    type: DimensionRows;\n    source: DataType[];\n  }>;\n\n  /**\n   * After main source/rows updated\n   */\n  @Event() aftersourceset: EventEmitter<{\n    type: DimensionRows;\n    source: DataType[];\n  }>;\n\n  /**\n   * Emitted after each source update, whether from the pinned or main viewport.\n   * Useful for tracking all changes originating from sources in both the pinned and main viewports.\n   */\n  @Event() afteranysource: EventEmitter<{\n    type: DimensionRows;\n    source: DataType[];\n  }>;\n\n  /**\n   * Emitted before a column update is applied.\n   * Listeners can use this event to perform any necessary actions or modifications before the column update is finalized.\n   */\n  @Event() beforecolumnsset: EventEmitter<ColumnCollection>;\n\n  /**\n   * Emitted before a column update is applied, after the column set is gathered and the viewport is updated.\n   * Useful for performing actions or modifications before the final application of the column update.\n   */\n  @Event() beforecolumnapplied: EventEmitter<ColumnCollection>;\n\n  /**  Column updated */\n  @Event() aftercolumnsset: EventEmitter<{\n    columns: ColumnCollection;\n    order: SortingOrder;\n  }>;\n\n  /**\n   * Emitted before applying a filter to the data source.\n   * Use e.preventDefault() to prevent cell focus change.\n   * Modify if you need to change filters.\n   */\n  @Event() beforefilterapply: EventEmitter<{ collection: Record<ColumnProp, FilterCollectionItem> }>;\n\n  /**\n   * Emitted before applying a filter to the data source.\n   * Use e.preventDefault() to prevent the default behavior of trimming values and applying the filter.\n   * Modify the `collection` property if you want to change the filters.\n   * Modify the `itemsToFilter` property if you want to filter the indexes for trimming.\n   */\n  @Event() beforefiltertrimmed: EventEmitter<{\n    collection: Record<ColumnProp, FilterCollectionItem>;\n    itemsToFilter: Record<number, boolean>;\n  }>;\n\n  /**\n   * Emitted before trimming values.\n   * Use e.preventDefault() to prevent the default behavior of trimming values.\n   * Modify the `trimmed` property if you want to filter the indexes for trimming.\n   */\n  @Event() beforetrimmed: EventEmitter<{\n    trimmed: Record<number, boolean>;\n    trimmedType: string;\n    type: string;\n  }>;\n\n  /**\n   * Emitted after trimmed values have been applied.\n   * Useful for notifying when trimming of values has taken place.\n   */\n  @Event() aftertrimmed: EventEmitter;\n\n  /**\n   * Emitted when the viewport is scrolled.\n   * Useful for tracking viewport scrolling events.\n   */\n  @Event() viewportscroll: EventEmitter<ViewPortScrollEvent>;\n\n  /**\n   * Before export\n   * Use e.preventDefault() to prevent export\n   * Replace data in Event in case you want to modify it in export\n   */\n  @Event() beforeexport: EventEmitter<DataInput>;\n\n  /**\n   * Emitted before editing starts.\n   * Use e.preventDefault() to prevent the default edit behavior.\n   */\n  @Event() beforeeditstart: EventEmitter<BeforeSaveDataDetails>;\n\n  /**\n   * Emitted after column resizing.\n   * Useful for retrieving the resized columns.\n   */\n  @Event() aftercolumnresize: EventEmitter<{\n    [index: number]: ColumnRegular;\n  }>;\n\n  /**\n   * Emitted before the row definition is applied.\n   * Useful for modifying or preventing the default row definition behavior.\n   */\n  @Event() beforerowdefinition: EventEmitter<{ vals: any; oldVals: any }>;\n\n  /**\n   * Emitted when the filter configuration is changed\n   */\n  @Event() filterconfigchanged: EventEmitter;\n\n  /**\n   * Emitted when the sorting configuration is changed\n   * SortingPlugin subsribed to this event\n   */\n  @Event() sortingconfigchanged: EventEmitter<SortingConfig>;\n\n  /**\n   * Emmited when the row headers are changed.\n   */\n  @Event() rowheaderschanged: EventEmitter;\n\n  /**\n   * Emmited before the grid is rendered.\n   */\n  @Event() beforegridrender: EventEmitter;\n\n\n  /**\n   * Emmited after the grid is rendered.\n   */\n  @Event() aftergridrender: EventEmitter;\n\n  /**\n   * Emmited after the grid is initialized. Connected to the DOM.\n   */\n  @Event() aftergridinit: EventEmitter;\n\n  /**\n   * Emmited after the additional data is changed\n   */\n  @Event() additionaldatachanged: EventEmitter<any>;\n\n  /**\n   * Emmited after the theme is changed\n   */\n  @Event() afterthemechanged: EventEmitter<Theme>;\n\n\n  /**\n   * Emmited after grid created\n   */\n  @Event() created: EventEmitter;\n\n  // #endregion\n\n  // #region Methods\n  /**\n   * Refreshes data viewport.\n   * Can be specific part as rgRow or pinned rgRow or 'all' by default.\n   */\n  @Method() async refresh(type: DimensionRows | 'all' = 'all') {\n    if (!this.dataProvider) {\n      throw new Error('Not connected');\n    }\n    this.dataProvider.refresh(type);\n  }\n\n  /**\n   * Refreshes data at specified cell.\n   * Useful for performance optimization.\n   * No viewport update will be triggered.\n   * \n   * @example\n   * const grid = document.querySelector('revo-grid');\n   * grid.setDataAt({ row: 0, col: 0, val: 'test' }); // refresh\n   */\n  @Method() async setDataAt(\n    { row, col, colType = 'rgCol', rowType = 'rgRow', val, skipDataUpdate = false }: {\n      row: number; // virtual\n      col: number; // virtual\n      val?: any;\n      skipDataUpdate?: boolean;\n    } & AllDimensionType,\n  ) {\n    if (this.dataProvider && this.columnProvider && !skipDataUpdate) {\n      const columnProp = this.columnProvider.getColumn(col, colType)?.prop;\n      if (typeof columnProp !== 'undefined') {\n        this.dataProvider.setCellData({\n          type: rowType,\n          rowIndex: row,\n          prop: columnProp,\n          val,\n        }, false);\n      }\n    }\n    const dataElement: HTMLRevogrDataElement | null =\n      this.element.querySelector(\n        `revogr-data[type=\"${rowType}\"][col-type=\"${colType}\"]`,\n      );\n    return dataElement?.updateCell({\n      row,\n      col,\n    });\n  }\n\n  /**\n   * Scrolls viewport to specified row by index.\n   */\n  @Method() async scrollToRow(coordinate = 0) {\n    if (!this.dimensionProvider) {\n      throw new Error('Not connected');\n    }\n    const y = this.dimensionProvider.getViewPortPos({\n      coordinate,\n      dimension: 'rgRow',\n    });\n    await this.scrollToCoordinate({ y });\n  }\n\n  /**\n   * Scrolls viewport to specified column by index.\n   */\n  @Method() async scrollToColumnIndex(coordinate = 0) {\n    if (!this.dimensionProvider) {\n      throw new Error('Not connected');\n    }\n    const x = this.dimensionProvider.getViewPortPos({\n      coordinate,\n      dimension: 'rgCol',\n    });\n    await this.scrollToCoordinate({ x });\n  }\n\n  /**\n   * Scrolls viewport to specified column by prop\n   */\n  @Method() async scrollToColumnProp(\n    prop: ColumnProp,\n    dimension: DimensionTypeCol = 'rgCol',\n  ) {\n    if (!this.dimensionProvider || !this.columnProvider) {\n      throw new Error('Not connected');\n    }\n\n    const coordinate = this.columnProvider.getColumnIndexByProp(\n      prop,\n      dimension,\n    );\n    if (coordinate < 0) {\n      // already on the screen\n      return;\n    }\n    const x = this.dimensionProvider.getViewPortPos({\n      coordinate,\n      dimension,\n    });\n    await this.scrollToCoordinate({ x });\n  }\n\n  /** Update columns */\n  @Method() async updateColumns(cols: ColumnRegular[]) {\n    this.columnProvider?.updateColumns(cols);\n  }\n\n  /** Add trimmed by type */\n  @Method() async addTrimmed(\n    trimmed: Record<number, boolean>,\n    trimmedType = 'external',\n    type: DimensionRows = 'rgRow',\n  ) {\n    if (!this.dataProvider) {\n      throw new Error('Not connected');\n    }\n    const event = this.beforetrimmed.emit({\n      trimmed,\n      trimmedType,\n      type,\n    });\n    if (event.defaultPrevented) {\n      return event;\n    }\n    this.dataProvider.setTrimmed({ [trimmedType]: event.detail.trimmed }, type);\n    this.aftertrimmed.emit();\n    return event;\n  }\n\n  /**  Scrolls view port to coordinate */\n  @Method() async scrollToCoordinate(cell: Partial<Cell>) {\n    this.viewport?.scrollToCell(cell);\n  }\n\n  /**  Open editor for cell. */\n  @Method() async setCellEdit(\n    rgRow: number,\n    prop: ColumnProp,\n    rowSource: DimensionRows = 'rgRow',\n  ) {\n    const rgCol = getColumnByProp(this.columns, prop);\n    if (!rgCol) {\n      return;\n    }\n    await timeout();\n    const colGroup = rgCol.pin || 'rgCol';\n    if (!this.columnProvider) {\n      throw new Error('Not connected');\n    }\n    this.viewport?.setEdit(\n      rgRow,\n      this.columnProvider.getColumnIndexByProp(prop, colGroup),\n      colGroup,\n      rowSource,\n    );\n  }\n\n  /**  Set focus range. */\n  @Method() async setCellsFocus(\n    cellStart: Cell = { x: 0, y: 0 },\n    cellEnd: Cell = { x: 0, y: 0 },\n    colType = 'rgCol',\n    rowType = 'rgRow',\n  ) {\n    this.viewport?.setFocus(colType, rowType, cellStart, cellEnd);\n  }\n\n  /**  Get data from source */\n  @Method() async getSource(type: DimensionRows = 'rgRow') {\n    if (!this.dataProvider) {\n      throw new Error('Not connected');\n    }\n    return this.dataProvider.stores[type].store.get('source');\n  }\n\n  /**\n   * Get data from visible part of source\n   * Trimmed/filtered rows will be excluded\n   * @param type - type of source\n   */\n  @Method() async getVisibleSource(type: DimensionRows = 'rgRow') {\n    if (!this.dataProvider) {\n      throw new Error('Not connected');\n    }\n    return getVisibleSourceItem(this.dataProvider.stores[type].store);\n  }\n\n  /**\n   * Provides access to rows internal store observer\n   * Can be used for plugin support\n   * @param type - type of source\n   */\n  @Method() async getSourceStore(\n    type: DimensionRows = 'rgRow',\n  ): Promise<Observable<DSourceState<DataType, DimensionRows>>> {\n    if (!this.dataProvider) {\n      throw new Error('Not connected');\n    }\n    return this.dataProvider.stores[type].store;\n  }\n  /**\n   * Provides access to column internal store observer\n   * Can be used for plugin support\n   * @param type - type of column\n   */\n  @Method() async getColumnStore(\n    type: DimensionCols = 'rgCol',\n  ): Promise<Observable<DSourceState<ColumnRegular, DimensionCols>>> {\n    if (!this.columnProvider) {\n      throw new Error('Not connected');\n    }\n    return this.columnProvider.stores[type].store;\n  }\n\n  /**\n   * Update column sorting\n   * @param column - column prop and cellCompare\n   * @param order - order to apply\n   * @param additive - if false will replace current order\n   * \n   * later passed to SortingPlugin\n   */\n  @Method() async updateColumnSorting(\n    column: Pick<ColumnRegular, 'prop' | 'cellCompare'>,\n    order: 'asc' | 'desc' | undefined,\n    additive: boolean,\n  ) {\n    this.sortingconfigchanged.emit({\n      columns: [{\n        prop: column.prop,\n        order,\n        cellCompare: column.cellCompare,\n      }],\n      additive,\n    });\n  }\n\n  /**\n   * Clears column sorting\n   */\n  @Method() async clearSorting() {\n    this.sortingconfigchanged.emit({\n      columns: [],\n    });\n  }\n\n  /**\n   * Receive all columns in data source\n   */\n  @Method() async getColumns(): Promise<ColumnRegular[]> {\n    if (!this.columnProvider) {\n      throw new Error('Not connected');\n    }\n    return this.columnProvider.getColumns();\n  }\n\n  /**\n   * Clear current grid focus. Grid has no longer focus on it.\n   */\n  @Method() async clearFocus() {\n    const focused = this.viewport?.getFocused();\n    const event = this.beforefocuslost.emit(focused);\n    if (event.defaultPrevented) {\n      return;\n    }\n    this.selectionStoreConnector?.clearAll();\n  }\n\n  /**\n   * Get all active plugins instances\n   */\n  @Method() async getPlugins(): Promise<PluginBaseComponent[]> {\n    return this.pluginService.get();\n  }\n\n  /**\n   * Get the currently focused cell.\n   */\n  @Method() async getFocused(): Promise<FocusedData | null> {\n    return this.viewport?.getFocused() ?? null;\n  }\n\n  /**\n   * Get size of content\n   * Including all pinned data\n   */\n  @Method() async getContentSize(): Promise<Cell> {\n    if (!this.dimensionProvider) {\n      throw new Error('Not connected');\n    }\n    return this.dimensionProvider?.getFullSize();\n  }\n  /**\n   * Get the currently selected Range.\n   */\n  @Method() async getSelectedRange(): Promise<RangeArea & AllDimensionType | null> {\n    return this.viewport?.getSelectedRange() ?? null;\n  }\n\n  /**\n   * Refresh extra elements. Triggers re-rendering of extra elements and functions.\n   * Part of extraElements and registerVNode methods.\n   * Useful for plugins.\n   */\n  @Method() async refreshExtraElements() {\n    this.extraService?.refresh();\n  }\n\n  /**\n   * Get all providers for grid\n   * Useful for external grid integration\n   */\n  @Method() async getProviders() {\n    return this.getPluginData();\n  }\n\n  // #endregion\n\n  // #region Listeners outside scope\n  private clickTrackForFocusClear?: number;\n  @Listen('touchstart', { target: 'document' })\n  @Listen('mousedown', { target: 'document' })\n  mousedownHandle(event: MouseEvent | TouchEvent) {\n    const screenX = getPropertyFromEvent(event, 'screenX');\n    const screenY = getPropertyFromEvent(event, 'screenY');\n    if (screenX === null || screenY === null) {\n      return;\n    }\n\n    this.clickTrackForFocusClear = screenX + screenY;\n  }\n  /**\n   * To keep your elements from losing focus use mouseup/touchend e.preventDefault();\n   */\n  @Listen('touchend', { target: 'document' })\n  @Listen('mouseup', { target: 'document' })\n  async mouseupHandle(event: MouseEvent | TouchEvent) {\n    const screenX = getPropertyFromEvent(event, 'screenX');\n    const screenY = getPropertyFromEvent(event, 'screenY');\n    if (screenX === null || screenY === null) {\n      return;\n    }\n\n    if (event.defaultPrevented) {\n      return;\n    }\n    const pos = screenX + screenY;\n    // detect if mousemove then do nothing\n    if (Math.abs((this.clickTrackForFocusClear ?? 0) - pos) > 10) {\n      return;\n    }\n\n    // Check if action finished inside the document\n    // if event prevented, or it is current table don't clear focus\n    const path = event.composedPath();\n    if (!path.includes(this.element) &&\n        !(this.element.shadowRoot && path.includes(this.element.shadowRoot))\n      ) {\n      // Perform actions if the click is outside the component\n      await this.clearFocus();\n    }\n  }\n  // #endregion\n\n  // #region Listeners\n  /** Drag events */\n  @Listen('rowdragstartinit') onRowDragStarted(\n    e: CustomEvent<HTMLRevogrOrderEditorElementEventMap['rowdragstartinit']>,\n  ) {\n    const dragStart = this.rowdragstart.emit(e.detail);\n    if (dragStart.defaultPrevented) {\n      e.preventDefault();\n      return;\n    }\n    this.orderService?.start(this.element, {\n      ...e.detail,\n      ...dragStart.detail,\n    });\n  }\n\n  @Listen('rowdragendinit') onRowDragEnd() {\n    this.orderService?.end();\n  }\n\n  @Listen('roworderchange') onRowOrderChange(e: CustomEvent<HTMLRevogrOrderEditorElementEventMap['roworderchange']>) {\n    this.dataProvider?.changeOrder(e.detail);\n  }\n\n  @Listen('rowdragmoveinit') onRowDrag({ detail }: CustomEvent<PositionItem>) {\n    this.orderService?.move(detail);\n  }\n\n  @Listen('rowdragmousemove') onRowMouseMove(e: CustomEvent<Cell>) {\n    this.orderService?.moveTip(e.detail);\n  }\n\n  @Listen('celleditapply') async onCellEdit(\n    e: CustomEvent<BeforeSaveDataDetails>,\n  ) {\n    const { defaultPrevented, detail } = this.beforeedit.emit(e.detail);\n    await timeout();\n    // apply data\n    if (!defaultPrevented) {\n      this.dataProvider?.setCellData(detail);\n\n      // @feature: incrimental update for cells\n      // this.dataProvider.setCellData(detail, false);\n      // await this.setDataAt({\n      //   row: detail.rowIndex,\n      //   col: detail.colIndex,\n      //   rowType: detail.type,\n      //   colType: detail.colType,\n      // });\n      this.afteredit.emit(detail);\n    }\n  }\n\n  @Listen('rangeeditapply') onRangeEdit(\n    e: CustomEvent<HTMLRevogrOverlaySelectionElementEventMap['rangeeditapply']>,\n  ) {\n    if (!this.dataProvider) {\n      throw new Error('Not connected');\n    }\n    const { defaultPrevented, detail } = this.beforerangeedit.emit(e.detail);\n    if (defaultPrevented) {\n      e.preventDefault();\n      return;\n    }\n    this.dataProvider.setRangeData(detail.data, detail.type);\n    this.afteredit.emit(detail);\n  }\n\n  @Listen('selectionchangeinit') onRangeChanged(\n    e: CustomEvent<HTMLRevogrOverlaySelectionElementEventMap['selectionchangeinit']>) {\n    const beforeange = this.beforerange.emit(e.detail);\n    if (beforeange.defaultPrevented) {\n      e.preventDefault();\n    }\n    const beforeFill = this.beforeautofill.emit(beforeange.detail);\n    if (beforeFill.defaultPrevented) {\n      e.preventDefault();\n    }\n  }\n\n  @Listen('rowdropinit') onRowDropped(\n    e: CustomEvent<{ from: number; to: number }>,\n  ) {\n    // e.cancelBubble = true;\n    const { defaultPrevented } = this.roworderchanged.emit(e.detail);\n    if (defaultPrevented) {\n      e.preventDefault();\n    }\n  }\n\n  @Listen('beforeheaderclick') onHeaderClick(\n    e: CustomEvent<InitialHeaderClick>,\n  ) {\n    const { defaultPrevented } = this.headerclick.emit({\n      ...e.detail.column,\n      originalEvent: e.detail.originalEvent,\n    });\n    if (defaultPrevented) {\n      e.preventDefault();\n    }\n  }\n\n  @Listen('beforecellfocusinit') onCellFocus(\n    e: CustomEvent<BeforeSaveDataDetails>,\n  ) {\n    const { defaultPrevented } = this.beforecellfocus.emit(e.detail);\n    if (!this.canFocus || defaultPrevented) {\n      e.preventDefault();\n    }\n  }\n\n  // #endregion\n\n  // #region Private Properties\n  @Element() element: HTMLRevoGridElement;\n  extraElements: HTMLRevogrExtraElement['nodes'] = [];\n  /** \n   * Service for rendering extra elements as virtual nodes\n   * Part of extraElements and registerVNode methods\n   */\n  extraService?: HTMLRevogrExtraElement;\n\n  columnProvider?: ColumnDataProvider;\n  dataProvider?: DataProvider;\n  dimensionProvider?: DimensionProvider;\n  viewportProvider?: ViewportProvider;\n  themeService: ThemeService;\n  pluginService = new PluginService();\n  viewport: ViewportService | null = null;\n  isInited = false;\n\n  orderService: OrdererService;\n  selectionStoreConnector?: SelectionStoreConnector;\n  scrollingService: GridScrollingService;\n\n  // #endregion\n\n  // #region Watchers\n  @Watch('columnTypes') columnTypesChanged() {\n    // Column format change will trigger column structure update\n    this.columnChanged(this.columns);\n  }\n\n  @Watch('columns') columnChanged(\n    newVal: (ColumnGrouping | ColumnRegular)[] = [],\n    _prevVal: (ColumnGrouping | ColumnRegular)[] | undefined = undefined,\n    __watchName: string = 'columns',\n    init = false,\n  ) {\n    if (!this.dimensionProvider || !this.columnProvider) {\n      return;\n    }\n    const columnGather = getColumns(\n      newVal,\n      0,\n      this.columnTypes,\n    );\n    const beforeSetEvent = this.beforecolumnsset.emit(columnGather);\n    if (beforeSetEvent.defaultPrevented) {\n      return;\n    }\n    this.dimensionProvider.applyNewColumns(\n      beforeSetEvent.detail.columns,\n      this.disableVirtualX,\n      init,\n    );\n    const beforeApplyEvent = this.beforecolumnapplied.emit(columnGather);\n    if (beforeApplyEvent.defaultPrevented) {\n      return;\n    }\n    const columns = this.columnProvider.setColumns(beforeApplyEvent.detail);\n    this.aftercolumnsset.emit({\n      columns,\n      order: Object.entries(beforeApplyEvent.detail.sort).reduce((acc: SortingOrder, [prop, column]) => {\n        acc[prop] = column.order;\n        return acc;\n      }, {}),\n    });\n  }\n\n  @Watch('disableVirtualX') disableVirtualXChanged(\n    newVal = false,\n    prevVal = false,\n  ) {\n    if (newVal === prevVal) {\n      return;\n    }\n    this.columnChanged(this.columns);\n  }\n\n  @Watch('rowSize') rowSizeChanged(s: number) {\n    if (!this.dimensionProvider) {\n      return;\n    }\n    // clear existing data\n    this.dimensionProvider.setSettings({ originItemSize: s }, 'rgRow');\n    this.rowDefChanged(this.rowDefinitions, this.rowDefinitions, 'rowSize', true);\n  }\n\n  @Watch('theme') themeChanged(\n    t: Theme,\n    _?: Theme,\n    __ = 'theme',\n    init = false,\n  ) {\n    if (!this.dimensionProvider) {\n      return;\n    }\n    this.themeService.register(t);\n    this.dimensionProvider.setSettings(\n      { originItemSize: this.themeService.rowSize },\n      'rgRow',\n    );\n    this.dimensionProvider.setSettings(\n      { originItemSize: this.colSize },\n      'rgCol',\n    );\n    // if theme change we need to reapply row size and reset viewport\n    if (!init) {\n      // clear existing data\n      this.dimensionProvider.setSettings(\n        { originItemSize: this.themeService.rowSize },\n        'rgRow',\n      );\n      this.rowDefChanged(\n        // for cases when some custom size present and not\n        this.rowDefinitions,\n        this.rowDefinitions,\n        'theme',\n        true,\n      );\n    }\n    this.afterthemechanged.emit(t);\n  }\n\n  @Watch('source')\n  @Watch('pinnedBottomSource')\n  @Watch('pinnedTopSource')\n  dataSourceChanged<T extends DataType>(\n    newVal: T[] = [],\n    _: T[] | undefined,\n    watchName: string,\n  ) {\n    if (!this.dataProvider) {\n      return;\n    }\n    let type: DimensionRows = 'rgRow';\n    switch (watchName) {\n      case 'pinnedBottomSource':\n        type = 'rowPinEnd';\n        break;\n      case 'pinnedTopSource':\n        type = 'rowPinStart';\n        break;\n      case 'source':\n        type = 'rgRow';\n        /**\n         * Applied for source only for cross compatability between plugins\n         */\n        const beforesourceset = this.beforesourceset.emit({\n          type,\n          source: newVal,\n        });\n        newVal = beforesourceset.detail.source as T[];\n        break;\n    }\n    const beforesourceset = this.beforeanysource.emit({\n      type,\n      source: newVal,\n    });\n    const newSource = [...beforesourceset.detail.source];\n    this.dataProvider.setData(newSource, type, this.disableVirtualY);\n\n    /** \n     * Applied for source only for cross compatability between plugins\n     */\n    if (watchName === 'source') {\n      this.aftersourceset.emit({\n        type,\n        source: newVal,\n      });\n    }\n    this.afteranysource.emit({\n      type,\n      source: newVal,\n    });\n  }\n\n  @Watch('disableVirtualY') disableVirtualYChanged(\n    newVal = false,\n    prevVal = false,\n  ) {\n    if (newVal === prevVal) {\n      return;\n    }\n    this.dataSourceChanged(this.source, this.source, 'source');\n  }\n\n  @Watch('rowDefinitions') rowDefChanged(\n    after: RowDefinition[],\n    before?: RowDefinition[],\n    _watchName?: string,\n    forceUpdate = true,\n  ) {\n    // in firefox it's triggered before init\n    if (!this.dimensionProvider || !this.dataProvider) {\n      return;\n    }\n    const {\n      detail: { vals: newVal, oldVals: oldVal },\n    } = this.beforerowdefinition.emit({\n      vals: after,\n      oldVals: before,\n    });\n    // apply new values\n    const newRows = rowDefinitionByType(newVal);\n    // clear current defs\n    if (oldVal) {\n      const remove = rowDefinitionRemoveByType(oldVal);\n      // clear all old data and drop sizes\n      for (const t in remove) {\n        if (remove.hasOwnProperty(t)) {\n          const type = t as DimensionRows;\n          const store = this.dataProvider.stores[type];\n          const sourceLength = store.store.get('source').length;\n          this.dimensionProvider.clearSize(type, sourceLength);\n        }\n      }\n    }\n    // set new sizes\n    rowTypes.forEach((t) => {\n      const newSizes = newRows[t];\n      // apply new sizes or force update\n      if (newSizes || forceUpdate) {\n        this.dimensionProvider?.setCustomSizes(t, newSizes?.sizes || {});\n      }\n    });\n  }\n\n  @Watch('trimmedRows') trimmedRowsChanged(\n    newVal: Record<number, boolean> = {},\n  ) {\n    this.addTrimmed(newVal);\n  }\n  /**\n   * Grouping\n   */\n  @Watch('grouping') groupingChanged(newVal: GroupingOptions = {}) {\n    this.pluginService.getByClass(GroupingRowPlugin)?.setGrouping(newVal || {});\n  }\n  /**\n   * Stretch Plugin Apply\n   */\n  @Watch('stretch') applyStretch(isStretch: boolean | string) {\n    if (!this.dimensionProvider || !this.dataProvider || !this.columnProvider || !this.viewportProvider) {\n      return;\n    }\n    if (isStretch === 'false') {\n      isStretch = false;\n    }\n\n    const pluginData = this.getPluginData();\n    if (!pluginData) {\n      return;\n    }\n    const stretch = this.pluginService.getByClass(StretchColumn);\n    if ((typeof isStretch === 'boolean' && isStretch) || isStretch === 'true') {\n      if (!stretch) {\n        this.pluginService.add(new StretchColumn(this.element, pluginData));\n      } else if (isStretchPlugin(stretch)) {\n        stretch.applyStretch(this.columnProvider.getRawColumns());\n      }\n    } else if (stretch) {\n      this.pluginService.remove(stretch);\n    }\n  }\n\n  @Watch('filter') applyFilter(cfg: boolean | ColumnFilterConfig) {\n    this.filterconfigchanged.emit(cfg);\n  }\n\n  @Watch('sorting') applySorting(cfg?: SortingConfig) {\n    this.sortingconfigchanged.emit(cfg);\n  }\n\n  @Watch('rowHeaders') rowHeadersChange(rowHeaders?: RowHeaders | boolean) {\n    this.rowheaderschanged.emit(rowHeaders);\n  }\n\n  /**\n   * Register external VNodes\n   */\n  @Watch('registerVNode') registerOutsideVNodes(elements: HTMLRevogrExtraElement['nodes'] = []) {\n    this.extraElements = elements;\n  }\n\n  @Watch('additionalData') additionalDataChanged(data: any) {\n    this.additionaldatachanged.emit(data);\n  }\n\n  /**\n   * User can add plugins via plugins property\n   */\n  @Watch('plugins') pluginsChanged(plugins: GridPlugin[] = [], prevPlugins?: GridPlugin[]) {\n    this.pluginService.addUserPluginsAndCreate(this.element, plugins, prevPlugins, this.getPluginData());\n \n  }\n  // #endregion\n\n  // #region Plugins\n  private setPlugins() {\n    // remove old plugins if any\n    this.removePlugins();\n    // pass data provider to plugins\n    const pluginData = this.getPluginData();\n    if (!pluginData) {\n      return;\n    }\n\n    // register system plugins\n    this.setCorePlugins(pluginData);\n    // register user plugins\n    this.pluginsChanged(this.plugins);\n  }\n  \n  private setCorePlugins(pluginData: PluginProviders) {\n    if (this.accessible) {\n      this.pluginService.add(new WCAGPlugin(this.element, pluginData));\n    }\n\n    // register auto size plugin\n    if (this.autoSizeColumn) {\n      this.pluginService.add(new AutoSizeColumnPlugin(\n        this.element,\n        pluginData,\n        typeof this.autoSizeColumn === 'object'\n          ? this.autoSizeColumn\n          : undefined,\n      ));\n    }\n\n    // register filter plugin\n    if (this.filter) {\n      this.pluginService.add(\n        new FilterPlugin(\n          this.element,\n          pluginData,\n          typeof this.filter === 'object' ? this.filter : undefined,\n        ),\n      );\n    }\n\n    // register export plugin\n    if (this.exporting) {\n      this.pluginService.add(new ExportFilePlugin(this.element, pluginData));\n    }\n\n    // register sorting plugin\n    this.pluginService.add(new SortingPlugin(this.element, pluginData));\n\n    // register grouping plugin\n    this.pluginService.add(new GroupingRowPlugin(this.element, pluginData));\n    if (this.canMoveColumns) {\n      this.pluginService.add(new ColumnMovePlugin(this.element, pluginData));\n    }\n  }\n\n  getPluginData(): PluginProviders | undefined {\n    if (!this.dimensionProvider || !this.dataProvider || !this.columnProvider || !this.viewportProvider || !this.selectionStoreConnector) {\n      return;\n    }\n\n    // pass data provider to plugins\n    const pluginData: PluginProviders = {\n      data: this.dataProvider,\n      column: this.columnProvider,\n      dimension: this.dimensionProvider,\n      viewport: this.viewportProvider,\n      selection: this.selectionStoreConnector,\n      plugins: this.pluginService,\n    };\n\n    return pluginData;\n  }\n\n  private removePlugins() {\n    this.pluginService.destroy();\n  }\n  // #endregion\n\n\n  // if reconnect to dom we need to set up plugins\n  connectedCallback() {\n    if (this.isInited) {\n      this.setPlugins();\n    }\n    this.created.emit();\n  }\n\n  /**\n   * Called once just after the component is first connected to the DOM.\n   * Since this method is only called once, it's a good place to load data asynchronously and to setup the state\n   * without triggering extra re-renders.\n   * A promise can be returned, that can be used to wait for the first render().\n   */\n  componentWillLoad() {\n    // #region Setup Providers\n    this.viewportProvider = new ViewportProvider();\n    this.themeService = new ThemeService({\n      rowSize: this.rowSize,\n    });\n    this.dimensionProvider = new DimensionProvider(this.viewportProvider, {\n      realSizeChanged: (k: MultiDimensionType) =>\n        this.contentsizechanged.emit(k),\n    });\n    this.columnProvider = new ColumnDataProvider();\n    this.selectionStoreConnector = new SelectionStoreConnector();\n    this.dataProvider = new DataProvider(this.dimensionProvider);\n    // #endregion\n\n    this.registerOutsideVNodes(this.registerVNode);\n\n    // init plugins\n    this.setPlugins();\n\n    // set data\n    this.applyStretch(this.stretch);\n    this.themeChanged(this.theme, undefined, undefined, true);\n    this.columnChanged(this.columns, undefined, undefined, true);\n    this.dataSourceChanged(this.source, undefined, 'source');\n    this.dataSourceChanged(this.pinnedTopSource, undefined, 'pinnedTopSource');\n    this.dataSourceChanged(\n      this.pinnedBottomSource,\n      undefined,\n      'pinnedBottomSource',\n    );\n    if (Object.keys(this.trimmedRows ?? {}).length > 0) {\n      this.trimmedRowsChanged(this.trimmedRows);\n    }\n    this.rowDefChanged(this.rowDefinitions);\n\n    // init grouping\n    if (this.grouping && Object.keys(this.grouping).length > 0) {\n      this.groupingChanged(this.grouping);\n    }\n\n    // init scrolling service\n    this.scrollingService = new GridScrollingService(\n      (e: ViewPortScrollEvent) => {\n        this.dimensionProvider?.setViewPortCoordinate({\n          coordinate: e.coordinate,\n          type: e.dimension,\n        });\n        this.viewportscroll.emit(e);\n      },\n    );\n\n    this.aftergridinit.emit();\n    // set inited flag for connectedCallback\n    this.isInited = true;\n  }\n\n  componentWillRender() {\n    const event = this.beforegridrender.emit();\n    if (event.defaultPrevented) {\n      return false;\n    }\n    return Promise.all(this.jobsBeforeRender);\n  }\n\n  componentDidRender() {\n    this.aftergridrender.emit();\n  }\n\n  render() {\n    if (!this.dimensionProvider || !this.dataProvider || !this.columnProvider || !this.viewportProvider || !this.selectionStoreConnector) {\n      return;\n    }\n    const contentHeight =\n      this.dimensionProvider.stores['rgRow'].store.get('realSize');\n\n    // init viewport service helpers\n    this.viewport = new ViewportService(\n      {\n        columnProvider: this.columnProvider,\n        dataProvider: this.dataProvider,\n        dimensionProvider: this.dimensionProvider,\n        viewportProvider: this.viewportProvider,\n        scrollingService: this.scrollingService,\n        orderService: this.orderService,\n        selectionStoreConnector: this.selectionStoreConnector,\n        disableVirtualX: this.disableVirtualX,\n        disableVirtualY: this.disableVirtualY,\n        resize: c => this.aftercolumnresize.emit(c),\n      },\n      contentHeight,\n    );\n\n    // #region ViewportSections\n    /**\n     * The code renders a viewport divided into sections.\n     * It starts by rendering the pinned start, data, and pinned end sections.\n     * Within each section, it renders columns along with their headers, pinned top, center data, and pinned bottom.\n     * The code iterates over the columns and their data to generate the view port's HTML structure.\n     */\n\n    const viewportSections: (VNode | VNode[])[] = [];\n\n    // Row headers setting\n    if (this.rowHeaders && this.viewport.columns.length) {\n      const anyView = this.viewport.columns[0];\n      viewportSections.push(\n        <revogr-row-headers\n          additionalData={this.additionalData}\n          height={contentHeight}\n          rowClass={this.rowClass}\n          resize={this.resize}\n          dataPorts={anyView.dataPorts}\n          headerProp={anyView.headerProp}\n          jobsBeforeRender={this.jobsBeforeRender}\n          rowHeaderColumn={\n            typeof this.rowHeaders === 'object' ? this.rowHeaders : undefined\n          }\n          onScrollview={({ detail: e }: CustomEvent) =>\n            this.scrollingService.proxyScroll(e, 'headerRow')\n          }\n          onRef={({ detail: e }: CustomEvent) =>\n            this.scrollingService.registerElement(e, 'headerRow')\n          }\n        />,\n      );\n    }\n\n    // Viewport section render\n    const isMobile = isMobileDevice();\n    const viewPortHtml: VNode[] = [];\n\n    // Render viewports column(horizontal sections)\n    for (let view of this.viewport.columns) {\n      const headerProperties: HeaderProperties = {\n        ...view.headerProp,\n        type: view.type,\n        additionalData: this.additionalData,\n        viewportCol: view.viewportCol,\n        selectionStore: view.columnSelectionStore,\n        canResize: this.resize,\n        readonly: this.readonly,\n        columnFilter: !!this.filter,\n      };\n      // Column headers\n      const dataViews: VNode[] = [\n        <revogr-header {...headerProperties} slot={HEADER_SLOT} />,\n      ];\n\n      // Render viewport data (vertical sections)\n      view.dataPorts.forEach(data => {\n        const key = `${data.type}_${view.type}`;\n        const dataView = (\n          <revogr-overlay-selection\n            {...data}\n            canDrag={this.canDrag && data.canDrag}\n            isMobileDevice={isMobile}\n            onSelectall={() => this.selectionStoreConnector?.selectAll()}\n            editors={this.editors}\n            readonly={this.readonly}\n            range={this.range}\n            useClipboard={this.useClipboard}\n            applyChangesOnClose={this.applyOnClose}\n            additionalData={this.additionalData}\n            slot={data.slot}\n            onBeforenextvpfocus={(e) => this.selectionStoreConnector?.beforeNextFocusCell(e.detail)}\n            onCanceledit={() => this.selectionStoreConnector?.setEdit(false)}\n            onSetedit={({ detail }) => {\n              const event = this.beforeeditstart.emit(detail);\n              if (!event.defaultPrevented) {\n                this.selectionStoreConnector?.setEdit(detail.val);\n              }\n            }}\n          >\n            <revogr-data\n              {...data}\n              colType={view.type}\n              key={key}\n              readonly={this.readonly}\n              range={this.range}\n              rowClass={this.rowClass}\n              rowSelectionStore={data.rowSelectionStore}\n              additionalData={this.additionalData}\n              jobsBeforeRender={this.jobsBeforeRender}\n              slot={DATA_SLOT}\n            >\n              <slot name={`data-${view.type}-${data.type}`} />\n            </revogr-data>\n            <revogr-temp-range\n              selectionStore={data.selectionStore}\n              dimensionRow={data.dimensionRow}\n              dimensionCol={data.dimensionCol}\n            />\n            <revogr-focus\n              colData={data.colData}\n              dataStore={data.dataStore}\n              focusTemplate={this.focusTemplate}\n              rowType={data.type}\n              colType={view.type}\n              selectionStore={data.selectionStore}\n              dimensionRow={data.dimensionRow}\n              dimensionCol={data.dimensionCol}\n            >\n              <slot name={`focus-${view.type}-${data.type}`} />\n            </revogr-focus>\n          </revogr-overlay-selection>\n        );\n\n        dataViews.push(dataView);\n      });\n\n      // Add viewport scroll in the end\n      viewPortHtml.push(\n        <revogr-viewport-scroll\n          {...view.prop}\n          ref={el =>\n            this.scrollingService.registerElement(el, `${view.prop.key}`)\n          }\n          onScrollviewport={e =>\n            this.scrollingService.proxyScroll(e.detail, `${view.prop.key}`)\n          }\n          onScrollviewportsilent={e =>\n            this.scrollingService.scrollSilentService(\n              e.detail,\n              `${view.prop.key}`,\n            )\n          }\n        >\n          {dataViews}\n        </revogr-viewport-scroll>,\n      );\n    }\n\n    viewportSections.push(viewPortHtml);\n    // #endregion\n\n    const typeRow: DimensionType = 'rgRow';\n    const typeCol: DimensionType = 'rgCol';\n\n    const viewports = this.viewportProvider.stores;\n    const dimensions = this.dimensionProvider.stores;\n\n    const verticalScroll = (\n      <revogr-scroll-virtual\n        class=\"vertical\"\n        dimension={typeRow}\n        clientSize={viewports[typeRow].store.get('clientSize')}\n        virtualSize={viewports[typeRow].store.get('virtualSize')}\n        realSize={dimensions[typeRow].store.get('realSize')}\n        ref={el => this.scrollingService.registerElement(el, 'rowScroll')}\n        onScrollvirtual={e => this.scrollingService.proxyScroll(e.detail)}\n      />\n    );\n\n    const horizontalScroll = (\n      <revogr-scroll-virtual\n          class=\"horizontal\"\n          dimension={typeCol}\n          clientSize={viewports[typeCol].store.get('clientSize')}\n          virtualSize={viewports[typeCol].store.get('virtualSize')}\n          realSize={dimensions[typeCol].store.get('realSize')}\n          ref={el => this.scrollingService.registerElement(el, 'colScroll')}\n          onScrollvirtual={e => this.scrollingService.proxyScroll(e.detail)}\n        />\n    );\n\n    return (\n      <Host>\n        {this.hideAttribution ? null : (\n          <revogr-attribution class=\"attribution\" />\n        )}\n        <slot name=\"header\" />\n        <div\n          class=\"main-viewport\"\n          onClick={(e: MouseEvent) => {\n            if (e.currentTarget === e.target) {\n              this.viewport?.clearEdit();\n            }\n          }}\n        >\n          <div class=\"viewports\">\n            <slot name=\"viewport\" />\n            {viewportSections}\n            {verticalScroll}\n            <OrderRenderer ref={e => (this.orderService = e)} />\n          </div>\n        </div>\n        {horizontalScroll}\n        <revogr-extra ref={el => (this.extraService = el)} nodes={this.extraElements} />\n        <slot name=\"footer\" />\n      </Host>\n    );\n  }\n\n  disconnectedCallback() {\n    // Remove all plugins, to avoid memory leaks\n    // and unexpected behaviour when the component is removed\n    this.removePlugins();\n  }\n}\n"], "version": 3}