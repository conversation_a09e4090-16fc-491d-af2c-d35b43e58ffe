{"file": "revogr-filter-panel.js", "mappings": ";;;;;;;AAAA,CAAC,SAAS,OAAO,GAAA;AACf,IAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;QAC9B,OAAO,CAAC,SAAS,CAAC,OAAO;YACrB,OAAO,CAAC,SAA+E,CAAC,iBAAiB,IAAI,OAAO,CAAC,SAAS,CAAC,qBAAqB;;AAG1J,IAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;AAC9B,QAAA,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,CAAS,EAAA;YAC7C,IAAI,EAAE,GAAuD,IAAI;AAEjE,YAAA,GAAG;AACD,gBAAA,IAAI,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;AACzC,oBAAA,OAAO,EAAE;;gBAEX,EAAE,GAAG,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC,UAAU;aACvC,QAAQ,EAAE,KAAK,IAAI,IAAI,EAAE,CAAC,QAAQ,KAAK,CAAC;AACzC,YAAA,OAAO,IAAI;AACb,SAAC;;AAEL,CAAC,GAAG;;ACnBJ,MAAM,cAAc,GAAG,kgUAAkgU;;AC4BzhU,MAAM,WAAW,GAAe,MAAM;AAEtC,MAAM,iBAAiB,GAAG,mBAAmB;AAC7C,MAAM,wBAAwB,GAAG,0BAA0B;AAC3D,MAAM,SAAS,GAAG,YAAY;MAYjB,WAAW,iBAAAA,kBAAA,CAAA,MAAA,WAAA,SAAA,WAAA,CAAA;AAJxB,IAAA,WAAA,GAAA;;;;;QAKU,IAAA,CAAA,sBAAsB,GAAmB;AAC/C,YAAA,KAAK,EAAE,WAAW;AAClB,YAAA,EAAE,EAAE,OAAO;AACX,YAAA,IAAI,EAAE,MAAM;;AAEZ,YAAA,KAAK,EAAE,OAAO;AACd,YAAA,MAAM,EAAE,QAAQ;AAChB,YAAA,GAAG,EAAE,eAAe;AACpB,YAAA,WAAW,EAAE,gBAAgB;AAC7B,YAAA,GAAG,EAAE,KAAK;AACV,YAAA,EAAE,EAAE,IAAI;SACT;AAGQ,QAAA,IAAa,CAAA,aAAA,GAAG,KAAK;AACrB,QAAA,IAAQ,CAAA,QAAA,GAAG,CAAC;AACZ,QAAA,IAAe,CAAA,eAAA,GAAG,EAAE;AACpB,QAAA,IAAiB,CAAA,iBAAA,GAAe,WAAW;AAE3C,QAAA,IAAW,CAAA,WAAA,GAAoB,EAAE;AAClC,QAAA,IAAW,CAAA,WAAA,GAA2B,EAAE;AACxC,QAAA,IAAc,CAAA,cAAA,GAAkC,EAAE;AAE1D;;AAEG;AACK,QAAA,IAAuB,CAAA,uBAAA,GAAG,KAAK;AACvC;;AAEG;AACK,QAAA,IAAmB,CAAA,mBAAA,GAAG,IAAI;AA6J1B,QAAA,IAAA,CAAA,oBAAoB,GAAG,QAAQ,CAAC,MAAK;YAC3C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;SACzC,EAAE,GAAG,CAAC;AAgUR;AA3d8C,IAAA,WAAW,CAAC,CAAa,EAAA;;AAEpE,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB;;AAEF,QAAA,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE;QAC7B,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;AACjD,QAAA,IAAI,MAAM,YAAY,iBAAiB,EAAE;;AAEvC,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACzB;;AAEF,YAAA,MAAM,CAAC,KAAK,GAAG,WAAW;;AAE5B,QAAA,IAAI,CAAC,iBAAiB,GAAG,WAAW;AACpC,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,WAAW;;AAEjC,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE;QAEzB,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;AAE9C,QAAA,IACE,CAAC,CAAC,MAAM,YAAY,WAAW;YAC/B,SAAS;AACT,YAAA,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;YACtB,IAAI,CAAC,mBAAmB,EACxB;AACA,YAAA,IAAI,CAAC,OAAO,GAAG,SAAS;;;IAIlB,MAAM,IAAI,CAAC,SAAoB,EAAA;AACvC,QAAA,IAAI,CAAC,OAAO,GAAG,SAAS;QACxB,IAAI,CAAC,WAAW,GAAG,CAAA,SAAS,KAAA,IAAA,IAAT,SAAS,KAAA,MAAA,GAAA,MAAA,GAAT,SAAS,CAAE,WAAW,KAAI,EAAE;AAC/C,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,WAAW;;;AAI9C,IAAA,MAAM,UAAU,GAAA;QACxB,OAAO,IAAI,CAAC,OAAO;;IAGrB,mBAAmB,GAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACvB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;YACzB,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;AACjD,YAAA,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;;gBAE9B,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM;;;;IAKpD,kBAAkB,GAAA;;QAChB,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,IAAI;QAC/B,IAAI,OAAO,IAAI,KAAK,WAAW;AAAE,YAAA,OAAO,EAAE;QAE1C,MAAM,WAAW,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,EAAE;AAChD,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACzB,IAAI,CAAC,sBAAsB,EAC3B,IAAI,CAAC,cAAc,CACpB;QACD,QACE,CAAK,CAAA,KAAA,EAAA,EAAA,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAA,EACpB,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,KAAI;AACjC,YAAA,IAAI,WAAW;AACf,YAAA,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB;;;AAIF,YAAA,IAAI,KAAK,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC/C,WAAW,IACT,CAAK,CAAA,KAAA,EAAA,EAAA,OAAO,EAAE,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAA,EACnD,CAAC,CAAA,WAAW,EACV,EAAA,IAAI,EAAE,MAAM,CAAC,QAAQ,KAAK,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,EAAA,CACtD,CACE,CACP;;AAGH,YAAA,QACE,CAAK,CAAA,KAAA,EAAA,EAAA,GAAG,EAAE,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAA,EAC3C,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,EAAA,EAClC,CAAA,CAAA,QAAA,EAAA,EACE,KAAK,EAAC,0BAA0B,EAChC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,EAAA,EAErD,IAAI,CAAC,mBAAmB,CACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAClC,IAAI,CACL,CACM,EACT,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAE,wBAAwB,EAAA,EAAG,WAAW,CAAO,EACzD,CAAA,CAAA,KAAA,EAAA,EAAK,OAAO,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC,EAAA,EAChD,CAAC,CAAA,WAAW,EAAG,IAAA,CAAA,CACX,CACF,EACN,CAAM,CAAA,KAAA,EAAA,IAAA,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAO,CACtC;AAEV,SAAC,CAAC,EAED,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,oBAAoB,EAAG,CAAA,GAAG,EAAE,CACpF;;AAIF,IAAA,WAAW,CAAC,EAAuB,EAAA;;QACzC,IAAI,CAAC,EAAE,EAAE;YACP;;QAGF,MAAM,QAAQ,GAAG,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,QAAQ,EAAE;YACb;;AAGF,QAAA,MAAM,GAAG,GAAG,EAAE,CAAC,qBAAqB,EAAE;AACtC,QAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,qBAAqB,EAAE;QAChD,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK;QAEzC,IAAI,GAAG,CAAC,IAAI,GAAG,OAAO,IAAI,EAAE,CAAC,UAAU,EAAE;YACvC,EAAE,CAAC,KAAK,CAAC,IAAI,GAAG,CAAG,EAAA,OAAO,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,EAAE,CAAC,aAAa,0CAAE,qBAAqB,EAAA,CAAG,IAAI,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,CAAC,CAAC,CAAA,EAAA,CAAI;;;AAIlF,IAAA,kBAAkB,CAAC,CAAQ,EAAE,IAAgB,EAAE,KAAa,EAAA;QAClE,IAAI,EAAE,CAAC,CAAC,MAAM,YAAY,iBAAiB,CAAC,EAAE;YAC5C;;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,KAAmB;;QAGjE,IAAI,CAAC,QAAQ,EAAE;;QAGf,UAAU,CAAC,MAAK;YACd,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CACnC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CACnD;AACD,YAAA,IAAI,KAAK,YAAY,gBAAgB,EAAE;gBACrC,KAAK,CAAC,KAAK,EAAE;;SAEhB,EAAE,CAAC,CAAC;AAEL,QAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,IAAI,CAAC,oBAAoB,EAAE;;;AAQvB,IAAA,cAAc,CAAC,CAAQ,EAAA;AAC7B,QAAA,MAAM,EAAE,GAAG,CAAC,CAAC,MAA2B;AACxC,QAAA,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC,KAAmB;QAC/C,IAAI,CAAC,kBAAkB,EAAE;;QAGzB,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAsB;QACzE,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,CAAC,KAAK,GAAG,WAAW;AAC1B,YAAA,IAAI,CAAC,iBAAiB,GAAG,WAAW;;AAGtC,QAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,IAAI,CAAC,oBAAoB,EAAE;;;IAIvB,kBAAkB,GAAA;;QACxB,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,IAAI;AAC/B,QAAA,IAAI,EAAE,IAAI,IAAI,IAAI,KAAK,CAAC,CAAC;YAAE;QAE3B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE;;AAG7B,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,MAAM;YAAE;QAEvC,IAAI,CAAC,QAAQ,EAAE;AACf,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ;AAEpC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;YAC1B,EAAE,EAAE,IAAI,CAAC,eAAe;YACxB,IAAI,EAAE,IAAI,CAAC,iBAAiB;AAC5B,YAAA,KAAK,EAAE,EAAE;AACT,YAAA,QAAQ,EAAE,KAAK;AAChB,SAAA,CAAC;;QAGF,UAAU,CAAC,MAAK;AACd,YAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CACnC,eAAe,GAAG,IAAI,CAAC,eAAe,CACnB;AACrB,YAAA,IAAI,KAAK;gBAAE,KAAK,CAAC,KAAK,EAAE;SACzB,EAAE,CAAC,CAAC;;IAGC,MAAM,GAAA;QACZ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;;IAGlC,QAAQ,GAAA;AACd,QAAA,IAAI,CAAC,OAAO,GAAG,SAAS;;IAGlB,OAAO,GAAA;;QACb,IAAI,CAAC,aAAa,EAAE;AAEpB,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,IAAI,CAAC;;QAGzC,IAAI,CAAC,QAAQ,EAAE;;AAGT,IAAA,cAAc,CAAC,EAAU,EAAA;;QAC/B,IAAI,CAAC,aAAa,EAAE;;QAGpB,IAAI,CAAC,QAAQ,EAAE;QAEf,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,IAAI;QAE/B,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAJ,MAAA,GAAA,IAAI,GAAI,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK;YAAE;AAEZ,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC;QAC/C,IAAI,KAAK,KAAK,EAAE;YAAE;AAClB,QAAA,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;AAGtB,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAA,MAAA,GAAJ,IAAI,GAAI,EAAE,CAAC;AAE3D,QAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,IAAI,CAAC,oBAAoB,EAAE;;;AAIvB,IAAA,iBAAiB,CAAC,EAAU,EAAA;;QAClC,IAAI,CAAC,aAAa,EAAE;;QAGpB,IAAI,CAAC,QAAQ,EAAE;QAEf,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,IAAI;QAE/B,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAJ,MAAA,GAAA,IAAI,GAAI,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK;YAAE;AAEZ,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC;QAC/C,IAAI,KAAK,KAAK,EAAE;YAAE;QAElB,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,KAAK,GAAG,IAAI,GAAG,KAAK;AACtE,QAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,IAAI,CAAC,oBAAoB,EAAE;;;IAIvB,aAAa,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC;;;AAIhD,IAAA,mBAAmB,CAAC,IAAgB,EAAE,oBAAoB,GAAG,KAAK,EAAA;AAChE,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB;;QAEF,MAAM,OAAO,GAAY,EAAE;AAC3B,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI;AAE9B,QAAA,MAAM,MAAM,GAAG,IAAI,GAAG,EAAU;AAChC,QAAA,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAI;AACvD,YAAA,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AACxB,gBAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,oBAAA,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;;AAE3B,aAAC,CAAC;AACJ,SAAC,CAAC;QAEF,IAAI,CAAC,oBAAoB,EAAE;AACzB,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACzB,IAAI,CAAC,sBAAsB,EAC3B,IAAI,CAAC,cAAc,CACpB;AAED,YAAA,OAAO,CAAC,IAAI,CACV,CAAA,CAAA,QAAA,EAAA,EACE,QAAQ,EAAE,IAAI,CAAC,iBAAiB,KAAK,WAAW,EAChD,KAAK,EAAE,WAAW,EAEjB,EAAA,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG;kBAC/D,KAAK,CAAC;kBACN,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAC1B,CACV;;QAGH,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1E,YAAA,IAAI,KAAK,CAAC,MAAM,EAAE;AAChB,gBAAA,OAAO,CAAC,IAAI,CACV,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,KACZ,CAAA,CAAA,QAAA,EAAA,EAAQ,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,KAAK,CAAC,IACnC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CACb,CACV,CAAC,CACH;AACD,gBAAA,OAAO,CAAC,IAAI,CAAC,cAAQ,QAAQ,EAAA,IAAA,EAAA,CAAU,CAAC;;;AAG5C,QAAA,OAAO,OAAO;;IAGhB,WAAW,CAAC,IAAgB,EAAE,KAAa,EAAA;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AAE5C,QAAA,IAAI,CAAC,aAAa;AAAE,YAAA,OAAO,EAAE;AAE7B,QAAA,MAAM,WAAW,GAAG,CAAC,KAAW,KAAI;AAClC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK;AAC3C,YAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACjC,IAAI,CAAC,oBAAoB,EAAE;;AAE/B,SAAC;QAED,MAAM,SAAS,GAAG,MAAK;YACrB,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAsB;YACzE,IAAI,MAAM,EAAE;AACV,gBAAA,MAAM,CAAC,KAAK,GAAG,WAAW;AAC1B,gBAAA,IAAI,CAAC,iBAAiB,GAAG,WAAW;gBACpC,IAAI,CAAC,kBAAkB,EAAE;gBACzB,MAAM,CAAC,KAAK,EAAE;;AAElB,SAAC;AAED,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACzB,IAAI,CAAC,sBAAsB,EAC3B,IAAI,CAAC,cAAc,CACpB;AACD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK;AAClE,QAAA,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC/B,OAAO,KAAK,CAAC,CAAC,EAAE;AACd,gBAAA,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK;AACjC,gBAAA,MAAM,EAAE,aAAa,CAAC,KAAK,CAAC;gBAC5B,IAAI;gBACJ,KAAK;gBACL,WAAW,EAAE,KAAK,CAAC,WAAW;AAC9B,gBAAA,OAAO,EAAE,CAAC,KAAU,KAAI;oBACtB,WAAW,CAAC,KAAK,CAAC;iBACnB;gBACD,OAAO,EAAE,MAAK;AACZ,oBAAA,SAAS,EAAE;;AAEd,aAAA,CAAC;;QAEJ,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,YAAY,EAAE;AAC/C,YAAA,OAAO,EAAE;;QAEX,QACE,aACE,EAAE,EAAE,CAAA,aAAA,EAAgB,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAA,CAAE,EAC7C,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,IAAI,EAAE,KAAK,KAAK,YAAY,GAAG,MAAM,GAAG,MAAM,EAC9C,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK,EACjC,OAAO,EAAE,CAAC,CAAC,KAAI;AACb,gBAAA,IAAI,CAAC,CAAC,MAAM,YAAY,gBAAgB,EAAE;AACxC,oBAAA,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;;AAE/B,aAAC,EACD,SAAS,EAAE,CAAC,IAAG;gBACb,IAAI,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,OAAO,EAAE;oBACnC,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAsB;oBACzE,IAAI,MAAM,EAAE;AACV,wBAAA,SAAS,EAAE;;oBAEb;;;gBAGF,CAAC,CAAC,eAAe,EAAE;aACpB,EAAA,CACD;;IAIN,MAAM,GAAA;;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,OAAO,CAAA,CAAC,IAAI,EAAA,EAAC,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAA,CAAS;;AAElD,QAAA,MAAM,KAAK,GAAG;AACZ,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,IAAI,EAAE,CAAG,EAAA,IAAI,CAAC,OAAO,CAAC,CAAC,CAAI,EAAA,CAAA;AAC3B,YAAA,GAAG,EAAE,CAAG,EAAA,IAAI,CAAC,OAAO,CAAC,CAAC,CAAI,EAAA,CAAA;SAC3B;AAED,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACzB,IAAI,CAAC,sBAAsB,EAC3B,IAAI,CAAC,cAAc,CACpB;AAED,QAAA,QACE,CAAA,CAAC,IAAI,EAAA,EACH,KAAK,EAAE,KAAK,EACZ,GAAG,EAAE,EAAE,IAAG;;AACR,gBAAA,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,0CAAE,WAAW,MAAK,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;AAC7D,aAAC,EAAA,EAED,CAAM,CAAA,MAAA,EAAA,EAAA,IAAI,EAAC,QAAQ,EAAG,CAAA,EACpB,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,EAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,EAAG,IAAI,CAAC,OAAO,CAAC,KAAI,EAAE,EAE/C,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,0CAAE,kBAAkB,MAAK,IAAI,KACzC;YACE,CAAQ,CAAA,OAAA,EAAA,IAAA,EAAA,KAAK,CAAC,KAAK,CAAS;AAC5B,YAAA,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,eAAe,EAAA,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAO;YAC5D,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,YAAY,EAAA,EACrB,CAAA,CAAA,QAAA,EAAA,EACE,EAAE,EAAE,SAAS,EACb,KAAK,EAAC,YAAY,EAClB,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAA,EAEpC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAC1C;SAEZ,CACF,EAED,CAAQ,CAAA,MAAA,EAAA,IAAA,CAAA,EACR,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,gBAAgB,EAAA,EACxB,IAAI,CAAC,uBAAuB,IAAI;AAC/B,YAAA,CACE,CAAA,QAAA,EAAA,EAAA,EAAE,EAAC,kBAAkB,EAAA,YAAA,EACV,MAAM,EACjB,KAAK,EAAC,mBAAmB,EACzB,OAAO,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE,EAE3B,EAAA,KAAK,CAAC,IAAI,CACJ;AACT,YAAA,CACE,CAAA,QAAA,EAAA,EAAA,EAAE,EAAC,gBAAgB,EAAA,YAAA,EACR,IAAI,EACf,KAAK,EAAC,mBAAmB,EACzB,OAAO,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,EAE7B,EAAA,KAAK,CAAC,MAAM,CACN;AACV,SAAA,EACA,CAAC,IAAI,CAAC,uBAAuB,IAAI;AAChC,YAAA,CACE,CAAA,QAAA,EAAA,EAAA,EAAE,EAAC,gBAAgB,EAAA,YAAA,EACR,IAAI,EACf,KAAK,EAAC,mBAAmB,EACzB,OAAO,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,EAE7B,EAAA,KAAK,CAAC,EAAE,CACF;AAET,YAAA,CACE,CAAA,QAAA,EAAA,EAAA,EAAE,EAAC,mBAAmB,EAAA,YAAA,EACX,OAAO,EAClB,KAAK,EAAC,qBAAqB,EAC3B,OAAO,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE,EAE5B,EAAA,KAAK,CAAC,KAAK,CACL;AACV,SAAA,CACG,EACN,CAAA,CAAA,MAAA,EAAA,EAAM,IAAI,EAAC,QAAQ,EAAG,CAAA,CACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/utils/closest.polifill.ts", "src/plugins/filter/filter.style.scss?tag=revogr-filter-panel", "src/plugins/filter/filter.panel.tsx"], "sourcesContent": ["(function closest() {\n  if (!Element.prototype.matches) {\n    Element.prototype.matches =\n      ((Element.prototype as unknown) as { msMatchesSelector: (selectors: string) => boolean }).msMatchesSelector || Element.prototype.webkitMatchesSelector;\n  }\n\n  if (!Element.prototype.closest) {\n    Element.prototype.closest = function (s: string) {\n      let el: HTMLElement | Element | (Node & ParentNode) | null = this;\n\n      do {\n        if (Element.prototype.matches.call(el, s)) {\n          return el;\n        }\n        el = el.parentElement || el.parentNode;\n      } while (el !== null && el.nodeType === 1);\n      return null;\n    };\n  }\n})();\n", "revogr-filter-panel {\n  position: absolute;\n  display: block;\n  top: 0;\n  left: 0;\n  z-index: 100;\n  max-height: calc(100% - 80px);\n  min-width: 250px;\n  overflow: auto;\n\n  opacity: 1;\n  transform: none;\n  background-color: var(--revo-grid-filter-panel-bg, #fff);\n  border: 1px solid var(--revo-grid-filter-panel-border, #cecece);\n  transform-origin: 62px 0px;\n  box-shadow: 0 5px 18px -2px var(--revo-grid-filter-panel-shadow, rgba(0, 0, 0, 0.15));\n  padding: 10px;\n  border-radius: 8px;\n\n  min-width: 220px;\n  text-align: left;\n\n  .filter-holder > div {\n    display: flex;\n    flex-direction: column;\n  }\n\n  label {\n    font-size: 13px;\n    display: block;\n    padding: 8px 0;\n  }\n\n  select {\n    width: 100%;\n  }\n\n  input[type='text'] {\n    border: 0;\n    min-height: 34px;\n    margin: 5px 0;\n    background: var(--revo-grid-filter-panel-input-bg, #f3f3f3);\n    border-radius: 5px;\n    padding: 0 10px;\n    box-sizing: border-box;\n    width: 100%;\n  }\n\n  button {\n    margin-top: 10px;\n    margin-right: 5px;\n  }\n\n  .filter-actions {\n    text-align: right;\n    margin-right: -5px;\n  }\n}\n\n.rgHeaderCell {\n  &:hover .rv-filter {\n    transition:\n      opacity 267ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,\n      transform 178ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n  }\n  &:hover .rv-filter,\n  .rv-filter.active {\n    opacity: 1;\n  }\n\n  .rv-filter {\n    $btn-size: 24px;\n    height: $btn-size;\n    width: $btn-size;\n    background: none;\n    border: 0;\n    opacity: 0;\n    visibility: visible;\n    cursor: pointer;\n    border-radius: 4px;\n\n    &.active {\n      color: #10224a;\n    }\n\n    .filter-img {\n      $img-size: 11px;\n      color: gray;\n      width: $img-size;\n    }\n  }\n}\n\n.select-css {\n  $gradient: 'data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23007CB2%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E';\n\n  display: block;\n  font-family: sans-serif;\n  line-height: 1.3;\n  padding: 0.6em 1.4em 0.5em 0.8em;\n  width: 100%;\n  max-width: 100%; /* useful when width is set to anything other than 100% */\n  box-sizing: border-box;\n  margin: 0;\n  border: 1px solid var(--revo-grid-filter-panel-select-border, #d9d9d9);\n  box-shadow: transparent;\n  border-radius: 0.5em;\n  appearance: none;\n  background-color: var(--revo-grid-filter-panel-input-bg, #f3f3f3);\n  background-image: url($gradient);\n  background-repeat: no-repeat, repeat;\n  /* arrow icon position (1em from the right, 50% vertical) , then gradient position*/\n  background-position:\n    right 0.7em top 50%,\n    0 0;\n  /* icon size, then gradient */\n  background-size:\n    0.65em auto,\n    100%;\n\n  /* Hide arrow icon in IE browsers */\n  &::-ms-expand {\n    display: none;\n  }\n  /* Hover style */\n  &:hover {\n    border-color: var(--revo-grid-filter-panel-select-border, #d9d9d9);\n  }\n  /* Focus style */\n  &:focus {\n    border-color: var(--revo-grid-filter-panel-select-border-hover, #d9d9d9);\n    box-shadow: 0 0 1px 3px rgba(59, 153, 252, 0.7);\n    box-shadow: 0 0 0 3px -moz-mac-focusring;\n    outline: none;\n  }\n\n  /* Set options to normal weight */\n  option {\n    font-weight: normal;\n  }\n\n  /* Disabled styles */\n  &:disabled,\n  &[aria-disabled='true'] {\n    color: gray;\n    background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22graytext%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E'),\n      linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);\n  }\n\n  &:disabled:hover,\n  &[aria-disabled='true'] {\n    border-color: var(--revo-grid-filter-panel-select-border, #d9d9d9);\n  }\n}\n\n.multi-filter-list {\n  margin-top: 5px;\n  margin-bottom: 5px;\n\n  div {\n    white-space: nowrap;\n  }\n\n  .multi-filter-list-action {\n    display: flex;\n\n    justify-content: space-between;\n    align-items: center;\n  }\n\n  .and-or-button {\n    margin: 0 0 0 10px;\n    min-width: 58px;\n    cursor: pointer;\n  }\n  .trash-button {\n    $btn-remove-size: 22px;\n    margin: 0 0 -2px 6px;\n    cursor: pointer;\n\n    width: $btn-remove-size;\n    height: 100%;\n    font-size: 16px;\n\n    .trash-img {\n      width: 1em;\n    }\n  }\n}\n\n.add-filter-divider {\n  display: block;\n  margin: 0 -10px 10px -10px;\n\n  border-bottom: 1px solid var(--revo-grid-filter-panel-divider, #d9d9d9);\n  height: 10px;\n}\n\n.select-input {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n", "// filter.panel.tsx\n\nimport {\n  h,\n  Component,\n  Event,\n  EventEmitter,\n  Host,\n  Listen,\n  Method,\n  Prop,\n  State,\n  type VNode,\n  Element,\n} from '@stencil/core';\nimport debounce from 'lodash/debounce';\n\nimport { AndOrButton, isFilterBtn, TrashButton } from './filter.button';\nimport '../../utils/closest.polifill';\nimport {\n  FilterCaptions,\n  LogicFunction,\n  MultiFilterItem,\n  ShowData,\n} from './filter.types';\nimport type { ColumnProp } from '@type';\nimport { FilterType } from './filter.indexed';\n\nconst defaultType: FilterType = 'none';\n\nconst FILTER_LIST_CLASS = 'multi-filter-list';\nconst FILTER_LIST_CLASS_ACTION = 'multi-filter-list-action';\nconst FILTER_ID = 'add-filter';\n\n/**\n * Filter panel for editing filters\n */\n/**\n * @internal\n */\n@Component({\n  tag: 'revogr-filter-panel',\n  styleUrl: 'filter.style.scss',\n})\nexport class FilterPanel {\n  private filterCaptionsInternal: FilterCaptions = {\n    title: 'Filter by',\n    ok: 'Close',\n    save: 'Save',\n    // drops the filter\n    reset: 'Reset',\n    cancel: 'Cancel',\n    add: 'Add condition',\n    placeholder: 'Enter value...',\n    and: 'and',\n    or: 'or',\n  };\n\n  @Element() element!: HTMLElement;\n  @State() isFilterIdSet = false;\n  @State() filterId = 0;\n  @State() currentFilterId = -1;\n  @State() currentFilterType: FilterType = defaultType;\n  @State() changes: ShowData | undefined;\n  @State() filterItems: MultiFilterItem = {};\n  @Prop() filterNames: Record<string, string> = {};\n  @Prop() filterEntities: Record<string, LogicFunction> = {};\n  @Prop() filterCaptions: Partial<FilterCaptions> | undefined;\n  /**\n   * Disables dynamic filtering. A way to apply filters on Save only\n   */\n  @Prop() disableDynamicFiltering = false;\n  /**\n   * If true, closes the filter panel when clicking outside\n   */\n  @Prop() closeOnOutsideClick = true;\n  @Event() filterChange: EventEmitter<MultiFilterItem>;\n  @Event() resetChange: EventEmitter<ColumnProp>;\n\n  @Listen('mousedown', { target: 'document' }) onMouseDown(e: MouseEvent) {\n    // click on anything then select drops values to default\n    if (!this.changes) {\n      return;\n    }\n    const path = e.composedPath();\n    const select = document.getElementById(FILTER_ID);\n    if (select instanceof HTMLSelectElement) {\n      // click on select should be skipped\n      if (path.includes(select)) {\n        return;\n      }\n      select.value = defaultType;\n    }\n    this.currentFilterType = defaultType;\n    if (this.changes) {\n      this.changes.type = defaultType;\n    }\n    this.currentFilterId = -1;\n\n    const isOutside = !path.includes(this.element);\n\n    if (\n      e.target instanceof HTMLElement &&\n      isOutside &&\n      !isFilterBtn(e.target) &&\n      this.closeOnOutsideClick\n    ) {\n      this.changes = undefined;\n    }\n  }\n\n  @Method() async show(newEntity?: ShowData) {\n    this.changes = newEntity;\n    this.filterItems = newEntity?.filterItems || {};\n    if (this.changes) {\n      this.changes.type = this.changes.type || defaultType;\n    }\n  }\n\n  @Method() async getChanges() {\n    return this.changes;\n  }\n\n  componentWillRender() {\n    if (!this.isFilterIdSet) {\n      this.isFilterIdSet = true;\n      const filterItems = Object.keys(this.filterItems);\n      for (const prop of filterItems) {\n        // we set the proper filterId so there won't be any conflict when removing filters\n        this.filterId += this.filterItems[prop].length;\n      }\n    }\n  }\n\n  getFilterItemsList() {\n    const prop = this.changes?.prop;\n    if (typeof prop === 'undefined') return '';\n\n    const propFilters = this.filterItems[prop] ?? [];\n    const capts = Object.assign(\n      this.filterCaptionsInternal,\n      this.filterCaptions,\n    );\n    return (\n      <div key={this.filterId}>\n        {propFilters.map((filter, index) => {\n          let andOrButton;\n          if (filter.hidden) {\n            return;\n          }\n\n          // hide toggle button if there is only one filter and the last one\n          if (index !== this.filterItems[prop].length - 1) {\n            andOrButton = (\n              <div onClick={() => this.toggleFilterAndOr(filter.id)}>\n                <AndOrButton\n                  text={filter.relation === 'and' ? capts.and : capts.or}\n                />\n              </div>\n            );\n          }\n\n          return (\n            <div key={filter.id} class={FILTER_LIST_CLASS}>\n              <div class={{ 'select-input': true }}>\n                <select\n                  class=\"select-css select-filter\"\n                  onChange={e => this.onFilterTypeChange(e, prop, index)}\n                >\n                  {this.renderSelectOptions(\n                    this.filterItems[prop][index].type,\n                    true,\n                  )}\n                </select>\n                <div class={FILTER_LIST_CLASS_ACTION}>{andOrButton}</div>\n                <div onClick={() => this.onRemoveFilter(filter.id)}>\n                  <TrashButton />\n                </div>\n              </div>\n              <div>{this.renderExtra(prop, index)}</div>\n            </div>\n          );\n        })}\n\n        {propFilters.filter(f => !f.hidden).length > 0 ? <div class=\"add-filter-divider\" /> : ''}\n      </div>\n    );\n  }\n\n  private autoCorrect(el?: HTMLElement | null) {\n    if (!el) {\n      return;\n    }\n\n    const revoGrid = el.closest('revo-grid');\n    if (!revoGrid) {\n      return;\n    }\n\n    const pos = el.getBoundingClientRect();\n    const gridPos = revoGrid.getBoundingClientRect();\n    const maxLeft = gridPos.right - pos.width;\n\n    if (pos.left > maxLeft && el.offsetLeft) {\n      el.style.left = `${maxLeft - (el.parentElement?.getBoundingClientRect().left ?? 0)}px`;\n    }\n  }\n\n  private onFilterTypeChange(e: Event, prop: ColumnProp, index: number) {\n    if (!(e.target instanceof HTMLSelectElement)) {\n      return;\n    }\n    this.filterItems[prop][index].type = e.target.value as FilterType;\n\n    // this re-renders the input to know if we need extra input\n    this.filterId++;\n\n    // adding setTimeout will wait for the next tick DOM update then focus on input\n    setTimeout(() => {\n      const input = document.getElementById(\n        'filter-input-' + this.filterItems[prop][index].id,\n      );\n      if (input instanceof HTMLInputElement) {\n        input.focus();\n      }\n    }, 0);\n\n    if (!this.disableDynamicFiltering) {\n      this.debouncedApplyFilter();\n    }\n  }\n\n  private debouncedApplyFilter = debounce(() => {\n    this.filterChange.emit(this.filterItems);\n  }, 400);\n\n  private onAddNewFilter(e: Event) {\n    const el = e.target as HTMLSelectElement;\n    this.currentFilterType = el.value as FilterType;\n    this.addNewFilterToProp();\n\n    // reset value after adding new filter\n    const select = document.getElementById('add-filter') as HTMLSelectElement;\n    if (select) {\n      select.value = defaultType;\n      this.currentFilterType = defaultType;\n    }\n\n    if (!this.disableDynamicFiltering) {\n      this.debouncedApplyFilter();\n    }\n  }\n\n  private addNewFilterToProp() {\n    const prop = this.changes?.prop;\n    if (!(prop || prop === 0)) return;\n\n    if (!this.filterItems[prop]) {\n      this.filterItems[prop] = [];\n    }\n\n    if (this.currentFilterType === 'none') return;\n\n    this.filterId++;\n    this.currentFilterId = this.filterId;\n\n    this.filterItems[prop].push({\n      id: this.currentFilterId,\n      type: this.currentFilterType,\n      value: '',\n      relation: 'and',\n    });\n\n    // adding setTimeout will wait for the next tick DOM update then focus on input\n    setTimeout(() => {\n      const input = document.getElementById(\n        'filter-input-' + this.currentFilterId,\n      ) as HTMLInputElement;\n      if (input) input.focus();\n    }, 0);\n  }\n\n  private onSave() {\n    this.filterChange.emit(this.filterItems);\n  }\n\n  private onCancel() {\n    this.changes = undefined;\n  }\n\n  private onReset() {\n    this.assertChanges();\n\n    this.resetChange.emit(this.changes?.prop);\n\n    // this updates the DOM which is used by getFilterItemsList() key\n    this.filterId++;\n  }\n\n  private onRemoveFilter(id: number) {\n    this.assertChanges();\n\n    // this is for reactivity issues for getFilterItemsList()\n    this.filterId++;\n\n    const prop = this.changes?.prop;\n\n    const items = this.filterItems[prop ?? ''];\n    if (!items) return;\n\n    const index = items.findIndex(d => d.id === id);\n    if (index === -1) return;\n    items.splice(index, 1);\n\n    // let's remove the prop if no more filters so the filter icon will be removed\n    if (items.length === 0) delete this.filterItems[prop ?? ''];\n\n    if (!this.disableDynamicFiltering) {\n      this.debouncedApplyFilter();\n    }\n  }\n\n  private toggleFilterAndOr(id: number) {\n    this.assertChanges();\n\n    // this is for reactivity issues for getFilterItemsList()\n    this.filterId++;\n\n    const prop = this.changes?.prop;\n\n    const items = this.filterItems[prop ?? ''];\n    if (!items) return;\n\n    const index = items.findIndex(d => d.id === id);\n    if (index === -1) return;\n\n    items[index].relation = items[index].relation === 'and' ? 'or' : 'and';\n    if (!this.disableDynamicFiltering) {\n      this.debouncedApplyFilter();\n    }\n  }\n\n  private assertChanges() {\n    if (!this.changes) {\n      throw new Error('Changes required per edit');\n    }\n  }\n\n  renderSelectOptions(type: FilterType, isDefaultTypeRemoved = false) {\n    if (!this.changes) {\n      return;\n    }\n    const options: VNode[] = [];\n    const prop = this.changes.prop;\n\n    const hidden = new Set<string>();\n    Object.entries(this.filterItems).forEach(([_, values]) => {\n      values.forEach((filter) => {\n        if (filter.hidden) {\n          hidden.add(filter.type);\n        }\n      })\n    });\n\n    if (!isDefaultTypeRemoved) {\n      const capts = Object.assign(\n        this.filterCaptionsInternal,\n        this.filterCaptions,\n      );\n\n      options.push(\n        <option\n          selected={this.currentFilterType === defaultType}\n          value={defaultType}\n        >\n          {prop && this.filterItems[prop] && this.filterItems[prop].length > 0\n            ? capts.add\n            : this.filterNames[defaultType]}\n        </option>,\n      );\n    }\n\n    for (let gIndex in this.changes.filterTypes) {\n      const group = this.changes.filterTypes[gIndex].filter(k => !hidden.has(k));\n      if (group.length) {\n        options.push(\n          ...group.map(k => (\n            <option value={k} selected={type === k}>\n              {this.filterNames[k]}\n            </option>\n          )),\n        );\n        options.push(<option disabled></option>);\n      }\n    }\n    return options;\n  }\n\n  renderExtra(prop: ColumnProp, index: number) {\n    const currentFilter = this.filterItems[prop];\n\n    if (!currentFilter) return '';\n\n    const applyFilter = (value?: any) => {\n      this.filterItems[prop][index].value = value;\n      if (!this.disableDynamicFiltering) {\n        this.debouncedApplyFilter();\n      }\n    };\n\n    const focusNext = () => {\n      const select = document.getElementById('add-filter') as HTMLSelectElement;\n      if (select) {\n        select.value = defaultType;\n        this.currentFilterType = defaultType;\n        this.addNewFilterToProp();\n        select.focus();\n      }\n    };\n\n    const capts = Object.assign(\n      this.filterCaptionsInternal,\n      this.filterCaptions,\n    );\n    const extra = this.filterEntities[currentFilter[index].type].extra;\n    if (typeof extra === 'function') {\n      return extra(h, {\n        value: currentFilter[index].value,\n        filter: currentFilter[index],\n        prop,\n        index,\n        placeholder: capts.placeholder,\n        onInput: (value: any) => {\n          applyFilter(value);\n        },\n        onFocus: () => {\n          focusNext();\n        }\n      });\n    }\n    if (extra !== 'input' && extra !== 'datepicker') {\n      return '';\n    }\n    return (\n      <input\n        id={`filter-input-${currentFilter[index].id}`}\n        placeholder={capts.placeholder}\n        type={extra === 'datepicker' ? 'date' : 'text'}\n        value={currentFilter[index].value}\n        onInput={(e) => {\n          if (e.target instanceof HTMLInputElement) {\n            applyFilter(e.target.value);\n          }\n        }}\n        onKeyDown={e => {\n          if (e.key.toLowerCase() === 'enter') {\n            const select = document.getElementById('add-filter') as HTMLSelectElement;\n            if (select) {\n              focusNext();\n            }\n            return;\n          }\n          // keep event local, don't escalate farther to dom\n          e.stopPropagation();\n        }}\n      />\n    );\n  }\n\n  render() {\n    if (!this.changes) {\n      return <Host style={{ display: 'none' }}></Host>;\n    }\n    const style = {\n      display: 'block',\n      left: `${this.changes.x}px`,\n      top: `${this.changes.y}px`,\n    };\n\n    const capts = Object.assign(\n      this.filterCaptionsInternal,\n      this.filterCaptions,\n    );\n\n    return (\n      <Host\n        style={style}\n        ref={el => {\n          this.changes?.autoCorrect !== false && this.autoCorrect(el);\n        }}\n      >\n        <slot slot=\"header\" />\n        { this.changes.extraContent?.(this.changes) || '' }\n\n        { this.changes?.hideDefaultFilters !== true && (\n          [\n            <label>{capts.title}</label>,\n            <div class=\"filter-holder\">{this.getFilterItemsList()}</div>,\n            <div class=\"add-filter\">\n              <select\n                id={FILTER_ID}\n                class=\"select-css\"\n                onChange={e => this.onAddNewFilter(e)}\n              >\n                {this.renderSelectOptions(this.currentFilterType)}\n              </select>\n            </div>\n          ]\n        )}\n\n        <slot />\n        <div class=\"filter-actions\">\n          {this.disableDynamicFiltering && [\n            <button\n              id=\"revo-button-save\"\n              aria-label=\"save\"\n              class=\"revo-button green\"\n              onClick={() => this.onSave()}\n            >\n              {capts.save}\n            </button>,\n            <button\n              id=\"revo-button-ok\"\n              aria-label=\"ok\"\n              class=\"revo-button green\"\n              onClick={() => this.onCancel()}\n            >\n              {capts.cancel}\n            </button>,\n          ]}\n          {!this.disableDynamicFiltering && [\n            <button\n              id=\"revo-button-ok\"\n              aria-label=\"ok\"\n              class=\"revo-button green\"\n              onClick={() => this.onCancel()}\n            >\n              {capts.ok}\n            </button>,\n\n            <button\n              id=\"revo-button-reset\"\n              aria-label=\"reset\"\n              class=\"revo-button outline\"\n              onClick={() => this.onReset()}\n            >\n              {capts.reset}\n            </button>,\n          ]}\n        </div>\n        <slot slot=\"footer\" />\n      </Host>\n    );\n  }\n}\n"], "version": 3}