/* RevoGrid custom elements */
export { RevoGridComponent as RevoGrid } from '../dist/types/components/revoGrid/revo-grid';
export { defineCustomElement as defineCustomElementRevoGrid } from './revo-grid';
export { Attribution as RevogrAttribution } from '../dist/types/components/attribution/revogr-attribution';
export { defineCustomElement as defineCustomElementRevogrAttribution } from './revogr-attribution';
export { Clipboard as RevogrClipboard } from '../dist/types/components/clipboard/revogr-clipboard';
export { defineCustomElement as defineCustomElementRevogrClipboard } from './revogr-clipboard';
export { RevogrData as RevogrData } from '../dist/types/components/data/revogr-data';
export { defineCustomElement as defineCustomElementRevogrData } from './revogr-data';
export { RevoEdit as RevogrEdit } from '../dist/types/components/editors/revogr-edit';
export { defineCustomElement as defineCustomElementRevogrEdit } from './revogr-edit';
export { RevoGridExtra as RevogrExtra } from '../dist/types/components/extra/revogr-extra';
export { defineCustomElement as defineCustomElementRevogrExtra } from './revogr-extra';
export { FilterPanel as RevogrFilterPanel } from '../dist/types/plugins/filter/filter.panel';
export { defineCustomElement as defineCustomElementRevogrFilterPanel } from './revogr-filter-panel';
export { RevogrFocus as RevogrFocus } from '../dist/types/components/selectionFocus/revogr-focus';
export { defineCustomElement as defineCustomElementRevogrFocus } from './revogr-focus';
export { RevogrHeaderComponent as RevogrHeader } from '../dist/types/components/header/revogr-header';
export { defineCustomElement as defineCustomElementRevogrHeader } from './revogr-header';
export { OrderEditor as RevogrOrderEditor } from '../dist/types/components/order/revogr-order-editor';
export { defineCustomElement as defineCustomElementRevogrOrderEditor } from './revogr-order-editor';
export { OverlaySelection as RevogrOverlaySelection } from '../dist/types/components/overlay/revogr-overlay-selection';
export { defineCustomElement as defineCustomElementRevogrOverlaySelection } from './revogr-overlay-selection';
export { RevogrRowHeaders as RevogrRowHeaders } from '../dist/types/components/rowHeaders/revogr-row-headers';
export { defineCustomElement as defineCustomElementRevogrRowHeaders } from './revogr-row-headers';
export { RevogrScrollVirtual as RevogrScrollVirtual } from '../dist/types/components/scrollable/revogr-scroll-virtual';
export { defineCustomElement as defineCustomElementRevogrScrollVirtual } from './revogr-scroll-virtual';
export { RevogrFocus as RevogrTempRange } from '../dist/types/components/selectionTempRange/revogr-temp-range';
export { defineCustomElement as defineCustomElementRevogrTempRange } from './revogr-temp-range';
export { RevogrViewportScroll as RevogrViewportScroll } from '../dist/types/components/scroll/revogr-viewport-scroll';
export { defineCustomElement as defineCustomElementRevogrViewportScroll } from './revogr-viewport-scroll';
export { VNodeToHtml as VnodeHtml } from '../dist/types/components/vnode/vnode-converter';
export { defineCustomElement as defineCustomElementVnodeHtml } from './vnode-html';

/**
 * Get the base path to where the assets can be found. Use "setAssetPath(path)"
 * if the path needs to be customized.
 */
export declare const getAssetPath: (path: string) => string;

/**
 * Used to manually set the base path where assets can be found.
 * If the script is used as "module", it's recommended to use "import.meta.url",
 * such as "setAssetPath(import.meta.url)". Other options include
 * "setAssetPath(document.currentScript.src)", or using a bundler's replace plugin to
 * dynamically set the path at build time, such as "setAssetPath(process.env.ASSET_PATH)".
 * But do note that this configuration depends on how your script is bundled, or lack of
 * bundling, and where your assets can be loaded from. Additionally custom bundling
 * will have to ensure the static assets are copied to its build directory.
 */
export declare const setAssetPath: (path: string) => void;

/**
 * Used to specify a nonce value that corresponds with an application's CSP.
 * When set, the nonce will be added to all dynamically created script and style tags at runtime.
 * Alternatively, the nonce value can be set on a meta tag in the DOM head
 * (<meta name="csp-nonce" content="{ nonce value here }" />) which
 * will result in the same behavior.
 */
export declare const setNonce: (nonce: string) => void

export interface SetPlatformOptions {
  raf?: (c: FrameRequestCallback) => number;
  ael?: (el: EventTarget, eventName: string, listener: EventListenerOrEventListenerObject, options: boolean | AddEventListenerOptions) => void;
  rel?: (el: EventTarget, eventName: string, listener: EventListenerOrEventListenerObject, options: boolean | AddEventListenerOptions) => void;
}
export declare const setPlatformOptions: (opts: SetPlatformOptions) => void;
export * from '../dist/types';
