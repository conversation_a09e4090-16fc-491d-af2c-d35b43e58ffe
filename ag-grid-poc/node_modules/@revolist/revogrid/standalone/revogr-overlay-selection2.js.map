{"file": "revogr-overlay-selection2.js", "mappings": ";;;;;;;;;;;;;;AA2CA,MAAM,eAAe,GAAa;AAChC,IAAA,WAAW,CAAC,GAAG;AACf,IAAA,WAAW,CAAC,QAAQ;AACpB,IAAA,WAAW,CAAC,UAAU;AACtB,IAAA,WAAW,CAAC,UAAU;AACtB,IAAA,WAAW,CAAC,WAAW;CACxB;MACY,eAAe,CAAA;AAC1B,IAAA,WAAA,CAAoB,EAAU,EAAA;QAAV,IAAE,CAAA,EAAA,GAAF,EAAE;;AAEtB,IAAA,MAAM,OAAO,CACX,CAAgB,EAChB,QAAiB,EACjB,UAAmB,EACnB,EAAE,KAAK,EAAE,KAAK,EAAgD,EAAA;;QAG9D,IAAI,UAAU,EAAE;AACd,YAAA,QAAQ,CAAC,CAAC,IAAI;gBACZ,KAAK,WAAW,CAAC,MAAM;AACrB,oBAAA,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;oBAChB;gBACF,KAAK,WAAW,CAAC,GAAG;AAClB,oBAAA,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,QAAQ,CAAC;oBACpC;;YAEJ;;;;QAMF,IAAI,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;AAC5B,YAAA,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;YACnB;;;QAIF,IAAI,CAAC,KAAK,EAAE;YACV;;;AAIF,QAAA,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;AACjB,YAAA,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,QAAQ,CAAC;YACpC;;;AAIF,QAAA,IAAI,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AAC1B,YAAA,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;YAChB;;;AAIF,QAAA,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;YACb;;;AAIF,QAAA,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;YACZ;;;AAIF,QAAA,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;AACd,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;YACvB;;;AAIF,QAAA,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;YACZ,IAAI,QAAQ,EAAE;AACZ,gBAAA,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;YAEnB;;;QAIF,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;YACrB;;;QAIF,IAAI,MAAM,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE;YAC9C;;;AAII,IAAA,SAAS,CAAC,CAAgB,EAAA;AAChC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;AACjD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;;AAEjD,QAAA,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;YACpB;;QAEF,CAAC,CAAC,cAAc,EAAE;AAClB,QAAA,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;;AAGrB,IAAA,MAAM,kBAAkB,CAAC,CAAgB,EAAE,QAAiB,EAAA;QAC1D,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,QAAQ,CAAC;QACjD,IAAI,CAAC,IAAI,EAAE;AACT,YAAA,OAAO,KAAK;;;;;AAMd,QAAA,MAAM,OAAO,CAAC,eAAe,GAAG,EAAE,CAAC;AAEnC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;AACjD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;AACjD,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC;;IAGzE,iBAAiB,CACf,OAAsB,EACtB,KAAuB,EACvB,KAAkB,EAClB,OAAO,GAAG,KAAK,EAAA;AAEf,QAAA,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;AACpB,YAAA,OAAO,KAAK;;AAEd,QAAA,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC;QAC1D,IAAI,CAAC,IAAI,EAAE;AACT,YAAA,OAAO,KAAK;;QAEd,MAAM,KAAK,GAAc,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE;QAC1C,IAAI,OAAO,EAAE;AACX,YAAA,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACtE,gBAAA,OAAO,KAAK;;AAEd,YAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC;YAC5C,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;;QAE7B,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,CAClB,IAAI,CAAC,KAAK,EACV,OAAO,EACP,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ;AACpC,cAAE;AACF,cAAE,aAAa,CAAC,IAAI,CAAC,KAAK;kBACtB;kBACA,CAAC,CACR;;;IAIH,kBAAkB,CAChB,CAAgB,EAChB,QAAiB,EAAA;AAEjB,QAAA,MAAM,OAAO,GAAG,QAAQ,IAAI,CAAC,CAAC,QAAQ;QACtC,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;YACpC,CAAC,CAAC,cAAc,EAAE;;AAGpB,QAAA,IAAI,CAAC,CAAC,QAAQ,EAAE;AACd,YAAA,QAAQ,CAAC,CAAC,IAAI;gBACZ,KAAK,WAAW,CAAC,GAAG;AAClB,oBAAA,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;;;AAInD,QAAA,QAAQ,CAAC,CAAC,IAAI;YACZ,KAAK,WAAW,CAAC,QAAQ;AACvB,gBAAA,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;YACxC,KAAK,WAAW,CAAC,UAAU;gBACzB,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE;YACvC,KAAK,WAAW,CAAC,UAAU;AACzB,gBAAA,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;YACxC,KAAK,WAAW,CAAC,GAAG;YACpB,KAAK,WAAW,CAAC,WAAW;gBAC1B,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE;;;AAG5C;;MC1KY,eAAe,CAAA;AAU1B,IAAA,WAAA,CAAoB,EAAU,EAAA;QAAV,IAAE,CAAA,EAAA,GAAF,EAAE;QATd,IAAY,CAAA,YAAA,GAAwB,IAAI;QACxC,IAAe,CAAA,eAAA,GAAgB,IAAI;QACnC,IAAa,CAAA,aAAA,GAAgB,IAAI;QACjC,IAAY,CAAA,YAAA,GAAgB,IAAI;;AAQxC;;;;AAIG;AACH,IAAA,cAAc,CAAC,KAAuB,EAAE,cAAoB,EAAE,QAAQ,GAAG,KAAK,EAAA;AAC5E,QAAA,IAAI,YAAY;QAChB,IAAI,KAAK,EAAE;YACT,YAAY,GAAG,OAAO,CACpB,KAAK,EACL,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,EAC1B,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAC3B;;aACI;AACL,YAAA,YAAY,GAAG,OAAO,CAEf,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,cAAc,KACjB,EAAE,EAAE,cAAc,CAAC,CAAC,EACpB,EAAE,EAAE,cAAc,CAAC,CAAC,EAEtB,CAAA,EAAA,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,EAC1B,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAC3B;;QAEH,QACE,CACE,CAAA,KAAA,EAAA,EAAA,KAAK,EAAE;gBACL,CAAC,kBAAkB,GAAG,IAAI;gBAC1B,CAAC,YAAY,GAAG,QAAQ;AACzB,aAAA,EACD,KAAK,EAAE;AACL,gBAAA,IAAI,EAAE,CAAA,EAAG,YAAY,CAAC,KAAK,CAAI,EAAA,CAAA;AAC/B,gBAAA,GAAG,EAAE,CAAA,EAAG,YAAY,CAAC,MAAM,CAAI,EAAA,CAAA;AAChC,aAAA,EACD,WAAW,EAAE,CAAC,CAAa,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EACvD,YAAY,EAAE,CAAC,CAAa,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAA,CACxD;;AAIE,IAAA,eAAe,CACrB,CAA0B,EAC1B,IAAI,GAAwB,UAAA,8BAAA;QAE5B,IAAI,MAAM,GAAmB,IAAI;AACjC,QAAA,IAAI,CAAC,CAAC,MAAM,YAAY,OAAO,EAAE;AAC/B,YAAA,MAAM,GAAG,CAAC,CAAC,MAAM;;QAEnB,IAAI,CAAC,MAAM,EAAE;YACX;;AAEF,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC;QACpD,CAAC,CAAC,cAAc,EAAE;;AAGpB,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,YAAY;;AAG5B;;AAEG;AACH,IAAA,kBAAkB,CAAC,CAA0B,EAAA;;AAE3C,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CACjC,CAAC,CAA0B,EAAE,IAAe,KAC1C,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,EACnC,CAAC,CACF;;AAEH,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;;;IAI1C,QAAQ,CAAC,KAAkB,EAAE,KAAuB,EAAA;;AAE1D,QAAA,IAAI,CAAC,KAAK,IAAI,KAAK,EAAE;AACnB,YAAA,KAAK,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE;;QAEpC,OAAO,KAAK,IAAI,IAAI;;AAGtB;;;AAGG;IACK,mBAAmB,CAAC,KAA8B,EAAE,IAAe,EAAA;;AAEzE,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB;;QAEF,MAAM,CAAC,GAAG,oBAAoB,CAAC,KAAK,EAAE,SAAS,EAAE,YAAY,CAAC;QAC9D,MAAM,CAAC,GAAG,oBAAoB,CAAC,KAAK,EAAE,SAAS,EAAE,YAAY,CAAC;;QAE9D,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;YAC5B;;AAEF,QAAA,MAAM,OAAO,GAAG,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC;;AAG9C,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa;;;;QAK1C,IAAI,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE;YACvC;;AAEF,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO;QAE3B,MAAM,MAAM,GACV,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC;YACpC,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC;;QAGtC,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC;;aACrB;AACL,YAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC;AAC9D,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC;gBACnB,IAAI;gBACJ,IAAI,EAAE,IAAI,CAAC,YAAY;AACxB,aAAA,CAAC;;;AAIN;;;;;AAKG;AACH,IAAA,cAAc,CACZ,MAAe,EACf,IAAe,EACf,IAAI,GAAyB,WAAA,+BAAA;;QAG7B,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,qBAAqB,EAAE;AACpD,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;AAC5D,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,QAAA,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC;;AAGhE;;AAEG;IACH,sBAAsB,CAAC,KAAkB,EAAE,QAA0B,EAAA;;AAEnE,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;;YAExB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;;AAGrD,YAAA,IAAI,IAAI,CAAC,YAAY,KAAA,UAAA,8BAA4B;AAC/C,gBAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC;;gBAG/D,IAAI,KAAK,EAAE;AACT,oBAAA,MAAM,EACJ,gBAAgB,EAAE,SAAS,EAC3B,MAAM,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,GAC5B,GAAG,IAAI,CAAC,EAAE,CAAC,mBAAmB,CAAC;wBAC9B,KAAK;AACN,qBAAA,CAAC;;AAGF,oBAAA,IAAI,CAAC,SAAS,IAAI,QAAQ,EAAE;AAC1B,wBAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC;;yBACtC;;AAEL,wBAAA,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC;;;;iBAGzB;;gBAEL,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC;;;;QAKhE,IAAI,CAAC,kBAAkB,EAAE;;AAG3B;;AAEG;IACK,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI;AAC3B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;AAG3B;;AAEG;AACH,IAAA,YAAY,CAAC,OAAmB,EAAE,QAA0B,EAAE,QAA0B,EAAA;AACtF,QAAA,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;AACrB,YAAA,IAAI,EAAE,OAAO;YACb,MAAM,EAAE,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC;YACxD,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;YACnC,QAAQ;YACR;AACD,SAAA,CAAC;AAEF,QAAA,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;;;IAIpB,kBAAkB,CAAC,QAAmB,EAAE,WAAsB,EAAA;AACpE,QAAA,MAAM,SAAS,GAAiB;YAC9B,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;AACnC,YAAA,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,IAAI;AACnC,YAAA,OAAO,EAAE,EAAE;AACX,YAAA,OAAO,EAAE,EAAE;YACX,QAAQ;AACR,YAAA,QAAQ,EAAE,WAAW;SACtB;QACD,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,YAAY,CAC7D,SAAS,EACT,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,OAAO,CAC9B;AACD,QAAA,SAAS,CAAC,OAAO,GAAG,OAAO;AAC3B,QAAA,SAAS,CAAC,OAAO,GAAG,OAAO;QAC3B,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,SAAS,CAAC;;AAG3C,QAAA,IAAI,CAAC,CAAC,gBAAgB,EAAE;AACtB,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC;YAC1B;;QAGF,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC;AAChC,QAAA,IAAI,CAAC,CAAC,gBAAgB,EAAE;AACtB,YAAA,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC1B;;QAEF,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC;;AAG7D;;;AAGG;IACK,cAAc,CAAC,KAAmB,EAAE,GAAiB,EAAA;;AAE3D,QAAA,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE;YAClB;;QAGF,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC;AACrC,QAAA,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;;AAE7B;;ACjUD,MAAM,qBAAqB,GAAG,m9PAAm9P;;MCgEp+P,gBAAgB,iBAAAA,kBAAA,CAAA,MAAA,gBAAA,SAAA,WAAA,CAAA;AAJ7B,IAAA,WAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDE;;AAEG;AACK,QAAA,IAAmB,CAAA,mBAAA,GAAG,KAAK;AAgL3B,QAAA,IAAe,CAAA,eAAA,GAA2B,IAAI;AAC9C,QAAA,IAAe,CAAA,eAAA,GAA2B,IAAI;AAG9C,QAAA,IAAyB,CAAA,yBAAA,GAAmB,EAAE;AAipBvD;;;AA3oBC,IAAA,WAAW,CAAC,CAA0B,EAAA;;QACpC,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACpC,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,kBAAkB,CAAC,CAAC,CAAC;;;AAI/C;;;AAGG;IAIH,SAAS,GAAA;;;;;AAIP,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,sBAAsB,CAC1C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAChC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CACjC;;AAGH;;;AAGG;AACsB,IAAA,UAAU,CAAC,CAA8B,EAAA;;;AAEhE,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;;AAGvC;;;AAGG;AACsC,IAAA,OAAO,CAAC,CAAgB,EAAA;;QAE/D,IAAI,CAAC,WAAW,CAAC,IAAI,iBAAG,QAAQ,EAAE,CAAC,EAAA,EAAK,IAAI,CAAC,OAAO,EAAE,EAAG;;AAG3D;;;AAGG;AACwC,IAAA,SAAS,CAAC,CAAgB,EAAA;;;QAEnE,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAA,MAAA,CAAA,MAAA,CAAA,EAAG,QAAQ,EAAE,CAAC,EAAK,EAAA,IAAI,CAAC,OAAO,EAAE,EAAG;QACzE,IAAI,CAAC,CAAC,gBAAgB,IAAI,KAAK,CAAC,gBAAgB,EAAE;YAChD;;;AAGF,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,OAAO,CAC3B,CAAC,EACD,IAAI,CAAC,KAAK,EACV,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EACjC;YACE,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;YACvC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;AACxC,SAAA,CACF;;;AAIH;;AAEG;AACsB,IAAA,mBAAmB,CAC1C,cAA+C,EAAA;;AAG/C,QAAA,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;AAChD,QAAA,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,CAAC;QACzC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CACjC,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CACnE;AAED,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC;YACzC,cAAc;AACd,YAAA,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAC5C,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,iBAAiB,KAAI;gBACvC,IAAI,iBAAiB,EAAE;AACrB,oBAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;AACpC,oBAAA,OAAO,KAAK;;qBACP;oBACL,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC;;aAErC;YACD,MAAM,EAAE,GAAG,IAAG;AACZ,gBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjB;;AAEF,gBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;aACjB;YACD,MAAM,EAAE,YAAW;;AACjB,gBAAA,OAAM,MAAA,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,aAAa,EAAE,CAAA;gBACtC,IAAI,CAAC,SAAS,EAAE;aACjB;AACD,YAAA,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;AACnD,YAAA,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE;AACpE,YAAA,OAAO,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE;YAC7B,SAAS,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;AACvC,SAAA,CAAC;QACF,IAAI,CAAC,qBAAqB,EAAE;;;IAK9B,qBAAqB,GAAA;AACnB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC;YACzC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS;AAEzB,YAAA,mBAAmB,EAAE,CAAC,IACpB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACzB,CAAC,CAAA,EACD,IAAI,CAAC,KAAK,CACb,EAAA,EAAA,YAAY,EAAO,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAA,EAC1C,YAAY,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAC1C,EAAA,CAAA,CAAA;YACJ,YAAY,EAAE,CAAC,IAAG;AAChB,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,+BACjD,SAAS,EAAE,CAAC,EACT,EAAA,IAAI,CAAC,OAAO,EAAE,GACd,IAAI,CAAC,KAAK,CAAA,CACb;AACF,gBAAA,IAAI,cAAc,CAAC,gBAAgB,EAAE;AACnC,oBAAA,OAAO,IAAI;;AAEb,gBAAA,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC;aAC/D;AACD,YAAA,gBAAgB,EAAE,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;AAEnD,YAAA,SAAS,EAAE,CAAC,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,YAAA,cAAc,EAAE,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;AAEhD,YAAA,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;AAC/C,YAAA,OAAO,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE;AAC9B,SAAA,CAAC;;;IAMJ,gBAAgB,GAAA;;QACd,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,OAAO,EAAE;AAC7B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC;QACpE,IAAI,CAAC,qBAAqB,EAAE;;IAG9B,iBAAiB,GAAA;QACf,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC;;IAG/C,oBAAoB,GAAA;;;AAElB,QAAA,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;AAChD,QAAA,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,CAAC;QACzC,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,OAAO,EAAE;;AAG/B,IAAA,MAAM,mBAAmB,GAAA;;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE;YACb,OAAM,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,gBAAgB,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;;;AAIvC,IAAA,WAAW,CAAC,KAAgB,EAAA;AAClC,QAAA,MAAM,IAAI,GAAG,OAAO,CAClB,KAAK,EACL,IAAI,CAAC,YAAY,CAAC,KAAK,EACvB,IAAI,CAAC,YAAY,CAAC,KAAK,CACxB;AACD,QAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC;QACrC,OAAO;YACL,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAE,sBAAsB,EAAE,KAAK,EAAE,MAAM,EAAA,EAC9C,IAAI,CAAC,cAAc,KAClB,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,gBAAgB,EAAA,EACzB,CAAM,CAAA,MAAA,EAAA,EAAA,KAAK,EAAE,YAAY,EAAS,CAAA,EAClC,CAAA,CAAA,MAAA,EAAA,EAAM,KAAK,EAAE,YAAY,EAAS,CAAA,CAC9B,CACP,CACG;SACP;;IAGK,YAAY,GAAA;;QAElB,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC;;AAEhD,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;AAC9B,YAAA,OAAO,IAAI;;AAEb,QAAA,MAAM,mBAAmB,GACvB,QAAQ,CAAC,GAAG;AACV,YAAA,WAAW,CACT,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAC9D;AACL,QAAA,MAAM,QAAQ,mCACT,QAAQ,CAAA,EACR,IAAI,CAAC,aAAa,CAAC,WAAW,CAC/B,QAAQ,CAAC,CAAC,EACV,QAAQ,CAAC,CAAC,EACV,mBAAmB,CACpB,CACF;AACD,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,+BAC5C,KAAK,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACA,QAAQ,CACX,EAAA,EAAA,EAAE,EAAE,QAAQ,CAAC,CAAC,EACd,EAAE,EAAE,QAAQ,CAAC,CAAC,EAEb,CAAA,EAAA,EAAA,IAAI,CAAC,KAAK,CAAA,EAAA,EACb,YAAY,EAAO,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAC1C,EAAA,YAAY,oBAAO,IAAI,CAAC,YAAY,CAAC,KAAK,KAC1C;;AAGF,QAAA,IAAI,WAAW,CAAC,gBAAgB,EAAE;AAChC,YAAA,OAAO,IAAI;;QAGb,MAAM,IAAI,GAAG,OAAO,CAClB,WAAW,CAAC,MAAM,CAAC,KAAK,EACxB,WAAW,CAAC,MAAM,CAAC,YAAY,EAC/B,WAAW,CAAC,MAAM,CAAC,YAAY,CAChC;AACD,QAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC;QACrC,QACE,CACE,CAAA,aAAA,EAAA,EAAA,KAAK,EAAE,MAAM,EACb,GAAG,EAAE,EAAE,KAAK,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,EACjC,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,QAAQ,EAAE,QAAQ,EAClB,WAAW,EAAE,IAAI,CAAC,mBAAmB,EACrC,cAAc,EAAE,CAAC,CAAC,KAAI;gBACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;AAClC,aAAC,EACD,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAC/D,MAAM,EAAE,aAAa,CACnB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EACtC,IAAI,CAAC,OAAO,CACb,EAAA,CACD;;AAIE,IAAA,UAAU,CAAC,CAA+B,EAAA;AAChD,QAAA,IAAI,CAAC,CAAC,gBAAgB,EAAE;YACtB;;AAEF,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;AACjD,QAAA,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE;AAC5B,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;;;AAI9B,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE;YAC/B,IAAI,CAAC,SAAS,EAAE;;;IAIpB,MAAM,GAAA;;QACJ,MAAM,KAAK,GAAY,EAAE;AACzB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE;;QAGpC,IAAI,QAAQ,EAAE;AACZ,YAAA,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;;aACf;YACL,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;YAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;;YAG9C,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY,EAAE;AACzC,gBAAA,KAAK,CAAC,IAAI,CACR,CAAA,CAAA,kBAAA,EAAA,EACE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,YAAY,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,EACxC,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,EACvD,GAAG,EAAE,CAAC,KAAK,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,EAC9B,aAAa,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,EAAA,CAC1C,CACH;;;YAIH,IAAI,KAAK,EAAE;gBACT,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;;YAGxC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE;AACzC,gBAAA,KAAK,CAAC,IAAI,CAAC,MAAA,IAAI,CAAC,eAAe,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;;;AAIrF,YAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,gBAAA,KAAK,CAAC,IAAI,CACR,CAAA,CAAA,qBAAA,EAAA,EACE,GAAG,EAAE,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAChC,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,YAAY,EAAE,IAAI,CAAC,YAAY,EAC/B,YAAY,EAAE,IAAI,CAAC,YAAY,EAC/B,MAAM,EAAE,IAAI,CAAC,OAAO,EACpB,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAC3B,kBAAkB,EAAE,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAA,CAC7C,CACH;;;AAGL,QAAA,QACE,CAAA,CAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EACH,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,EACtC,UAAU,EAAE,CAAC,CAAa,KAAK,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,EACxD,WAAW,EAAE,CAAC,CAAa,KAAK,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAC1D,YAAY,EAAE,CAAC,CAAa,KAAK,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,EACjE,WAAW,EAAE,CAAC,CAAmC,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;AAEvE,YAAA,UAAU,EAAE,CAAC,CAA+B,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAA,EAElE,KAAK,EACN,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,MAAM,EAAG,CAAA,CACf;;AAIX;;AAEG;AACK,IAAA,OAAO,CAAC,KAAW,EAAE,GAAS,EAAE,OAAuB,EAAA;;QAE7D,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CACpD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CACjD;QACD,IAAI,gBAAgB,EAAE;AACpB,YAAA,OAAO,KAAK;;AAEd,QAAA,MAAM,MAAM,GACV,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,KAAK,kCACA,KAAK,CAAA,EAAA,EACR,EAAE,EAAE,GAAG,CAAC,CAAC,EACT,EAAE,EAAE,GAAG,CAAC,CAAC,EAAA,CAAA,EAEX,IAAI,EAAE,OAAO,EACV,EAAA,IAAI,CAAC,KAAK,CAAA,EAAA,EACb,YAAY,EAAO,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAC1C,EAAA,YAAY,oBAAO,IAAI,CAAC,YAAY,CAAC,KAAK,IAC3C;;QAGD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/C,QAAA,IAAI,UAAU,CAAC,gBAAgB,EAAE;AAC/B,YAAA,OAAO,KAAK;;AAEd,QAAA,MAAM,EAAE,KAAK,EAAE,GAAG,UAAU,CAAC,MAAM;;AAGnC,QAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA,MAAA,CAAA,MAAA,CAAA,EACzB,KAAK,EAAE;gBACL,CAAC,EAAE,KAAK,CAAC,CAAC;gBACV,CAAC,EAAE,KAAK,CAAC,CAAC;AACX,aAAA,EACD,GAAG,EAAE;gBACH,CAAC,EAAE,KAAK,CAAC,EAAE;gBACX,CAAC,EAAE,KAAK,CAAC,EAAE;aACZ,EAAA,EACE,UAAU,CAAC,MAAM,CACpB,CAAA,CAAC,gBAAgB;;AAGb,IAAA,iBAAiB,CAAC,KAAgB,EAAA;AACxC,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO;;AAE/B,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAC3C,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,KAAK,EAAO,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAK,CACd,EAAA,EAAA,IAAI,CAAC,KAAK,CAAA,EAAA,EACb,YAAY,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAC1C,EAAA,YAAY,EAAO,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CAAC,YAAY,CAAC,KAAK,KAC1C;AACF,QAAA,IAAI,UAAU,CAAC,gBAAgB,EAAE;AAC/B,YAAA,OAAO,KAAK;;AAEd,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,0BAA0B,CACxD,UAAU,CAAC,MAAM,CAAC,KAAK,EACvB,IAAI,CAAC,SAAS,CACf;;QAED,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;AACtC,QAAA,IAAI,CAAC,CAAC,gBAAgB,EAAE;AACtB,YAAA,OAAO,KAAK;;;AAGd,QAAA,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAM,UAAU,CAAC,MAAM,CAAC,KAAK,CAAE,EAAA,EAAA,IAAI,IAAG;AAC5D,QAAA,IAAI,CAAC,CAAC,gBAAgB,EAAE;AACtB,YAAA,OAAO,KAAK;;AAEd,QAAA,OAAO,CAAC,CAAC,CAAC,gBAAgB;;AAG5B;;AAEG;AACK,IAAA,iBAAiB,CAAC,CAAa,EAAA;;AAErC,QAAA,IAAI,CAAC,CAAC,gBAAgB,EAAE;YACtB;;;AAIF,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE;QAC3B,MAAM,SAAS,GAAG,wBAAwB,CAAC,CAAC,EAAE,IAAI,CAAC;QACnD,IAAI,CAAC,SAAS,EAAE;YACd;;QAEF,IAAI,CAAC,MAAM,EAAE;;AAGf;;AAEG;AACK,IAAA,kBAAkB,CAAC,CAA0B,EAAE,KAAK,GAAG,KAAK,EAAA;;;AAElE,QAAA,MAAM,aAAa,GAAG,CAAC,CAAC,MAAiC;;QAEzD,IAAI,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE;YACpD;;;AAIF,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE;QAC3B,MAAM,SAAS,GAAG,wBAAwB,CAAC,CAAC,EAAE,IAAI,CAAC;QACnD,IAAI,CAAC,SAAS,EAAE;YACd;;;AAIF,QAAA,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,QAAQ,CAAC;;AAG/C,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,aAAa;AACX,iBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,cAAc,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;;;YAIrE,IAAI,CAAC,KAAK,EAAE;gBACV,CAAC,CAAC,cAAc,EAAE;;AACb,iBAAA,IACL,iBAAiB,CAAE,CAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,EAC7D;;;gBAGA,CAAC,CAAC,cAAc,EAAE;;;;AAKxB;;AAEG;IACO,MAAM,CAAC,GAAG,GAAG,EAAE,EAAA;;AACvB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YAClB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;YAC9C,IAAI,CAAC,KAAK,EAAE;gBACV;;AAEF,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AAC7D,YAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAI,iCACb,IAAI,CAAA,EAAA,EACP,GAAG,EAAA,CAAA,CACH;;;AAIN;;;AAGG;IACK,MAAM,SAAS,CAAC,CAAoC,EAAA;AAC1D,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;QACtB,IAAI,CAAC,aAAD,CAAC,KAAA,MAAA,GAAA,MAAA,GAAD,CAAC,CAAE,MAAM,EAAE;AACb,YAAA,MAAM,IAAI,CAAC,SAAS,EAAE;;;AAI1B;;;AAGG;AACO,IAAA,QAAQ,CAAC,CAAkB,EAAA;QACnC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC;AAC1E,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC;;IAG7B,SAAS,GAAA;QACf,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;QAC9C,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;QAC5C,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;;AAEhC,QAAA,OAAO,KAAK;;AAEN,IAAA,MAAM,CAAC,CAAe,EAAA;;AAC5B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE;QAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC;AACtD,QAAA,IAAI,YAAY,CAAC,gBAAgB,EAAE;AACjC,YAAA,OAAO,KAAK;;AAEd,QAAA,IAAI,SAAqC;QAEzC,IAAI,KAAK,EAAE;AACT,YAAA,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CACzD,KAAK,EACL,IAAI,CAAC,SAAS,CACf;AACD,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,iBACxC,KAAK;gBACL,IAAI;AACJ,gBAAA,OAAO,EACJ,EAAA,IAAI,CAAC,KAAK,EACb;AACF,YAAA,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;AAC3B,gBAAA,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI;;;AAIjC,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,SAAS,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC;AACpC,QAAA,OAAO,IAAI;;AAGL,IAAA,OAAO,CAAC,IAAgB,EAAA;;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;AAC9C,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI;AAC1D,QAAA,IAAI,CAAC,KAAK,IAAI,SAAS,EAAE;YACvB;;AAEF,QAAA,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,yBAAyB,CACnE,KAAK,EACL,IAAI,CACL;AACD,QAAA,MAAM,EAAE,gBAAgB,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAClE,MAAA,CAAA,MAAA,CAAA,EAAA,IAAI,EAAE,OAAO,EACb,MAAM,EAAE,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,EACrD,KAAK,EACF,EAAA,IAAI,CAAC,KAAK,EACb;QAEF,IAAI,QAAQ,EAAE;YACZ;;AAEF,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;;AAGnD,IAAA,MAAM,SAAS,GAAA;;AACrB,QAAA,MAAM,QAAQ,GAAG,OAAM,MAAA,IAAI,CAAC,eAAe,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,kBAAkB,CAC7D,IAAI,aAAa,CAAC,SAAS,EAAE;YAC3B,IAAI,EAAE,WAAW,CAAC,UAAU;AAC7B,SAAA,CAAC,EACF,IAAI,CAAC,KAAK,CACX,CAAA;QACD,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,SAAS,EAAE;;;IAIV,SAAS,GAAA;;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;QAC9C,IAAI,KAAK,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;AACtC,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,KAAK,EAAE,EAAE,CAAC;AAC7D,YAAA,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;;AACjD,aAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YACzB,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;YAChD,IAAI,CAAC,OAAO,EAAE;gBACZ;;AAEF,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;YACjE,IAAI,CAAC,QAAQ,CAAC;gBACZ,KAAK,EAAE,OAAO,CAAC,CAAC;gBAChB,KAAK,EAAE,OAAO,CAAC,CAAC;AAChB,gBAAA,GAAG,EAAE,EAAE;gBACP,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,aAAA,CAAC;;;IAIE,YAAY,CAAC,EAAE,MAAM,EAA6C,EAAA;QACxE,MAAM,CAAC,IAAI,GAAG,WAAW,CACvB,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CACpE;;AAGH;;AAEG;IACO,OAAO,GAAA;;AACf,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,OAAO,KAAK;;QAEd,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;AAC9C,QAAA,OAAO,KAAK,IAAI,EAAC,MAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAA;;AAGnE,IAAA,IAAI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC;;AAGxC;;AAEG;AACH,IAAA,KAAK,CAAC,IAAW,EAAE,WAAW,GAAG,KAAK,EAAA;AACpC,QAAA,IAAI,CAAC,IAAI;AAAE,YAAA,OAAO,KAAK;QAEvB,MAAM,GAAG,GAAG,IAAI;QAChB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;AAE9C,QAAA,IAAI,WAAW,IAAI,KAAK,EAAE;YACxB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC;YAClC,IAAI,KAAK,EAAE;AACT,gBAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;;;QAIxC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;;AAGhC,IAAA,IAAI,KAAK,GAAA;QACP,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;AACnC,YAAA,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;SACjC;;AAGH;;AAEG;IACO,OAAO,GAAA;QACf,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,OAAO;AAChB,YAAA,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK;AAC7B,YAAA,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;YACvC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;YACvC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC;SACtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/components/overlay/keyboard.service.ts", "src/components/overlay/autofill.service.tsx", "src/components/overlay/revogr-overlay-style.scss?tag=revogr-overlay-selection", "src/components/overlay/revogr-overlay-selection.tsx"], "sourcesContent": ["import { getRange } from '@store';\nimport {\n  codesLetter,\n  isAll,\n  isClear,\n  isCopy,\n  isCut,\n  isEnterKeyValue,\n  isPaste,\n  isTab,\n  timeout,\n  RESIZE_INTERVAL,\n  type Observable,\n} from '../../utils';\nimport {\n  EventData,\n  getCoordinate,\n  isAfterLast,\n  isBeforeFirst,\n} from './selection.utils';\nimport { Cell, Nullable, RangeArea, SelectionStoreState } from '@type';\n\ntype Config = {\n  selectionStore: Observable<SelectionStoreState>;\n\n  // Apply changes from edit.\n  change(val?: any): void;\n  // Cancels edit. Escape changes.\n  cancel(): void;\n\n  clearCell(): void;\n  focus(\n    focus: Cell,\n    changes: Partial<Cell>,\n    focusNextViewport?: number,\n  ): boolean;\n\n  getData(): any;\n  internalPaste(): void;\n  range(range: RangeArea | null): boolean;\n  selectAll(): void;\n};\n\nconst DIRECTION_CODES: string[] = [\n  codesLetter.TAB,\n  codesLetter.ARROW_UP,\n  codesLetter.ARROW_DOWN,\n  codesLetter.ARROW_LEFT,\n  codesLetter.ARROW_RIGHT,\n];\nexport class KeyboardService {\n  constructor(private sv: Config) {}\n\n  async keyDown(\n    e: KeyboardEvent,\n    canRange: boolean,\n    isEditMode: boolean,\n    { range, focus }: Nullable<Pick<EventData, 'range' | 'focus'>>,\n  ) {\n    // IF EDIT MODE\n    if (isEditMode) {\n      switch (e.code) {\n        case codesLetter.ESCAPE:\n          this.sv.cancel();\n          break;\n        case codesLetter.TAB:\n          this.keyChangeSelection(e, canRange);\n          break;\n      }\n      return;\n    }\n\n    // IF NOT EDIT MODE\n\n    // pressed clear key\n    if (range && isClear(e.code)) {\n      this.sv.clearCell();\n      return;\n    }\n\n    // below works with focus only\n    if (!focus) {\n      return;\n    }\n\n    // tab key means same as arrow right\n    if (isTab(e.code)) {\n      this.keyChangeSelection(e, canRange);\n      return;\n    }\n\n    // pressed enter\n    if (isEnterKeyValue(e.key)) {\n      this.sv.change();\n      return;\n    }\n\n    // copy operation\n    if (isCopy(e)) {\n      return;\n    }\n\n    // cut operation\n    if (isCut(e)) {\n      return;\n    }\n\n    // paste operation\n    if (isPaste(e)) {\n      this.sv.internalPaste();\n      return;\n    }\n\n    // select all\n    if (isAll(e)) {\n      if (canRange) {\n        this.selectAll(e);\n      }\n      return;\n    }\n\n    // pressed letter key\n    if (e.key.length === 1) {\n      this.sv.change(e.key);\n      return;\n    }\n\n    // pressed arrow, change selection position\n    if (await this.keyChangeSelection(e, canRange)) {\n      return;\n    }\n  }\n\n  private selectAll(e: KeyboardEvent) {\n    const range = this.sv.selectionStore.get('range');\n    const focus = this.sv.selectionStore.get('focus');\n    // if no range or focus - do nothing\n    if (!range || !focus) {\n      return;\n    }\n    e.preventDefault();\n    this.sv.selectAll();\n  }\n\n  async keyChangeSelection(e: KeyboardEvent, canRange: boolean) {\n    const data = this.changeDirectionKey(e, canRange);\n    if (!data) {\n      return false;\n    }\n\n    // this interval needed for several cases\n    // grid could be resized before next click\n    // at this case to avoid screen jump we use this interval\n    await timeout(RESIZE_INTERVAL + 30);\n\n    const range = this.sv.selectionStore.get('range');\n    const focus = this.sv.selectionStore.get('focus');\n    return this.keyPositionChange(data.changes, range, focus, data.isMulti);\n  }\n\n  keyPositionChange(\n    changes: Partial<Cell>,\n    range: RangeArea | null,\n    focus: Cell | null,\n    isMulti = false,\n  ) {\n    if (!range || !focus) {\n      return false;\n    }\n    const data = getCoordinate(range, focus, changes, isMulti);\n    if (!data) {\n      return false;\n    }\n    const eData: EventData = this.sv.getData();\n    if (isMulti) {\n      if (isAfterLast(data.end, eData.lastCell) || isBeforeFirst(data.start)) {\n        return false;\n      }\n      const range = getRange(data.start, data.end);\n      return this.sv.range(range);\n    }\n    return this.sv.focus(\n      data.start,\n      changes,\n      isAfterLast(data.start, eData.lastCell)\n        ? 1\n        : isBeforeFirst(data.start)\n          ? -1\n          : 0,\n    );\n  }\n\n  /** Monitor key direction changes */\n  changeDirectionKey(\n    e: KeyboardEvent,\n    canRange: boolean,\n  ): { changes: Partial<Cell>; isMulti?: boolean } | void {\n    const isMulti = canRange && e.shiftKey;\n    if (DIRECTION_CODES.includes(e.code)) {\n      e.preventDefault();\n    }\n\n    if (e.shiftKey) {\n      switch (e.code) {\n        case codesLetter.TAB:\n          return { changes: { x: -1 }, isMulti: false };\n      }\n    }\n\n    switch (e.code) {\n      case codesLetter.ARROW_UP:\n        return { changes: { y: -1 }, isMulti };\n      case codesLetter.ARROW_DOWN:\n        return { changes: { y: 1 }, isMulti };\n      case codesLetter.ARROW_LEFT:\n        return { changes: { x: -1 }, isMulti };\n      case codesLetter.TAB:\n      case codesLetter.ARROW_RIGHT:\n        return { changes: { x: 1 }, isMulti };\n    }\n  }\n}\n", "import debounce from 'lodash/debounce';\nimport { DebouncedFunc } from 'lodash';\n\nimport { h } from '@stencil/core';\nimport { CELL_HANDLER_CLASS, MOBILE_CLASS } from '../../utils/consts';\nimport {\n  collectModelsOfRange,\n  EventData,\n  getCell,\n  getCurrentCell,\n  isAfterLast,\n} from './selection.utils';\nimport { DSourceState, getRange } from '@store';\nimport ColumnService from '../data/column.service';\nimport { getPropertyFromEvent } from '../../utils/events';\nimport {\n  DataLookup,\n  DataType,\n  DimensionSettingsState,\n  TempRange,\n  ChangedRange,\n  BeforeRangeSaveDataDetails,\n  RangeArea,\n  Cell,\n  DimensionRows, Nullable,\n} from '@type';\nimport { Observable } from '../../utils';\n\ntype Config = {\n  dimensionRow: Observable<DimensionSettingsState>;\n  dimensionCol: Observable<DimensionSettingsState>;\n  columnService: ColumnService;\n  dataStore: Observable<DSourceState<DataType, DimensionRows>>;\n\n  setTempRange(e: Nullable<TempRange> | null): Event | null;\n  selectionChanged(e: ChangedRange): Event;\n  rangeCopy(e: ChangedRange): Event;\n  rangeDataApply(e: BeforeRangeSaveDataDetails): CustomEvent;\n  setRange(e: RangeArea | null): boolean;\n  clearRangeDataApply(e: { range: RangeArea }): CustomEvent<{\n    range: RangeArea;\n  }>;\n\n  getData(): any;\n};\n\nconst enum AutoFillType {\n  selection = 'Selection',\n  autoFill = 'AutoFill',\n}\n\nexport class AutoFillService {\n  private autoFillType: AutoFillType | null = null;\n  private autoFillInitial: Cell | null = null;\n  private autoFillStart: Cell | null = null;\n  private autoFillLast: Cell | null = null;\n\n  private onMouseMoveAutofill: DebouncedFunc<\n    (e: MouseEvent | TouchEvent, data: EventData) => void\n  >;\n\n  constructor(private sv: Config) {}\n\n  /**\n   * Render autofill box\n   * @param range\n   * @param selectionFocus\n   */\n  renderAutofill(range: RangeArea | null, selectionFocus: Cell, isMobile = false) {\n    let handlerStyle;\n    if (range) {\n      handlerStyle = getCell(\n        range,\n        this.sv.dimensionRow.state,\n        this.sv.dimensionCol.state,\n      );\n    } else {\n      handlerStyle = getCell(\n        {\n          ...selectionFocus,\n          x1: selectionFocus.x,\n          y1: selectionFocus.y,\n        },\n        this.sv.dimensionRow.state,\n        this.sv.dimensionCol.state,\n      );\n    }\n    return (\n      <div\n        class={{\n          [CELL_HANDLER_CLASS]: true,\n          [MOBILE_CLASS]: isMobile,\n        }}\n        style={{\n          left: `${handlerStyle.right}px`,\n          top: `${handlerStyle.bottom}px`,\n        }}\n        onMouseDown={(e: MouseEvent) => this.autoFillHandler(e)}\n        onTouchStart={(e: TouchEvent) => this.autoFillHandler(e)}\n      />\n    );\n  }\n\n  private autoFillHandler(\n    e: MouseEvent | TouchEvent,\n    type = AutoFillType.autoFill,\n  ) {\n    let target: Element | null = null;\n    if (e.target instanceof Element) {\n      target = e.target;\n    }\n    if (!target) {\n      return;\n    }\n    this.selectionStart(target, this.sv.getData(), type);\n    e.preventDefault();\n  }\n\n  get isAutoFill() {\n    return !!this.autoFillType;\n  }\n\n  /**\n   * Process mouse move events\n   */\n  selectionMouseMove(e: MouseEvent | TouchEvent) {\n    // initiate mouse move debounce if not present\n    if (!this.onMouseMoveAutofill) {\n      this.onMouseMoveAutofill = debounce(\n        (e: MouseEvent | TouchEvent, data: EventData) =>\n          this.doAutofillMouseMove(e, data),\n        5,\n      );\n    }\n    if (this.isAutoFill) {\n      this.onMouseMoveAutofill(e, this.sv.getData());\n    }\n  }\n\n  private getFocus(focus: Cell | null, range: RangeArea | null) {\n    // there was an issue that it was taking last cell from range but focus was out\n    if (!focus && range) {\n      focus = { x: range.x, y: range.y };\n    }\n    return focus || null;\n  }\n\n  /**\n   * Autofill logic:\n   * on mouse move apply based on previous direction (if present)\n   */\n  private doAutofillMouseMove(event: MouseEvent | TouchEvent, data: EventData) {\n    // if no initial - not started\n    if (!this.autoFillInitial) {\n      return;\n    }\n    const x = getPropertyFromEvent(event, 'clientX', MOBILE_CLASS);\n    const y = getPropertyFromEvent(event, 'clientY', MOBILE_CLASS);\n    // skip touch\n    if (x === null || y === null) {\n      return;\n    }\n    const current = getCurrentCell({ x, y }, data);\n\n    // first time or direction equal to start(same as first time)\n    if (!this.autoFillLast) {\n      if (!this.autoFillLast) {\n        this.autoFillLast = this.autoFillStart;\n      }\n    }\n\n    // check if not the latest, if latest - do nothing\n    if (isAfterLast(current, data.lastCell)) {\n      return;\n    }\n    this.autoFillLast = current;\n\n    const isSame =\n      current.x === this.autoFillInitial.x &&\n      current.y === this.autoFillInitial.y;\n\n    // if same as initial - clear\n    if (isSame) {\n      this.sv.setTempRange(null);\n    } else {\n      const area = getRange(this.autoFillInitial, this.autoFillLast);\n      this.sv.setTempRange({\n        area,\n        type: this.autoFillType,\n      });\n    }\n  }\n\n  /**\n   * Range selection started\n   * Mode @param type:\n   * Can be triggered from MouseDown selection on element\n   * Or can be triggered on corner square drag\n   */\n  selectionStart(\n    target: Element,\n    data: EventData,\n    type = AutoFillType.selection,\n  ) {\n    /** Get cell by autofill element */\n    const { top, left } = target.getBoundingClientRect();\n    this.autoFillInitial = this.getFocus(data.focus, data.range);\n    this.autoFillType = type;\n    this.autoFillStart = getCurrentCell({ x: left, y: top }, data);\n  }\n\n  /**\n   * Clear current range selection on mouse up and mouse leave events\n   */\n  clearAutoFillSelection(focus: Cell | null, oldRange: RangeArea | null) {\n    // If autofill was active, apply autofill values\n    if (this.autoFillInitial) {\n      // Fetch latest focus\n      this.autoFillInitial = this.getFocus(focus, oldRange);\n\n      // Apply range data if autofill mode is active\n      if (this.autoFillType === AutoFillType.autoFill) {\n        const range = getRange(this.autoFillInitial, this.autoFillLast);\n\n        // If range is present, apply data\n        if (range) {\n          const {\n            defaultPrevented: stopApply,\n            detail: { range: newRange },\n          } = this.sv.clearRangeDataApply({\n            range,\n          });\n\n          // If data apply was not prevented, apply new range\n          if (!stopApply && oldRange) {\n            this.applyRangeWithData(newRange, oldRange);\n          } else {\n            // If data apply was prevented, clear temporary range\n            this.sv.setTempRange(null);\n          }\n        }\n      } else {\n        // If not autofill mode, apply range only\n        this.applyRangeOnly(this.autoFillInitial, this.autoFillLast);\n      }\n    }\n\n    // Reset autofill state\n    this.resetAutoFillState();\n  }\n\n  /**\n   * Reset autofill state\n   */\n  private resetAutoFillState() {\n    this.autoFillType = null;\n    this.autoFillInitial = null;\n    this.autoFillLast = null;\n    this.autoFillStart = null;\n  }\n\n  /**\n   * Trigger range apply events and handle responses\n   */\n  onRangeApply(newData: DataLookup, newRange: RangeArea | null, oldRange: RangeArea | null) {\n    this.sv.rangeDataApply({\n      data: newData,\n      models: collectModelsOfRange(newData, this.sv.dataStore),\n      type: this.sv.dataStore.get('type'),\n      oldRange,\n      newRange\n    });\n\n    this.sv.setRange(newRange);\n  }\n\n  /** Apply range and copy data during range application */\n  private applyRangeWithData(newRange: RangeArea, rangeToCopy: RangeArea) {\n    const rangeData: ChangedRange = {\n      type: this.sv.dataStore.get('type'),\n      colType: this.sv.columnService.type,\n      newData: {},\n      mapping: {},\n      newRange,\n      oldRange: rangeToCopy,\n    };\n    const { mapping, changed } = this.sv.columnService.getRangeData(\n      rangeData,\n      this.sv.columnService.columns,\n    );\n    rangeData.newData = changed;\n    rangeData.mapping = mapping;\n    let e = this.sv.selectionChanged(rangeData);\n\n    // if default prevented - clear range\n    if (e.defaultPrevented) {\n      this.sv.setTempRange(null);\n      return;\n    }\n\n    e = this.sv.rangeCopy(rangeData);\n    if (e.defaultPrevented) {\n      this.sv.setRange(newRange);\n      return;\n    }\n    this.onRangeApply(rangeData.newData, newRange, rangeToCopy);\n  }\n\n  /**\n   * Update range selection only,\n   * no data change (mouse selection)\n   */\n  private applyRangeOnly(start?: Cell | null, end?: Cell | null) {\n    // no changes to apply\n    if (!start || !end) {\n      return;\n    }\n\n    const newRange = getRange(start, end);\n    this.sv.setRange(newRange);\n  }\n}\n", "@mixin autofill-handle($handler-size: 14px, $icon-size: 10px) {\n  .autofill-handle {\n    position: absolute;\n    width: $handler-size;\n    height: $handler-size;\n    margin-left: -$handler-size + 1;\n    margin-top: -$handler-size + 1;\n    z-index: 10;\n    cursor: crosshair;\n\n    &::before {\n      content: '';\n      position: absolute;\n      right: 0;\n      bottom: 0;\n      width: $icon-size;\n      height: $icon-size;\n      background: $selection-border;\n      border: 1px solid white;\n      box-sizing: border-box;\n    }\n  }\n}\n\nrevogr-overlay-selection {\n  display: block;\n  position: relative;\n  width: 100%;\n\n\n  @include autofill-handle;\n\n  &.mobile {\n    @include autofill-handle(30px, 12px);\n  }\n\n  .selection-border-range {\n    position: absolute;\n    pointer-events: none;\n    z-index: 9;\n\n    .range-handlers {\n      height: 100%;\n      background-color: transparent;\n      width: calc(50% + (50% / 2));\n      max-width: 50px;\n      min-width: 20px;\n      left: 50%;\n      transform: translateX(-50%);\n      position: absolute;\n\n      $btn-size: 20px;\n      $handler-w: 15px;\n      $handler-h: 5px;\n      \n      > span {\n        pointer-events: auto;\n        height: $btn-size;\n        width: $btn-size;\n        position: absolute;\n        left: 50%;\n        transform: translateX(-50%);\n\n\n        &:before, &:after {\n          position: absolute;\n          border-radius: 5px;\n          width: $handler-w;\n          height: $handler-h;\n          left: 50%;\n          transform: translateX(-50%);\n          background-color: rgba(black, 20%);\n        }\n        &:first-child {\n          top: -($handler-h + 2px);\n          &:before{\n            content: '';\n            top: 0;\n          }\n        }\n        &:last-child {\n          bottom: -($handler-h + 2px);\n          &:after{\n            content: '';\n            bottom: 0;\n          }\n        }\n      }\n    }\n    @include selection();\n  }\n\n  revogr-edit {\n    z-index: 10;\n  }\n}\n", "import {\n  Component,\n  Event,\n  EventEmitter,\n  h,\n  Host,\n  Listen,\n  Prop,\n  type VNode,\n  Element,\n  Watch,\n} from '@stencil/core';\nimport ColumnService, { getCellEditor } from '../data/column.service';\nimport { codesLetter } from '../../utils/key.codes';\nimport { MOBILE_CLASS, SELECTION_BORDER_CLASS } from '../../utils/consts';\nimport { type DSourceState, getRange, isRangeSingleCell } from '@store';\nimport {\n  collectModelsOfRange,\n  EventData,\n  getCell,\n  getFocusCellBasedOnEvent,\n  styleByCellProps,\n} from './selection.utils';\nimport { isEditInput } from '../editors/edit.utils';\nimport { KeyboardService } from './keyboard.service';\nimport { AutoFillService } from './autofill.service';\nimport { verifyTouchTarget } from '../../utils/events';\nimport { getCellData, type Observable } from '../../utils';\n\nimport type {\n  SelectionStoreState,\n  DimensionSettingsState,\n  DataType,\n  DimensionRows,\n  ColumnRegular,\n  DimensionCols,\n  DragStartEvent,\n  Cell,\n  MultiDimensionType,\n  Nullable,\n  RangeClipboardCopyEventProps,\n  RangeClipboardPasteEvent,\n  FocusRenderEvent,\n  ApplyFocusEvent,\n  AllDimensionType,\n  DataFormat,\n  Editors,\n  BeforeSaveDataDetails,\n  BeforeEdit,\n  RangeArea,\n  TempRange,\n  ChangedRange,\n  BeforeRangeSaveDataDetails,\n  SaveDataDetails,\n  EditCellStore,\n} from '@type';\n\n/**\n * Component for overlaying the grid with the selection.\n */\n@Component({\n  tag: 'revogr-overlay-selection',\n  styleUrl: 'revogr-overlay-style.scss',\n})\nexport class OverlaySelection {\n  // #region Properties\n  /**\n   * Readonly mode.\n   */\n  @Prop() readonly: boolean;\n  /**\n   * Range selection allowed.\n   */\n  @Prop() range: boolean;\n  /**\n   * Enable revogr-order-editor component (read more in revogr-order-editor component).\n   * Allows D&D.\n   */\n  @Prop() canDrag: boolean;\n\n  /**\n   * Enable revogr-clipboard component (read more in revogr-clipboard component).\n   * Allows copy/paste.\n   */\n  @Prop() useClipboard: boolean;\n\n  /** Stores */\n  /** Selection, range, focus. */\n  @Prop() selectionStore!: Observable<SelectionStoreState>;\n  /** Dimension settings Y. */\n  @Prop() dimensionRow: Observable<DimensionSettingsState>;\n  /** Dimension settings X. */\n  @Prop() dimensionCol!: Observable<DimensionSettingsState>;\n\n  // --------------------------------------------------------------------------\n  //\n  //  Static stores, not expected to change during component lifetime\n  //\n  // --------------------------------------------------------------------------\n\n  /**\n   * Row data store.\n   */\n  @Prop() dataStore!: Observable<DSourceState<DataType, DimensionRows>>;\n\n  /**\n   * Column data store.\n   */\n  @Prop() colData!: Observable<DSourceState<ColumnRegular, DimensionCols>>;\n  /**\n   * Last real coordinates positions + 1.\n   */\n  @Prop() lastCell: Cell;\n  /**\n   * Custom editors register.\n   */\n  @Prop() editors: Editors;\n  /**\n   * If true applys changes when cell closes if not Escape.\n   */\n  @Prop() applyChangesOnClose = false;\n  /**\n   * Additional data to pass to renderer.\n   */\n  @Prop() additionalData: any;\n\n  /**\n   * Is mobile view mode.\n   */\n  @Prop() isMobileDevice: boolean;\n\n  // #endregion\n\n  // #region Events\n  /**\n   * Before clipboard copy happened. Validate data before copy.\n   * To prevent the default behavior of editing data and use your own implementation, call `e.preventDefault()`.\n   */\n  @Event({ eventName: 'beforecopyregion', cancelable: true })\n  beforeCopyRegion: EventEmitter;\n  /**\n   * Before region paste happened.\n   */\n  @Event({ eventName: 'beforepasteregion', cancelable: true })\n  beforeRegionPaste: EventEmitter;\n\n  /**\n   * Cell edit apply to the data source.\n   * Triggers datasource edit on the root level.\n   */\n  @Event({ eventName: 'celleditapply', cancelable: true })\n  cellEditApply: EventEmitter<BeforeSaveDataDetails>;\n\n  /**\n   * Before cell focus.\n   */\n  @Event({ eventName: 'beforecellfocusinit', cancelable: true })\n  beforeFocusCell: EventEmitter<BeforeSaveDataDetails>;\n\n  /**\n   * Fired when change of viewport happens.\n   * Usually when we switch between pinned regions.\n   */\n  @Event({ eventName: 'beforenextvpfocus', cancelable: true })\n  beforeNextViewportFocus: EventEmitter<Cell>;\n\n  /**\n   * Set edit cell.\n   */\n  @Event({ eventName: 'setedit' }) setEdit: EventEmitter<BeforeEdit>;\n\n  /**\n   * Before range applied.\n   * First step in triggerRangeEvent.\n   */\n  @Event({ eventName: 'beforeapplyrange' })\n  beforeApplyRange: EventEmitter<FocusRenderEvent>;\n  /**\n   * Before range selection applied.\n   * Second step in triggerRangeEvent.\n   */\n  @Event({ eventName: 'beforesetrange' }) beforeSetRange: EventEmitter;\n\n  /**\n   * Set range.\n   * Third step in triggerRangeEvent.\n   */\n  @Event({ eventName: 'setrange' }) setRange: EventEmitter<\n    RangeArea & { type: MultiDimensionType }\n  >;\n\n  /**\n   * Before editor render.\n   */\n  @Event({ eventName: 'beforeeditrender' })\n  beforeEditRender: EventEmitter<FocusRenderEvent>;\n\n\n  /** Select all cells from keyboard. */\n  @Event({ eventName: 'selectall' }) selectAll: EventEmitter;\n  /**\n   * Cancel edit. Used for editors support when editor close requested.\n   */\n  @Event({ eventName: 'canceledit' }) cancelEdit: EventEmitter;\n\n  /**\n   * Set temp range area during autofill.\n   */\n  @Event({ eventName: 'settemprange' })\n  setTempRange: EventEmitter<Nullable<TempRange> | null>;\n\n  /**\n   * Before set temp range area during autofill.\n   */\n  @Event({ eventName: 'beforesettemprange' })\n  beforeSetTempRange: EventEmitter<\n    { tempRange: Nullable<TempRange> | null } & EventData & AllDimensionType\n  >;\n\n  /**\n   * Before cell get focused.\n   * To prevent the default behavior of applying the edit data, you can call `e.preventDefault()`.\n   */\n  @Event({ eventName: 'applyfocus' })\n  applyFocus: EventEmitter<FocusRenderEvent>;\n\n  /**\n   * Cell get focused.\n   * To prevent the default behavior of applying the edit data, you can call `e.preventDefault()`.\n   */\n  @Event({ eventName: 'focuscell' }) focusCell: EventEmitter<ApplyFocusEvent & FocusRenderEvent>;\n  /** Range data apply. */\n  @Event({ eventName: 'beforerangedataapply' })\n  beforeRangeDataApply: EventEmitter<FocusRenderEvent>;\n  /** Autofill data in range. First step in applyRangeWithData */\n  @Event({ eventName: 'selectionchangeinit', cancelable: true })\n  selectionChange: EventEmitter<ChangedRange>;\n  /** Before range copy. */\n  @Event({ eventName: 'beforerangecopyapply', cancelable: true, bubbles: true })\n  beforeRangeCopyApply: EventEmitter<ChangedRange>;\n\n  /** Range data apply.\n   * Triggers datasource edit on the root level.\n   */\n  @Event({ eventName: 'rangeeditapply', cancelable: true })\n  rangeEditApply: EventEmitter<BeforeRangeSaveDataDetails>;\n  /** Range copy. */\n  @Event({ eventName: 'clipboardrangecopy', cancelable: true })\n  /**\n   * Range copy event.\n   * This event is triggered when data is ready to be copied to the clipboard.\n   * If you want to prevent the default behavior of copying data, you can call `e.preventDefault()`.\n   * If you want to modify the data that will be copied to the clipboard, modify the `data` property of the event object.\n   */\n  rangeClipboardCopy: EventEmitter<RangeClipboardCopyEventProps>;\n\n  /**\n   * Range paste event.\n   */\n  @Event({ eventName: 'clipboardrangepaste', cancelable: true })\n  rangeClipboardPaste: EventEmitter<RangeClipboardPasteEvent>;\n\n  /**\n   * Before key up event proxy, used to prevent key up trigger.\n   * If you have some custom behaviour event, use this event to check if it wasn't processed by internal logic.\n   * Call preventDefault().\n   */\n  @Event({ eventName: 'beforekeydown' })\n  beforeKeyDown: EventEmitter<{ original: KeyboardEvent } & EventData>;\n  /**\n   * Before key down event proxy, used to prevent key down trigger.\n   * If you have some custom behaviour event, use this event to check if it wasn't processed by internal logic.\n   * Call preventDefault().\n   */\n  @Event({ eventName: 'beforekeyup' }) beforeKeyUp: EventEmitter<\n    { original: KeyboardEvent } & EventData\n  >;\n  /**\n   * Runs before cell save.\n   * Can be used to override or cancel original save.\n   */\n  @Event({ eventName: 'beforecellsave', cancelable: true })\n  beforeCellSave: EventEmitter;\n\n  /**\n   * Runs when edit finished save started, first in chain event\n   */\n  @Event({ eventName: 'celledit' }) cellEditDone: EventEmitter<SaveDataDetails>;\n\n  // #endregion\n\n  // #region Private Properties\n  @Element() element: HTMLElement;\n  private clipboard?: HTMLRevogrClipboardElement;\n\n  protected columnService: ColumnService;\n  private keyboardService: KeyboardService | null = null;\n  private autoFillService: AutoFillService | null = null;\n  private orderEditor?: HTMLRevogrOrderEditorElement;\n  private revogrEdit?: HTMLRevogrEditElement;\n  private unsubscribeSelectionStore: { (): void }[] = [];\n  // #endregion\n\n  // #region Listeners\n  @Listen('touchmove', { target: 'document' })\n  @Listen('mousemove', { target: 'document' })\n  onMouseMove(e: MouseEvent | TouchEvent) {\n    if (this.selectionStore.get('focus')) {\n      this.autoFillService?.selectionMouseMove(e);\n    }\n  }\n\n  /**\n   * Action finished inside the document.\n   * Pointer left document, clear any active operation.\n   */\n  @Listen('touchend', { target: 'document' })\n  @Listen('mouseup', { target: 'document' })\n  @Listen('mouseleave', { target: 'document' })\n  onMouseUp() {\n    // Clear autofill selection\n    // when pointer left document,\n    // clear any active operation.\n    this.autoFillService?.clearAutoFillSelection(\n      this.selectionStore.get('focus'),\n      this.selectionStore.get('range'),\n    );\n  }\n\n  /**\n   * Row drag started.\n   * This event is fired when drag action started on cell.\n   */\n  @Listen('dragstartcell') onCellDrag(e: CustomEvent<DragStartEvent>) {\n    // Invoke drag start on order editor.\n    this.orderEditor?.dragStart(e.detail);\n  }\n\n  /**\n   * Get keyboard down from element.\n   * This event is fired when keyboard key is released.\n   */\n  @Listen('keyup', { target: 'document' }) onKeyUp(e: KeyboardEvent) {\n    // Emit before key up event.\n    this.beforeKeyUp.emit({ original: e, ...this.getData() });\n  }\n\n  /**\n   * Get keyboard down from element.\n   * This event is fired when keyboard key is pressed.\n   */\n  @Listen('keydown', { target: 'document' }) onKeyDown(e: KeyboardEvent) {\n    // Emit before key down event and check if default prevention is set.\n    const proxy = this.beforeKeyDown.emit({ original: e, ...this.getData() });\n    if (e.defaultPrevented || proxy.defaultPrevented) {\n      return;\n    }\n    // Invoke key down on keyboard service.\n    this.keyboardService?.keyDown(\n      e,\n      this.range,\n      !!this.selectionStore.get('edit'),\n      {\n        focus: this.selectionStore.get('focus'),\n        range: this.selectionStore.get('range'),\n      },\n    );\n  }\n  // #endregion\n\n  /**\n   * Selection & Keyboard\n   */\n  @Watch('selectionStore') selectionServiceSet(\n    selectionStore: Observable<SelectionStoreState>,\n  ) {\n    // clear subscriptions\n    this.unsubscribeSelectionStore.forEach(v => v());\n    this.unsubscribeSelectionStore.length = 0;\n    this.unsubscribeSelectionStore.push(\n      selectionStore.onChange('nextFocus', v => v && this.doFocus(v, v)),\n    );\n\n    this.keyboardService = new KeyboardService({\n      selectionStore,\n      range: r => !!r && this.triggerRangeEvent(r),\n      focus: (f, changes, focusNextViewport) => {\n        if (focusNextViewport) {\n          this.beforeNextViewportFocus.emit(f);\n          return false;\n        } else {\n          return this.doFocus(f, f, changes);\n        }\n      },\n      change: val => {\n        if (this.readonly) {\n          return;\n        }\n        this.doEdit(val);\n      },\n      cancel: async () => {\n        await this.revogrEdit?.cancelChanges();\n        this.closeEdit();\n      },\n      clearCell: () => !this.readonly && this.clearCell(),\n      internalPaste: () => !this.readonly && this.beforeRegionPaste.emit(),\n      getData: () => this.getData(),\n      selectAll: () => this.selectAll.emit(),\n    });\n    this.createAutoFillService();\n  }\n  /** Autofill */\n  @Watch('dimensionRow')\n  @Watch('dimensionCol')\n  createAutoFillService() {\n    this.autoFillService = new AutoFillService({\n      dimensionRow: this.dimensionRow,\n      dimensionCol: this.dimensionCol,\n      columnService: this.columnService,\n      dataStore: this.dataStore,\n\n      clearRangeDataApply: e =>\n        this.beforeRangeDataApply.emit({\n          ...e,\n          ...this.types,\n          rowDimension: { ...this.dimensionRow.state },\n          colDimension: { ...this.dimensionCol.state },\n        }),\n      setTempRange: e => {\n        const tempRangeEvent = this.beforeSetTempRange.emit({\n          tempRange: e,\n          ...this.getData(),\n          ...this.types,\n        });\n        if (tempRangeEvent.defaultPrevented) {\n          return null;\n        }\n        return this.setTempRange.emit(tempRangeEvent.detail.tempRange);\n      },\n      selectionChanged: e => this.selectionChange.emit(e),\n\n      rangeCopy: e => this.beforeRangeCopyApply.emit(e),\n      rangeDataApply: e => this.rangeEditApply.emit(e),\n\n      setRange: e => !!e && this.triggerRangeEvent(e),\n      getData: () => this.getData(),\n    });\n  }\n\n  /** Columns */\n  @Watch('dataStore')\n  @Watch('colData')\n  columnServiceSet() {\n    this.columnService?.destroy();\n    this.columnService = new ColumnService(this.dataStore, this.colData);\n    this.createAutoFillService();\n  }\n\n  connectedCallback() {\n    this.columnServiceSet();\n    this.selectionServiceSet(this.selectionStore);\n  }\n\n  disconnectedCallback() {\n    // clear subscriptions\n    this.unsubscribeSelectionStore.forEach(v => v());\n    this.unsubscribeSelectionStore.length = 0;\n    this.columnService?.destroy();\n  }\n\n  async componentWillRender() {\n    const editCell = this.selectionStore.get('edit');\n    if (!editCell) {\n      await this.revogrEdit?.beforeDisconnect?.();\n    }\n  }\n\n  private renderRange(range: RangeArea) {\n    const cell = getCell(\n      range,\n      this.dimensionRow.state,\n      this.dimensionCol.state,\n    );\n    const styles = styleByCellProps(cell);\n    return [\n      <div class={SELECTION_BORDER_CLASS} style={styles}>\n        {this.isMobileDevice && (\n          <div class=\"range-handlers\">\n            <span class={MOBILE_CLASS}></span>\n            <span class={MOBILE_CLASS}></span>\n          </div>\n        )}\n      </div>,\n    ];\n  }\n\n  private renderEditor() {\n    // Check if edit access\n    const editCell = this.selectionStore.get('edit');\n    // Readonly or Editor closed\n    if (this.readonly || !editCell) {\n      return null;\n    }\n    const enteredOrModelValue =\n      editCell.val ||\n        getCellData(\n          this.columnService.rowDataModel(editCell.y, editCell.x).value\n        );\n    const editable = {\n      ...editCell,\n      ...this.columnService.getSaveData(\n        editCell.y,\n        editCell.x,\n        enteredOrModelValue,\n      ),\n    };\n    const renderEvent = this.beforeEditRender.emit({\n      range: {\n        ...editCell,\n        x1: editCell.x,\n        y1: editCell.y,\n      },\n      ...this.types,\n      rowDimension: { ...this.dimensionRow.state },\n      colDimension: { ...this.dimensionCol.state },\n    });\n\n    // Render prevented\n    if (renderEvent.defaultPrevented) {\n      return null;\n    }\n\n    const cell = getCell(\n      renderEvent.detail.range,\n      renderEvent.detail.rowDimension,\n      renderEvent.detail.colDimension,\n    );\n    const styles = styleByCellProps(cell);\n    return (\n      <revogr-edit\n        style={styles}\n        ref={el => (this.revogrEdit = el)}\n        additionalData={this.additionalData}\n        editCell={editable}\n        saveOnClose={this.applyChangesOnClose}\n        onCelleditinit={(e) => {\n          this.cellEditDone.emit(e.detail);\n        }}\n        column={this.columnService.rowDataModel(editCell.y, editCell.x)}\n        editor={getCellEditor(\n          this.columnService.columns[editCell.x],\n          this.editors,\n        )}\n      />\n    );\n  }\n\n  private onEditCell(e: CustomEvent<SaveDataDetails>) {\n    if (e.defaultPrevented) {\n      return;\n    }\n    const saveEv = this.beforeCellSave.emit(e.detail);\n    if (!saveEv.defaultPrevented) {\n      this.cellEdit(saveEv.detail);\n    }\n\n    // if not clear navigate to next cell after edit\n    if (!saveEv.detail.preventFocus) {\n      this.focusNext();\n    }\n  }\n\n  render() {\n    const nodes: VNode[] = [];\n    const editCell = this.renderEditor();\n\n    // Editor\n    if (editCell) {\n      nodes.push(editCell);\n    } else {\n      const range = this.selectionStore.get('range');\n      const focus = this.selectionStore.get('focus');\n\n      // Clipboard\n      if ((range || focus) && this.useClipboard) {\n        nodes.push(\n          <revogr-clipboard\n            readonly={this.readonly}\n            onCopyregion={e => this.onCopy(e.detail)}\n            onClearregion={() => !this.readonly && this.clearCell()}\n            ref={e => (this.clipboard = e)}\n            onPasteregion={e => this.onPaste(e.detail)}\n          />,\n        );\n      }\n\n      // Range\n      if (range) {\n        nodes.push(...this.renderRange(range));\n      }\n      // Autofill\n      if (focus && !this.readonly && this.range) {\n        nodes.push(this.autoFillService?.renderAutofill(range, focus, this.isMobileDevice));\n      }\n\n      // Order\n      if (this.canDrag) {\n        nodes.push(\n          <revogr-order-editor\n            ref={e => (this.orderEditor = e)}\n            dataStore={this.dataStore}\n            dimensionRow={this.dimensionRow}\n            dimensionCol={this.dimensionCol}\n            parent={this.element}\n            rowType={this.types.rowType}\n            onRowdragstartinit={e => this.rowDragStart(e)}\n          />,\n        );\n      }\n    }\n    return (\n      <Host\n        class={{ mobile: this.isMobileDevice }}\n        onDblClick={(e: MouseEvent) => this.onElementDblClick(e)}\n        onMouseDown={(e: MouseEvent) => this.onElementMouseDown(e)}\n        onTouchStart={(e: TouchEvent) => this.onElementMouseDown(e, true)}\n        onCloseedit={(e: CustomEvent<boolean | undefined>) => this.closeEdit(e)}\n        // it's done to be able to throw events from different levels, not just from editor\n        onCelledit={(e: CustomEvent<SaveDataDetails>) => this.onEditCell(e)}\n      >\n        {nodes}\n        <slot name=\"data\" />\n      </Host>\n    );\n  }\n\n  /**\n   * Executes the focus operation on the specified range of cells.\n   */\n  private doFocus(focus: Cell, end: Cell, changes?: Partial<Cell>) {\n    // 1. Trigger beforeFocus event\n    const { defaultPrevented } = this.beforeFocusCell.emit(\n      this.columnService.getSaveData(focus.y, focus.x),\n    );\n    if (defaultPrevented) {\n      return false;\n    }\n    const evData: FocusRenderEvent = {\n      range: {\n        ...focus,\n        x1: end.x,\n        y1: end.y,\n      },\n      next: changes,\n      ...this.types,\n      rowDimension: { ...this.dimensionRow.state },\n      colDimension: { ...this.dimensionCol.state },\n    };\n\n    // 2. Trigger apply focus event\n    const applyEvent = this.applyFocus.emit(evData);\n    if (applyEvent.defaultPrevented) {\n      return false;\n    }\n    const { range } = applyEvent.detail;\n\n    // 3. Trigger focus event\n    return !this.focusCell.emit({\n      focus: {\n        x: range.x,\n        y: range.y,\n      },\n      end: {\n        x: range.x1,\n        y: range.y1,\n      },\n      ...applyEvent.detail,\n    }).defaultPrevented;\n  }\n\n  private triggerRangeEvent(range: RangeArea) {\n    const type = this.types.rowType;\n    // 1. Apply range\n    const applyEvent = this.beforeApplyRange.emit({\n      range: { ...range },\n      ...this.types,\n      rowDimension: { ...this.dimensionRow.state },\n      colDimension: { ...this.dimensionCol.state },\n    });\n    if (applyEvent.defaultPrevented) {\n      return false;\n    }\n    const data = this.columnService.getRangeTransformedToProps(\n      applyEvent.detail.range,\n      this.dataStore,\n    );\n    // 2. Before set range\n    let e = this.beforeSetRange.emit(data);\n    if (e.defaultPrevented) {\n      return false;\n    }\n    // 3. Set range\n    e = this.setRange.emit({ ...applyEvent.detail.range, type });\n    if (e.defaultPrevented) {\n      return false;\n    }\n    return !e.defaultPrevented;\n  }\n\n  /**\n   * Open Editor on DblClick\n   */\n  private onElementDblClick(e: MouseEvent) {\n    // DblClick prevented outside - Editor will not open\n    if (e.defaultPrevented) {\n      return;\n    }\n\n    // Get data from the component\n    const data = this.getData();\n    const focusCell = getFocusCellBasedOnEvent(e, data);\n    if (!focusCell) {\n      return;\n    }\n    this.doEdit();\n  }\n\n  /**\n   * Handle mouse down event on Host element\n   */\n  private onElementMouseDown(e: MouseEvent | TouchEvent, touch = false) {\n    // Get the target element from the event object\n    const targetElement = e.target as HTMLElement | undefined;\n    // Ignore focus if clicked input\n    if (isEditInput(targetElement) || e.defaultPrevented) {\n      return;\n    }\n\n    // Get data from the component\n    const data = this.getData();\n    const focusCell = getFocusCellBasedOnEvent(e, data);\n    if (!focusCell) {\n      return;\n    }\n\n    // Set focus on the current cell\n    this.focus(focusCell, this.range && e.shiftKey);\n\n    // Initiate autofill selection\n    if (this.range) {\n      targetElement &&\n        this.autoFillService?.selectionStart(targetElement, this.getData());\n\n      // Prevent default behavior for mouse events,\n      // but only if target element is not a mobile input\n      if (!touch) {\n        e.preventDefault();\n      } else if (\n        verifyTouchTarget((e as TouchEvent).touches[0], MOBILE_CLASS)\n      ) {\n        // Prevent default behavior for touch events\n        // if target element is a mobile input\n        e.preventDefault();\n      }\n    }\n  }\n\n  /**\n   * Start cell editing\n   */\n  protected doEdit(val = '') {\n    if (this.canEdit()) {\n      const focus = this.selectionStore.get('focus');\n      if (!focus) {\n        return;\n      }\n      const data = this.columnService.getSaveData(focus.y, focus.x);\n      this.setEdit?.emit({\n        ...data,\n        val,\n      });\n    }\n  }\n\n  /**\n   * Close editor event triggered\n   * @param details - if it requires focus next\n   */\n  private async closeEdit(e?: CustomEvent<boolean | undefined>) {\n    this.cancelEdit.emit();\n    if (e?.detail) {\n      await this.focusNext();\n    }\n  }\n\n  /**\n   * Edit finished.\n   * Close Editor and save.\n   */\n  protected cellEdit(e: SaveDataDetails) {\n    const dataToSave = this.columnService.getSaveData(e.rgRow, e.rgCol, e.val);\n    this.cellEditApply.emit(dataToSave);\n  }\n\n  private getRegion() {\n    const focus = this.selectionStore.get('focus');\n    let range = this.selectionStore.get('range');\n    if (!range) {\n      range = getRange(focus, focus);\n    }\n    return range;\n  }\n  private onCopy(e: DataTransfer) {\n    const range = this.getRegion();\n    const canCopyEvent = this.beforeCopyRegion.emit(range);\n    if (canCopyEvent.defaultPrevented) {\n      return false;\n    }\n    let rangeData: DataFormat[][] | undefined;\n\n    if (range) {\n      const { data, mapping } = this.columnService.copyRangeArray(\n        range,\n        this.dataStore,\n      );\n      const event = this.rangeClipboardCopy.emit({\n        range,\n        data,\n        mapping,\n        ...this.types,\n      });\n      if (!event.defaultPrevented) {\n        rangeData = event.detail.data;\n      }\n    }\n\n    this.clipboard?.doCopy(e, rangeData);\n    return true;\n  }\n\n  private onPaste(data: string[][]) {\n    const focus = this.selectionStore.get('focus');\n    const isEditing = this.selectionStore.get('edit') !== null;\n    if (!focus || isEditing) {\n      return;\n    }\n    let { changed, range } = this.columnService.getTransformedDataToApply(\n      focus,\n      data,\n    );\n    const { defaultPrevented: canPaste } = this.rangeClipboardPaste.emit({\n      data: changed,\n      models: collectModelsOfRange(changed, this.dataStore),\n      range,\n      ...this.types,\n    });\n\n    if (canPaste) {\n      return;\n    }\n    this.autoFillService?.onRangeApply(changed, range, range);\n  }\n\n  private async focusNext() {\n    const canFocus = await this.keyboardService?.keyChangeSelection(\n      new KeyboardEvent('keydown', {\n        code: codesLetter.ARROW_DOWN,\n      }),\n      this.range,\n    );\n    if (!canFocus) {\n      this.closeEdit();\n    }\n  }\n\n  protected clearCell() {\n    const range = this.selectionStore.get('range');\n    if (range && !isRangeSingleCell(range)) {\n      const data = this.columnService.getRangeStaticData(range, '');\n      this.autoFillService?.onRangeApply(data, range, range);\n    } else if (this.canEdit()) {\n      const focused = this.selectionStore.get('focus');\n      if (!focused) {\n        return;\n      }\n      const cell = this.columnService.getSaveData(focused.y, focused.x);\n      this.cellEdit({\n        rgRow: focused.y,\n        rgCol: focused.x,\n        val: '',\n        type: cell.type,\n        prop: cell.prop,\n      });\n    }\n  }\n\n  private rowDragStart({ detail }: CustomEvent<{ cell: Cell; text: string }>) {\n    detail.text = getCellData(\n      this.columnService.rowDataModel(detail.cell.y, detail.cell.x).value\n    );\n  }\n\n  /**\n   * Verify if edit allowed.\n   */\n  protected canEdit() {\n    if (this.readonly) {\n      return false;\n    }\n    const focus = this.selectionStore.get('focus');\n    return focus && !this.columnService?.isReadOnly(focus.y, focus.x);\n  }\n\n  get edited(): EditCellStore | null {\n    return this.selectionStore.get('edit');\n  }\n\n  /**\n   * Sets the focus on a cell and optionally edits a range.\n   */\n  focus(cell?: Cell, isRangeEdit = false) {\n    if (!cell) return false;\n\n    const end = cell;\n    const start = this.selectionStore.get('focus');\n\n    if (isRangeEdit && start) {\n      const range = getRange(start, end);\n      if (range) {\n        return this.triggerRangeEvent(range);\n      }\n    }\n\n    return this.doFocus(cell, end);\n  }\n\n  get types(): AllDimensionType {\n    return {\n      rowType: this.dataStore.get('type'),\n      colType: this.columnService.type,\n    };\n  }\n\n  /**\n   * Collect data\n   */\n  protected getData(): EventData {\n    return {\n      el: this.element,\n      rows: this.dimensionRow.state,\n      cols: this.dimensionCol.state,\n      lastCell: this.lastCell,\n      focus: this.selectionStore.get('focus'),\n      range: this.selectionStore.get('range'),\n      edit: this.selectionStore.get('edit'),\n    };\n  }\n}\n"], "version": 3}