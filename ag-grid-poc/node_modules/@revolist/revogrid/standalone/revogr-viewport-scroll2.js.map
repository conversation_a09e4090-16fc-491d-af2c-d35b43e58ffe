{"file": "revogr-viewport-scroll2.js", "mappings": ";;;;;;;AAAA;;AAEG;AAgBH;;AAEG;AACU,MAAA,WAAW,GAAG,SAAS;AACvB,MAAA,WAAW,GAAG,SAAS;AACvB,MAAA,YAAY,GAAG,UAAU;AACzB,MAAA,SAAS,GAAG,OAAO;AAEhC;;;;AAIG;AACa,SAAA,WAAW,CACzB,IAAoB,EACpB,OAA2B,EAAA;;AAG3B,IAAA,MAAM,sBAAsB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC;;AAGlF,IAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC;;IAG1E,OAAO;AACL,QAAA,CAAC,EAAE,sBAAsB;AACzB,QAAA,CAAC,EAAE,mBAAmB;KACvB;AACH;AAoBM,SAAU,qBAAqB,CACnC,IAAoB,EACpB,IAAmB,EACnB,IAAc,EACd,KAAe,EAAA;IAEf,OAAO;QACL,OAAO,EAAE,IAAI,CAAC,QAAQ;QACtB,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK;QAC/C,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,KAAK;;AAEvC,QAAA,QAAQ,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;QACjC,IAAI;QACJ,IAAI;QACJ,OAAO,EAAE,CAAC,KAAK;QACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;QACvB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,KAAK;QACrC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK;QACjD,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK;AACzC,QAAA,KAAK,EAAE;AACL,cAAE,EAAE,MAAM,EAAE,CAAG,EAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI;AAC9D,cAAE,SAAS;KACd;AACH;;ACpFc,MAAO,iBAAiB,CAAA;AAqBpC,IAAA,WAAA,CACE,EAAe,EACE,MAGR,EACT,QAAqC,EAAA;QAJpB,IAAM,CAAA,MAAA,GAAN,MAAM;QAtBjB,IAAc,CAAA,cAAA,GAA0B,IAAI;AAC5C,QAAA,IAAA,CAAA,YAAY,GAAgB;AAClC,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,MAAM,EAAE,CAAC;SACV;AACQ,QAAA,IAAA,CAAA,KAAK,GAAG,QAAQ,CACvB,CAAC,CAAoC,KAAI;;AACvC,YAAA,MAAM,KAAK,GAAG;gBACZ,KAAK,EAAE,CAAC,CAAC,KAAK;gBACd,MAAM,EAAE,CAAC,CAAC,MAAM;aACjB;YACD,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC;AACvC,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK;SAC1B,EACD,EAAE,EACF;AACE,YAAA,OAAO,EAAE,KAAK;AACd,YAAA,QAAQ,EAAE,IAAI;AACf,SAAA,CACF;QASC,MAAM,MAAM,GAAkB,EAAE;AAChC,QAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;YAC3B,IAAI,OAAO,EAAE;AACX,gBAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;;AAExB,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC;;AAGvB,IAAA,IAAI,CAAC,EAAe,EAAE,MAAA,GAAwB,EAAE,EAAA;AAC9C,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,CAAC,CAAC,KAAI;AAC9D,YAAA,IAAI,CAAC,CAAC,MAAM,EAAE;gBACZ,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;oBACtB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;;qBACvB;oBACL,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC;;;AAG5C,SAAC,CAAC;AACF,QAAA,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;AACpB,QAAA,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACvB,YAAA,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;AACzB,SAAC,CAAC;;IAGJ,OAAO,GAAA;;AACL,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,UAAU,EAAE;AACjC,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;AAE7B;;AC/DD,MAAM,4BAA4B,GAAG,4lOAA4lO;;MC4CpnO,oBAAoB,iBAAAA,kBAAA,CAAA,MAAA,oBAAA,SAAA,WAAA,CAAA;AAJjC,IAAA,WAAA,GAAA;;;;;;;AAUE;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAG,CAAC;AACxB;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAG,CAAC;AAia1B;IAnXW,MAAM,SAAS,CAAC,CAAsB,EAAA;;QAC9C,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC;QACrD,CAAA,EAAA,GAAA,IAAI,CAAC,kBAAkB,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,SAAS,CAAC,CAAC,CAAC;;AAGvC;;;AAGG;AACO,IAAA,MAAM,YAAY,CAAC,CAAsB,EAAE,MAAM,GAAG,KAAK,EAAA;;QACjE,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE;AACvC,gBAAA,QAAQ,CAAC,CAAC,SAAS;;AAEjB,oBAAA,KAAK,OAAO;AACV,wBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,GAAG,CAAc,WAAA,EAAA,GAAE,GAAG,CAAC,CAAC,UAAU,KAAK;wBAC1E;;;YAGN;;AAEF,QAAA,IAAI,CAAC,CAAC,KAAK,EAAE;AACX,YAAA,QAAQ,CAAC,CAAC,SAAS;AACjB,gBAAA,KAAK,OAAO;AACV,oBAAA,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK;oBACzD;AACF,gBAAA,KAAK,OAAO;oBACV,CAAC,CAAC,UAAU,GAAG,CAAC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,SAAS,mCAAI,CAAC,IAAI,CAAC,CAAC,KAAK;oBAC9D;;AAEJ,YAAA,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;AAEnB,QAAA,OAAO,CAAC;;AAGV;;AAEG;AAC4B,IAAA,kBAAkB,CAAC,EAChD,MAAM,EAAE,CAAC,GACqB,EAAA;AAC9B,QAAA,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;;AAE5B;;AAEG;AAC8B,IAAA,oBAAoB,CAAC,EACpD,MAAM,EAAE,CAAC,GACqB,EAAA;AAC9B,QAAA,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;;AAE9B;;AAEG;IAC0B,WAAW,CAAC,EACvC,MAAM,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,GACO,EAAA;QACnC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC;;IAG5C,iBAAiB,GAAA;AACf;;AAEG;;AAEH,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CACtD,IAAI,EACJ,OAAO,EACP,QAAQ,CACT;AACD,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAC1D,IAAI,EACJ,OAAO,EACP,QAAQ,CACT;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAC1C,cAAc,IAAI,QAAQ,CAAC,eAAe,GAAG,CAAC,GAAG,EAAE,CACpD;AACD;;AAEG;AACH,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAAC;;;AAG/C,YAAA,SAAS,EAAE,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;YAC3C,WAAW,EAAE,CAAC,IAAG;AACf,gBAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC;AACtC,gBAAA,QAAQ,CAAC,CAAC,SAAS;AACjB,oBAAA,KAAK,OAAO;;wBAEV,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU;wBAC/C;AACF,oBAAA,KAAK,OAAO;AACV,wBAAA,IAAI,IAAI,CAAC,cAAc,EAAE;;4BAEvB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,CAAC,CAAC,UAAU;;4BAE5C,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE;gCACvC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE;;;wBAG5C;;aAEL;AACF,SAAA,CAAC;;IAGJ,gBAAgB,GAAA;;AAEd,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,iBAAiB,CACxC,IAAI,CAAC,gBAAgB,EACrB,CAAC,KAAK,KAAI;;YACR,MAAM,GAAG,GAUL,EAAE;AAEN,YAAA,IAAI,gBAAgB,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC;YACxC,IAAI,gBAAgB,EAAE;gBACpB,gBAAgB;oBACd,CAAC,CAAA,EAAA,GAAA,MAAA,IAAI,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,YAAY,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,CAAC;yBAC9B,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAI,CAAC,CAAC;;YAEpC,GAAG,CAAC,KAAK,GAAG;AACV,gBAAA,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,IAAI,CAAC,aAAa;gBAC/B,MAAM,EAAE,MAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,SAAS,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAI,CAAC;AAC3C,gBAAA,QAAQ,EAAE,KAAK;aAChB;AACD,YAAA,MAAM,eAAe,GAAG,KAAK,CAAC,KAAK,IAAI,CAAC;YACxC,GAAG,CAAC,KAAK,GAAG;AACV,gBAAA,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,IAAI,CAAC,YAAY;AAC9B,gBAAA,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;AACxC,gBAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,KAAK,OAAO;aACnC;;AAED,YAAA,MAAM,UAAU,GAAoB,CAAC,OAAO,EAAE,OAAO,CAAC;AACtD,YAAA,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;AAClC,gBAAA,MAAM,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC;AAC3B,gBAAA,IAAI,CAAC,IAAI;oBAAE;AACX,gBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,SAAS,EAAE,IAAI,CAAC,SAAS;AAC1B,iBAAA,CAAC;AACF,gBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjB;;gBAEF,CAAA,EAAA,GAAA,IAAI,CAAC,kBAAkB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,CAAC,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,mCAAI,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC;;AAElE,gBAAA,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC;;SAEnE,EACD,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAC3B;;AAGH;;;;;;;AAOG;AACH,IAAA,mBAAmB,CACjB,IAAmB,EACnB,IAAY,EACZ,gBAAwB,EAAA;;AAGxB,QAAA,MAAM,SAAS,GAAG,IAAI,GAAG,gBAAgB;AACzC,QAAA,IAAI,EAA2B;;QAE/B,QAAQ,IAAI;AACV,YAAA,KAAK,OAAO;AACV,gBAAA,EAAE,GAAG,IAAI,CAAC,gBAAgB;gBAC1B;AACF,YAAA,KAAK,OAAO;AACV,gBAAA,EAAE,GAAG,IAAI,CAAC,cAAc;gBACxB;;;QAGJ,IAAI,SAAS,EAAE;YACb,EAAE,KAAF,IAAA,IAAA,EAAE,KAAF,MAAA,GAAA,MAAA,GAAA,EAAE,CAAE,SAAS,CAAC,GAAG,CAAC,CAAU,OAAA,EAAA,IAAI,CAAE,CAAA,CAAC;;aAC9B;YACL,EAAE,KAAF,IAAA,IAAA,EAAE,KAAF,MAAA,GAAA,MAAA,GAAA,EAAE,CAAE,SAAS,CAAC,MAAM,CAAC,CAAU,OAAA,EAAA,IAAI,CAAE,CAAA,CAAC;;QAExC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;;IAG7C,oBAAoB,GAAA;;QAClB,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,OAAO,EAAE;;AAG/B,IAAA,MAAM,kBAAkB,GAAA;;AACtB,QAAA,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAC/B;YACE,WAAW,EAAE,IAAI,CAAC,aAAa;YAC/B,UAAU,EAAE,MAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAI,CAAC;AAClD,YAAA,WAAW,EAAE,CAAC;SACf,EACD,OAAO,CACR;AAED,QAAA,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAC/B;YACE,WAAW,EAAE,IAAI,CAAC,YAAY;AAC9B,YAAA,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW;AAC7C,YAAA,WAAW,EAAE,CAAC;SACf,EACD,OAAO,CACR;QACD,IAAI,CAAC,mBAAmB,CACtB,OAAO,EACP,CAAA,EAAA,GAAA,MAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,YAAY,mCAAI,CAAC,EACtC,IAAI,CAAC,aAAa,CACnB;AACD,QAAA,IAAI,CAAC,mBAAmB,CACtB,OAAO,EACP,IAAI,CAAC,gBAAgB,CAAC,WAAW,EACjC,IAAI,CAAC,YAAY,CAClB;;IAGH,MAAM,GAAA;QACJ,QACE,CAAC,CAAA,IAAI,EACH,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,oBAAoB,EAClC,QAAQ,EAAE,CAAC,CAAU,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,EAAA,EAEtD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAC,qBAAqB,EAC3B,KAAK,EAAE,EAAE,KAAK,EAAE,CAAG,EAAA,IAAI,CAAC,YAAY,CAAI,EAAA,CAAA,EAAE,EAAA,EAE1C,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,gBAAgB,EAAC,GAAG,EAAE,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAA,EACrD,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAE,WAAW,EAAA,CAAI,CACvB,EACN,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAC,gBAAgB,EACtB,GAAG,EAAE,EAAE,KAAK,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,EACrC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAChC,QAAQ,EAAE,CAAC,CAAa,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,EAAA,EAEzD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAC,iBAAiB,EACvB,KAAK,EAAE,EAAE,MAAM,EAAE,CAAA,EAAG,IAAI,CAAC,aAAa,CAAA,EAAA,CAAI,EAAE,EAAA,EAE5C,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAE,YAAY,EAAA,CAAI,CACxB,CACF,EACN,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,gBAAgB,EAAC,GAAG,EAAE,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAA,EACrD,CAAM,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAE,WAAW,EAAA,CAAI,CACvB,CACF,CACD;;AAGX;;;AAGG;AACO,IAAA,MAAM,WAAW,CAAC,IAAmB,EAAE,CAAU,EAAA;QACzD,IAAI,EAAE,CAAC,CAAC,MAAM,YAAY,WAAW,CAAC,EAAE;YACtC;;QAEF,IAAI,MAAM,GAAG,CAAC;QACd,QAAQ,IAAI;AACV,YAAA,KAAK,OAAO;AACV,gBAAA,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU;gBAC5B;AACF,YAAA,KAAK,OAAO;AACV,gBAAA,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS;gBAC3B;;;AAIJ,QAAA,IAAI,MAAM,GAAG,CAAC,EAAE;AACd,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC;YAC/D;;AAEF,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC;;AAGlC;;AAEG;AACK,IAAA,aAAa,CACnB,IAAmB,EACnB,UAAkB,EAClB,OAAO,GAAG,KAAK,EAAA;QAEf,MAAM,gBAAgB,GAAG,MAAK;;AAC5B,YAAA,CAAA,EAAA,GAAA,IAAI,CAAC,kBAAkB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,CAC7B,UAAU,EACV,IAAI,EACJ,SAAS,EACT,SAAS,EACT,OAAO,CACR;AACH,SAAC;;QAED,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE;AACnD,YAAA,gBAAgB,EAAE;;aACb;AACL,YAAA,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,gBAAgB,EAAE,CAAC;;;AAI9F;;;;;AAKG;AACK,IAAA,oBAAoB,CAC1B,IAAmB,EACnB,KAAY,EACZ,CAAmB,EAAA;;QAEnB,MAAM,SAAS,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,SAAS,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,CAAC;QACrD,MAAM,YAAY,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,YAAY,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,CAAC;QAC3D,MAAM,YAAY,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,YAAY,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,CAAC;;AAG3D,QAAA,MAAM,QAAQ,GAAG,SAAS,GAAG,YAAY,IAAI,YAAY,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC;QACzE,MAAM,KAAK,GAAG,SAAS,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC;AAC7C,QAAA,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;YACvB,CAAA,EAAA,GAAA,CAAC,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAI;;QAEtB,MAAM,GAAG,GAAG,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC;AAChC,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,kBAAkB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC;;AAGhD;;;;;AAKG;AACK,IAAA,sBAAsB,CAC5B,IAAmB,EACnB,KAAY,EACZ,CAAmB,EAAA;;AAEnB,QAAA,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;YACb;;QAEF,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,gBAAgB;;AAGtE,QAAA,MAAM,OAAO,GAAG,UAAU,GAAG,WAAW,IAAI,WAAW,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC;;QAGvE,MAAM,MAAM,GAAG,UAAU,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC;AAC/C,QAAA,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,EAAE;YACvB,CAAA,EAAA,GAAA,CAAC,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAI;;QAEtB,MAAM,GAAG,GAAG,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC;AACjC,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,kBAAkB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/components/revoGrid/viewport.helpers.ts", "src/components/revoGrid/viewport.resize.service.ts", "src/components/scroll/revogr-viewport-scroll-style.scss?tag=revogr-viewport-scroll", "src/components/scroll/revogr-viewport-scroll.tsx"], "sourcesContent": ["/**\n * Collects data for pinned columns in the required @ViewportProps format.\n */\n\nimport type {\n  DimensionRows,\n  MultiDimensionType,\n  SlotType,\n  Cell,\n  ViewportColumn,\n  ColumnRegular,\n  DimensionCols,\n  ViewportState,\n  DimensionSettingsState,\n  DataType,\n} from '@type';\nimport type { Observable } from '../../utils';\nimport type { DSourceState } from '../../store';\n/**\n * Represents the slot names for the viewport slots.\n */\nexport const HEADER_SLOT = 'header'; // Slot name for the header slot\nexport const FOOTER_SLOT = 'footer'; // Slot name for the footer slot\nexport const CONTENT_SLOT = 'content'; // Slot name for the content slot\nexport const DATA_SLOT = 'data'; // Slot name for the data slot\n\n/**\n * Returns the last visible cell in the viewport for a given row type.\n * Coordinates are not zero-based and are relative to the viewport.\n * If needed to be zero-based they can be adjusted by subtracting 1.\n */\nexport function getLastCell(\n  data: ViewportColumn,\n  rowType: MultiDimensionType,\n): Cell {\n  // Get the last visible column count from the viewport column data.\n  const lastVisibleColumnCount = data.viewports[data.colType].store.get('realCount');\n\n  // Get the last visible row count for the given row type from the viewport column data.\n  const lastVisibleRowCount = data.viewports[rowType].store.get('realCount');\n\n  // Return the last visible cell with the last visible column count and row count.\n  return {\n    x: lastVisibleColumnCount,\n    y: lastVisibleRowCount,\n  };\n}\n\n/**\n * Represents the partition of viewport data.\n */\nexport type VPPartition = {\n  colData: Observable<DSourceState<ColumnRegular, DimensionCols>>;\n  viewportCol: Observable<ViewportState>;\n  viewportRow: Observable<ViewportState>;\n  lastCell: Cell;\n  slot: SlotType;\n  type: DimensionRows;\n  canDrag: boolean;\n  position: Cell;\n  dataStore: Observable<DSourceState<DataType, DimensionRows>>;\n  dimensionCol: Observable<DimensionSettingsState>;\n  dimensionRow: Observable<DimensionSettingsState>;\n  style?: { height: string };\n};\n\nexport function viewportDataPartition(\n  data: ViewportColumn,\n  type: DimensionRows,\n  slot: SlotType,\n  fixed?: boolean,\n): VPPartition {\n  return {\n    colData: data.colStore,\n    viewportCol: data.viewports[data.colType].store,\n    viewportRow: data.viewports[type].store,\n    // lastCell is the last real coordinate + 1\n    lastCell: getLastCell(data, type),\n    slot,\n    type,\n    canDrag: !fixed,\n    position: data.position,\n    dataStore: data.rowStores[type].store,\n    dimensionCol: data.dimensions[data.colType].store,\n    dimensionRow: data.dimensions[type].store,\n    style: fixed\n      ? { height: `${data.dimensions[type].store.get('realSize')}px` }\n      : undefined,\n  };\n}\n", "import throttle from 'lodash/throttle';\ntype ResizeEntry = {\n  width: number;\n  height: number;\n};\nexport default class GridResizeService {\n  private resizeObserver: ResizeObserver | null = null;\n  private previousSize: ResizeEntry = {\n    width: 0,\n    height: 0,\n  };\n  readonly apply = throttle(\n    (e: { width: number, height: number }) => {\n      const entry = {\n        width: e.width,\n        height: e.height,\n      };\n      this.resize?.(entry, this.previousSize);\n      this.previousSize = entry;\n    },\n    40,\n    {\n      leading: false,\n      trailing: true,\n    }\n  );\n  constructor(\n    el: HTMLElement,\n    private readonly resize: (\n      entry: ResizeEntry,\n      previousSize: ResizeEntry,\n    ) => void,\n    elements: (HTMLElement | undefined)[],\n  ) {\n    const extras: HTMLElement[] = [];\n    elements.forEach((element) => {\n      if (element) {\n        extras.push(element);\n      }\n    });\n    this.init(el, extras);\n  }\n\n  init(el: HTMLElement, extras: HTMLElement[] = []) {\n    const observer = this.resizeObserver = new ResizeObserver((e) => {\n      if (e.length) {\n        if (e[0].target === el) {\n          this.apply(e[0].contentRect);\n        } else {\n          this.apply(el.getBoundingClientRect());\n        }\n      }\n    });\n    observer.observe(el);\n    extras.forEach((extra) => {\n      observer.observe(extra);\n    });\n  }\n\n  destroy() {\n    this.resizeObserver?.disconnect();\n    this.resizeObserver = null;\n  }\n}\n", "@mixin noScroll {\n  /* Hide scrollbar for IE and Edge */\n  -ms-overflow-style: none;\n  scrollbar-width: none; /* Firefox */\n  /* Hide scrollbar for Chrome, Safari and Opera */\n  &::-webkit-scrollbar {\n    display: none;\n    -webkit-appearance: none;\n  }\n}\n\n.rowHeaders {\n  z-index: 2;\n  font-size: 10px;\n  display: flex;\n  height: 100%;\n\n  revogr-data .rgCell {\n    text-align: center;\n  }\n\n  .rgCell {\n    padding: 0 1em !important;\n    min-width: 100%;\n  }\n}\n\nrevogr-viewport-scroll {\n  @include noScroll;\n\n  overflow-x: auto;\n  overflow-y: hidden;\n  position: relative;\n  z-index: 1;\n  height: 100%;\n\n  &.colPinStart,\n  &.colPinEnd {\n    z-index: 2;\n  }\n\n  // make sure it would work\n  &.colPinEnd:has(.active) {\n    overflow: visible;\n  }\n\n  &.rgCol {\n    flex-grow: 1;\n  }\n\n  .content-wrapper {\n    overflow: hidden;\n  }\n\n  .inner-content-table {\n    display: flex;\n    flex-direction: column;\n    max-height: 100%;\n    width: 100%;\n    min-width: 100%;\n    position: relative;\n    z-index: 0;\n  }\n\n  .vertical-inner {\n    overflow-y: auto;\n    position: relative;\n    width: 100%;\n    flex-grow: 1;\n    outline: none; // avoid accessibility focus issue\n    @include noScroll;\n\n    revogr-data,\n    revogr-overlay-selection {\n      height: 100%;\n    }\n  }\n}\n", "import {\n  Component,\n  Event,\n  EventEmitter,\n  h,\n  Method,\n  Element,\n  Prop,\n  Host,\n  Listen,\n} from '@stencil/core';\n\nimport GridResizeService from '../revoGrid/viewport.resize.service';\nimport LocalScrollService from '../../services/local.scroll.service';\nimport { LocalScrollTimer } from '../../services/local.scroll.timer';\nimport {\n  CONTENT_SLOT,\n  FOOTER_SLOT,\n  HEADER_SLOT,\n} from '../revoGrid/viewport.helpers';\nimport type {\n  DimensionCols,\n  DimensionType,\n  ElementScroll,\n  ScrollCoordinateEvent,\n  ViewPortResizeEvent,\n  ViewPortScrollEvent,\n} from '@type';\n\ntype Delta = 'deltaX' | 'deltaY';\ntype LocalScrollEvent = {\n  preventDefault(): void;\n} & { [x in Delta]: number };\n\n/**\n * Viewport scroll component for RevoGrid\n * @slot - content\n * @slot header - header\n * @slot footer - footer\n */\n@Component({\n  tag: 'revogr-viewport-scroll',\n  styleUrl: 'revogr-viewport-scroll-style.scss',\n})\nexport class RevogrViewportScroll implements ElementScroll {\n  /**\n   * Enable row header\n   */\n  @Prop() readonly rowHeader: boolean;\n\n  /**\n   * Width of inner content\n   */\n  @Prop() contentWidth = 0;\n  /**\n   * Height of inner content\n   */\n  @Prop() contentHeight = 0;\n\n  @Prop() colType!: DimensionCols | 'rowHeaders';\n\n  /**\n   * Before scroll event\n   */\n  @Event({ eventName: 'scrollviewport', bubbles: true })\n  scrollViewport: EventEmitter<ViewPortScrollEvent>;\n  /**\n   * Viewport resize\n   */\n  @Event({ eventName: 'resizeviewport' })\n  resizeViewport: EventEmitter<ViewPortResizeEvent>;\n\n  /**\n   * Triggered on scroll change, can be used to get information about scroll visibility\n   */\n  @Event() scrollchange: EventEmitter<{\n    type: DimensionType;\n    hasScroll: boolean;\n  }>;\n\n  /**\n   * Silently scroll to coordinate\n   * Made to align negative coordinates for mobile devices\n   */\n  @Event({ eventName: 'scrollviewportsilent' })\n  silentScroll: EventEmitter<ViewPortScrollEvent>;\n\n  @Element() horizontalScroll: HTMLElement;\n\n  private verticalScroll?: HTMLElement;\n  private header?: HTMLElement;\n  private footer?: HTMLElement;\n\n  /**\n   * Static functions to bind wheel change\n   */\n  private horizontalMouseWheel: (e: Partial<LocalScrollEvent>) => void;\n  private verticalMouseWheel: (e: Partial<LocalScrollEvent>) => void;\n\n  private resizeService?: GridResizeService;\n  private localScrollService: LocalScrollService;\n  private localScrollTimer: LocalScrollTimer;\n\n  @Method() async setScroll(e: ViewPortScrollEvent) {\n    this.localScrollTimer.latestScrollUpdate(e.dimension);\n    this.localScrollService?.setScroll(e);\n  }\n\n  /**\n   * update on delta in case we don't know existing position or external change\n   * @param e\n   */\n  @Method() async changeScroll(e: ViewPortScrollEvent, silent = false) {\n    if (silent) {\n      if (e.coordinate && this.verticalScroll) {\n        switch (e.dimension) {\n          // for mobile devices to skip negative scroll loop. only on vertical scroll\n          case 'rgRow':\n            this.verticalScroll.style.transform = `translateY(${-1 * e.coordinate}px)`;\n            break;\n        }\n      }\n      return;\n    }\n    if (e.delta) {\n      switch (e.dimension) {\n        case 'rgCol':\n          e.coordinate = this.horizontalScroll.scrollLeft + e.delta;\n          break;\n        case 'rgRow':\n          e.coordinate = (this.verticalScroll?.scrollTop ?? 0) + e.delta;\n          break;\n      }\n      this.setScroll(e);\n    }\n    return e;\n  }\n\n  /**\n   * Dispatch this event to trigger vertical mouse wheel from plugins\n   */\n  @Listen('mousewheel-vertical') mousewheelVertical({\n    detail: e,\n  }: CustomEvent<LocalScrollEvent>) {\n    this.verticalMouseWheel(e);\n  }\n  /**\n   * Dispatch this event to trigger horizontal mouse wheel from plugins\n   */\n  @Listen('mousewheel-horizontal') mousewheelHorizontal({\n    detail: e,\n  }: CustomEvent<LocalScrollEvent>) {\n    this.horizontalMouseWheel(e);\n  }\n  /**\n   * Allows to use outside listener\n   */\n  @Listen('scroll-coordinate') scrollApply({\n    detail: { type, coordinate },\n  }: CustomEvent<ScrollCoordinateEvent>) {\n    this.applyOnScroll(type, coordinate, true);\n  }\n\n  connectedCallback() {\n    /**\n     * Bind scroll functions for farther usage\n     */\n    // allow mousewheel for all devices including mobile\n    this.verticalMouseWheel = this.onVerticalMouseWheel.bind(\n      this,\n      'rgRow',\n      'deltaY',\n    );\n    this.horizontalMouseWheel = this.onHorizontalMouseWheel.bind(\n      this,\n      'rgCol',\n      'deltaX',\n    );\n    this.localScrollTimer = new LocalScrollTimer(\n      'ontouchstart' in document.documentElement ? 0 : 10,\n    );\n    /**\n     * Create local scroll service\n     */\n    this.localScrollService = new LocalScrollService({\n      // to improve safari smoothnes on scroll\n      // skipAnimationFrame: isSafariDesktop(),\n      runScroll: e => this.scrollViewport.emit(e),\n      applyScroll: e => {\n        this.localScrollTimer.setCoordinate(e);\n        switch (e.dimension) {\n          case 'rgCol':\n            // this will trigger on scroll event\n            this.horizontalScroll.scrollLeft = e.coordinate;\n            break;\n          case 'rgRow':\n            if (this.verticalScroll) {\n              // this will trigger on scroll event\n              this.verticalScroll.scrollTop = e.coordinate;\n              // for mobile devices to skip negative scroll loop. only on vertical scroll\n              if (this.verticalScroll.style.transform) {\n                this.verticalScroll.style.transform = '';\n              }\n            }\n            break;\n        }\n      },\n    });\n  }\n\n  componentDidLoad() {\n    // track viewport resize\n    this.resizeService = new GridResizeService(\n      this.horizontalScroll,\n      (entry) => {\n        const els: Partial<\n          Record<\n            DimensionType,\n            {\n              size: number;\n              contentSize: number;\n              scroll: number;\n              noScroll: boolean;\n            }\n          >\n        > = {};\n\n        let calculatedHeight = entry.height || 0;\n        if (calculatedHeight) {\n          calculatedHeight -=\n            (this.header?.clientHeight ?? 0) +\n            (this.footer?.clientHeight ?? 0);\n        }\n        els.rgRow = {\n          size: calculatedHeight,\n          contentSize: this.contentHeight,\n          scroll: this.verticalScroll?.scrollTop ?? 0,\n          noScroll: false,\n        };\n        const calculatedWidth = entry.width || 0;\n        els.rgCol = {\n          size: calculatedWidth,\n          contentSize: this.contentWidth,\n          scroll: this.horizontalScroll.scrollLeft,\n          noScroll: this.colType !== 'rgCol',\n        };\n        // Process changes in order: width first, then height\n        const dimensions: DimensionType[] = ['rgCol', 'rgRow'];\n        for (const dimension of dimensions) {\n          const item = els[dimension];\n          if (!item) continue;\n          this.resizeViewport.emit({\n            dimension,\n            size: item.size,\n            rowHeader: this.rowHeader,\n          });\n          if (item.noScroll) {\n            continue;\n          }\n          this.localScrollService?.scroll(item.scroll ?? 0, dimension, true);\n          // track scroll visibility on outer element change\n          this.setScrollVisibility(dimension, item.size, item.contentSize);\n        }\n      },\n      [this.footer, this.header],\n    );\n  }\n\n  /**\n   * Check if scroll present or not per type\n   * Trigger this method on inner content size change or on outer element size change\n   * If inner content bigger then outer size then scroll is present and mousewheel binding required\n   * @param type - dimension type 'rgRow/y' or 'rgCol/x'\n   * @param size - outer content size\n   * @param innerContentSize - inner content size\n   */\n  setScrollVisibility(\n    type: DimensionType,\n    size: number,\n    innerContentSize: number,\n  ) {\n    // test if scroll present\n    const hasScroll = size < innerContentSize;\n    let el: HTMLElement | undefined;\n    // event reference for binding\n    switch (type) {\n      case 'rgCol':\n        el = this.horizontalScroll;\n        break;\n      case 'rgRow':\n        el = this.verticalScroll;\n        break;\n    }\n    // based on scroll visibility assign or remove class and event\n    if (hasScroll) {\n      el?.classList.add(`scroll-${type}`);\n    } else {\n      el?.classList.remove(`scroll-${type}`);\n    }\n    this.scrollchange.emit({ type, hasScroll });\n  }\n\n  disconnectedCallback() {\n    this.resizeService?.destroy();\n  }\n\n  async componentDidRender() {\n    this.localScrollService.setParams(\n      {\n        contentSize: this.contentHeight,\n        clientSize: this.verticalScroll?.clientHeight ?? 0,\n        virtualSize: 0,\n      },\n      'rgRow',\n    );\n\n    this.localScrollService.setParams(\n      {\n        contentSize: this.contentWidth,\n        clientSize: this.horizontalScroll.clientWidth,\n        virtualSize: 0,\n      },\n      'rgCol',\n    );\n    this.setScrollVisibility(\n      'rgRow',\n      this.verticalScroll?.clientHeight ?? 0,\n      this.contentHeight,\n    );\n    this.setScrollVisibility(\n      'rgCol',\n      this.horizontalScroll.clientWidth,\n      this.contentWidth,\n    );\n  }\n\n  render() {\n    return (\n      <Host\n        onWheel={this.horizontalMouseWheel}\n        onScroll={(e: UIEvent) => this.applyScroll('rgCol', e)}\n      >\n        <div\n          class=\"inner-content-table\"\n          style={{ width: `${this.contentWidth}px` }}\n        >\n          <div class=\"header-wrapper\" ref={e => (this.header = e)}>\n            <slot name={HEADER_SLOT} />\n          </div>\n          <div\n            class=\"vertical-inner\"\n            ref={el => (this.verticalScroll = el)}\n            onWheel={this.verticalMouseWheel}\n            onScroll={(e: MouseEvent) => this.applyScroll('rgRow', e)}\n          >\n            <div\n              class=\"content-wrapper\"\n              style={{ height: `${this.contentHeight}px` }}\n            >\n              <slot name={CONTENT_SLOT} />\n            </div>\n          </div>\n          <div class=\"footer-wrapper\" ref={e => (this.footer = e)}>\n            <slot name={FOOTER_SLOT} />\n          </div>\n        </div>\n      </Host>\n    );\n  }\n  /**\n   * Extra layer for scroll event monitoring, where MouseWheel event is not passing\n   * We need to trigger scroll event in case there is no mousewheel event\n   */\n  @Method() async applyScroll(type: DimensionType, e: UIEvent) {\n    if (!(e.target instanceof HTMLElement)) {\n      return;\n    }\n    let scroll = 0;\n    switch (type) {\n      case 'rgCol':\n        scroll = e.target.scrollLeft;\n        break;\n      case 'rgRow':\n        scroll = e.target.scrollTop;\n        break;\n    }\n\n    // for mobile devices to skip negative scroll loop\n    if (scroll < 0) {\n      this.silentScroll.emit({ dimension: type, coordinate: scroll });\n      return;\n    }\n    this.applyOnScroll(type, scroll);\n  }\n\n  /**\n   * Applies change on scroll event only if mousewheel event happened some time ago\n   */\n  private applyOnScroll(\n    type: DimensionType,\n    coordinate: number,\n    outside = false,\n  ) {\n    const lastScrollUpdate = () => {\n      this.localScrollService?.scroll(\n        coordinate,\n        type,\n        undefined,\n        undefined,\n        outside,\n      );\n    };\n    // apply after throttling\n    if (this.localScrollTimer.isReady(type, coordinate)) {\n      lastScrollUpdate();\n    } else {\n      this.localScrollTimer.throttleLastScrollUpdate(type, coordinate, () => lastScrollUpdate());\n    }\n  }\n\n  /**\n   * On vertical mousewheel event\n   * @param type\n   * @param delta\n   * @param e\n   */\n  private onVerticalMouseWheel(\n    type: DimensionType,\n    delta: Delta,\n    e: LocalScrollEvent,\n  ) {\n    const scrollTop = this.verticalScroll?.scrollTop ?? 0;\n    const clientHeight = this.verticalScroll?.clientHeight ?? 0;\n    const scrollHeight = this.verticalScroll?.scrollHeight ?? 0;\n\n    // Detect if the user has reached the bottom\n    const atBottom = scrollTop + clientHeight >= scrollHeight && e.deltaY > 0;\n    const atTop = scrollTop === 0 && e.deltaY < 0;\n    if (!atBottom && !atTop) {\n      e.preventDefault?.();\n    }\n    const pos = scrollTop + e[delta];\n    this.localScrollService?.scroll(pos, type, undefined, e[delta]);\n    this.localScrollTimer.latestScrollUpdate(type);\n  }\n\n  /**\n   * On horizontal mousewheel event\n   * @param type\n   * @param delta\n   * @param e\n   */\n  private onHorizontalMouseWheel(\n    type: DimensionType,\n    delta: Delta,\n    e: LocalScrollEvent,\n  ) {\n    if (!e.deltaX) {\n      return;\n    }\n    const { scrollLeft, scrollWidth, clientWidth } = this.horizontalScroll;\n\n    // Detect if the user has reached the right end\n    const atRight = scrollLeft + clientWidth >= scrollWidth && e.deltaX > 0;\n\n    // Detect if the user has reached the left end\n    const atLeft = scrollLeft === 0 && e.deltaX < 0;\n    if (!atRight && !atLeft) {\n      e.preventDefault?.();\n    }\n    const pos = scrollLeft + e[delta];\n    this.localScrollService?.scroll(pos, type, undefined, e[delta]);\n    this.localScrollTimer.latestScrollUpdate(type);\n  }\n}\n"], "version": 3}