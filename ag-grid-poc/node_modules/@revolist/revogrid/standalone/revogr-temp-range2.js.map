{"file": "revogr-temp-range2.js", "mappings": ";;;;;;;;AAAA,MAAM,uBAAuB,GAAG,41MAA41M;;MCc/2M,WAAW,iBAAAA,kBAAA,CAAA,MAAA,WAAA,SAAA,WAAA,CAAA;AAJxB,IAAA,WAAA,GAAA;;;AAwBmB,QAAA,IAAA,CAAA,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAc,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;AAqEhF;AAnES,IAAA,QAAQ,CAAC,CAAc,EAAA;AAC7B,QAAA,CAAC,aAAD,CAAC,KAAA,MAAA,GAAA,MAAA,GAAD,CAAC,CAAE,cAAc,CAAC;AAChB,YAAA,KAAK,EAAE,SAAS;AAChB,YAAA,MAAM,EAAE,SAAS;AAClB,SAAA,CAAC;;IAGJ,kBAAkB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,EAAE,EAAE;AACX,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;;;IAI1B,MAAM,GAAA;QACJ,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC;QACjD,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,eAAe,CAAC;QACrD,IAAI,CAAC,IAAI,EAAE;YACT;;QAEF,IAAI,UAAU,GAAG,QAAQ;QACzB,IAAI,UAAU,GAAG,OAAO;AACxB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;QAC7B,IAAI,CAAC,KAAK,EAAE;YACV;;QAEF,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE;YACpB,UAAU,GAAG,KAAK;;QAEpB,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE;YACpB,UAAU,GAAG,MAAM;;AAErB,QAAA,MAAM,cAAc,GAAG,CAAA,EAAG,UAAU,CAAI,CAAA,EAAA,UAAU,EAAE;AACpD,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,EACvB,IAAI,CAAC,YAAY,CAAC,KAAK,EACvB,IAAI,CAAC,YAAY,CAAC,KAAK,CACxB;AACD,QAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC;AACrC,QAAA,MAAM,KAAK,GAAG;AACZ,YAAA,KAAK,EAAE;gBACL,CAAC,sBAAsB,GAAG,IAAI;AAC9B,gBAAA,CAAC,IAAI,IAAI,EAAE,GAAG,IAAI;AACnB,aAAA;AACD,YAAA,KAAK,EAAE,MAAM;AACb,YAAA,MAAM,EAAE;SACT;AACD,QAAA,QACE,CAAA,CAAC,IAAI,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAK,KAAK,CAAA,EACb,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAA,CAAI,CACpD;;IAIH,QAAQ,GAAA;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;QAC9C,IAAI,KAAK,EAAE;AACT,YAAA,OAAO,KAAK;;QAEd,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,OAAO,IAAI;;QAEb,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACK,KAAK,CAAA,EAAA,EACR,EAAE,EAAE,KAAK,CAAC,CAAC,EACX,EAAE,EAAE,KAAK,CAAC,CAAC,EACX,CAAA;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/components/selectionTempRange/revogr-temp-range-style.scss?tag=revogr-temp-range", "src/components/selectionTempRange/revogr-temp-range.tsx"], "sourcesContent": [".temp-bg-range {\n  display: block !important;\n  position: absolute;\n  pointer-events: none;\n  z-index: 9;\n  border: 1px solid rgb(255, 94, 0);\n  box-sizing: border-box;\n\n  &.Selection {\n    border: 1px dashed gray;\n  }\n\n  > div {\n    width: 1px;\n    height: 1px;\n    position: absolute;\n\n    &.top {\n      top: -1px;\n    }\n\n    &.bottom {\n      bottom: -1px;\n    }\n\n    &.left {\n      left: -1px;\n    }\n\n    &.right {\n      right: -1px;\n    }\n  }\n}\n", "import { Component, Prop, h, Host } from '@stencil/core';\nimport throttle from 'lodash/throttle';\nimport { TMP_SELECTION_BG_CLASS } from '../../utils/consts';\nimport { getCell, styleByCellProps } from '../overlay/selection.utils';\nimport { DimensionSettingsState, SelectionStoreState, RangeArea } from '@type';\nimport type { Observable } from '../../utils';\n\n/**\n * Temporary range selection component. Shows temporary range selection.\n */\n@Component({\n  tag: 'revogr-temp-range',\n  styleUrl: 'revogr-temp-range-style.scss',\n})\nexport class RevogrFocus {\n  /**\n   * Dynamic stores\n   */\n\n  /**\n   * Selection store, shows current selection and focus\n   */\n  @Prop() selectionStore: Observable<SelectionStoreState>;\n\n  /**\n   * Dimension row store\n   */\n  @Prop() dimensionRow: Observable<DimensionSettingsState>;\n  /**\n   * Dimension column store\n   */\n  @Prop() dimensionCol: Observable<DimensionSettingsState>;\n\n  el?: HTMLElement;\n  private readonly onChange = throttle((e: HTMLElement) => this.doChange(e), 300);\n\n  private doChange(e: HTMLElement) {\n    e?.scrollIntoView({\n      block: 'nearest',\n      inline: 'nearest',\n    });\n  }\n\n  componentDidRender() {\n    if (this.el) {\n      this.onChange(this.el);\n    }\n  }\n\n  render() {\n    const data = this.selectionStore.get('tempRange');\n    const type = this.selectionStore.get('tempRangeType');\n    if (!data) {\n      return;\n    }\n    let directionY = 'bottom';\n    let derectionX = 'right';\n    const range = this.getRange();\n    if (!range) {\n      return;\n    }\n    if (data.y < range.y) {\n      directionY = 'top';\n    }\n    if (data.x < range.x) {\n      derectionX = 'left';\n    }\n    const directionClass = `${derectionX} ${directionY}`;\n    const cell = getCell(data,\n      this.dimensionRow.state,\n      this.dimensionCol.state\n    );\n    const styles = styleByCellProps(cell);\n    const props = {\n      class: {\n        [TMP_SELECTION_BG_CLASS]: true,\n        [type || '']: true,\n      },\n      style: styles,\n      hidden: false\n    };\n    return (\n      <Host {...props}>\n        <div class={directionClass} ref={(e) => (this.el = e)} />\n      </Host>\n    );\n  }\n\n  private getRange(): RangeArea | null {\n    const range = this.selectionStore.get('range');\n    if (range) {\n      return range;\n    }\n    const focus = this.selectionStore.get('focus');\n    if (!focus) {\n      return null;\n    }\n    return {\n      ...focus,\n      x1: focus.x,\n      y1: focus.y,\n    };\n  }\n}\n"], "version": 3}