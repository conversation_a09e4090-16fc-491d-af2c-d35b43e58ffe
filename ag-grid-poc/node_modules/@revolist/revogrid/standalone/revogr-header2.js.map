{"file": "revogr-header2.js", "mappings": ";;;;;;;;;;;AAAA;;;;;;;AAOG;SACa,QAAQ,CACtB,MAA4B,EAC5B,SAAiB,EACjB,MAAuB,EAAA;;AAGvB,IAAA,MAAM,KAAK,GAAG,IAAI,WAAW,CAAiB,SAAS,EAAE;QACvD,MAAM;QACN,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE,IAAI;AACd,KAAA,CAAC;;IAGF,MAAM,KAAA,IAAA,IAAN,MAAM,KAAN,MAAA,GAAA,MAAA,GAAA,MAAM,CAAE,aAAa,CAAC,KAAK,CAAC;;AAG5B,IAAA,OAAO,KAAK;AACd;AAEA;;;;;;;AAOG;AACa,SAAA,eAAe,CAC7B,CAAgD;AAChD,SAAiB;AACjB,MAAuB,EAAA;;IAGvB,CAAC,CAAC,cAAc,EAAE;;IAGlB,OAAO,QAAQ,CAAiB,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC;AAC9D;;MCvCa,WAAW,GAAG,CAAC,EAAE,MAAM,EAAS,KAAI;;AAC/C,IAAA,OAAO,CAAG,CAAA,GAAA,EAAA,EAAA,KAAK,EAAE,CAAA,EAAA,GAAA,MAAM,KAAN,IAAA,IAAA,MAAM,KAAN,MAAA,GAAA,MAAA,GAAA,MAAM,CAAE,KAAK,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,UAAU,GAAI;AAClD;;ACaA,IAAY,YAIX;AAJD,CAAA,UAAY,YAAY,EAAA;AACtB,IAAA,YAAA,CAAA,OAAA,CAAA,GAAA,cAAsB;AACtB,IAAA,YAAA,CAAA,MAAA,CAAA,GAAA,aAAoB;AACpB,IAAA,YAAA,CAAA,KAAA,CAAA,GAAA,YAAkB;AACpB,CAAC,EAJW,YAAY,KAAZ,YAAY,GAIvB,EAAA,CAAA,CAAA;AAED,MAAM,WAAW,GAA2B;IAC1C,aAAa,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE;IACnD,cAAc,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE;IACpD,aAAa,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE;IAClD,cAAc,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE;IACpD,aAAa,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE;IAClD,cAAc,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE;IACpD,aAAa,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE;IAClD,cAAc,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE;CACrD;AAED,MAAM,YAAY,GAAG;AACnB,IAEA,CAAC,EAAE,MAAM;AACT,IAAA,CAAC,EAAE,MAAM;CACV;AAED,MAAM,YAAY,GAAG,CAAC,KAA2B,KAAiB;AAChE,IAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACK,KAAK,CACR,EAAA,EAAA,SAAS,EAAE,KAAK,CAAC,SAAS,IAAI,KAAK,EACnC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE,EAC1B,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,IAAI,EAAE,EAChD,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,CAAC,EAC7B,SAAS,EAAE,KAAK,CAAC,SAAS,IAAI,CAAC,EAC/B,CAAA;AACJ,CAAC;MAEY,eAAe,CAAA;IAyB1B,WACU,CAAA,YAAkC,EAClC,MAAiC,EAAA;;QADjC,IAAY,CAAA,YAAA,GAAZ,YAAY;QACZ,IAAM,CAAA,MAAA,GAAN,MAAM;QAnBR,IAAM,CAAA,MAAA,GAAG,CAAC;QACV,IAAM,CAAA,MAAA,GAAG,CAAC;QAEV,IAAK,CAAA,KAAA,GAAG,CAAC;QACT,IAAM,CAAA,MAAA,GAAG,CAAC;QAEV,IAAO,CAAA,OAAA,GAAG,CAAC;QACX,IAAO,CAAA,OAAA,GAAG,CAAC;QAKX,IAAc,CAAA,cAAA,GAAG,MAAM;AAS7B,QAAA,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,YAAY,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;QAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;QAE3C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS;QAChC,IAAI,CAAC,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,CAAC;QACpC,IAAI,CAAC,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,CAAC,SAAS,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,CAAC;AACrC,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;AACrC,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC;;AAGtB,IAAA,GAAG,CAAC,GAAgB,EAAA;AAClB,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG;QACd,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,IAAG;YAC1C,QAAQ,IAAI;AACV,gBAAA,KAAK,GAAG;AACN,oBAAA,IAAI,CAAC,cAAc,IAAI,EAAe;oBACtC;AACF,gBAAA,KAAK,GAAG;AACN,oBAAA,IAAI,CAAC,cAAc,IAAI,EAAe;oBACtC;AACF,gBAAA,KAAK,GAAG;AACN,oBAAA,IAAI,CAAC,cAAc,IAAI,EAAe;oBACtC;AACF,gBAAA,KAAK,GAAG;AACN,oBAAA,IAAI,CAAC,cAAc,IAAI,EAAe;;AAE5C,SAAC,CAAC;;IAEJ,SAAS,CAAC,SAAiB,EAAE,iBAAuB,EAAA;;AAClD,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB;;AAEF,QAAA,MAAM,MAAM,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC;QACpE,IAAI,CAAC,MAAM,CAAA,MAAA,CAAA,MAAA,CAAA,EACT,SAAS,EACT,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,IAAI,MAAM,GAAG,EAAE,GAAG,CAAC,CAAC,EACpD,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,EAClC,QAAQ,EAAE,IAAI,CAAC,OAAO,EACtB,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAA,EACnB,iBAAiB,CAAA,CACpB;;IAGI,OAAO,YAAY,CAAC,CAA0B,EAAA;;QACpD,MAAM,KAAK,GAAG,CAAe;QAC7B,OAAO,CAAA,MAAA,KAAK,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,KAAI,CAAC;;AAGnC,IAAA,UAAU,CAAC,KAA8B,EAAA;;AACvC,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB;;QAEF,IAAI,MAAM,EAAE,MAAM;AAClB,QAAA,IAAI,eAAe,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;YACvC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO;YACjC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO;;aAC5B;AACL,YAAA,MAAM,GAAG,KAAK,CAAC,OAAO;AACtB,YAAA,MAAM,GAAG,KAAK,CAAC,OAAO;;QAExB,IAAI,GAAG,GACL,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG;YACjD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG;QAEnD,IAAI,GAAG,GACL,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG;YACjD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG;QAEnD,IAAI,GAAG,IAAI,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC,CAAC,EAAE;AAC/C,YAAA,IAAI,KAAK,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM;AAChC,YAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK;AACnC,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,QAAQ;;AAExC,YAAA,IAAI,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE;gBACzB,QAAQ,GAAG,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;;;YAGvC,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE;gBACtC,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM;;AAGpC,YAAA,IAAI,CAAC,OAAO,GAAG,QAAQ;AACvB,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM;AAEpB,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,EAAG,CAAC,IAAI,CAAC,OAAO,IAAI;;;QAG1D,IAAI,GAAG,IAAI,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC,CAAC,EAAE;AAC/C,YAAA,MAAM,MAAM,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC;AACpE,YAAA,IAAI,KAAK,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM;AAChC,YAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK;YACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,QAAQ,IAAI,MAAM,GAAG,EAAE,GAAG,CAAC,CAAC;;AAG1D,YAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE;gBACxB,QAAQ,GAAG,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;;;YAGtC,IAAI,IAAI,CAAC,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE;gBACrC,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK;;AAGnC,YAAA,IAAI,CAAC,OAAO,GAAG,QAAQ;AACvB,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM;AAEpB,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,IAAI,CAAC,MAAM,EAAE;AACX,oBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,CAAC,IAAI,CAAC,OAAO,IAAI;;qBAChD;AACL,oBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,CAAA,EAAG,IAAI,CAAC,OAAO,CAAA,EAAA,CAAI;;;;AAIzD,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC;;AAGnC,IAAA,UAAU,CAAC,KAA8B,EAAA;AACvC,QAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;YAC1B;;;QAGF,KAAK,CAAC,cAAc,EAAE;QAEtB,IAAI,CAAC,WAAW,EAAE;AAClB,QAAA,KAAK,IAAI,OAAO,IAAI,WAAW,EAAE;AAC/B,YAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAA4B;YACjD,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAI,MAAM,KAAA,IAAA,IAAN,MAAM,KAAN,MAAA,GAAA,MAAA,GAAA,MAAM,CAAE,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA,EAAE;AACpE,gBAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;AACxD,gBAAA,IAAI,eAAe,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;AACvC,oBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;;qBACrC;AACL,oBAAA,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,EAAE;AAC9C,oBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC;;gBAEjC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG;AAC3C,gBAAA,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK;AACpC,gBAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;gBACzB;;;QAGJ,IAAI,CAAC,QAAQ,EAAE;;AAGjB,IAAA,QAAQ,CAAC,CAAa,EAAA;QACpB,CAAC,CAAC,cAAc,EAAE;AAClB,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;AAC1B,YAAA,IAAI,CAAC,WAAW,GAAG,CAAC;YACpB,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;AAC/B,YAAA,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG;AAClC,YAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;;QAE3B,IAAI,CAAC,WAAW,EAAE;QAClB,IAAI,CAAC,UAAU,EAAE;;AAGX,IAAA,WAAW,CACjB,EAAE,OAAO,EAAE,OAAO,EAAwC,EAC1D,MAAoB,EAAA;;QAEpB,MAAM,aAAa,GAAG,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC;QAChD,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;AAChC,QAAA,IAAI,CAAC,aAAa,GAAG,MAAM;QAE3B,IAAI,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC,CAAC,EAAE;AACxC,YAAA,IAAI,CAAC,MAAM,GAAG,OAAO;YACrB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW;AACjC,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,MAAA,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAI,CAAC;;AAG5D,YAAA,MAAM,WAAW,GACf,UAAU,CAAC,aAAa,CAAC,WAAW,CAAC;AACrC,gBAAA,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC;AACxC,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,CAAC;;AAGlE,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;AAC9B,gBAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;;;QAIhE,IAAI,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC,CAAC,EAAE;AACxC,YAAA,IAAI,CAAC,MAAM,GAAG,OAAO;YACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY;AACnC,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAA,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAI,CAAC;;AAG9D,YAAA,MAAM,WAAW,GACf,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC;AACpC,gBAAA,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC;AACzC,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,CAAC,CAAC;;AAEnE,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE;AAC/B,gBAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;;;;IAK5D,WAAW,GAAA;AACjB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;QACnD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;AAC5B,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,OAAO,CAAC;;QAE7C,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;AACnC,QAAA,IAAI,CAAC,aAAa,GAAG,SAAS;;IAGxB,QAAQ,GAAA;AACd,QAAA,QAAQ,CAAC,eAAe,CAAC,gBAAgB,CACvC,SAAS,EACT,IAAI,CAAC,WAAW,EAChB,IAAI,CACL;AACD,QAAA,QAAQ,CAAC,eAAe,CAAC,gBAAgB,CACvC,UAAU,EACV,IAAI,CAAC,WAAW,EAChB,IAAI,CACL;AACD,QAAA,QAAQ,CAAC,eAAe,CAAC,gBAAgB,CACvC,WAAW,EACX,IAAI,CAAC,aAAa,EAClB,IAAI,CACL;AACD,QAAA,QAAQ,CAAC,eAAe,CAAC,gBAAgB,CACvC,WAAW,EACX,IAAI,CAAC,aAAa,EAClB,IAAI,CACL;QACD,QAAQ,CAAC,eAAe,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC;;IAGnE,UAAU,GAAA;AAChB,QAAA,QAAQ,CAAC,eAAe,CAAC,mBAAmB,CAC1C,SAAS,EACT,IAAI,CAAC,WAAW,EAChB,IAAI,CACL;AACD,QAAA,QAAQ,CAAC,eAAe,CAAC,mBAAmB,CAC1C,UAAU,EACV,IAAI,CAAC,WAAW,EAChB,IAAI,CACL;AACD,QAAA,QAAQ,CAAC,eAAe,CAAC,mBAAmB,CAC1C,WAAW,EACX,IAAI,CAAC,aAAa,EAClB,IAAI,CACL;AACD,QAAA,QAAQ,CAAC,eAAe,CAAC,mBAAmB,CAC1C,WAAW,EACX,IAAI,CAAC,aAAa,EAClB,IAAI,CACL;QACD,QAAQ,CAAC,eAAe,CAAC,mBAAmB,CAC1C,YAAY,EACZ,IAAI,CAAC,WAAW,CACjB;;AAEJ;;AC/UM,MAAM,gBAAgB,GAAwB,CACnD,KAAqC,EACrC,QAAiB,KACf;IACF,MAAM,SAAS,GAAY,EAAE;AAC7B,IAAA,MAAM,SAAS,GACb,CAAC,KAAK,CAAC,SAAS;AACd,QAAA,IAAI,eAAe,CAAC,KAAK,EAAE,CAAC,IAAG;;YAC7B,IAAI,CAAC,CAAC,SAAS,KAAK,YAAY,CAAC,GAAG,EAAE;AACpC,gBAAA,CAAA,EAAA,GAAA,KAAK,CAAC,QAAQ,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,CAAC,CAAC;;AAEvB,SAAC,CAAC;AACJ,QAAA,IAAI;AAEN,IAAA,IAAI,KAAK,CAAC,MAAM,EAAE;AAChB,QAAA,IAAI,KAAK,CAAC,SAAS,EAAE;AACnB,YAAA,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE;AAC1B,gBAAA,SAAS,CAAC,IAAI,CACZ,WACE,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,EAChC,UAAU,EAAE,CAAC,IAAG;;wBACd,CAAC,CAAC,cAAc,EAAE;AAClB,wBAAA,CAAA,EAAA,GAAA,KAAK,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,CAAC,CAAC;qBACtB,EACD,WAAW,EAAE,CAAC,CAAa,KAAK,SAAS,KAAT,IAAA,IAAA,SAAS,uBAAT,SAAS,CAAE,UAAU,CAAC,CAAC,CAAC,EACxD,YAAY,EAAE,CAAC,CAAa,KAAK,SAAS,aAAT,SAAS,KAAA,MAAA,GAAA,MAAA,GAAT,SAAS,CAAE,UAAU,CAAC,CAAC,CAAC,EACzD,KAAK,EAAE,CAAA,oBAAA,EAAuB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA,CAAE,EAC/C,CAAA,CACH;;;aAEE;AACL,YAAA,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE;AAC1B,gBAAA,SAAS,CAAC,IAAI,CACZ,CAAA,CAAA,KAAA,EAAA,EACE,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,EAChC,YAAY,EAAE,CAAC,CAAa,KAAK,CAAC,CAAC,cAAc,EAAE,EACnD,UAAU,EAAE,CAAC,IAAG;;wBACd,CAAC,CAAC,cAAc,EAAE;AAClB,wBAAA,CAAA,EAAA,GAAA,KAAK,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,CAAC,CAAC;AACvB,qBAAC,EACD,KAAK,EAAE,CAAA,8BAAA,EAAiC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAE,CAAA,EAAA,CACzD,CACH;;;;IAIP,QACE,2BAAS,KAAK,EAAA,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAI,SAAS,KAAA,IAAA,IAAT,SAAS,KAAT,MAAA,GAAA,MAAA,GAAA,SAAS,CAAE,GAAG,CAAC,CAAC,CAAC,CAAA,EAAA,CAAA;QAC7C,QAAQ;QACR,SAAS,CACN;AAEV,CAAC;;ACrDM,MAAM,eAAe,GAAG;AAExB,MAAM,kBAAkB,GAI1B,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,QAAQ,KAAI;AACjD,IAAA,IAAI,WAAW,GAAyB,CAAA,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAE,IAAI,KAAI,EAAE;IACxD,IAAI,SAAS,GAAG,KAAK;IACrB,IAAI,IAAI,aAAJ,IAAI,KAAA,MAAA,GAAA,MAAA,GAAJ,IAAI,CAAE,cAAc,EAAE;QACxB,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,EAAE,cAAc,CAAC;;IAE5D,IAAI,IAAI,aAAJ,IAAI,KAAA,MAAA,GAAA,MAAA,GAAJ,IAAI,CAAE,gBAAgB,EAAE;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;QACzC,IAAI,KAAK,EAAE;AACT,YAAA,SAAS,GAAG,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC;;;AAGzC,IAAA,MAAM,WAAW,GACZ,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,SAAS,CACZ,EAAA,EAAA,WAAW,CAAC,CAAa,EAAA;AACvB,YAAA,QAAQ,CAAC,CAAC,CAAC,aAAa,EAAE,eAAe,EAAE;gBACzC,IAAI;AACJ,gBAAA,KAAK,EAAE,CAAC;AACT,aAAA,CAAC;AACJ,SAAC,GACF;AACD,IAAA,QACE,CAAA,CAAC,gBAAgB,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAK,WAAW,CAAA;AAC/B,QAAA,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,gBAAgB,EAAA,EAAE,WAAW,CAAO;QAC9C,QAAQ,CACQ;AAEvB,CAAC;;ACxCD,IAAI,cAAc,IAAI,WAAW;AACjC,EAAE,IAAI;AACN,IAAI,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,gBAAgB,CAAC;AAClD,IAAI,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACpB,IAAI,OAAO,IAAI;AACf,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,CAAC,EAAE,CAAC;;ACNJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE;AAC7C,EAAE,IAAI,GAAG,IAAI,WAAW,IAAI,cAAc,EAAE;AAC5C,IAAI,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE;AAChC,MAAM,cAAc,EAAE,IAAI;AAC1B,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,UAAU,EAAE;AAClB,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK;AACvB;AACA;;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE;AAC/D,EAAE,IAAI,KAAK,GAAG,EAAE;AAChB,MAAM,MAAM,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM;;AAE/C,EAAE,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE;AAC3B,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC5B,IAAI,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;AACtD;AACA,EAAE,OAAO,WAAW;AACpB;;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE;AACnE,EAAE,QAAQ,CAAC,UAAU,EAAE,SAAS,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE;AACxD,IAAI,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC;AAC3D,GAAG,CAAC;AACJ,EAAE,OAAO,WAAW;AACpB;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,MAAM,EAAE,WAAW,EAAE;AAC/C,EAAE,OAAO,SAAS,UAAU,EAAE,QAAQ,EAAE;AACxC,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,eAAe,GAAG,cAAc;AACrE,QAAQ,WAAW,GAAiC,EAAE;;AAEtD,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,YAAY,CAAC,QAAW,CAAC,EAAE,WAAW,CAAC;AAC3E,GAAG;AACH;;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,GAAG,gBAAgB,CAAC,SAAS,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE;AAC1D,EAAE,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC;AACrC,CAAC,CAAC;;ACAF,MAAM,cAAc,GAAG,CAAC,CAAoB,KAA0B;;AACpE,IAAA,MAAM,SAAS,GAA+B;QAC5C,CAAC,YAAY,GAAG,IAAI;AACpB,QAAA,CAAC,qBAAqB,GAAG,CAAC,EAAC,CAAA,EAAA,GAAA,CAAC,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,QAAQ,CAAA;KAC5C;AACD,IAAA,IAAI,MAAA,CAAC,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK,EAAE;QACjB,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI;;AAEhC,IAAA,MAAM,SAAS,GAAmC;AAChD,QAAA,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS;QAC9B,SAAS,EAAE,CAAC,CAAC,SAAS;QACtB,QAAQ,EAAE,CAAA,CAAA,EAAA,GAAA,CAAC,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,OAAO,KAAI,YAAY;AACzC,QAAA,QAAQ,EAAE,CAAA,EAAA,GAAA,CAAC,CAAC,IAAI,0CAAE,OAAO;AACzB,QAAA,MAAM,EAAE,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC;AACzB,QAAA,KAAK,EAAE,SAAS;AAChB,QAAA,KAAK,EAAE;AACL,YAAA,KAAK,EAAE,CAAG,EAAA,CAAC,CAAC,MAAM,CAAC,IAAI,CAAI,EAAA,CAAA;AAC3B,YAAA,SAAS,EAAE,CAAc,WAAA,EAAA,CAAC,CAAC,MAAM,CAAC,KAAK,CAAK,GAAA,CAAA;AAC7C,SAAA;QACD,QAAQ,EAAE,CAAC,CAAC,QAAQ;AACpB,QAAA,UAAU,CAAC,aAAyB,EAAA;;YAClC,CAAA,EAAA,GAAA,CAAC,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,CAAA,EAAA;gBACb,MAAM,EAAE,CAAC,CAAC,IAAI;AACd,gBAAA,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS;gBACzB,aAAa;AACb,gBAAA,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS;AAC5B,aAAA,CAAC;SACH;AACD,QAAA,OAAO,CAAC,aAAyB,EAAA;YAC/B,IAAI,aAAa,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE;gBAChD;;YAEF,CAAC,CAAC,OAAO,CAAC;gBACR,MAAM,EAAE,CAAC,CAAC,IAAI;AACd,gBAAA,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS;gBACzB,aAAa;AACb,gBAAA,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS;AAC5B,aAAA,CAAC;SACH;KACF;AACD,IAAA,IAAI,CAAC,CAAC,KAAK,EAAE;QACX,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE;AACvE,YAAA,IAAI,OAAO,SAAS,CAAC,KAAK,KAAK,QAAQ,EAAE;AACvC,gBAAA,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,IAAI;;;;AAIzC,IAAA,QACE,CAAC,CAAA,kBAAkB,IACjB,IAAI,EAAE,CAAC,CAAC,IAAI,EACZ,KAAK,EAAE,SAAS,EAChB,cAAc,EAAE,CAAC,CAAC,cAAc,EAAA;AAE/B,QAAA,CAAA,CAAC,WAAW,EAAC,EAAA,MAAM,EAAE,CAAC,CAAC,IAAI,EAAI,CAAA;AAC/B,QAAA,CAAC,CAAC,SAAS,IAAI,CAAA,MAAA,CAAC,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,MAAM,MAAK,KAAK,IACtC,CAAA,CAAC,YAAY,EAAA,EAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAI,CAAA,KAEhC,EAAE,CACH,CACkB;AAEzB,CAAC;;AC7ED,MAAM,mBAAmB,GAAG,CAAC,CAA2B,KAA0B;AAChF,IAAA,MAAM,UAAU,GAAqC;QACnD,SAAS,EAAE,CAAC,CAAC,SAAS;QACtB,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,YAAY;AAC/C,QAAA,QAAQ,EAAE,CAAC;AAEX,QAAA,MAAM,EAAE,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC;AACzB,QAAA,KAAK,EAAE;YACL,CAAC,YAAY,GAAG,IAAI;AACrB,SAAA;AACD,QAAA,KAAK,EAAE;AACL,YAAA,SAAS,EAAE,CAAA,WAAA,EAAc,CAAC,CAAC,KAAK,CAAK,GAAA,CAAA;YACrC,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAI,EAAA,CAAA;AAC9B,SAAA;QACD,QAAQ,EAAE,CAAC,CAAC,QAAQ;KACrB;AACD,IAAA,QACE,CAAC,CAAA,kBAAkB,EACjB,EAAA,IAAI,kCACC,CAAC,CAAC,KAAK,CAAA,EAAA,EACV,IAAI,EAAE,EAAE,EACR,SAAS,EAAE,CAAC,CAAC,SAAS,EACtB,KAAK,EAAE,CAAC,CAAC,KAAK,KAEhB,KAAK,EAAE,UAAU,EACjB,cAAc,EAAE,CAAC,CAAC,cAAc,EAAA,CAChC;AAEN,CAAC;;AC7CD,MAAM,oBAAoB,GAAG,w8PAAw8P;;MCoCx9P,qBAAqB,iBAAAA,kBAAA,CAAA,MAAA,qBAAA,SAAA,WAAA,CAAA;AAJlC,IAAA,WAAA,GAAA;;;;;;;;;;AAoBE;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAG,CAAC;AA8BzB;;AAEG;AACK,QAAA,IAAc,CAAA,cAAA,GAAQ,EAAE;AAiMjC;AAxIS,IAAA,QAAQ,CAAC,EAAE,KAAK,EAAsB,EAAE,KAAa,EAAA;QAC3D,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAC/B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AAE9B,YAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAG,CACN,EAAA,EAAA,IAAI,EAAE,KAAK,IAAI,SAAS,EAAA,CAAA;AAE3B,SAAA,CAAC;AACF,QAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;YAC1B;;AAEF,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,EAAE,CAAC;;AAGzC,IAAA,aAAa,CACnB,QAAgB,EAChB,UAAkB,EAClB,QAAgB,EAAA;QAEhB,MAAM,KAAK,GAAwB,EAAE;AACrC,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,WAAW,CAAC;QAC9D,MAAM,MAAM,GAAG,QAAQ,IAAI,QAAQ,GAAG,UAAU,GAAG,CAAC,CAAC;AACrD,QAAA,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,EAAE;AAC3C,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;YACpB,IAAI,IAAI,EAAE;gBACR,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM;;;AAGjC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;;IAG/B,kBAAkB,GAAA;QAChB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;;IAG7C,MAAM,GAAA;;QACJ,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC;AAC1C,QAAA,MAAM,KAAK,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,GAAG,CAAC,OAAO,CAAC;AAE/C,QAAA,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC;AACvD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,EAAE;QAE7C,OAAO;AACL,YAAA,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,aAAa,EAAA,EAAE,QAAQ,CAAO;AACzC,YAAA,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,CAAA,EAAG,gBAAgB,CAAI,CAAA,EAAA,uBAAuB,EAAE,EACzD,EAAA,KAAK,CACF;SACP;;IAGK,mBAAmB,CACzB,IAA2B,EAC3B,KAAuB,EAAA;QAEvB,MAAM,KAAK,GAAY,EAAE;AACzB,QAAA,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;YACtB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;AAC7C,YAAA,MAAM,KAAK,GAAsB;gBAC/B,KAAK;AACL,gBAAA,MAAM,EAAE,KAAK;gBACb,IAAI,EACC,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CACV,EAAA,EAAA,KAAK,EAAE,KAAK,CAAC,SAAS,EACtB,SAAS,EAAE,IAAI,CAAC,SAAS,EAC1B,CAAA;AACD,gBAAA,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY;gBAC9B,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,MAAM,EAAE,IAAI,CAAC,aAAa;gBAC1B,cAAc,EAAE,IAAI,CAAC,cAAc;AACnC,gBAAA,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC;AAChD,gBAAA,UAAU,EAAE,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5C,gBAAA,OAAO,EAAE,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;aAC9C;YACD,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC;AACjD,YAAA,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;AAC3B,gBAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA,cAAc,EAAK,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAK,CAAC,MAAM,CAAI,CAAA,CAAC;;;QAGpD,OAAO,EAAE,KAAK,EAAE;;IAGV,qBAAqB,GAAA;;QAC3B,MAAM,QAAQ,GAAY,EAAE;AAC5B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE;AAC3C,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBAClB,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;AAChC,oBAAA,MAAM,eAAe,GAAG,CAAA,EAAA,GAAA,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAI,EAAE;AAC9C,oBAAA,IAAI,eAAe,GAAG,EAAE,EAAE;wBACxB,MAAM,aAAa,GAAG,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;AAEhE,wBAAA,MAAM,UAAU,GAAG,cAAc,CAC/B,IAAI,CAAC,YAAY,CAAC,KAAK,EACvB,eAAe,CAChB,CAAC,KAAK;AACP,wBAAA,MAAM,QAAQ,GAAG,cAAc,CAC7B,IAAI,CAAC,YAAY,CAAC,KAAK,EACvB,aAAa,CACd,CAAC,GAAG;AAEL,wBAAA,MAAM,KAAK,GAA6B;4BACtC,SAAS,EAAE,IAAI,CAAC,SAAS;AACzB,4BAAA,KAAK,EAAE,UAAU;AACjB,4BAAA,GAAG,EAAE,QAAQ;4BACb,KAAK;4BACL,MAAM,EAAE,IAAI,CAAC,aAAa;4BAC1B,SAAS,EAAE,IAAI,CAAC,SAAS;4BACzB,cAAc,EAAE,IAAI,CAAC,cAAc;4BACnC,QAAQ,EAAE,CAAC,IAAG;;AACZ,gCAAA,OAAA,IAAI,CAAC,aAAa,CAChB,MAAA,CAAC,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAI,CAAC,EACf,eAAe,EACf,aAAa,CACd;6BAAA;yBACJ;wBACD,MAAM,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC;AACtD,wBAAA,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;AAC3B,4BAAA,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAAC,mBAAmB,EAAK,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAK,CAAC,MAAM,CAAI,CAAA,CAAC;;;;;AAKhE,YAAA,QAAQ,CAAC,IAAI,CAAC,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAE,GAAG,gBAAgB,CAAA,MAAA,CAAQ,EAAI,CAAA,CAAC;;AAE5D,QAAA,OAAO,QAAQ;;AAGjB,IAAA,IAAI,SAAS,GAAA;QACX,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,OAAO;YAClB,QAAQ,EAAE,IAAI,CAAC,WAAW;YAC1B,SAAS,EAAE,IAAI,CAAC,YAAY;YAC5B,SAAS,EAAE,IAAI,CAAC,cAAc;SAC/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement", "GroupHeaderRenderer"], "sources": ["src/plugins/dispatcher.ts", "src/plugins/sorting/sorting.sign.tsx", "src/components/header/resizable.directive.tsx", "src/components/header/resizable.element.tsx", "src/components/header/header-cell-renderer.tsx", "node_modules/lodash/_defineProperty.js", "node_modules/lodash/_baseAssignValue.js", "node_modules/lodash/_arrayAggregator.js", "node_modules/lodash/_baseAggregator.js", "node_modules/lodash/_createAggregator.js", "node_modules/lodash/keyBy.js", "src/components/header/header-renderer.tsx", "src/components/header/header-group-renderer.tsx", "src/components/header/revogr-header-style.scss?tag=revogr-header", "src/components/header/revogr-header.tsx"], "sourcesContent": ["/**\n * Dispatches a custom event to a specified target element.\n *\n * @param target - The target element to dispatch the event to.\n * @param eventName - The name of the custom event.\n * @param detail - Optional. The detail of the custom event.\n * @returns The custom event that was dispatched.\n */\nexport function dispatch<DispatchDetail = any>(\n  target: MouseEvent['target'],\n  eventName: string,\n  detail?: DispatchDetail,\n) {\n  // Create a new CustomEvent with the specified event name and detail.\n  const event = new CustomEvent<DispatchDetail>(eventName, {\n    detail,\n    cancelable: true, // Indicates whether the event can be canceled.\n    bubbles: true, // Indicates whether the event bubbles up through the DOM.\n  });\n\n  // Dispatch the event on the target element.\n  target?.dispatchEvent(event);\n\n  // Return the custom event that was dispatched.\n  return event;\n}\n\n/**\n * Dispatches a custom event based on an existing event object and prevents the default behavior of the original event.\n *\n * @param e - The original event object containing the target and preventDefault method.\n * @param eventName - The name of the custom event.\n * @param detail - Optional. The detail of the custom event.\n * @returns The custom event that was dispatched.\n */\nexport function dispatchByEvent<DispatchDetail = any>(\n  e: Pick<MouseEvent, 'target' | 'preventDefault'>, // The original event object containing the target and preventDefault method.\n  eventName: string, // The name of the custom event.\n  detail?: DispatchDetail, // Optional. The detail of the custom event.\n): CustomEvent {\n  // Prevent the default behavior of the original event.\n  e.preventDefault();\n\n  // Dispatch the custom event to the target element specified in the original event object.\n  return dispatch<DispatchDetail>(e.target, eventName, detail);\n}\n", "import { h } from '@stencil/core';\nimport { ColumnRegular } from '@type';\n\ntype Props = {\n  column: ColumnRegular;\n};\nexport const SortingSign = ({ column }: Props) => {\n  return <i class={column?.order ?? 'sort-off'} />;\n};\n", "export type ResizeProps = {\n  active: ('r' | 'rb' | 'b' | 'lb' | 'l' | 'lt' | 't' | 'rt')[]; // all\n  fitParent: boolean; // false\n  minWidth: number; // 0\n  minHeight: number; // 0\n\n  disableAttributes: ('l' | 't' | 'w' | 'h')[]; // []\n  maxWidth?: number;\n  maxHeight?: number;\n\n  onResize?(e: ResizeEvent): void;\n};\n\nexport type ResizeEvent = {\n  eventName: string;\n  changedX?: number;\n  changedY?: number;\n  width?: number;\n  height?: number;\n};\n\nexport enum ResizeEvents {\n  start = 'resize:start',\n  move = 'resize:move',\n  end = 'resize:end',\n}\n\nconst RESIZE_MASK: { [key: string]: any } = {\n  'resizable-r': { bit: 0b0001, cursor: 'ew-resize' },\n  'resizable-rb': { bit: 0b0011, cursor: 'se-resize' },\n  'resizable-b': { bit: 0b0010, cursor: 's-resize' },\n  'resizable-lb': { bit: 0b0110, cursor: 'sw-resize' },\n  'resizable-l': { bit: 0b0100, cursor: 'w-resize' },\n  'resizable-lt': { bit: 0b1100, cursor: 'nw-resize' },\n  'resizable-t': { bit: 0b1000, cursor: 'n-resize' },\n  'resizable-rt': { bit: 0b1001, cursor: 'ne-resize' },\n};\n\nconst DISABLE_MASK = {\n  l: 0b0001,\n  t: 0b0010,\n  w: 0b0100,\n  h: 0b1000,\n};\n\nconst defaultProps = (props: Partial<ResizeProps>): ResizeProps => {\n  return {\n    ...props,\n    fitParent: props.fitParent || false,\n    active: props.active || [],\n    disableAttributes: props.disableAttributes || [],\n    minWidth: props.minWidth || 0,\n    minHeight: props.minHeight || 0,\n  };\n};\n\nexport class ResizeDirective {\n  private $el: HTMLElement;\n  private props: ResizeProps;\n  private minW: number;\n  private minH: number;\n  private maxW: number;\n  private maxH: number;\n\n  private mouseX = 0;\n  private mouseY = 0;\n\n  private width = 0;\n  private height = 0;\n\n  private changeX = 0;\n  private changeY = 0;\n\n  private parent: { width: number; height: number };\n  private resizeState: number;\n  private activeResizer?: HTMLElement;\n  private disableCalcMap = 0b1111;\n\n  private mouseMoveFunc: () => void;\n  private mouseUpFunc: () => void;\n\n  constructor(\n    private initialProps: Partial<ResizeProps>,\n    private $event?: (e: ResizeEvent) => void,\n  ) {\n    this.props = defaultProps(initialProps);\n    this.mouseMoveFunc = this.handleMove.bind(this);\n    this.mouseUpFunc = this.handleUp.bind(this);\n\n    this.minW = this.props.minWidth;\n    this.minH = this.props.minHeight;\n    this.maxW = this.props.maxWidth ?? 0;\n    this.maxH = this.props.maxHeight ?? 0;\n    this.parent = { width: 0, height: 0 };\n    this.resizeState = 0;\n  }\n\n  set($el: HTMLElement) {\n    this.$el = $el;\n    this.props.disableAttributes.forEach(attr => {\n      switch (attr) {\n        case 'l':\n          this.disableCalcMap &= ~DISABLE_MASK.l;\n          break;\n        case 't':\n          this.disableCalcMap &= ~DISABLE_MASK.t;\n          break;\n        case 'w':\n          this.disableCalcMap &= ~DISABLE_MASK.w;\n          break;\n        case 'h':\n          this.disableCalcMap &= ~DISABLE_MASK.h;\n      }\n    });\n  }\n  emitEvent(eventName: string, additionalOptions?: any) {\n    if (!this.$event) {\n      return;\n    }\n    const isLeft = this.activeResizer?.classList.contains('resizable-l');\n    this.$event({\n      eventName,\n      width: this.width + this.changeX * (isLeft ? -1 : 1),\n      height: this.height + this.changeY,\n      changedX: this.changeX,\n      changedY: this.changeY,\n      ...additionalOptions,\n    });\n  }\n\n  private static isTouchEvent(e: MouseEvent | TouchEvent): e is TouchEvent {\n    const event = e as TouchEvent;\n    return event.touches?.length >= 0;\n  }\n\n  handleMove(event: MouseEvent | TouchEvent) {\n    if (!this.resizeState) {\n      return;\n    }\n    let eventY, eventX;\n    if (ResizeDirective.isTouchEvent(event)) {\n      eventY = event.touches[0].clientY;\n      eventX = event.touches[0].clientX;\n    } else {\n      eventY = event.clientY;\n      eventX = event.clientX;\n    }\n    let isX =\n      this.resizeState & RESIZE_MASK['resizable-r'].bit ||\n      this.resizeState & RESIZE_MASK['resizable-l'].bit;\n\n    let isY =\n      this.resizeState & RESIZE_MASK['resizable-t'].bit ||\n      this.resizeState & RESIZE_MASK['resizable-b'].bit;\n\n    if (isY && this.disableCalcMap & DISABLE_MASK.h) {\n      let diffY = eventY - this.mouseY;\n      let changedY = this.changeY + diffY;\n      const newHeight = this.height + changedY;\n      // if overcrossed min height\n      if (newHeight < this.minH) {\n        changedY = -(this.height - this.minH);\n      }\n      // if overcrossed max heiht\n      if (this.maxH && newHeight > this.maxH) {\n        changedY = this.maxH - this.height;\n      }\n\n      this.changeY = changedY;\n      this.mouseY = eventY;\n\n      if (this.activeResizer) {\n        this.activeResizer.style.bottom = `${-this.changeY}px`;\n      }\n    }\n    if (isX && this.disableCalcMap & DISABLE_MASK.w) {\n      const isLeft = this.activeResizer?.classList.contains('resizable-l');\n      let diffX = eventX - this.mouseX;\n      let changedX = this.changeX + diffX;\n      const newWidth = this.width + changedX * (isLeft ? -1 : 1);\n\n      // if overcrossed min width\n      if (newWidth < this.minW) {\n        changedX = -(this.width - this.minW);\n      }\n      // if overcrossed max width\n      if (this.maxW && newWidth > this.maxW) {\n        changedX = this.maxW - this.width;\n      }\n\n      this.changeX = changedX;\n      this.mouseX = eventX;\n\n      if (this.activeResizer) {\n        if (!isLeft) {\n          this.activeResizer.style.right = `${-this.changeX}px`;\n        } else {\n          this.activeResizer.style.left = `${this.changeX}px`;\n        }\n      }\n    }\n    this.emitEvent(ResizeEvents.move);\n  }\n\n  handleDown(event: MouseEvent | TouchEvent) {\n    if (event.defaultPrevented) {\n      return;\n    }\n    // stop other events if resize in progress\n    event.preventDefault();\n\n    this.dropInitial();\n    for (let elClass in RESIZE_MASK) {\n      const target = event.target as HTMLElement | null;\n      if (this.$el.contains(target) && target?.classList.contains(elClass)) {\n        document.body.style.cursor = RESIZE_MASK[elClass].cursor;\n        if (ResizeDirective.isTouchEvent(event)) {\n          this.setInitials(event.touches[0], target);\n        } else {\n          event.preventDefault && event.preventDefault();\n          this.setInitials(event, target);\n        }\n        this.resizeState = RESIZE_MASK[elClass].bit;\n        const eventName = ResizeEvents.start;\n        this.emitEvent(eventName);\n        break;\n      }\n    }\n    this.bindMove();\n  }\n\n  handleUp(e: MouseEvent) {\n    e.preventDefault();\n    if (this.resizeState !== 0) {\n      this.resizeState = 0;\n      document.body.style.cursor = '';\n      const eventName = ResizeEvents.end;\n      this.emitEvent(eventName);\n    }\n    this.dropInitial();\n    this.unbindMove();\n  }\n\n  private setInitials(\n    { clientX, clientY }: { clientX: number; clientY: number },\n    target?: HTMLElement,\n  ) {\n    const computedStyle = getComputedStyle(this.$el);\n    this.$el.classList.add('active');\n    this.activeResizer = target;\n\n    if (this.disableCalcMap & DISABLE_MASK.w) {\n      this.mouseX = clientX;\n      this.width = this.$el.clientWidth;\n      this.parent.width = this.$el.parentElement?.clientWidth ?? 0;\n\n      // min width\n      const minPaddingX =\n        parseFloat(computedStyle.paddingLeft) +\n        parseFloat(computedStyle.paddingRight);\n      this.minW = Math.max(minPaddingX, this.initialProps.minWidth || 0);\n\n      // max width\n      if (this.initialProps.maxWidth) {\n        this.maxW = Math.max(this.width, this.initialProps.maxWidth);\n      }\n    }\n\n    if (this.disableCalcMap & DISABLE_MASK.h) {\n      this.mouseY = clientY;\n      this.height = this.$el.clientHeight;\n      this.parent.height = this.$el.parentElement?.clientHeight ?? 0;\n\n      // min height\n      const minPaddingY =\n        parseFloat(computedStyle.paddingTop) +\n        parseFloat(computedStyle.paddingBottom);\n      this.minH = Math.max(minPaddingY, this.initialProps.minHeight || 0);\n      // max height\n      if (this.initialProps.maxHeight) {\n        this.maxH = Math.max(this.height, this.initialProps.maxHeight);\n      }\n    }\n  }\n\n  private dropInitial() {\n    this.changeX = this.changeY = this.minW = this.minH;\n    this.width = this.height = 0;\n    if (this.activeResizer) {\n      this.activeResizer.removeAttribute('style');\n    }\n    this.$el.classList.remove('active');\n    this.activeResizer = undefined;\n  }\n\n  private bindMove() {\n    document.documentElement.addEventListener(\n      'mouseup',\n      this.mouseUpFunc,\n      true,\n    );\n    document.documentElement.addEventListener(\n      'touchend',\n      this.mouseUpFunc,\n      true,\n    );\n    document.documentElement.addEventListener(\n      'mousemove',\n      this.mouseMoveFunc,\n      true,\n    );\n    document.documentElement.addEventListener(\n      'touchmove',\n      this.mouseMoveFunc,\n      true,\n    );\n    document.documentElement.addEventListener('mouseleave', this.mouseUpFunc);\n  }\n\n  private unbindMove() {\n    document.documentElement.removeEventListener(\n      'mouseup',\n      this.mouseUpFunc,\n      true,\n    );\n    document.documentElement.removeEventListener(\n      'touchend',\n      this.mouseUpFunc,\n      true,\n    );\n    document.documentElement.removeEventListener(\n      'mousemove',\n      this.mouseMoveFunc,\n      true,\n    );\n    document.documentElement.removeEventListener(\n      'touchmove',\n      this.mouseMoveFunc,\n      true,\n    );\n    document.documentElement.removeEventListener(\n      'mouseleave',\n      this.mouseUpFunc,\n    );\n  }\n}\n", "import { type FunctionalComponent, h, type VNode } from '@stencil/core';\nimport {\n  ResizeProps,\n  ResizeDirective,\n  ResizeEvents,\n} from './resizable.directive';\nimport type { CellProps } from '@type';\n\nexport type ResizableElementHTMLAttributes = Partial<ResizeProps> & CellProps;\n\nexport const ResizableElement: FunctionalComponent = (\n  props: ResizableElementHTMLAttributes,\n  children: VNode[],\n) => {\n  const resizeEls: VNode[] = [];\n  const directive =\n    (props.canResize &&\n      new ResizeDirective(props, e => {\n        if (e.eventName === ResizeEvents.end) {\n          props.onResize?.(e);\n        }\n      })) ||\n    null;\n\n  if (props.active) {\n    if (props.canResize) {\n      for (let p in props.active) {\n        resizeEls.push(\n          <div\n            onClick={e => e.preventDefault()}\n            onDblClick={e => {\n              e.preventDefault();\n              props.onDblClick?.(e);\n            }}\n            onMouseDown={(e: MouseEvent) => directive?.handleDown(e)}\n            onTouchStart={(e: TouchEvent) => directive?.handleDown(e)}\n            class={`resizable resizable-${props.active[p]}`}\n          />,\n        );\n      }\n    } else {\n      for (let p in props.active) {\n        resizeEls.push(\n          <div\n            onClick={e => e.preventDefault()}\n            onTouchStart={(e: TouchEvent) => e.preventDefault()}\n            onDblClick={e => {\n              e.preventDefault();\n              props.onDblClick?.(e);\n            }}\n            class={`no-resize resizable resizable-${props.active[p]}`}\n          />,\n        );\n      }\n    }\n  }\n  return (\n    <div {...props} ref={e => e && directive?.set(e)}>\n      {children}\n      {resizeEls}\n    </div>\n  );\n};\n", "import { type FunctionalComponent, h } from '@stencil/core';\nimport { dispatch } from '../../plugins/dispatcher';\nimport { doPropMerge } from '../data/column.service';\nimport {\n  ResizableElement,\n  ResizableElementHTMLAttributes,\n} from './resizable.element';\nimport { ColumnTemplateProp } from '@type';\n\nexport const ON_COLUMN_CLICK = 'columnclick';\n\nexport const HeaderCellRenderer: FunctionalComponent<{\n  props: ResizableElementHTMLAttributes;\n  additionalData: any;\n  data?: ColumnTemplateProp;\n}> = ({ data, props, additionalData }, children) => {\n  let colTemplate: ReturnType<typeof h> = data?.name || '';\n  let cellProps = props;\n  if (data?.columnTemplate) {\n    colTemplate = data.columnTemplate(h, data, additionalData);\n  }\n  if (data?.columnProperties) {\n    const extra = data.columnProperties(data);\n    if (extra) {\n      cellProps = doPropMerge(props, extra);\n    }\n  }\n  const resizeProps = {\n    ...cellProps,\n    onMouseDown(e: MouseEvent) {\n      dispatch(e.currentTarget, ON_COLUMN_CLICK, {\n        data,\n        event: e,\n      });\n    },\n  };\n  return (\n    <ResizableElement {...resizeProps}>\n      <div class=\"header-content\">{colTemplate}</div>\n      {children}\n    </ResizableElement>\n  );\n};\n", "import getNative from './_getNative.js';\n\nvar defineProperty = (function() {\n  try {\n    var func = getNative(Object, 'defineProperty');\n    func({}, '', {});\n    return func;\n  } catch (e) {}\n}());\n\nexport default defineProperty;\n", "import defineProperty from './_defineProperty.js';\n\n/**\n * The base implementation of `assignValue` and `assignMergeValue` without\n * value checks.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction baseAssignValue(object, key, value) {\n  if (key == '__proto__' && defineProperty) {\n    defineProperty(object, key, {\n      'configurable': true,\n      'enumerable': true,\n      'value': value,\n      'writable': true\n    });\n  } else {\n    object[key] = value;\n  }\n}\n\nexport default baseAssignValue;\n", "/**\n * A specialized version of `baseAggregator` for arrays.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform keys.\n * @param {Object} accumulator The initial aggregated object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction arrayAggregator(array, setter, iteratee, accumulator) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    var value = array[index];\n    setter(accumulator, value, iteratee(value), array);\n  }\n  return accumulator;\n}\n\nexport default arrayAggregator;\n", "import baseEach from './_baseEach.js';\n\n/**\n * Aggregates elements of `collection` on `accumulator` with keys transformed\n * by `iteratee` and values set by `setter`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform keys.\n * @param {Object} accumulator The initial aggregated object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction baseAggregator(collection, setter, iteratee, accumulator) {\n  baseEach(collection, function(value, key, collection) {\n    setter(accumulator, value, iteratee(value), collection);\n  });\n  return accumulator;\n}\n\nexport default baseAggregator;\n", "import arrayAggregator from './_arrayAggregator.js';\nimport baseAggregator from './_baseAggregator.js';\nimport baseIteratee from './_baseIteratee.js';\nimport isArray from './isArray.js';\n\n/**\n * Creates a function like `_.groupBy`.\n *\n * @private\n * @param {Function} setter The function to set accumulator values.\n * @param {Function} [initializer] The accumulator object initializer.\n * @returns {Function} Returns the new aggregator function.\n */\nfunction createAggregator(setter, initializer) {\n  return function(collection, iteratee) {\n    var func = isArray(collection) ? arrayAggregator : baseAggregator,\n        accumulator = initializer ? initializer() : {};\n\n    return func(collection, setter, baseIteratee(iteratee, 2), accumulator);\n  };\n}\n\nexport default createAggregator;\n", "import baseAssignValue from './_baseAssignValue.js';\nimport createAggregator from './_createAggregator.js';\n\n/**\n * Creates an object composed of keys generated from the results of running\n * each element of `collection` thru `iteratee`. The corresponding value of\n * each key is the last element responsible for generating the key. The\n * iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee to transform keys.\n * @returns {Object} Returns the composed aggregate object.\n * @example\n *\n * var array = [\n *   { 'dir': 'left', 'code': 97 },\n *   { 'dir': 'right', 'code': 100 }\n * ];\n *\n * _.keyBy(array, function(o) {\n *   return String.fromCharCode(o.code);\n * });\n * // => { 'a': { 'dir': 'left', 'code': 97 }, 'd': { 'dir': 'right', 'code': 100 } }\n *\n * _.keyBy(array, 'dir');\n * // => { 'left': { 'dir': 'left', 'code': 97 }, 'right': { 'dir': 'right', 'code': 100 } }\n */\nvar keyBy = createAggregator(function(result, value, key) {\n  baseAssignValue(result, key, value);\n});\n\nexport default keyBy;\n", "import { h } from '@stencil/core';\nimport type {\n  VirtualPositionItem,\n  ColumnTemplateProp,\n  InitialHeaderClick,\n  RangeArea\n} from '@type';\n\nimport { FilterButton } from '../../plugins/filter/filter.button';\nimport { SortingSign } from '../../plugins/sorting/sorting.sign';\nimport { ResizeEvent, ResizeProps } from './resizable.directive';\nimport {\n  DATA_COL,\n  FOCUS_CLASS,\n  HEADER_CLASS,\n  HEADER_SORTABLE_CLASS,\n  MIN_COL_SIZE,\n} from '../../utils/consts';\nimport { HeaderCellRenderer } from './header-cell-renderer';\nimport { ResizableElementHTMLAttributes } from './resizable.element';\n\nexport type HeaderRenderProps = {\n  column: VirtualPositionItem;\n  additionalData: any;\n  data: ColumnTemplateProp;\n  range?: RangeArea | null;\n  canResize?: boolean;\n  canFilter?: boolean;\n  onResize?(e: ResizeEvent): void;\n  onClick?(data: InitialHeaderClick): void;\n  onDblClick?(data: InitialHeaderClick): void;\n} & Partial<Pick<ResizeProps, 'active'>>;\n\nconst HeaderRenderer = (p: HeaderRenderProps): ReturnType<typeof h> => {\n  const cellClass: { [key: string]: boolean } = {\n    [HEADER_CLASS]: true,\n    [HEADER_SORTABLE_CLASS]: !!p.data?.sortable,\n  };\n  if (p.data?.order) {\n    cellClass[p.data.order] = true;\n  }\n  const dataProps: ResizableElementHTMLAttributes = {\n    [DATA_COL]: p.column.itemIndex,\n    canResize: p.canResize,\n    minWidth: p.data?.minSize || MIN_COL_SIZE,\n    maxWidth: p.data?.maxSize,\n    active: p.active || ['r'],\n    class: cellClass,\n    style: {\n      width: `${p.column.size}px`,\n      transform: `translateX(${p.column.start}px)`,\n    },\n    onResize: p.onResize,\n    onDblClick(originalEvent: MouseEvent) {\n      p.onDblClick?.({\n        column: p.data,\n        index: p.column.itemIndex,\n        originalEvent,\n        providers: p.data.providers,\n      });\n    },\n    onClick(originalEvent: MouseEvent) {\n      if (originalEvent.defaultPrevented || !p.onClick) {\n        return;\n      }\n      p.onClick({\n        column: p.data,\n        index: p.column.itemIndex,\n        originalEvent,\n        providers: p.data.providers,\n      });\n    },\n  };\n  if (p.range) {\n    if (p.column.itemIndex >= p.range.x && p.column.itemIndex <= p.range.x1) {\n      if (typeof dataProps.class === 'object') {\n        dataProps.class[FOCUS_CLASS] = true;\n      }\n    }\n  }\n  return (\n    <HeaderCellRenderer\n      data={p.data}\n      props={dataProps}\n      additionalData={p.additionalData}\n    >\n      {<SortingSign column={p.data} />}\n      {p.canFilter && p.data?.filter !== false ? (\n        <FilterButton column={p.data} />\n      ) : (\n        ''\n      )}\n    </HeaderCellRenderer>\n  );\n};\n\nexport default HeaderRenderer;\n", "import { h } from '@stencil/core';\nimport { Group } from '@store';\nimport type { CellProps, ProvidersColumns } from '@type';\nimport { ResizeEvent, ResizeProps } from './resizable.directive';\nimport { HEADER_CLASS, MIN_COL_SIZE } from '../../utils/consts';\nimport { HeaderCellRenderer } from './header-cell-renderer';\n\nexport type HeaderGroupRendererProps = {\n  start: number;\n  end: number;\n  group: Group;\n  providers: ProvidersColumns;\n  additionalData: any;\n  canResize?: boolean;\n  onResize?(e: ResizeEvent): void;\n} & Partial<Pick<ResizeProps, 'active'>>;\n\nconst HeaderGroupRenderer = (p: HeaderGroupRendererProps): ReturnType<typeof h> => {\n  const groupProps: CellProps & Partial<ResizeProps> = {\n    canResize: p.canResize,\n    minWidth: p.group.indexes.length * MIN_COL_SIZE,\n    maxWidth: 0,\n\n    active: p.active || ['r'],\n    class: {\n      [HEADER_CLASS]: true,\n    },\n    style: {\n      transform: `translateX(${p.start}px)`,\n      width: `${p.end - p.start}px`,\n    },\n    onResize: p.onResize,\n  };\n  return (\n    <HeaderCellRenderer\n      data={{\n        ...p.group,\n        prop: '',\n        providers: p.providers,\n        index: p.start,\n      }}\n      props={groupProps}\n      additionalData={p.additionalData}\n    />\n  );\n};\n\nexport default HeaderGroupRenderer;\n", "revogr-header {\n  position: relative;\n  z-index: 5;\n  display: block;\n  \n\n  .rgHeaderCell {\n    display: flex;\n\n    &.align-center {\n      text-align: center;\n    }\n    &.align-left {\n      text-align: left;\n    }\n    &.align-right {\n      text-align: right;\n    }\n    &.sortable {\n      cursor: pointer;\n    }\n\n    i {\n      &.asc,\n      &.desc {\n        &:after {\n          font-size: 13px;\n        }\n      }\n      &.asc {\n        &:after {\n          content: '↑';\n        }\n      }\n      &.desc {\n        &:after {\n          content: '↓';\n        }\n      }\n    }\n  }\n\n  .rgHeaderCell,\n  .grouped-cell {\n    position: absolute;\n    box-sizing: border-box;\n    height: 100%;\n    z-index: 1;\n  }\n\n  .header-rgRow {\n    display: block;\n    position: relative;\n\n    &.group {\n      z-index: 0;\n    }\n  }\n\n  .group-rgRow {\n    position: relative;\n  }\n\n  .rgHeaderCell {\n    &.active {\n      z-index: 10;\n\n      .resizable {\n        background-color: deepskyblue;\n      }\n    }\n    .header-content {\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      flex-grow: 1;\n    }\n    .resizable {\n      display: block;\n      position: absolute;\n      z-index: 90;\n      touch-action: none;\n      user-select: none;\n      &:hover {\n        background-color: deepskyblue;\n      }\n    }\n    $size: 6px;\n    > .resizable-r {\n      cursor: ew-resize;\n      width: $size;\n      right: 0;\n      top: 0;\n      height: 100%;\n    }\n    > .resizable-rb {\n      cursor: se-resize;\n      width: $size;\n      height: $size;\n      right: 0;\n      bottom: 0;\n    }\n\n    > .resizable-b {\n      cursor: s-resize;\n      height: $size;\n      bottom: 0;\n      width: 100%;\n      left: 0;\n    }\n\n    > .resizable-lb {\n      cursor: sw-resize;\n      width: $size;\n      height: $size;\n      left: 0;\n      bottom: 0;\n    }\n\n    > .resizable-l {\n      cursor: w-resize;\n      width: $size;\n      left: 0;\n      height: 100%;\n      top: 0;\n    }\n\n    > .resizable-lt {\n      cursor: nw-resize;\n      width: $size;\n      height: $size;\n      left: 0;\n      top: 0;\n    }\n\n    > .resizable-t {\n      cursor: n-resize;\n      height: $size;\n      top: 0;\n      width: 100%;\n      left: 0;\n    }\n\n    > .resizable-rt {\n      cursor: ne-resize;\n      width: $size;\n      height: $size;\n      right: 0;\n      top: 0;\n    }\n  }\n  .rv-filter {\n    visibility: hidden;\n  }\n}\n", "import {\n  Component,\n  Element,\n  Event,\n  EventEmitter,\n  h,\n  Prop,\n  type VNode,\n} from '@stencil/core';\nimport keyBy from 'lodash/keyBy';\n\nimport { getItemByIndex, Groups } from '@store';\nimport { HEADER_ACTUAL_ROW_CLASS, HEADER_ROW_CLASS } from '../../utils/consts';\nimport HeaderRenderer, { HeaderRenderProps } from './header-renderer';\nimport { ResizeProps } from './resizable.directive';\nimport type {\n  ColumnRegular,\n  DimensionSettingsState,\n  InitialHeaderClick,\n  ViewportState,\n  ViewSettingSizeProp,\n  DimensionCols,\n  SelectionStoreState,\n  RangeArea,\n  VirtualPositionItem,\n  ProvidersColumns,\n} from '@type';\nimport type { Observable } from '../../utils';\nimport GroupHeaderRenderer, {\n  HeaderGroupRendererProps,\n} from './header-group-renderer';\n\n@Component({\n  tag: 'revogr-header',\n  styleUrl: 'revogr-header-style.scss',\n})\nexport class RevogrHeaderComponent {\n  // #region Properties\n  /**\n   * Stores\n   */\n  /** Viewport X */\n  @Prop() viewportCol: Observable<ViewportState>;\n  /** Dimension settings X */\n  @Prop() dimensionCol: Observable<DimensionSettingsState>;\n  /** Selection, range, focus */\n  @Prop() selectionStore: Observable<SelectionStoreState>;\n\n  /**\n   * Column groups\n   */\n  @Prop() groups: Groups;\n  /**\n   * Grouping depth, how many levels of grouping\n   */\n  @Prop() groupingDepth = 0;\n\n  /**\n   * Readonly mode\n   */\n  @Prop() readonly: boolean;\n  /**\n   * If columns can be resized\n   */\n  @Prop() canResize: boolean;\n  /**\n   * Defines resize position\n   */\n  @Prop() resizeHandler: ResizeProps['active'];\n\n  /**\n   * Columns - defines an array of grid columns.\n   */\n  @Prop() colData: ColumnRegular[];\n\n  /**\n   * Column filter\n   */\n  @Prop() columnFilter: boolean;\n\n  /**\n   * Column type\n   */\n  @Prop() type!: DimensionCols | 'rowHeaders';\n\n  /**\n   * Extra properties to pass into header renderer, such as vue or react components to handle parent\n   */\n  @Prop() additionalData: any = {};\n  // #endregion\n\n  // #region Events\n\n  /**\n   * On initial header click\n   */\n  @Event({\n    eventName: 'beforeheaderclick',\n  })\n  initialHeaderClick: EventEmitter<InitialHeaderClick>;\n\n  /**\n   * On header resize\n   */\n  @Event({\n    eventName: 'headerresize',\n  })\n  headerresize: EventEmitter<ViewSettingSizeProp>;\n\n  /**\n   * On before header resize\n   */\n  @Event({ eventName: 'beforeheaderresize', cancelable: true })\n  beforeResize: EventEmitter<ColumnRegular[]>;\n\n  /**\n   * On header double click\n   */\n  @Event({\n    eventName: 'headerdblclick',\n  })\n  headerdblClick: EventEmitter<InitialHeaderClick>;\n\n  /**\n   * Before each header cell render function. Allows to override cell properties\n   */\n  @Event({ eventName: 'beforeheaderrender' })\n  beforeHeaderRender: EventEmitter<HeaderRenderProps>;\n\n  /**\n   * Before each group header cell render function. Allows to override group header cell properties\n   */\n  @Event({ eventName: 'beforegroupheaderrender' })\n  beforeGroupHeaderRender: EventEmitter<HeaderGroupRendererProps>;\n\n  /**\n   * After all header cells rendered. Finalizes cell rendering.\n   */\n  @Event({ eventName: 'afterheaderrender' })\n  afterHeaderRender: EventEmitter<ProvidersColumns>;\n\n  // #endregion\n\n  @Element() element!: HTMLElement;\n\n  private onResize({ width }: { width?: number }, index: number) {\n    const col = this.colData[index];\n    const event = this.beforeResize.emit([\n      {\n        ...col,\n        size: width || undefined,\n      },\n    ]);\n    if (event.defaultPrevented) {\n      return;\n    }\n    this.headerresize.emit({ [index]: width || 0 });\n  }\n\n  private onResizeGroup(\n    changedX: number,\n    startIndex: number,\n    endIndex: number,\n  ) {\n    const sizes: ViewSettingSizeProp = {};\n    const cols = keyBy(this.viewportCol.get('items'), 'itemIndex');\n    const change = changedX / (endIndex - startIndex + 1);\n    for (let i = startIndex; i <= endIndex; i++) {\n      const item = cols[i];\n      if (item) {\n        sizes[i] = item.size + change;\n      }\n    }\n    this.headerresize.emit(sizes);\n  }\n\n  componentDidRender() {\n    this.afterHeaderRender.emit(this.providers);\n  }\n\n  render() {\n    const cols = this.viewportCol.get('items');\n    const range = this.selectionStore?.get('range');\n\n    const { cells } = this.renderHeaderColumns(cols, range);\n    const groupRow = this.renderGroupingColumns();\n\n    return [\n      <div class=\"group-rgRow\">{groupRow}</div>,\n      <div class={`${HEADER_ROW_CLASS} ${HEADER_ACTUAL_ROW_CLASS}`}>\n        {cells}\n      </div>,\n    ];\n  }\n\n  private renderHeaderColumns(\n    cols: VirtualPositionItem[],\n    range: RangeArea | null,\n  ) {\n    const cells: VNode[] = [];\n    for (let rgCol of cols) {\n      const colData = this.colData[rgCol.itemIndex];\n      const props: HeaderRenderProps = {\n        range,\n        column: rgCol,\n        data: {\n          ...colData,\n          index: rgCol.itemIndex,\n          providers: this.providers,\n        },\n        canFilter: !!this.columnFilter,\n        canResize: this.canResize,\n        active: this.resizeHandler,\n        additionalData: this.additionalData,\n        onResize: e => this.onResize(e, rgCol.itemIndex),\n        onDblClick: e => this.headerdblClick.emit(e),\n        onClick: e => this.initialHeaderClick.emit(e),\n      };\n      const event = this.beforeHeaderRender.emit(props);\n      if (!event.defaultPrevented) {\n        cells.push(<HeaderRenderer {...event.detail} />);\n      }\n    }\n    return { cells };\n  }\n\n  private renderGroupingColumns(): VNode[] {\n    const groupRow: VNode[] = [];\n    for (let i = 0; i < this.groupingDepth; i++) {\n      if (this.groups[i]) {\n        for (let group of this.groups[i]) {\n          const groupStartIndex = group.indexes[0] ?? -1;\n          if (groupStartIndex > -1) {\n            const groupEndIndex = groupStartIndex + group.indexes.length - 1;\n\n            const groupStart = getItemByIndex(\n              this.dimensionCol.state,\n              groupStartIndex,\n            ).start;\n            const groupEnd = getItemByIndex(\n              this.dimensionCol.state,\n              groupEndIndex,\n            ).end;\n\n            const props: HeaderGroupRendererProps = {\n              providers: this.providers,\n              start: groupStart,\n              end: groupEnd,\n              group,\n              active: this.resizeHandler,\n              canResize: this.canResize,\n              additionalData: this.additionalData,\n              onResize: e =>\n                this.onResizeGroup(\n                  e.changedX ?? 0,\n                  groupStartIndex,\n                  groupEndIndex,\n                ),\n            };\n            const event = this.beforeGroupHeaderRender.emit(props);\n            if (!event.defaultPrevented) {\n              groupRow.push(<GroupHeaderRenderer {...event.detail} />);\n            }\n          }\n        }\n      }\n      groupRow.push(<div class={`${HEADER_ROW_CLASS} group`} />);\n    }\n    return groupRow;\n  }\n\n  get providers(): ProvidersColumns<DimensionCols | 'rowHeaders'> {\n    return {\n      type: this.type,\n      readonly: this.readonly,\n      data: this.colData,\n      viewport: this.viewportCol,\n      dimension: this.dimensionCol,\n      selection: this.selectionStore,\n    };\n  }\n}\n"], "version": 3}