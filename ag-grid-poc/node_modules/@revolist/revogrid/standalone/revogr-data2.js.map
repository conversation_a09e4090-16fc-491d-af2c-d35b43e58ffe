{"file": "revogr-data2.js", "mappings": ";;;;;;;;;;AAYO,MAAM,aAAa,GAAG,EAAE;AAE/B,MAAM,WAAW,GAAG,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAY,EAAE,KAAc,KAAI;IACxF,MAAM,KAAK,GACN,MAAA,CAAA,MAAA,CAAA,EAAE,CAAC,QAAQ,GAAG,KAAK,EAAE,CACzB;AACD,IAAA,QACE,CAAA,CAAA,KAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACM,KAAK,EAAA,EACT,KAAK,EAAE,CAAS,MAAA,EAAA,QAAQ,IAAI,EAAE,CAAE,CAAA,EAChC,KAAK,EAAE;YACL,MAAM,EAAE,CAAG,EAAA,IAAI,CAAI,EAAA,CAAA;YACnB,SAAS,EAAE,CAAc,WAAA,EAAA,KAAK,CAAK,GAAA,CAAA;AACnC,YAAA,WAAW,EAAE,KAAK,GAAG,CAAA,EAAG,aAAa,GAAG,KAAK,CAAA,EAAA,CAAI,GAAG,SAAS;SAC9D,EAEA,CAAA,EAAA,KAAK,CACF;AAEV,CAAC;;SCnBe,WAAW,CACzB,CAAa,EACb,KAAe,EACf,YAAoB,EAAA;;AAEpB,IAAA,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,kBAAkB,EAAE;AAChD,QAAA,MAAM,EAAE;YACN,KAAK;YACL,YAAY;AACb,SAAA;AACD,QAAA,UAAU,EAAE,IAAI;AAChB,QAAA,OAAO,EAAE,IAAI;AACd,KAAA,CAAiE;IAClE,CAAA,EAAA,GAAA,CAAC,CAAC,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,aAAa,CAAC,KAAK,CAAC;AAChC;AAEa,MAAA,mBAAmB,GAAG,CAAC,KAAuB,KAAI;IAC7D,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,sBAAsB,EAAE,GAAG,KAAK;AACrE,IAAA,MAAM,IAAI,GAAG,KAAK,CAAC,iBAAiB,CAAC;AACrC,IAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,CAAC;AACtC,IAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;IAEnD,IAAI,sBAAsB,EAAE;AAC1B,QAAA,QACE,CAAA,CAAC,WAAW,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAK,KAAK,EAAA,EAAE,QAAQ,EAAC,aAAa,EAAC,KAAK,EAAE,KAAK,EAAA,CAAA;AACzD,YAAA,CAAA,CAAA,KAAA,EAAA,EAAK,OAAO,EAAE,CAAC,IAAI,WAAW,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,EAChD,EAAA,sBAAsB,CAAC,CAAC,EACpB,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAK,CACR,EAAA,EAAA,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO,EAChC,IAAI;gBACJ,QAAQ;AACR,gBAAA,KAAK,EACL,CAAA,CAAA,CACE,CACM;;AAIlB,IAAA,QACE,CAAA,CAAC,WAAW,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAK,KAAK,EAAE,EAAA,QAAQ,EAAC,aAAa,EAAC,KAAK,EAAE,KAAK,EAAA,CAAA,EACxD,SAAS,IAAI;AACZ,QAAA,CAAA,CAAA,QAAA,EAAA,EACE,KAAK,EAAE,EAAE,CAAC,gBAAgB,GAAG,IAAI,EAAE,EACnC,OAAO,EAAE,CAAC,IAAI,WAAW,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,EAE7C,EAAA,kBAAkB,CAAC,QAAQ,CAAC,CACtB;QACT,IAAI;AACL,KAAA,CACW;AAElB;MAEa,kBAAkB,GAAG,CAAC,QAAQ,GAAG,KAAK,KAAI;AACrD,IAAA,QACE,CAAA,CAAA,KAAA,EAAA,EAAA,aAAA,EACc,MAAM,EAClB,KAAK,EAAE,EAAE,SAAS,EAAE,CAAA,OAAA,EAAU,CAAC,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAM,IAAA,CAAA,EAAE,EACzD,SAAS,EAAC,OAAO,EACjB,OAAO,EAAC,aAAa,EAAA;QAErB,CACE,CAAA,MAAA,EAAA,EAAA,IAAI,EAAC,cAAc,EACnB,CAAC,EAAC,yRAAyR,EAAA,CACrR,CACJ;AAEV;;AC1DA,SAAS,UAAU,CAAC,CAAc,EAAA;;IAChC,MAAM,GAAG,GAAuB,EAAE;;IAGlC,MAAM,QAAQ,GAAG,CAAA,EAAA,GAAA,CAAC,CAAC,WAAW,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,YAAY;IACnD,IAAI,QAAQ,EAAE;AACZ,QAAA,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC;;;;SAKnD;AACH,QAAA,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE;;YAEzB,IAAI,KAAK,aAAL,KAAK,KAAA,MAAA,GAAA,MAAA,GAAL,KAAK,CAAE,KAAK,EAAE;gBAChB,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC,CAAC,WAAW,CAAC;;AAE7D,YAAA,OAAO,EAAE;;;AAIX,QAAA,IACE,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO;AAC5B,YAAA,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,CAAC,EAC7D;AACA,YAAA,GAAG,CAAC,IAAI,CACN,CAAA,CAAA,MAAA,EAAA,EACE,KAAK,EAAE,eAAe,EACtB,WAAW,EAAE,aAAa,IAAG;;AAC3B,oBAAA,OAAA,MAAA,CAAC,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,IAAI,CAAC;wBACpB,aAAa;wBACb,KAAK,EAAE,CAAC,CAAC,WAAW;AACrB,qBAAA,CAAC;AAAA,iBAAA,EAAA;AAGJ,gBAAA,CAAA,CAAA,MAAA,EAAA,EAAM,KAAK,EAAE,eAAe,EAAI,CAAA,CAC3B,CACR;;QAGH,GAAG,CAAC,IAAI,CAAC,CAAA,EACP,iBAAiB,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,CAC7D,CAAA,CAAE,CAAC;;AAEL,IAAA,OAAO,GAAG;AACZ;AAEa,MAAA,YAAY,GAAG,CAAC,EAC3B,WAAW,EACX,SAAS,GAIV,KAAW;IACV,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC;AACjD,IAAA,QACE,CAAA,CAAA,KAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAS,SAAS,EAAA,EAAE,MAAM,EAAE,MAAM,EAAA,CAAA,EAC/B,MAAM,EAAE,CACL;AAEV;;AC7EA;;AAEG;MACU,kBAAkB,CAAA;AAA/B,IAAA,WAAA,GAAA;QACU,IAAY,CAAA,YAAA,GAAqB,IAAI;;IAC7C,eAAe,CAAC,CAAY,EAAE,YAAgC,EAAA;;AAE5D,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,YAAY,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,KAAI;;;AAE9B,gBAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE;oBAC9B;;;AAIF,gBAAA,IACE,GAAG;oBACH,GAAG,CAAC,KAAK,YAAY,WAAW;oBAChC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAC/C;oBACA,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC;AAC7C,oBAAA,IAAI,CAAA,EAAA,GAAA,GAAG,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;AAClD,wBAAA,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAC3C,iBAAiB,EACjB,EAAE,CACH;;;AAGP,aAAC,CAAC;;;QAIJ,IAAI,CAAC,EAAE;AACL,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;gBAChC,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/B,gBAAA,IACE,GAAG;oBACH,GAAG,CAAC,KAAK,YAAY,WAAW;oBAChC,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAChD;AACA,oBAAA,MAAM,KAAK,IAAI,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,IAAI,EAAE,CAAC;AAC/C,oBAAA,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,IAAI,GAAG,GAAG,iBAAiB;oBAC3D,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC;;;;AAIhD,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC;;AAGvB,IAAA,YAAY,CAAC,CAAS,EAAA;QACpB,QACE,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE;;AAG/E;;ACvDD;;AAEG;AACa,SAAA,kBAAkB,CAAC,YAAqB,EAAE,MAA+B,EAAA;AACvF,IAAA,OAAO,IAAI,OAAO,CAA6C,OAAO,IAAG;QACvE,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC;AAClD,QAAA,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC;AAC/B,QAAA,KAAK,CAAC,MAAM,GAAG,MAAM;AACrB,QAAA,KAAK,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAG;YACjC,KAAK,CAAC,MAAM,EAAE;AACd,YAAA,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;AACnB,SAAC,CAAC;AACJ,KAAC,CAAC;AACJ;;AChBA,MAAM,kBAAkB,GAAG,wuOAAwuO;;MCkDtvO,UAAU,iBAAAA,kBAAA,CAAA,MAAA,UAAA,SAAAC,aAAA,CAAA;AAJvB,IAAA,WAAA,GAAA;;;;;;;;AAqDE;;;;AAIG;AACK,QAAA,IAAgB,CAAA,gBAAA,GAA4C,EAAE;AAoDtE;;AAEG;AACK,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,GAAG,EAAiB;AA0MhD;AApOC;;AAEG;IACO,MAAM,UAAU,CAAC,CAG1B,EAAA;;;QAEC,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,UAAU,0CAAG,CAAC,CAAC,GAAG,CAAC;AAC9D,QAAA,IAAI,CAAA,EAAA,GAAA,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAA,MAAA,GAAA,MAAA,GAAJ,IAAI,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,MAAM,EAAE;AACzB,YAAA,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CACvC,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CAAC,MAAM,CACpB;YACD,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,IAAI;AACpC,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;;;IAcV,iBAAiB,GAAA;QACnC,IAAI,CAAC,aAAa,EAAE;;IAEJ,eAAe,GAAA;QAC/B,IAAI,CAAC,aAAa,EAAE;;IAEtB,aAAa,GAAA;;QACX,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,OAAO,EAAE;AAC7B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC;;QAEpE,IAAI,CAAC,SAAS,GAAG;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,SAAS;YACpB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,WAAW;YAC1B,SAAS,EAAE,IAAI,CAAC,YAAY;YAC5B,SAAS,EAAE,IAAI,CAAC,iBAAiB;SAClC;QAED,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAI;AACzB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CACrD,OAAO,EACP,CAAC,CAAY,KACX,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAChE;;IAGH,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,EAAE;QAClD,IAAI,CAAC,aAAa,EAAE;;IAGtB,oBAAoB,GAAA;;QAClB,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,OAAO,EAAE;QAC7B,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAI;;AAG3B,IAAA,MAAM,mBAAmB,GAAA;AACvB,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YACzB,OAAO,EAAE,IAAI,CAAC,IAAI;YAClB,OAAO,EAAE,IAAI,CAAC,OAAwB;AACvC,SAAA,CAAC;AACF,QAAA,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAGvF,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;;IAG5C,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE;AAC7B,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO;AAC9C,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACvB;;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC;AAC1C,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB;;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC;AAC1C,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB;;QAEF,MAAM,OAAO,GAAY,EAAE;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC;QACjD,MAAM,sBAAsB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,wBAAwB,CAAC;AAC3E,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC;AAC7D,QAAA,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;AACtB,YAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC;;AAG/D,YAAA,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;gBACxB,MAAM,MAAM,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACP,KAAK,CAAA,EAAA,EACR,KAAK,EAAE,KAAK,CAAC,SAAS,EACtB,KAAK,EAAE,QAAQ,EACf,sBAAsB;;oBAEtB,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,KAAK,YAAY,EAC1E,WAAW,EAAE,IAAI,EACjB,SAAS,EAAE,IAAI,CAAC,SAAS,EAAA,CAC1B;AACD,gBAAA,OAAO,CAAC,IAAI,CAAC,CAAA,CAAC,mBAAmB,EAAK,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,MAAM,CAAI,CAAA,CAAC;gBACjD;;;YAGF,MAAM,KAAK,GAA8B,EAAE;;AAG3C,YAAA,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;AACtB,gBAAA,MAAM,MAAM,mCACP,IAAI,CAAC,aAAa,CAAC,YAAY,CAChC,KAAK,CAAC,SAAS,EACf,KAAK,CAAC,SAAS,CAChB,CACD,EAAA,EAAA,SAAS,EAAE,IAAI,CAAC,SAAS,EAAA,CAC1B;;AAGD,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;;AAGpE,gBAAA,IAAI,SAAS,CAAC,gBAAgB,EAAE;oBAC9B;;AAGF,gBAAA,MAAM,EACJ,MAAM,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,GACnE,GAAG,SAAS;AAEb,gBAAA,MAAM,YAAY,GAAc;AAC9B,oBAAA,CAAC,QAAQ,GAAG,WAAW,CAAC,SAAS;AACjC,oBAAA,CAAC,QAAQ,GAAG,QAAQ,CAAC,SAAS;AAC9B,oBAAA,KAAK,EAAE;AACL,wBAAA,KAAK,EAAE,CAAA,EAAG,WAAW,CAAC,IAAI,CAAI,EAAA,CAAA;AAC9B,wBAAA,SAAS,EAAE,CAAA,WAAA,EAAc,WAAW,CAAC,KAAK,CAAK,GAAA,CAAA;AAC/C,wBAAA,MAAM,EAAE,QAAQ,CAAC,IAAI,GAAG,CAAG,EAAA,QAAQ,CAAC,IAAI,CAAA,EAAA,CAAI,GAAG,SAAS;AACzD,qBAAA;iBACF;AACD;;AAEG;gBACH,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC,SAAS,IAAI,YAAY,CAAC,KAAK,EAAE;oBAC9D,YAAY,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,aAAa,GAAG,UAAU,CAAA,EAAA,CAAI;;gBAGpE,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAC9C,QAAQ,CAAC,SAAS,EAClB,WAAW,CAAC,SAAS,EACrB,YAAY,EACZ,WAAW,CACZ;;;gBAID,MAAM,QAAQ,GAAG,CAAA,CAAC,YAAY,EAAA,EAC5B,WAAW,EAAE;wBACX,WAAW;wBACX,cAAc,EAAE,IAAI,CAAC,cAAc;wBACnC,aAAa,EAAE,IAAI,CAAC,aAAa;AAClC,qBAAA,EACD,SAAS,EAAE,KAAK,EAAA,CAChB;AACF,gBAAA,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;;;;AAKtB,YAAA,IAAI,QAAQ,GAAG,IAAI,CAAC;AAClB,kBAAE,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ;kBAC7D,EAAE;YACN,IAAI,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;AACzD,gBAAA,QAAQ,IAAI,CAAA,CAAA,EAAI,iBAAiB,CAAA,CAAE;;AAErC,YAAA,MAAM,GAAG,IACP,CAAA,CAAC,WAAW,EAAA,EACV,KAAK,EAAE,KAAK,CAAC,SAAS,EACtB,QAAQ,EAAE,QAAQ,EAClB,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,KAAK,EAAE,KAAK,CAAC,KAAK,EAAA,EAEjB,KAAK,CACM,CACf;AACD,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AACxB,gBAAA,IAAI,EAAE,GAAG;AACT,gBAAA,IAAI,EAAE,KAAK;AACX,gBAAA,KAAK,EAAE,QAAQ;AACf,gBAAA,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;gBAChC,OAAO,EAAE,IAAI,CAAC,IAAI;AACnB,aAAA,CAAC;AACF,YAAA,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;YACjB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC;;;AAG7C,QAAA,QACE,EAAC,IAAI,EAAA,IAAA,EACH,CAAQ,CAAA,MAAA,EAAA,IAAA,CAAA,EACP,OAAO,CACH;;AAIX,IAAA,uBAAuB,CACrB,KAAuB,EACvB,GAAwB,EACxB,MAA2B,EAAA;AAE3B,QAAA,MAAM,MAAM,GAA4C;YACtD,MAAM;YACN,GAAG;YACH,KAAK;YACL,OAAO,EAAE,KAAK,CAAC,IAAI;YACnB,OAAO,EAAE,KAAK,CAAC,OAAO;SACvB;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement", "HTMLElement"], "sources": ["src/components/data/row-renderer.tsx", "src/plugins/groupingRow/grouping.row.renderer.tsx", "src/components/data/cell-renderer.tsx", "src/components/data/row-highlight.plugin.ts", "src/components/vnode/vnode.utils.ts", "src/components/data/revogr-data-style.scss?tag=revogr-data", "src/components/data/revogr-data.tsx"], "sourcesContent": ["import { h, type VNode } from '@stencil/core';\nimport { JSXBase } from '@stencil/core/internal';\nimport { DATA_ROW } from '../../utils/consts';\n\nexport interface RowProps extends JSXBase.HTMLAttributes {\n  size: number;\n  start: number;\n  index: number;\n  rowClass?: string;\n  depth?: number;\n}\n\nexport const PADDING_DEPTH = 10;\n\nconst RowRenderer = ({ rowClass, index, size, start, depth }: RowProps, cells: VNode[]) => {\n  const props = {\n    ...{ [DATA_ROW]: index },\n  };\n  return (\n    <div\n      {...props}\n      class={`rgRow ${rowClass || ''}`}\n      style={{\n        height: `${size}px`,\n        transform: `translateY(${start}px)`,\n        paddingLeft: depth ? `${PADDING_DEPTH * depth}px` : undefined,\n      }}\n    >\n      {cells}\n    </div>\n  );\n};\n\nexport default RowRenderer;\n", "import { h } from '@stencil/core';\nimport RowRenderer from '../../components/data/row-renderer';\nimport {\n  GROUP_DEPTH,\n  GROUP_EXPANDED,\n  GROUP_EXPAND_BTN,\n  PSEUDO_GROUP_ITEM,\n  GROUP_EXPAND_EVENT,\n} from './grouping.const';\nimport type { RowGroupingProps } from './grouping.row.types';\nimport type { DataType } from '@type';\n\nexport function expandEvent(\n  e: MouseEvent,\n  model: DataType,\n  virtualIndex: number,\n) {\n  const event = new CustomEvent(GROUP_EXPAND_EVENT, {\n    detail: {\n      model,\n      virtualIndex,\n    },\n    cancelable: true,\n    bubbles: true,\n  }) as CustomEvent<HTMLRevoGridElementEventMap['groupexpandclick']>;\n  e.target?.dispatchEvent(event);\n}\n\nexport const GroupingRowRenderer = (props: RowGroupingProps) => {\n  const { model, itemIndex, hasExpand, groupingCustomRenderer } = props;\n  const name = model[PSEUDO_GROUP_ITEM];\n  const expanded = model[GROUP_EXPANDED];\n  const depth = parseInt(model[GROUP_DEPTH], 10) || 0;\n\n  if (groupingCustomRenderer) {\n    return (\n      <RowRenderer {...props} rowClass=\"groupingRow\" depth={depth}>\n        <div onClick={e => expandEvent(e, model, itemIndex)}>\n          {groupingCustomRenderer(h, {\n            ...props,\n            colType: props.providers.colType,\n            name,\n            expanded,\n            depth,\n          })}\n        </div>\n      </RowRenderer>\n    );\n  }\n\n  return (\n    <RowRenderer {...props} rowClass=\"groupingRow\" depth={depth}>\n      {hasExpand && [\n        <button\n          class={{ [GROUP_EXPAND_BTN]: true }}\n          onClick={e => expandEvent(e, model, itemIndex)}\n        >\n          {expandSvgIconVNode(expanded)}\n        </button>,\n        name,\n      ]}\n    </RowRenderer>\n  );\n};\n\nexport const expandSvgIconVNode = (expanded = false) => {\n  return (\n    <svg\n      aria-hidden=\"true\"\n      style={{ transform: `rotate(${!expanded ? -90 : 0}deg)` }}\n      focusable=\"false\"\n      viewBox=\"0 0 448 512\"\n    >\n      <path\n        fill=\"currentColor\"\n        d=\"M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z\"\n      ></path>\n    </svg>\n  );\n};\n\nexport default GroupingRowRenderer;\n", "import { h, type VNode, Build, EventEmitter } from '@stencil/core';\nimport {\n  DragStartEvent,\n  CellTemplateProp,\n} from '@type';\n\nimport {\n  DRAGGABLE_CLASS,\n  DRAG_ICON_CLASS,\n  getCellDataParsed,\n} from '../../utils';\n\nimport { isRowDragService } from './column.service';\n\ninterface RenderProps {\n  schemaModel: CellTemplateProp;\n  additionalData?: any;\n  dragStartCell?: EventEmitter<DragStartEvent>;\n}\n\n\nfunction renderCell(v: RenderProps) {\n  const els: (VNode | string)[] = [];\n\n  // #region Custom cell\n  const template = v.schemaModel.column?.cellTemplate;\n  if (template) {\n    els.push(template(h, v.schemaModel, v.additionalData));\n  }\n  // #endregion\n\n  // #region Regular cell\n  else {\n    if (!v.schemaModel.column) {\n      // something is wrong with data\n      if (Build?.isDev) {\n        console.error('Investigate column problem.', v.schemaModel);\n      }\n      return '';\n    }\n\n    // Row drag\n    if (\n      v.schemaModel.column.rowDrag &&\n      isRowDragService(v.schemaModel.column.rowDrag, v.schemaModel)\n    ) {\n      els.push(\n        <span\n          class={DRAGGABLE_CLASS}\n          onMouseDown={originalEvent =>\n            v.dragStartCell?.emit({\n              originalEvent,\n              model: v.schemaModel,\n            })\n          }\n        >\n          <span class={DRAG_ICON_CLASS} />\n        </span>,\n      );\n    }\n    \n    els.push(`${\n      getCellDataParsed(v.schemaModel.model, v.schemaModel.column)\n    }`);\n  }\n  return els;\n}\n\nexport const CellRenderer = ({\n  renderProps,\n  cellProps,\n}: {\n  renderProps: RenderProps;\n  cellProps: any;\n}): VNode => {\n  const render = renderCell.bind(null, renderProps);\n  return (\n    <div {...cellProps} redraw={render}>\n      {render()}\n    </div>\n  );\n};\n", "import { type VNode } from '@stencil/core';\nimport { ROW_FOCUSED_CLASS } from '../../utils/consts';\nimport { RangeArea } from '@type';\n\n/**\n * Class is responsible for highlighting rows in a table.\n */\nexport class RowHighlightPlugin {\n  private currentRange: RangeArea | null = null;\n  selectionChange(e: RangeArea, renderedRows: Map<number, VNode>) {\n    // clear previous range\n    if (this.currentRange) {\n      renderedRows.forEach((row, y) => {\n        // skip current range\n        if (e && y >= e.y && y <= e.y1) {\n          return;\n        }\n\n        // clear previous range\n        if (\n          row &&\n          row.$elm$ instanceof HTMLElement &&\n          row.$elm$.classList.contains(ROW_FOCUSED_CLASS)\n        ) {\n          row.$elm$.classList.remove(ROW_FOCUSED_CLASS);\n          if (row.$attrs$?.class.includes(ROW_FOCUSED_CLASS)) {\n            row.$attrs$.class = row.$attrs$.class.replace(\n              ROW_FOCUSED_CLASS,\n              '',\n            );\n          }\n        }\n      });\n    }\n\n    // apply new range\n    if (e) {\n      for (let y = e.y; y <= e.y1; y++) {\n        const row = renderedRows.get(y);\n        if (\n          row &&\n          row.$elm$ instanceof HTMLElement &&\n          !row.$elm$.classList.contains(ROW_FOCUSED_CLASS)\n        ) {\n          const attrs = (row.$attrs$ = row.$attrs$ || {});\n          attrs.class = (attrs.class || '') + ' ' + ROW_FOCUSED_CLASS;\n          row.$elm$.classList.add(ROW_FOCUSED_CLASS);\n        }\n      }\n    }\n    this.currentRange = e;\n  }\n\n  isRowFocused(y: number) {\n    return (\n      this.currentRange && y >= this.currentRange.y && y <= this.currentRange.y1\n    );\n  }\n}\n", "import { type VNode } from '@stencil/core';\nimport { type JSX } from '../../components';\n\n/**\n * Converts a VNode element into an HTML element and appends it to the specified parentHolder.\n */\nexport function convertVNodeToHTML(parentHolder: Element, redraw: JSX.VnodeHtml['redraw']): Promise<{ html: string; vnodes: (VNode[]) | null }> {\n  return new Promise<{ html: string; vnodes: (VNode[]) | null }>(resolve => {\n    const vnode = document.createElement('vnode-html');\n    parentHolder.appendChild(vnode);\n    vnode.redraw = redraw;\n    vnode.addEventListener('html', e => {\n      vnode.remove();\n      resolve(e.detail);\n    });\n  });\n}\n", "revogr-data {\n  display: block;\n  width: 100%;\n  position: relative;\n\n  .rgRow {\n    position: absolute;\n    width: 100%;\n    left: 0;\n\n    &.groupingRow {\n      font-weight: 600;\n      text-align: left;\n\n      .group-expand {\n        width: 25px;\n        height: 100%;\n        max-height: 25px;\n        margin-right: 2px;\n        background-color: transparent;\n        border-color: transparent;\n        vertical-align: middle;\n        padding-left: 5px;\n        display: inline-flex;\n\n        svg {\n          width: 7px;\n        }\n      }\n    }\n  }\n\n  .revo-draggable {\n    $w: 24px;\n\n    border: none;\n    height: 32px;\n    display: inline-flex;\n    outline: 0;\n    padding: 0;\n    font-size: 0.8125rem;\n    box-sizing: border-box;\n    align-items: center;\n    white-space: nowrap;\n    vertical-align: middle;\n    justify-content: center;\n    text-decoration: none;\n    width: $w;\n    height: 100%;\n    cursor: pointer;\n    display: inline-flex;\n\n    &:hover {\n      > .revo-drag-icon {\n        opacity: 1;\n        zoom: 1.2;\n        font-weight: 600;\n      }\n    }\n\n    > .revo-drag-icon {\n      pointer-events: none;\n      transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, zoom 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n    }\n  }\n\n  .rgCell {\n    top: 0;\n    left: 0;\n    position: absolute;\n    box-sizing: border-box;\n    height: 100%;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    outline: none;\n\n    &.align-center {\n      text-align: center;\n    }\n    &.align-left {\n      text-align: left;\n    }\n    &.align-right {\n      text-align: right;\n    }\n  }\n}\n", "import {\n  type VNode,\n  Component,\n  Host,\n  Watch,\n  Element,\n  Event,\n  Prop,\n  EventEmitter,\n  h,\n  Method,\n  State,\n} from '@stencil/core';\n\nimport ColumnService from './column.service';\nimport { DATA_COL, DATA_ROW, ROW_FOCUSED_CLASS } from '../../utils/consts';\n\nimport { type DSourceState, getSourceItem } from '@store';\nimport RowRenderer, { PADDING_DEPTH } from './row-renderer';\nimport GroupingRowRenderer from '../../plugins/groupingRow/grouping.row.renderer';\nimport { isGrouping } from '../../plugins/groupingRow/grouping.service';\nimport type { AllDimensionType, CellTemplateProp, DimensionCols, DimensionRows } from '@type';\nimport { RowHighlightPlugin } from './row-highlight.plugin';\nimport { convertVNodeToHTML } from '../vnode/vnode.utils';\nimport { CellRenderer } from './cell-renderer';\nimport type {\n  ViewportState,\n  DimensionSettingsState,\n  BeforeRowRenderEvent,\n  Providers,\n  ColumnRegular,\n  DataType,\n  CellProps,\n  BeforeCellRenderEvent,\n  DragStartEvent,\n  VirtualPositionItem,\n  RangeArea,\n  SelectionStoreState,\n} from '@type';\nimport type { Observable } from '../../utils';\nimport type { RowGroupingProps } from '../../plugins';\n\n/**\n * This component is responsible for rendering data\n * Rows, columns, groups and cells\n */\n@Component({\n  tag: 'revogr-data',\n  styleUrl: 'revogr-data-style.scss',\n})\nexport class RevogrData {\n  // #region Properties\n  /**\n   * Readonly mode\n   */\n  @Prop() readonly: boolean;\n  /**\n   * Range allowed\n   */\n  @Prop() range: boolean;\n\n  /**\n   * Defines property from which to read row class\n   */\n  @Prop() rowClass: string;\n  /**\n   * Additional data to pass to renderer\n   * Used in plugins such as vue or react to pass root app entity to cells\n   */\n  @Prop() additionalData: any;\n  /** Stores */\n  /** Selection, range, focus for row selection */\n  @Prop() rowSelectionStore!: Observable<SelectionStoreState>;\n  /** Viewport Y */\n  @Prop() viewportRow!: Observable<ViewportState>;\n  /** Viewport X */\n  @Prop() viewportCol!: Observable<ViewportState>;\n  /** Dimension settings Y */\n  @Prop() dimensionRow!: Observable<DimensionSettingsState>;\n\n  /** Static stores, not expected to change during component lifetime */\n  /**\n   * Column source\n   */\n  @Prop() colData!: Observable<DSourceState<ColumnRegular, DimensionCols>>;\n  /**\n   * Data rows source\n   */\n  @Prop() dataStore!: Observable<DSourceState<DataType, DimensionRows>>;\n  /**\n   * Row data type\n   */\n  @Prop({ reflect: true }) type!: DimensionRows;\n\n  /**\n   * Column data type\n   */\n  @Prop({ reflect: true }) colType!: DimensionCols | 'rowHeaders';\n\n  /**\n   * Prevent rendering until job is done.\n   * Can be used for initial rendering performance improvement.\n   * When several plugins require initial rendering this will prevent double initial rendering.\n   */\n  @Prop() jobsBeforeRender: (Promise<any> | (() => Promise<any>))[] = [];\n  // #endregion\n\n  /**\n   * Before each row render\n   */\n  @Event() beforerowrender: EventEmitter<BeforeRowRenderEvent>;\n  \n  /**\n   * When data render finished for the designated type\n   */\n  @Event() afterrender: EventEmitter<{ type: DimensionRows }>;\n  /**\n   * Before each cell render function. Allows to override cell properties\n   */\n  @Event({ eventName: 'beforecellrender' })\n  beforeCellRender: EventEmitter<BeforeCellRenderEvent<CellTemplateProp>>;\n\n  /**\n   * Before data render\n   */\n  @Event({ eventName: 'beforedatarender' })\n  beforeDataRender: EventEmitter<AllDimensionType>;\n  /**\n   * Event emitted on cell drag start\n   */\n  @Event({ eventName: 'dragstartcell' })\n  dragStartCell: EventEmitter<DragStartEvent>;\n\n  /**\n   * Pointed cell update.\n   */\n  @Method() async updateCell(e: {\n    row: number; // virtual\n    col: number; // virtual\n  }) {\n    // Stencil tweak to update cell content\n    const cell = this.renderedRows.get(e.row)?.$children$?.[e.col];\n    if (cell?.$attrs$?.redraw) {\n      const children = await convertVNodeToHTML(\n        this.element,\n        cell.$attrs$.redraw,\n      );\n      cell.$elm$.innerHTML = children.html;\n      cell.$key$ = Math.random();\n    }\n  }\n\n  @Element() element!: Element;\n  @State() providers: Providers;\n  private columnService: ColumnService;\n  private rowHighlightPlugin: RowHighlightPlugin;\n  /**\n   * Rendered rows - virtual index vs vnode\n   */\n  private renderedRows = new Map<number, VNode>();\n  private rangeUnsubscribe: (() => void) | undefined;\n\n  @Watch('dataStore') onDataStoreChange() {\n    this.onStoreChange();\n  }\n  @Watch('colData') onColDataChange() {\n    this.onStoreChange();\n  }\n  onStoreChange() {\n    this.columnService?.destroy();\n    this.columnService = new ColumnService(this.dataStore, this.colData);\n    // make sure we have correct data, before render\n    this.providers = {\n      type: this.type,\n      colType: this.colType,\n      readonly: this.readonly,\n      data: this.dataStore,\n      columns: this.colData,\n      viewport: this.viewportCol,\n      dimension: this.dimensionRow,\n      selection: this.rowSelectionStore,\n    };\n\n    this.rangeUnsubscribe?.();\n    this.rangeUnsubscribe = this.rowSelectionStore.onChange(\n      'range',\n      (e: RangeArea) =>\n        this.rowHighlightPlugin.selectionChange(e, this.renderedRows),\n    );\n  }\n\n  connectedCallback() {\n    this.rowHighlightPlugin = new RowHighlightPlugin();\n    this.onStoreChange();\n  }\n\n  disconnectedCallback() {\n    this.columnService?.destroy();\n    this.rangeUnsubscribe?.();\n  }\n\n  async componentWillRender() {\n    this.beforeDataRender.emit({\n      rowType: this.type,\n      colType: this.colType as DimensionCols,\n    });\n    return Promise.all(this.jobsBeforeRender.map(p => typeof p === 'function' ? p() : p));\n  }\n\n  componentDidRender() {\n    this.afterrender.emit({ type: this.type });\n  }\n\n  render() {\n    this.renderedRows = new Map();\n    const columnsData = this.columnService.columns;\n    if (!columnsData.length) {\n      return;\n    }\n    const rows = this.viewportRow.get('items');\n    if (!rows.length) {\n      return;\n    }\n    const cols = this.viewportCol.get('items');\n    if (!cols.length) {\n      return;\n    }\n    const rowsEls: VNode[] = [];\n    const depth = this.dataStore.get('groupingDepth');\n    const groupingCustomRenderer = this.dataStore.get('groupingCustomRenderer');\n    const groupDepth = this.columnService.hasGrouping ? depth : 0;\n    for (let rgRow of rows) {\n      const dataItem = getSourceItem(this.dataStore, rgRow.itemIndex);\n\n      // #region Grouping\n      if (isGrouping(dataItem)) {\n        const gmodel: RowGroupingProps = {\n          ...rgRow,\n          index: rgRow.itemIndex,\n          model: dataItem,\n          groupingCustomRenderer,\n          // Only show expand button if grouping is enabled and not in row headers\n          hasExpand: this.columnService.hasGrouping && this.colType !== 'rowHeaders',\n          columnItems: cols,\n          providers: this.providers,\n        };\n        rowsEls.push(<GroupingRowRenderer {...gmodel} />);\n        continue;\n      }\n      // #endregion\n      const cells: (VNode | string | void)[] = [];\n\n      // #region Cells\n      for (let rgCol of cols) {\n        const smodel: CellTemplateProp = {\n          ...this.columnService.rowDataModel(\n            rgRow.itemIndex,\n            rgCol.itemIndex,\n          ),\n          providers: this.providers,\n        };\n\n        // call before cell render\n        const cellEvent = this.triggerBeforeCellRender(smodel, rgRow, rgCol);\n\n        // if event was prevented\n        if (cellEvent.defaultPrevented) {\n          continue;\n        }\n\n        const {\n          detail: { column: columnProps, row: rowProps, model: schemaModel },\n        } = cellEvent;\n\n        const defaultProps: CellProps = {\n          [DATA_COL]: columnProps.itemIndex,\n          [DATA_ROW]: rowProps.itemIndex,\n          style: {\n            width: `${columnProps.size}px`,\n            transform: `translateX(${columnProps.start}px)`,\n            height: rowProps.size ? `${rowProps.size}px` : undefined,\n          },\n        };\n        /**\n         * For grouping, can be removed in the future and replaced with event\n         */\n        if (groupDepth && !columnProps.itemIndex && defaultProps.style) {\n          defaultProps.style.paddingLeft = `${PADDING_DEPTH * groupDepth}px`;\n        }\n\n        const props = this.columnService.mergeProperties(\n          rowProps.itemIndex,\n          columnProps.itemIndex,\n          defaultProps,\n          schemaModel,\n        );\n\n        // Never use webcomponent for cell render\n        // It's very slow because of webcomponent initialization takes time\n        const cellNode = <CellRenderer\n          renderProps={{\n            schemaModel,\n            additionalData: this.additionalData,\n            dragStartCell: this.dragStartCell,\n          }}\n          cellProps={props}\n        />;\n        cells.push(cellNode);\n      }\n      // #endregion\n\n      // #region Rows\n      let rowClass = this.rowClass\n        ? this.columnService.getRowClass(rgRow.itemIndex, this.rowClass)\n        : '';\n      if (this.rowHighlightPlugin.isRowFocused(rgRow.itemIndex)) {\n        rowClass += ` ${ROW_FOCUSED_CLASS}`;\n      }\n      const row: VNode = (\n        <RowRenderer\n          index={rgRow.itemIndex}\n          rowClass={rowClass}\n          size={rgRow.size}\n          start={rgRow.start}\n        >\n          {cells}\n        </RowRenderer>\n      );\n      this.beforerowrender.emit({\n        node: row,\n        item: rgRow,\n        model: dataItem,\n        colType: this.columnService.type,\n        rowType: this.type,\n      });\n      rowsEls.push(row);\n      this.renderedRows.set(rgRow.itemIndex, row);\n      // #endregion\n    }\n    return (\n      <Host>\n        <slot />\n        {rowsEls}\n      </Host>\n    );\n  }\n\n  triggerBeforeCellRender(\n    model: CellTemplateProp,\n    row: VirtualPositionItem,\n    column: VirtualPositionItem,\n  ) {\n    const detail: BeforeCellRenderEvent<CellTemplateProp> = {\n      column,\n      row,\n      model,\n      rowType: model.type,\n      colType: model.colType,\n    };\n    return this.beforeCellRender.emit(detail);\n  }\n}\n"], "version": 3}