{"file": "column.service.js", "mappings": ";;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,EAAE,IAAI,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC9B,MAAM,SAAS,GAAG,MAAM,GAAG,CAAC;;AAE5B,EAAE,OAAO,MAAM,KAAK,MAAM,IAAI,SAAS,GAAG,MAAM,GAAG,SAAS,GAAG,MAAM,IAAI,CAAC;AAC1E;;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE;AAC9D,EAAE,IAAI,KAAK,GAAG,EAAE;AAChB,MAAM,MAAM,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM;;AAE/C,EAAE,IAAI,SAAS,IAAI,MAAM,EAAE;AAC3B,IAAI,WAAW,GAAG,KAAK,CAAC,EAAE,KAAK,CAAC;AAChC;AACA,EAAE,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE;AAC3B,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AACnE;AACA,EAAE,OAAO,WAAW;AACpB;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,SAAS,EAAE;AAClC,EAAE,OAAO,SAAS,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC9C,IAAI,IAAI,KAAK,GAAG,EAAE;AAClB,QAAQ,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC;AACjC,QAAQ,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC;AAChC,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM;;AAE7B,IAAI,OAAO,MAAM,EAAE,EAAE;AACrB,MAAM,IAAI,GAAG,GAAG,KAAK,CAAsB,EAAE,KAAK,CAAC;AACnD,MAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC,KAAK,KAAK,EAAE;AAC5D,QAAQ;AACR;AACA;AACA,IAAI,OAAO,MAAM;AACjB,GAAG;AACH;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,GAAG,aAAa,EAAE;;ACV7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE;AACtC,EAAE,OAAO,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC;AAClD;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE;AAC7C,EAAE,OAAO,SAAS,UAAU,EAAE,QAAQ,EAAE;AACxC,IAAI,IAAI,UAAU,IAAI,IAAI,EAAE;AAC5B,MAAM,OAAO,UAAU;AACvB;AACA,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE;AAClC,MAAM,OAAO,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC;AAC3C;AACA,IAAI,IAAI,MAAM,GAAG,UAAU,CAAC,MAAM;AAClC,QAAQ,KAAK,GAAwB,EAAE;AACvC,QAAQ,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC;;AAErC,IAAI,QAA8B,EAAE,KAAK,GAAG,MAAM,GAAG;AACrD,MAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,KAAK,EAAE;AAChE,QAAQ;AACR;AACA;AACA,IAAI,OAAO,UAAU;AACrB,GAAG;AACH;;AC1BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACG,IAAC,QAAQ,GAAG,cAAc,CAAC,UAAU;;ACXxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE;AAC5E,EAAE,QAAQ,CAAC,UAAU,EAAE,SAAS,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE;AAC1D,IAAI,WAAW,GAAG;AAClB,SAAS,SAAS,GAAG,KAAK,EAAE,KAAK;AACjC,QAAQ,QAAQ,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC;AACvD,GAAG,CAAC;AACJ,EAAE,OAAO,WAAW;AACpB;;ACdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE;AACnD,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,WAAW,GAAG,UAAU;AAC3D,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC;;AAEtC,EAAE,OAAO,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,QAAW,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,CAAC;AACtF;;AC3BM,SAAU,WAAW,CAAC,GAAS,EAAA;IACnC,IAAI,OAAO,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,IAAI,EAAE;AAC9C,QAAA,OAAO,EAAE;;AAEX,IAAA,OAAO,GAAG;AACZ;SAEgB,UAAU,CAAC,KAAkB,GAAA,EAAE,EAAE,MAAsB,EAAA;IACrE,IAAI,CAAC,MAAM,EAAE;QACX;;AAEF,IAAA,IAAI,MAAM,CAAC,UAAU,EAAE;QACrB,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC;;AAEzC,IAAA,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;AAC3B;AAEgB,SAAA,iBAAiB,CAAC,KAAe,EAAE,MAAqB,EAAA;IACtE,OAAO,WAAW,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC/C;AA8BA;;AAEG;AACG,SAAU,aAAa,CAAC,KAAoB,EAAA;AAChD,IAAA,IAAI,KAAK,CAAC,GAAG,EAAE;QACb,OAAO,KAAK,CAAC,GAAG;;AAElB,IAAA,OAAO,OAAO;AAChB;AAEM,SAAU,cAAc,CAAC,IAAqB,EAAA;IAClD,MAAM,GAAG,GAAwB,EAAE;AACnC,IAAA,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;AACnC,QAAA,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,YAAA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI;;;AAGnB,IAAA,OAAO,GAAG;AACZ;AAGA;;AAEG;AACG,SAAU,aAAa,CAC3B,OAAuC,EAAA;AAEvC,IAAA,OAAO,CAAC,CAAE,OAA0B,CAAC,QAAQ;AAC/C;AAEA;;AAEG;AACG,SAAU,UAAU,CACxB,OAAmB,EACnB,KAAK,GAAG,CAAC,EACT,KAAmB,EAAA;AAEnB,IAAA,MAAM,UAAU,GAAqB;;AAEnC,QAAA,OAAO,EAAE;AACP,YAAA,KAAK,EAAE,EAAE;AACT,YAAA,WAAW,EAAE,EAAE;AACf,YAAA,SAAS,EAAE,EAAE;AACd,SAAA;;AAED,QAAA,YAAY,EAAE,EAAE;;AAEhB,QAAA,cAAc,EAAE;AACd,YAAA,KAAK,EAAE,EAAE;AACT,YAAA,WAAW,EAAE,EAAE;AACf,YAAA,SAAS,EAAE,EAAE;AACd,SAAA;;AAED,QAAA,QAAQ,EAAE,KAAK;;AAEf,QAAA,IAAI,EAAE,EAAE;KACT;IAED,OAAO,MAAM,CACX,OAAO,EACP,CAAC,GAAqB,EAAE,OAAuC,KAAI;;AAEjE,QAAA,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;YAC1B,GAAG,GAAG,WAAW,CACf,GAAG,EACH,OAAO,EACP,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,EAC9C,KAAK,CACN;AACD,YAAA,OAAO,GAAG;;;AAGZ,QAAA,MAAM,wBAAwB,GAAG,OAAO,CAAC,UAAU,KAAI,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,MAAA,GAAA,MAAA,GAAL,KAAK,CAAG,OAAO,CAAC,UAAU,CAAC,CAAA;;AAElF,QAAA,MAAM,aAAa,GACd,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,wBAAwB,CACxB,EAAA,OAAO,CACX;;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE;YACtB,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;;;aAEhC;AACL,YAAA,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;;AAEpD,QAAA,IAAI,aAAa,CAAC,KAAK,EAAE;YACvB,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,aAAa;;;QAG9C,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YACzC,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE;;AAE3C,QAAA,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;;QAGxD,aAAa,CAAC,WAAW,IAAI,aAAa,CAAC,WAAW,CAAC,aAAa,CAAC;AACrE,QAAA,OAAO,GAAG;KACX,EACD,UAAU,CACX;AACH;AAEM,SAAU,WAAW,CACzB,GAAM,EACN,OAAuB,EACvB,UAAa,EACb,KAAK,GAAG,CAAC,EAAA;;IAGT,MAAM,KAAK,GACN,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CACV,EAAA,EAAA,KAAK,EACL,OAAO,EAAE,EAAE,EAAA,CACZ;;AAGD,IAAA,WAAW,CAAC,OAAO,CAAC,IAAI,IAAG;QACzB,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;QACpC,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;;QAG/C,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE;;AAGlD,YAAA,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM;YACxC,IAAI,UAAU,EAAE;AACd,gBAAA,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM;;AAEtC,gBAAA,UAAU,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC;;AAGlC,gBAAA,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACxB,KAAK,CAAA,EAAA,EACR,OAAO,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAClE;;;AAGR,KAAC,CAAC;;AAEF,IAAA,KAAK,IAAI,CAAC,IAAI,UAAU,CAAC,cAAc,EAAE;QACvC,MAAM,GAAG,GAAG,CAAkB;QAC9B,MAAM,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC;QACrD,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC;;AAEjD,IAAA,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC;IAC1D,GAAG,CAAC,IAAI,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,GAAG,CAAC,IAAI,CAAA,EAAK,UAAU,CAAC,IAAI,CAAE;IAC9C,GAAG,CAAC,YAAY,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACX,GAAG,CAAC,YAAY,CAAA,EAChB,UAAU,CAAC,YAAY,CAC3B;AACD,IAAA,OAAO,GAAG;AACZ;AAEA,SAAS,UAAU,CACjB,OAAmB,EACnB,IAAgB,EAAA;AAEhB,IAAA,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE;AACvB,QAAA,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;YACpB,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC;YAC1C,IAAI,KAAK,EAAE;AACT,gBAAA,OAAO,KAAK;;;AAET,aAAA,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;AAC1B,YAAA,OAAO,CAAC;;;AAGZ,IAAA,OAAO,SAAS;AAClB;AAEgB,SAAA,eAAe,CAC7B,OAAmB,EACnB,IAAgB,EAAA;AAEhB,IAAA,OAAO,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC;AAClC;;ACtPA;;AAEG;AAOH,SAAS,YAAY,GAAA;IACnB,OAAO;AACL,QAAA,KAAK,EAAE,IAAI;AACX,QAAA,SAAS,EAAE,IAAI;AACf,QAAA,aAAa,EAAE,IAAI;AACnB,QAAA,KAAK,EAAE,IAAI;AACX,QAAA,IAAI,EAAE,IAAI;AACV,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,SAAS,EAAE,IAAI;KAChB;AACH;MAEa,cAAc,CAAA;AAGzB,IAAA,WAAA,GAAA;QADQ,IAAW,CAAA,WAAA,GAAmB,EAAE;QAEtC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;AACxC,QAAA,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,KAAI;AACnC,YAAA,IAAI,GAAG,KAAK,WAAW,IAAI,CAAC,MAAM,EAAE;gBAClC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC;;AAEzC,SAAC,CAAC;;IAGJ,QAAQ,CAAwC,QAAa,EAAE,EAAgD,EAAA;AAC7G,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;;IAG1D,UAAU,GAAA;QACR,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;;IAGjF,QAAQ,CAAC,KAAW,EAAE,GAAU,EAAA;QAC9B,IAAI,CAAC,GAAG,EAAE;YACR,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC;;aAC1B;AACL,YAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;gBACnB,KAAK;AACL,gBAAA,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC;AAC3B,gBAAA,IAAI,EAAE,IAAI;AACV,gBAAA,SAAS,EAAE,IAAI;AAChB,aAAA,CAAC;;;AAIN,IAAA,YAAY,CAAC,KAAW,EAAA;QACtB,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;;AAG5C,IAAA,WAAW,CAAC,KAAiC,EAAA;AAC3C,QAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,MAAA,GAAA,MAAA,GAAL,KAAK,CAAE,IAAI,EAAE,aAAa,EAAE,KAAK,KAAL,IAAA,IAAA,KAAK,uBAAL,KAAK,CAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;IAG1F,SAAS,GAAA;QACP,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;;;AAI3C,IAAA,YAAY,CAAC,KAAuB,EAAA;AAClC,QAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;;IAE9D,QAAQ,CAAC,KAAW,EAAE,GAAS,EAAA;QAC7B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC;AAClC,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;AAG1B,IAAA,WAAW,CAAC,QAAc,EAAA;QACxB,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,CAAC;;AAGpC,IAAA,OAAO,CAAC,GAAsB,EAAA;QAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;AACrC,QAAA,IAAI,KAAK,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACpC,YAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;AACnB,gBAAA,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE;AACtC,aAAA,CAAC;YACF;;QAEF,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;IAGtC,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;AAClC,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;;AAEvB;;AC3EY,MAAA,WAAW,GAAG;MAEd,uBAAuB,CAAA;AAApC,IAAA,WAAA,GAAA;;QAEU,IAAK,CAAA,KAAA,GAAG,KAAK;QACZ,IAAM,CAAA,MAAA,GAAqD,EAAE;QAE7D,IAAY,CAAA,YAAA,GAAqB,EAAE;QACnC,IAAS,CAAA,SAAA,GAAoC,EAAE;AAExD;;AAEG;QACM,IAAY,CAAA,YAAA,GAAgD,EAAE;QAC9D,IAAa,CAAA,aAAA,GAAiC,EAAE;QAChD,IAAa,CAAA,aAAA,GAAiC,EAAE;QAiCxC,IAAQ,CAAA,QAAA,GAAc,EAAE;;AA/BzC,IAAA,IAAI,YAAY,GAAA;;AACd,QAAA,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YACzB,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;AAC5B,gBAAA,MAAM,OAAO,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,0CAAE,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;gBACrD,IAAI,OAAO,EAAE;oBACX,OAAO;wBACL,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,wBAAA,IAAI,EAAE,OAAO;AACb,wBAAA,QAAQ,EAAE;AACR,4BAAA,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;AAClB,4BAAA,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;AACnB,yBAAA;qBACF;;;;AAIP,QAAA,OAAO,IAAI;;AAGb,IAAA,IAAI,IAAI,GAAA;;AACN,QAAA,OAAO,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;;AAGpD,IAAA,IAAI,OAAO,GAAA;;AACT,QAAA,OAAO,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;;AAGrD,IAAA,IAAI,aAAa,GAAA;;AACf,QAAA,OAAO,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;;AAKrD,IAAA,eAAe,CAAC,CAAW,EAAA;QACzB,IAAI,CAAC,CAAC,EAAE;AACN,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;;AAExB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI;YACjB;;AAEF,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;AACnC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;;;IAKzB,YAAY,GAAA;AACV,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;gBACzB,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;oBAC5B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE;;;AAG/B,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;;IAItB,cAAc,CAAC,CAAS,EAAE,IAAmB,EAAA;;AAE3C,QAAA,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;YACpB,OAAO,IAAI,cAAc,EAAE;;AAE7B,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACxB,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;QAE7B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,cAAc,EAAE;;AAE3C,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC;AAC3B,QAAA,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI;AAC5B,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;IAG7B,WAAW,CAAC,CAAS,EAAE,IAAmB,EAAA;;AAExC,QAAA,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;YACpB,OAAO,IAAI,cAAc,EAAE;;AAE7B,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;AACrB,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;QAE1B,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,cAAc,EAAE;;AAExC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC;AAC3B,QAAA,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI;AAC5B,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;AAG1B;;AAEG;AACH,IAAA,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAQ,EAAA;;;QAErB,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;YACxC,OAAO,IAAI,cAAc,EAAE;;QAE7B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;AACnB,YAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE;;QAErB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;YAErB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE1B,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,cAAc,EAAE;;AAExC,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,QAAQ,CAAC,OAAO,EAAE,CAAC,IAAG;YACvC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;YACpC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;AACnC,SAAC,CAAC;;AAEF,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAGlB,OAAO,CAAC,CAAS,EAAE,CAAS,EAAA;;QAClC,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,OAAO,EAAE;QAC/B,CAAA,EAAA,GAAA,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,OAAO,EAAE;AAE5B,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AACxB,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;AAE3B,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;YACzB,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;AAClC,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;AAC5B,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;;;AAGhC,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;YACzB,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;AAClC,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;AAC5B,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;;AAEhC,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;YAClB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;AAG1B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;AAC7C,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;IAIzB,aAAa,CAAiB,QAAW,EAAE,QAAW,EAAA;QACpD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAC9C,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;;AAGlB;;;;AAIG;AACH,IAAA,mBAAmB,CAAC,KAAW,EAAA;;;AAE7B,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB;;;AAIF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC;AAC/D,QAAA,MAAM,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;;AAGvF,QAAA,CAAA,EAAA,GAAA,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAE,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,YAAY,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAM,KAAK,CAAK,EAAA,IAAI,CAAC,IAAI,EAAG;;AAGvD,IAAA,WAAW,CAAiB,QAAW,EAAE,KAAQ,EAAE,GAAM,EAAA;AACvD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjD,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;;AAG1C,IAAA,KAAK,CAAC,KAAqB,EAAE,EAAE,KAAK,EAAE,GAAG,EAA8B,EAAA;QACrE,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;QAC9D,IAAI,CAAC,mBAAmB,EAAE;AACxB,YAAA,OAAO,IAAI;;;QAIb,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC;AAC5C,QAAA,MAAM,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,mBAAmB,EAAE,QAAQ,CAAC;;QAGhF,IAAI,IAAI,aAAJ,IAAI,KAAA,MAAA,GAAA,MAAA,GAAJ,IAAI,CAAE,KAAK,EAAE;YACf,MAAM,IAAI,mCAAQ,KAAK,CAAA,EAAK,IAAI,CAAC,IAAI,CAAE;AACvC,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;AAClD,YAAA,OAAO,IAAI;;QAGb,IAAI,QAAQ,EAAE;AACZ,YAAA,KAAK,GAAG,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC;AACtC,YAAA,GAAG,GAAG,aAAa,CAAC,GAAG,EAAE,QAAQ,CAAC;;AAEpC,QAAA,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC;AAC1B,QAAA,OAAO,KAAK;;AAGd;;;AAGG;AACH,IAAA,sBAAsB,CAAC,KAAqB,EAAA;AAC1C,QAAA,IAAI,mBAAqC;;AAGzC,QAAA,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YACzB,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBAC5B,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAG3B,gBAAA,IAAI,CAAC,KAAK,KAAK,EAAE;oBACf,CAAC,CAAC,UAAU,EAAE;;qBACT;;AAEL,oBAAA,mBAAmB,GAAG;AACpB,wBAAA,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;AAClB,wBAAA,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE;qBAClB;;;;AAKP,QAAA,OAAO,mBAAmB;;AAG5B;;;;AAIG;AACH,IAAA,YAAY,CACV,KAAW,EACX,mBAAyB,EACzB,QAAc,EAAA;;QAGd,MAAM,QAAQ,GAAyB,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;AAEhE,QAAA,IAAI,SAAqC;QACzC,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,aAAa,CAAuB,KAAI;AAC/E,gBAAA,IAAI,MAAM;gBACV,QAAQ,IAAI;AACV,oBAAA,KAAK,GAAG;;wBAEN,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,CAAC;wBAC/C;AACF,oBAAA,KAAK,GAAG;;wBAEN,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,CAAC;wBAC/C;;;AAIJ,gBAAA,IAAI,aAAa,IAAI,CAAC,EAAE;oBACtB,SAAS,GAAG,MAAM,CAAC,EAAE,mBAAmB,CAAC,IAAI,CAAC,CAAC;;qBAC1C;oBACL,SAAS,GAAG,MAAM,CAAC,EAAE,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAC/C,oBAAA,MAAM,YAAY,GAAG,SAAS,KAAA,IAAA,IAAT,SAAS,KAAT,MAAA,GAAA,MAAA,GAAA,SAAS,CAAE,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC;oBACrD,IAAI,YAAY,EAAE;wBAChB,QAAQ,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,aAAa;;;AAG3D,aAAC,CAAC;;QAEF,OAAO;AACL,YAAA,KAAK,EAAE,SAAS;AAChB,YAAA,IAAI,EAAE,QAAQ;SACf;;IAGH,QAAQ,GAAA;;AACN,QAAA,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YACzB,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;AAC5B,gBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,UAAU,EAAE;;;;AAKrC,IAAA,OAAO,CAAC,GAAsB,EAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB;;QAEF,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC;;AAGvC;;AAEG;IACH,SAAS,GAAA;AACP,QAAA,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YACzB,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/B,IAAI,CAAC,KAAK,EAAE;oBACV;;gBAEF,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC;gBAC5C,IAAI,QAAQ,EAAE;AACZ,oBAAA,KAAK,CAAC,QAAQ,CACZ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EACd,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CACzC;;;;;AAMD,IAAA,UAAU,CAAC,CAAS,EAAA;AAC1B,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;AAGf,IAAA,UAAU,CAAC,CAAS,EAAA;QAC1B,MAAM,MAAM,GAAoC,EAAE;AAClD,QAAA,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;AACzB,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE/B,QAAA,OAAO,MAAM;;AAEhB;;AC5VK,SAAU,aAAa,CAAC,GAAW,EAAA;IACvC,OAAO,GAAG,KAAK,WAAW;AAC5B;AAEgB,SAAA,QAAQ,CAAC,IAAU,EAAE,QAAc,EAAA;IACjD,MAAM,QAAQ,GAAkB,EAAE;AAClC,IAAA,IAAI,KAAK,GAAmB,CAAC,GAAG,EAAE,GAAG,CAAC;;AAGtC,IAAA,KAAK,IAAI,CAAC,IAAI,KAAK,EAAE;AACnB,QAAA,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YACf,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACrB,YAAA,OAAO,QAAQ;;;;AAInB,IAAA,KAAK,IAAI,CAAC,IAAI,KAAK,EAAE;QACnB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;AAC1B,YAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;AACnC,YAAA,OAAO,QAAQ;;;AAGnB,IAAA,OAAO,IAAI;AACb;AAEgB,SAAA,aAAa,CAAC,IAAU,EAAE,QAAc,EAAA;AACtD,IAAA,MAAM,WAAW,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAc,IAAI,CAAE;AACrC,IAAA,MAAM,eAAe,GAAmB,CAAC,GAAG,EAAE,GAAG,CAAC;AAElD,IAAA,KAAK,MAAM,UAAU,IAAI,eAAe,EAAE;AACxC,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACxB,YAAA,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC;;aACtB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;YACnD,WAAW,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC;;;AAItD,IAAA,OAAO,WAAW;AACpB;AAEgB,SAAA,QAAQ,CAAC,KAAmB,EAAE,GAAiB,EAAA;IAC7D,OAAO,KAAK,IAAI;AACd,UAAE;AACE,YAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3B,YAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3B,YAAA,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC5B,YAAA,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC7B;UACD,IAAI;AACV;AAEM,SAAU,iBAAiB,CAAC,CAAY,EAAA;AAC5C,IAAA,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;AACrC;;ACjDa,MAAA,QAAQ,GAAoB,CAAC,aAAa,EAAE,OAAO,EAAE,WAAW;AAChE,MAAA,WAAW,GAAoB;IAC1C,aAAa;IACb,OAAO;IACP,WAAW;;AAGP,SAAU,SAAS,CAAC,IAAyB,EAAA;IACjD,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;AACpC;;ACba,MAAA,WAAW,GAAG,CAAG,EAAA,cAAc;AAC/B,MAAA,iBAAiB,GAAG,CAAG,EAAA,cAAc;AACrC,MAAA,oBAAoB,GAAG,CAAG,EAAA,cAAc;AACxC,MAAA,uBAAuB,GAAG,CAAG,EAAA,cAAc;AAC3C,MAAA,mBAAmB,GAAG,CAAG,EAAA,cAAc;AACvC,MAAA,cAAc,GAAG,CAAG,EAAA,cAAc;AAClC,MAAA,iBAAiB,GAAG,CAAG,EAAA,cAAc;AACrC,MAAA,oBAAoB,GAAG,CAAG,EAAA,cAAc;AAC9C,MAAM,gBAAgB,GAAG;AACzB,MAAM,kBAAkB,GAAG;AAC3B,MAAM,iBAAiB,GAAkB;;ACOhD,SAAS,oBAAoB,CAAC,IAAc,EAAE,IAAqB,EAAA;AACjE,IAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;AAC3B;AAEA;AACM,SAAU,SAAS,CACvB,MAAkB,EAClB,KAAe,EACf,eAAe,GAAG,KAAK,EAAA;IAEvB,IAAI,KAAK,GAAG,CAAC;AACb,IAAA,MAAM,MAAM,GAA2B;AACrC,QAAA,MAAM,EAAE,EAAE;AACV,QAAA,YAAY,EAAE,EAAE;AAChB,QAAA,aAAa,EAAE,EAAE;KAClB;;AAED,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAG;AAChB,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,eAAe,EAAE;AACpB,YAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACzB;;;AAIF,QAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;AACrB,YAAA,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;gBACtB,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,GAAG,IAAI;;;aAEvD;AACL,YAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AACzB,YAAA,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,KAAK;AAC/B,YAAA,KAAK,EAAE;;AAEX,KAAC,CAAC;AACF,IAAA,OAAO,MAAM;AACf;AAEgB,SAAA,WAAW,CAAC,KAAA,GAAkB,EAAE,EAAA;AAC9C,IAAA,OAAO,KAAK,CAAC,cAAc,CAAC;AAC9B;AAEA,SAAS,gBAAgB,CAAC,EACxB,aAAa,EACb,SAAS,EACT,UAAU,EACV,SAAS,EACT,WAAW,EACX,YAAY,EACZ,WAAW,GASZ,EAAA;AACC,IAAA,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM;IAC9B,MAAM,gBAAgB,GAAe,EAAE;;IAEvC,IAAI,OAAO,GAA4B,EAAE;;IAGzC,IAAI,cAAc,GAA2B,EAAE;IAE/C,aAAa,CAAC,OAAO,CAAC,CAAC,kBAAkB,EAAE,OAAO,KAAI;QACpD,MAAM,QAAQ,GAAG,CAAC,GAAG,SAAS,EAAE,OAAO,CAAC;QACxC,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;AACpC,QAAA,MAAM,eAAe,GACnB,UAAU,KAAK,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC5D,gBAAgB,CAAC,IAAI,CAAC;YACpB,CAAC,iBAAiB,GAAG,OAAO;YAC5B,CAAC,WAAW,GAAG,KAAK;YACpB,CAAC,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;YAChD,CAAC,uBAAuB,GAAG,SAAS;YACpC,CAAC,cAAc,GAAG,eAAe;AACjC,YAAA,CAAC,iBAAiB,GAAG,WAAW,CAAC,KAAK,CAAC;AACvC,YAAA,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,OAAO;AAC9B,SAAA,CAAC;QACF,SAAS,IAAI,CAAC;;AAEd,QAAA,IAAI,CAAC,UAAU,IAAI,KAAK,EAAE;AACxB,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI;;AAE3B,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;;AAErC,YAAA,kBAAkB,CAAC,OAAO,CAAC,KAAK,IAAG;gBACjC,SAAS,IAAI,CAAC;gBACd,IAAI,CAAC,eAAe,EAAE;AACpB,oBAAA,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;;gBAE5B,cAAc,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,GAAG,SAAS,CAAC;AAC1D,aAAC,CAAC;AACF,YAAA,gBAAgB,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC;;aACvC;;YAEL,MAAM,QAAQ,GAAG,gBAAgB,CAAC;AAChC,gBAAA,aAAa,EAAE,kBAAkB;AACjC,gBAAA,SAAS,EAAE,QAAQ;AACnB,gBAAA,UAAU,EAAE,eAAe;gBAC3B,SAAS;gBACT,WAAW;gBACX,YAAY;gBACZ,WAAW;aACZ,CAAC,CAAC;YACH,gBAAgB,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;AACzC,YAAA,OAAO,mCAAQ,QAAQ,CAAC,OAAO,CAAK,EAAA,OAAO,CAAE;AAC7C,YAAA,cAAc,mCAAQ,QAAQ,CAAC,cAAc,CAAK,EAAA,cAAc,CAAE;AAClE,YAAA,SAAS,GAAG,QAAQ,CAAC,SAAS;;AAElC,KAAC,CAAC;IACF,OAAO;AACL,QAAA,MAAM,EAAE,gBAAgB;QACxB,cAAc;QACd,OAAO;QACP,SAAS;KACV;AACH;AAEA;;;;;AAKG;SACa,cAAc,CAC5B,KAAiB,EACjB,WAAyB,EACzB,EACE,YAAY,GAAG,EAAE,EACjB,WAAW,GAAG,KAAK,EACnB,aAAa,GAAG,oBAAoB,GACpB,EAAA;AAElB,IAAA,MAAM,YAAY,GAAgB,IAAI,GAAG,EAAE;IAE3C,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,aAAa,KAAI;AACpC,QAAA,MAAM,gBAAgB,GAAG,WAAW,CAAC,GAAG,CAAC,OAAO,IAAI,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACjF,QAAA,MAAM,cAAc,GAAG,gBAAgB,CAAC,GAAG,EAAE;QAC7C,IAAI,iBAAiB,GAAG,YAAY;AACpC,QAAA,gBAAgB,CAAC,OAAO,CAAC,KAAK,IAAG;YAC/B,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACjC,iBAAiB,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,GAAG,EAAE,CAAC;;AAEzC,YAAA,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAgB;AACjE,SAAC,CAAC;QACF,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;YAC1C,MAAM,UAAU,GAAe,EAAE;AACjC,YAAA,iBAAiB,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC;;QAEnD,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,cAAc,CAAe;QAC1E,cAAc,CAAC,IAAI,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACd,IAAI,CAAA,EAAA,EACP,CAAC,oBAAoB,GAAG,aAAa,EAAA,CAAA,CACrC;AACJ,KAAC,CAAC;AAEF,IAAA,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM;IAExC,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,gBAAgB,CAAC;AAC7E,QAAA,aAAa,EAAE,YAAY;AAC3B,QAAA,SAAS,EAAE,EAAE;AACb,QAAA,UAAU,EAAE,IAAI;QAChB,SAAS,EAAE,EAAE;QACb,WAAW;QACX,YAAY;QACZ;AACD,KAAA,CAAC;IAEF,OAAO;AACL,QAAA,gBAAgB;QAChB,KAAK,EAAE,aAAa;AACpB,QAAA,OAAO;AACP,QAAA,cAAc;KACf;AACH;AAEM,SAAU,eAAe,CAAC,KAAgB,EAAA;IAC9C,OAAO,KAAK,aAAL,KAAK,KAAA,MAAA,GAAA,MAAA,GAAL,KAAK,CAAG,iBAAiB,CAAC;AACnC;AAUM,SAAU,UAAU,CAAC,KAAgB,EAAA;AACzC,IAAA,OAAO,QAAO,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,MAAA,GAAA,MAAA,GAAL,KAAK,CAAG,iBAAiB,CAAC,CAAA,KAAK,WAAW;AAC1D;AAEM,SAAU,gBAAgB,CAAC,MAAsB,EAAA;AACrD,IAAA,OAAO,QAAO,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,MAAA,GAAA,MAAA,GAAN,MAAM,CAAG,mBAAmB,CAAC,CAAA,KAAK,WAAW;AAC7D;AAEgB,SAAA,iBAAiB,CAAI,MAAW,EAAE,MAAW,EAAA;AAC3D,IAAA,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM;IACxB,IAAI,CAAC,GAAG,CAAC;AACT,IAAA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QAClB,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE;AAC3B,YAAA,OAAO,CAAC;;;AAGZ,IAAA,OAAO,CAAC;AACV;AAEM,SAAU,cAAc,CAAC,EAAU,EAAA;IACvC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;;IAEjC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AAC9B,QAAA,OAAO,IAAI;;AAEb,IAAA,OAAO,UAAU;AACnB;AAEA;SACgB,WAAW,CACzB,YAAmB,EACnB,YAAsB,EACtB,SAAmB,EAAA;IAEnB,MAAM,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;IACjE,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,OAAO,KAAK;;IAGd,MAAM,KAAK,GAAG,iBAAiB,CAAC,YAAY,EAAE,SAAS,CAAC;AACxD,IAAA,OAAO,YAAY,CAAC,WAAW,CAAC,GAAG,KAAK;AAC1C;;AC5PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE;AACtC,EAAE,IAAI,KAAK,GAAG,EAAE;AAChB,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM;;AAE3B,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE;AACjB,IAAI,KAAK,GAAG,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC;AAClD;AACA,EAAE,GAAG,GAAG,GAAG,GAAG,MAAM,GAAG,MAAM,GAAG,GAAG;AACnC,EAAE,IAAI,GAAG,GAAG,CAAC,EAAE;AACf,IAAI,GAAG,IAAI,MAAM;AACjB;AACA,EAAE,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,MAAM,CAAC,CAAC;AAClD,EAAE,KAAK,MAAM,CAAC;;AAEd,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAC5B,EAAE,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE;AAC3B,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AACxC;AACA,EAAE,OAAO,MAAM;AACf;;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE;AAClC,EAAE,IAAI,MAAM,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM;AAC/C,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,EAAE;AACb;AACA,EAAE,IAAI,GAAG,IAAI,OAAO,GAAG,IAAI,QAAQ,IAAI,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE;AAC1E,IAAI,KAAK,GAAG,CAAC;AACb,IAAI,GAAG,GAAG,MAAM;AAChB;AACA,OAAO;AACP,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;AAChD,IAAI,GAAG,GAAG,GAAG,KAAK,SAAS,GAAG,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC;AACrD;AACA,EAAE,OAAO,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC;AACrC;;SCOgB,aAAa,CAC3B,MAAiD,EACjD,UAAmB,EAAE,EAAA;IAErB,MAAM,MAAM,GAAG,MAAM,KAAA,IAAA,IAAN,MAAM,KAAN,MAAA,GAAA,MAAA,GAAA,MAAM,CAAE,MAAM;IAC7B,IAAI,CAAC,MAAM,EAAE;AACX,QAAA,OAAO,SAAS;;;AAGlB,IAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,QAAA,OAAO,OAAO,CAAC,MAAM,CAAC;;AAExB,IAAA,OAAO,MAAM;AACf;AAEc,MAAO,aAAa,CAAA;AAEhC,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC;;IAM1C,WACU,CAAA,SAA4D,EAC5D,MAA8D,EAAA;QAD9D,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAM,CAAA,MAAA,GAAN,MAAM;QAVR,IAAW,CAAA,WAAA,GAAmB,EAAE;QAKxC,IAAW,CAAA,WAAA,GAAG,KAAK;QAOjB,IAAI,CAAC,WAAW,CAAC,IAAI,CACnB,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CACtD;QACD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;;AAGxB,IAAA,aAAa,CAAC,IAAqB,EAAA;AACzC,QAAA,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;AACtB,YAAA,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;AAC3B,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI;gBACvB;;AAEF,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;;;IAI5B,UAAU,CAAC,CAAS,EAAE,CAAS,EAAA;;QAC7B,MAAM,QAAQ,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,QAAQ;AAC1C,QAAA,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;AACpC,YAAA,OAAO,QAAQ,CAAC,IAAI,CAAC;;QAEvB,OAAO,CAAC,CAAC,QAAQ;;AAGnB,IAAA,eAAe,CACb,CAAS,EACT,CAAS,EACT,YAAuB,EACvB,WAA6B,EAAA;;AAE7B,QAAA,MAAM,KAAK,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACN,YAAY,CAChB;QACD,KAAK,CAAC,KAAK,GACN,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,GAAC,OAAO,KAAK,CAAC,KAAK,KAAK;cACvB,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;cACrB,KAAK,CAAC,KAAK,MACf,CAAC,UAAU,GAAG,IAAI,EAClB,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,EAAA,CACxC;AAED,QAAA,MAAM,KAAK,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAW,CAAC,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,EAAG,WAAW,CAAC;QAC/D,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,OAAO,KAAK;;AAEd,QAAA,OAAO,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC;;IAGlC,WAAW,CAAC,CAAS,EAAE,IAAY,EAAA;AACjC,QAAA,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,EAAE;AACpD,QAAA,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE;;AAG1B,IAAA,WAAW,CACT,QAAgB,EAChB,QAAgB,EAChB,GAAY,EAAA;QAEZ,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAClD,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC9B,YAAA,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;;QAE/B,OACK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CACP,EAAA,EAAA,GAAG,EACH,CAAA;;AAGJ;;;AAGG;IACH,YAAY,CAAC,QAAgB,EAAE,QAAgB,EAAA;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QACrC,MAAM,IAAI,GAAG,MAAM,KAAA,IAAA,IAAN,MAAM,KAAN,MAAA,GAAA,MAAA,GAAA,MAAM,CAAE,IAAI;AACzB,QAAA,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,IAAI,EAAE;QAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;QACvC,OAAO;YACL,IAAI;YACJ,KAAK;YACL,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;YAClC,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,OAAO,EAAE,IAAI,CAAC,IAAI;YAClB,IAAI;AACJ,YAAA,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC;SACjC;;IAGH,YAAY,CACV,CAAe,EACf,OAAwB,EAAA;;QAKxB,MAAM,OAAO,GAAe,EAAE;;AAG9B,QAAA,MAAM,aAAa,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC;AACtD,QAAA,MAAM,aAAa,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC;QACtD,MAAM,OAAO,GAAuB,EAAE;;AAGtC,QAAA,KACE,IAAI,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAClC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,EAC5B,QAAQ,EAAE,EAAE,CAAC,EAAE,EACf;;AAEA,YAAA,MAAM,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC;AACtD,YAAA,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,EAAE;;AAGhE,YAAA,KACE,IAAI,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAClC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,EAC5B,QAAQ,EAAE,EAAE,CAAC,EAAE,EACf;;AAEA,gBAAA,IACE,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACxB,oBAAA,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE;AACzB,oBAAA,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AACxB,oBAAA,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,EACzB;oBACA;;;gBAIF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC3B;;gBAEF,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAI;AACzC,gBAAA,MAAM,YAAY,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC;gBACvD,MAAM,cAAc,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI;;gBAGjD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;;AAExC,oBAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACtB,wBAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;;oBAExB,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC;;AAEjD,oBAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACtB,wBAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;;AAExB,oBAAA,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG;AACxB,wBAAA,QAAQ,EAAE,YAAY;AACtB,wBAAA,OAAO,EAAE,cAAc;AACvB,wBAAA,QAAQ,EAAE,WAAW;qBACtB;;;;QAIP,OAAO;YACL,OAAO;YACP,OAAO;SACR;;IAGH,yBAAyB,CACvB,KAAW,EACX,IAAoB,EAAA;QAKpB,MAAM,OAAO,GAAe,EAAE;AAC9B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM;AACjC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;AACrC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM;;AAEpD,QAAA,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC;QACtB,IAAI,MAAM,GAAG,CAAC;AACd,QAAA,KACE,IAAI,CAAC,GAAG,CAAC,EACT,QAAQ,GAAG,SAAS,IAAI,CAAC,GAAG,aAAa,EACzC,QAAQ,EAAE,EAAE,CAAC,EAAE,EACf;;YAEA,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,aAAa,CAAC;AACvC,YAAA,MAAM,aAAa,GAAG,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,MAAA,GAAA,MAAA,GAAA,OAAO,CAAE,MAAM,KAAI,CAAC;;AAE1C,YAAA,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC;AACtB,YAAA,KACE,IAAI,CAAC,GAAG,CAAC,EACT,QAAQ,GAAG,SAAS,IAAI,CAAC,GAAG,aAAa,EACzC,QAAQ,EAAE,EAAE,CAAC,EAAE,EACf;gBACA,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI;AACrC,gBAAA,MAAM,UAAU,GAAG,CAAC,GAAG,SAAS;;gBAGhC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;;AAExC,oBAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACtB,wBAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;;oBAExB,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC;;;YAG9C,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,GAAG,CAAC,CAAC;;AAEzC,QAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE;YAC5B,CAAC,EAAE,QAAQ,GAAG,CAAC;AACf,YAAA,CAAC,EAAE,MAAM;AACV,SAAA,CAAC;QACF,OAAO;YACL,OAAO;YACP,KAAK;SACN;;IAGH,kBAAkB,CAAC,CAAY,EAAE,KAAiB,EAAA;QAChD,MAAM,OAAO,GAAe,EAAE;;QAG9B,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,EAAE;;YAEpE,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,EAAE;;gBAEpE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC3B;;gBAEF,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI;;gBAGrC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;;AAExC,oBAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACtB,wBAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;;oBAExB,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK;;;;AAIlC,QAAA,OAAO,OAAO;;IAGhB,0BAA0B,CACxB,CAAY,EACZ,KAAwD,EAAA;;QAExD,MAAM,IAAI,GAOJ,EAAE;QAER,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;;QAEvC,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,EAAE;;YAEpE,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,EAAE;gBACpE,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAI;gBACzC,IAAI,CAAC,IAAI,CAAC;oBACR,IAAI;oBACJ,QAAQ;oBACR,QAAQ;AACR,oBAAA,KAAK,EAAE,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC;oBACrC,IAAI;oBACJ,OAAO,EAAE,IAAI,CAAC,IAAI;AACnB,iBAAA,CAAC;;;AAGN,QAAA,OAAO,IAAI;;IAGb,cAAc,CACZ,KAAgB,EAChB,KAAwD,EAAA;QAExD,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QAC9B,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QACjE,MAAM,MAAM,GAAmB,EAAE;QACjC,MAAM,OAAO,GAA2D,EAAE;;AAG1E,QAAA,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;YACxC,MAAM,KAAK,GAAiB,EAAE;AAC9B,YAAA,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE;;AAGf,YAAA,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;gBACtB,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;;gBAGpC,IAAI,CAAC,IAAI,EAAE;oBACT;;AAEF,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;AACtB,gBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;gBACf,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG;;AAGxB,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;;QAEpB,OAAO;AACL,YAAA,IAAI,EAAE,MAAM;YACZ,OAAO;SACR;;IAGH,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;AAErC;AAED;;AAEG;AACa,SAAA,gBAAgB,CAC9B,OAAgB,EAChB,KAA4B,EAAA;AAE5B,IAAA,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;AACjC,QAAA,OAAO,OAAO,CAAC,KAAK,CAAC;;IAEvB,OAAO,CAAC,CAAC,OAAO;AAClB;AAEA,SAAS,YAAY,CACnB,MAAA,GAAuD,EAAE,EACzD,SAAuD,EAAE,EAAA;AAEzD,IAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM,GAAG,EAAE,CAAC,MAAM,GAAG,IAAI,EAAE;;AAE7B,IAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM,GAAG,EAAE,CAAC,MAAM,GAAG,IAAI,EAAE;;IAE7B,OAAY,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,MAAM,CAAK,EAAA,MAAM,CAAG;AAClC;AAEgB,SAAA,WAAW,CAAC,QAAmB,EAAE,KAAgB,EAAA;;AAE/D,IAAA,IAAI,KAAK,CAAC,SAAS,EAAE;AACnB,QAAA,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC;QACxD,OAAO,KAAK,CAAC,SAAS;;AAExB,IAAA,IAAI,KAAK,GAAmB,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAK,CAAK,EAAA,QAAQ,CAAE;;AAEhD,IAAA,IAAI,KAAK,CAAC,KAAK,EAAE;AACf,QAAA,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;;AAEtD,IAAA,IAAI,KAAK,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,KAAK,CAAC,KAAK,CAAA,EAAK,KAAK,CAAC,KAAK,CAAE;;AAElD,IAAA,OAAO,KAAK;AACd;;;;", "names": [], "sources": ["node_modules/lodash/toInteger.js", "node_modules/lodash/_arrayReduce.js", "node_modules/lodash/_createBaseFor.js", "node_modules/lodash/_baseFor.js", "node_modules/lodash/_baseForOwn.js", "node_modules/lodash/_createBaseEach.js", "node_modules/lodash/_baseEach.js", "node_modules/lodash/_baseReduce.js", "node_modules/lodash/reduce.js", "src/utils/column.utils.ts", "src/store/selection/selection.store.ts", "src/services/selection.store.connector.ts", "src/store/selection/selection.helpers.ts", "src/store/index.ts", "src/plugins/groupingRow/grouping.const.ts", "src/plugins/groupingRow/grouping.service.ts", "node_modules/lodash/_baseSlice.js", "node_modules/lodash/slice.js", "src/components/data/column.service.ts"], "sourcesContent": ["import toFinite from './toFinite.js';\n\n/**\n * Converts `value` to an integer.\n *\n * **Note:** This method is loosely based on\n * [`ToInteger`](http://www.ecma-international.org/ecma-262/7.0/#sec-tointeger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toInteger(3.2);\n * // => 3\n *\n * _.toInteger(Number.MIN_VALUE);\n * // => 0\n *\n * _.toInteger(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toInteger('3.2');\n * // => 3\n */\nfunction toInteger(value) {\n  var result = toFinite(value),\n      remainder = result % 1;\n\n  return result === result ? (remainder ? result - remainder : result) : 0;\n}\n\nexport default toInteger;\n", "/**\n * A specialized version of `_.reduce` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the first element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */\nfunction arrayReduce(array, iteratee, accumulator, initAccum) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  if (initAccum && length) {\n    accumulator = array[++index];\n  }\n  while (++index < length) {\n    accumulator = iteratee(accumulator, array[index], index, array);\n  }\n  return accumulator;\n}\n\nexport default arrayReduce;\n", "/**\n * Creates a base function for methods like `_.forIn` and `_.forOwn`.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\nfunction createBaseFor(fromRight) {\n  return function(object, iteratee, keysFunc) {\n    var index = -1,\n        iterable = Object(object),\n        props = keysFunc(object),\n        length = props.length;\n\n    while (length--) {\n      var key = props[fromRight ? length : ++index];\n      if (iteratee(iterable[key], key, iterable) === false) {\n        break;\n      }\n    }\n    return object;\n  };\n}\n\nexport default createBaseFor;\n", "import createBaseFor from './_createBaseFor.js';\n\n/**\n * The base implementation of `baseForOwn` which iterates over `object`\n * properties returned by `keysFunc` and invokes `iteratee` for each property.\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @returns {Object} Returns `object`.\n */\nvar baseFor = createBaseFor();\n\nexport default baseFor;\n", "import baseFor from './_baseFor.js';\nimport keys from './keys.js';\n\n/**\n * The base implementation of `_.forOwn` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Object} Returns `object`.\n */\nfunction baseForOwn(object, iteratee) {\n  return object && baseFor(object, iteratee, keys);\n}\n\nexport default baseForOwn;\n", "import isArrayLike from './isArrayLike.js';\n\n/**\n * Creates a `baseEach` or `baseEachRight` function.\n *\n * @private\n * @param {Function} eachFunc The function to iterate over a collection.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\nfunction createBaseEach(eachFunc, fromRight) {\n  return function(collection, iteratee) {\n    if (collection == null) {\n      return collection;\n    }\n    if (!isArrayLike(collection)) {\n      return eachFunc(collection, iteratee);\n    }\n    var length = collection.length,\n        index = fromRight ? length : -1,\n        iterable = Object(collection);\n\n    while ((fromRight ? index-- : ++index < length)) {\n      if (iteratee(iterable[index], index, iterable) === false) {\n        break;\n      }\n    }\n    return collection;\n  };\n}\n\nexport default createBaseEach;\n", "import baseForOwn from './_baseForOwn.js';\nimport createBaseEach from './_createBaseEach.js';\n\n/**\n * The base implementation of `_.forEach` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n */\nvar baseEach = createBaseEach(baseForOwn);\n\nexport default baseEach;\n", "/**\n * The base implementation of `_.reduce` and `_.reduceRight`, without support\n * for iteratee shorthands, which iterates over `collection` using `eachFunc`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} accumulator The initial value.\n * @param {boolean} initAccum Specify using the first or last element of\n *  `collection` as the initial value.\n * @param {Function} eachFunc The function to iterate over `collection`.\n * @returns {*} Returns the accumulated value.\n */\nfunction baseReduce(collection, iteratee, accumulator, initAccum, eachFunc) {\n  eachFunc(collection, function(value, index, collection) {\n    accumulator = initAccum\n      ? (initAccum = false, value)\n      : iteratee(accumulator, value, index, collection);\n  });\n  return accumulator;\n}\n\nexport default baseReduce;\n", "import arrayReduce from './_arrayReduce.js';\nimport baseEach from './_baseEach.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseReduce from './_baseReduce.js';\nimport isArray from './isArray.js';\n\n/**\n * Reduces `collection` to a value which is the accumulated result of running\n * each element in `collection` thru `iteratee`, where each successive\n * invocation is supplied the return value of the previous. If `accumulator`\n * is not given, the first element of `collection` is used as the initial\n * value. The iteratee is invoked with four arguments:\n * (accumulator, value, index|key, collection).\n *\n * Many lodash methods are guarded to work as iteratees for methods like\n * `_.reduce`, `_.reduceRight`, and `_.transform`.\n *\n * The guarded methods are:\n * `assign`, `defaults`, `defaultsDeep`, `includes`, `merge`, `orderBy`,\n * and `sortBy`\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @returns {*} Returns the accumulated value.\n * @see _.reduceRight\n * @example\n *\n * _.reduce([1, 2], function(sum, n) {\n *   return sum + n;\n * }, 0);\n * // => 3\n *\n * _.reduce({ 'a': 1, 'b': 2, 'c': 1 }, function(result, value, key) {\n *   (result[value] || (result[value] = [])).push(key);\n *   return result;\n * }, {});\n * // => { '1': ['a', 'c'], '2': ['b'] } (iteration order is not guaranteed)\n */\nfunction reduce(collection, iteratee, accumulator) {\n  var func = isArray(collection) ? arrayReduce : baseReduce,\n      initAccum = arguments.length < 3;\n\n  return func(collection, baseIteratee(iteratee, 4), accumulator, initAccum, baseEach);\n}\n\nexport default reduce;\n", "import isArray from 'lodash/isArray';\nimport reduce from 'lodash/reduce';\n\nimport type {\n  ColumnData,\n  ColumnGrouping,\n  ColumnProp,\n  ColumnRegular,\n  ColumnTypes,\n  DataType,\n  DimensionCols,\n  ViewSettingSizeProp,\n} from '@type';\nimport { columnTypes, type Group as StoreGroup } from '@store';\n\nexport type ColumnItems = Record<DimensionCols, ColumnRegular[]>;\nexport interface ColumnGroup extends StoreGroup {\n  level: number;\n}\nexport type ColumnGroupingCollection = Record<DimensionCols, ColumnGroup[]>;\n\nexport function getCellData(val?: any) {\n  if (typeof val === 'undefined' || val === null) {\n    return '';\n  }\n  return val;\n}\n\nexport function getCellRaw(model: DataType = {}, column?: ColumnRegular) {\n  if (!column) {\n    return;\n  }\n  if (column.cellParser) {\n    return column.cellParser(model, column);\n  }\n  return model[column.prop];\n}\n\nexport function getCellDataParsed(model: DataType, column: ColumnRegular) {\n  return getCellData(getCellRaw(model, column));\n}\n\n/**\n * Column collection definition.\n * Used to access indexed data for columns.\n * Can be accessed via different events.\n */\nexport type ColumnCollection = {\n  /**\n   * Columns as they are in stores\n   */\n  columns: Record<DimensionCols, ColumnRegular[]>;\n  /**\n   * Columns indexed by prop for quick access, it's possible to have multiple columns with same prop but not recommended\n   */\n  columnByProp: Record<ColumnProp, ColumnRegular[]>;\n  /**\n   * Grouped columns\n   */\n  columnGrouping: ColumnGroupingCollection;\n  /**\n   * Max level of grouping\n   */\n  maxLevel: number;\n  /**\n   * Sorting\n   */\n  sort: Record<ColumnProp, ColumnRegular>;\n};\n\n/**\n * Get column type from column data\n */\nexport function getColumnType(rgCol: ColumnRegular): DimensionCols {\n  if (rgCol.pin) {\n    return rgCol.pin;\n  }\n  return 'rgCol';\n}\n\nexport function getColumnSizes(cols: ColumnRegular[]): ViewSettingSizeProp {\n  const res: ViewSettingSizeProp = {};\n  for (const [i, c] of cols.entries()) {\n    if (c.size) {\n      res[i] = c.size;\n    }\n  }\n  return res;\n}\n\n\n/**\n * Check if column is grouping column\n */\nexport function isColGrouping(\n  colData: ColumnGrouping | ColumnRegular,\n): colData is ColumnGrouping {\n  return !!(colData as ColumnGrouping).children;\n}\n\n/**\n * This function is used to create a collection of columns.\n */\nexport function getColumns(\n  columns: ColumnData,\n  level = 0,\n  types?: ColumnTypes,\n): ColumnCollection {\n  const collection: ColumnCollection = {\n    // columns as they are in stores per type\n    columns: {\n      rgCol: [],\n      colPinStart: [],\n      colPinEnd: [],\n    },\n    // columns indexed by prop for quick access\n    columnByProp: {},\n    // column grouping\n    columnGrouping: {\n      rgCol: [],\n      colPinStart: [],\n      colPinEnd: [],\n    },\n    // max depth level for column grouping\n    maxLevel: level,\n    // sorting\n    sort: {},\n  };\n\n  return reduce(\n    columns,\n    (res: ColumnCollection, colData: ColumnGrouping | ColumnRegular) => {\n      // Grouped column\n      if (isColGrouping(colData)) {\n        res = gatherGroup(\n          res,\n          colData,\n          getColumns(colData.children, level + 1, types),\n          level,\n        );\n        return res;\n      }\n      // Column type\n      const columnDefinitionFromType = colData.columnType && types?.[colData.columnType];\n      // Regular column\n      const regularColumn = {\n        ...columnDefinitionFromType,\n        ...colData,\n      };\n      // Regular column, no Pin\n      if (!regularColumn.pin) {\n        res.columns.rgCol.push(regularColumn);\n        // Pin\n      } else {\n        res.columns[regularColumn.pin].push(regularColumn);\n      }\n      if (regularColumn.order) {\n        res.sort[regularColumn.prop] = regularColumn;\n      }\n      // it's possible that some columns have same prop, but better to avoid it\n      if (!res.columnByProp[regularColumn.prop]) {\n        res.columnByProp[regularColumn.prop] = [];\n      }\n      res.columnByProp[regularColumn.prop].push(regularColumn);\n\n      // trigger setup hook if present\n      regularColumn.beforeSetup && regularColumn.beforeSetup(regularColumn);\n      return res;\n    },\n    collection,\n  );\n}\n\nexport function gatherGroup<T extends ColumnCollection>(\n  res: T,\n  colData: ColumnGrouping,\n  collection: T,\n  level = 0,\n): T {\n  // group template\n  const group: ColumnGroup = {\n    ...colData,\n    level,\n    indexes: [],\n  };\n\n  // check columns for update\n  columnTypes.forEach(type => {\n    const resultItem = res.columns[type];\n    const collectionItem = collection.columns[type];\n\n    // if column data\n    if (isArray(resultItem) && isArray(collectionItem)) {\n\n      // fill grouping\n      const itemLength = collectionItem.length;\n      if (itemLength) {\n        const columnLength = resultItem.length;\n        // fill columns\n        resultItem.push(...collectionItem);\n\n        // fill indexes per each viewport\n        res.columnGrouping[type].push({\n          ...group,\n          indexes: Array(itemLength).fill(columnLength).map((v, i) => v + i),\n        });\n      }\n    }\n  });\n  // merge column groupings\n  for (let k in collection.columnGrouping) {\n    const key = k as DimensionCols;\n    const collectionItem = collection.columnGrouping[key];\n    res.columnGrouping[key].push(...collectionItem);\n  }\n  res.maxLevel = Math.max(res.maxLevel, collection.maxLevel);\n  res.sort = { ...res.sort, ...collection.sort };\n  res.columnByProp = {\n    ...res.columnByProp,\n    ...collection.columnByProp,\n  };\n  return res;\n}\n\nfunction findColumn(\n  columns: ColumnData,\n  prop: ColumnProp,\n): ColumnRegular | undefined {\n  for (const c of columns) {\n    if (isColGrouping(c)) {\n      const found = findColumn(c.children, prop);\n      if (found) {\n        return found;\n      }\n    } else if (c.prop === prop) {\n      return c;\n    }\n  }\n  return undefined;\n}\n\nexport function getColumnByProp(\n  columns: ColumnData,\n  prop: ColumnProp,\n): ColumnRegular | undefined {\n  return findColumn(columns, prop);\n}\n", "/**\n * Selection store\n */\n\nimport { setStore, Observable } from '../../utils';\nimport { getRange } from '@store';\nimport type { SelectionStoreState, Cell, TempRange, RangeArea, Nullable } from '@type';\nimport { createStore } from '@stencil/store';\n\nfunction defaultState(): SelectionStoreState {\n  return {\n    range: null,\n    tempRange: null,\n    tempRangeType: null,\n    focus: null,\n    edit: null,\n    lastCell: null,\n    nextFocus: null,\n  };\n}\n\nexport class SelectionStore {\n  readonly store: Observable<SelectionStoreState>;\n  private unsubscribe: { (): void }[] = [];\n  constructor() {\n    this.store = createStore(defaultState());\n    this.store.on('set', (key, newVal) => {\n      if (key === 'tempRange' && !newVal) {\n        this.store.set('tempRangeType', null);\n      }\n    });\n  }\n\n  onChange<Key extends keyof SelectionStoreState>(propName: Key, cb: (newValue: SelectionStoreState[Key]) => void) {\n    this.unsubscribe.push(this.store.onChange(propName, cb));\n  }\n\n  clearFocus() {\n    setStore(this.store, { focus: null, range: null, edit: null, tempRange: null });\n  }\n\n  setFocus(focus: Cell, end?: Cell) {\n    if (!end) {\n      setStore(this.store, { focus });\n    } else {\n      setStore(this.store, {\n        focus,\n        range: getRange(focus, end),\n        edit: null,\n        tempRange: null,\n      });\n    }\n  }\n\n  setNextFocus(focus: Cell) {\n    setStore(this.store, { nextFocus: focus });\n  }\n\n  setTempArea(range: Nullable<TempRange> | null) {\n    setStore(this.store, { tempRange: range?.area, tempRangeType: range?.type, edit: null });\n  }\n\n  clearTemp() {\n    setStore(this.store, { tempRange: null });\n  }\n\n  /** Can be applied from selection change or from simple keyboard change clicks */\n  setRangeArea(range: RangeArea | null) {\n    setStore(this.store, { range, edit: null, tempRange: null });\n  }\n  setRange(start: Cell, end: Cell) {\n    const range = getRange(start, end);\n    this.setRangeArea(range);\n  }\n\n  setLastCell(lastCell: Cell) {\n    setStore(this.store, { lastCell });\n  }\n\n  setEdit(val?: string | boolean) {\n    const focus = this.store.get('focus');\n    if (focus && typeof val === 'string') {\n      setStore(this.store, {\n        edit: { x: focus.x, y: focus.y, val },\n      });\n      return;\n    }\n    setStore(this.store, { edit: null });\n  }\n\n  dispose() {\n    this.unsubscribe.forEach(f => f());\n    this.store.dispose();\n  }\n}\n", "import { cropCellToMax, isHiddenStore, nextCell, SelectionStore } from '@store';\nimport type {\n  MultiDimensionType,\n  DimensionCols,\n  DimensionRows,\n  Cell,\n  EditCellStore,\n  RangeArea,\n} from '@type';\n\ntype StoreByDimension = Record<number, SelectionStore>;\ntype FocusedStore = {\n  entity: SelectionStore;\n  cell: Cell;\n  position: Cell;\n};\n\ntype StoresMapping<T> = { [xOrY: number]: Partial<T> };\n\nexport const EMPTY_INDEX = -1;\n\nexport class SelectionStoreConnector {\n  // dirty flag required to cleanup whole store in case visibility of panels changed\n  private dirty = false;\n  readonly stores: { [y: number]: { [x: number]: SelectionStore } } = {};\n\n  readonly columnStores: StoreByDimension = {};\n  readonly rowStores: { [y: number]: SelectionStore } = {};\n\n  /**\n   * Helpers for data conversion\n   */\n  readonly storesByType: Partial<Record<MultiDimensionType, number>> = {};\n  readonly storesXToType: StoresMapping<DimensionCols> = {};\n  readonly storesYToType: StoresMapping<DimensionRows> = {};\n\n  get focusedStore(): FocusedStore | null {\n    for (let y in this.stores) {\n      for (let x in this.stores[y]) {\n        const focused = this.stores[y][x]?.store.get('focus');\n        if (focused) {\n          return {\n            entity: this.stores[y][x],\n            cell: focused,\n            position: {\n              x: parseInt(x, 10),\n              y: parseInt(y, 10),\n            },\n          };\n        }\n      }\n    }\n    return null;\n  }\n\n  get edit(): EditCellStore | null | undefined {\n    return this.focusedStore?.entity.store.get('edit');\n  }\n\n  get focused(): Cell | null | undefined {\n    return this.focusedStore?.entity.store.get('focus');\n  }\n\n  get selectedRange(): RangeArea | null | undefined {\n    return this.focusedStore?.entity.store.get('range');\n  }\n\n  private readonly sections: Element[] = [];\n\n  registerSection(e?: Element) {\n    if (!e) {\n      this.sections.length = 0;\n      // some elements removed, rebuild stores\n      this.dirty = true;\n      return;\n    }\n    if (this.sections.indexOf(e) === -1) {\n      this.sections.push(e);\n    }\n  }\n\n  // check if require to cleanup all stores\n  beforeUpdate() {\n    if (this.dirty) {\n      for (let y in this.stores) {\n        for (let x in this.stores[y]) {\n          this.stores[y][x].dispose();\n        }\n      }\n      this.dirty = false;\n    }\n  }\n\n  registerColumn(x: number, type: DimensionCols): SelectionStore {\n    // if hidden just create store but no operations needed\n    if (isHiddenStore(x)) {\n      return new SelectionStore();\n    }\n    if (this.columnStores[x]) {\n      return this.columnStores[x];\n    }\n    this.columnStores[x] = new SelectionStore();\n    // build cross-linking type to position\n    this.storesByType[type] = x;\n    this.storesXToType[x] = type;\n    return this.columnStores[x];\n  }\n\n  registerRow(y: number, type: DimensionRows): SelectionStore {\n    // if hidden just create store\n    if (isHiddenStore(y)) {\n      return new SelectionStore();\n    }\n    if (this.rowStores[y]) {\n      return this.rowStores[y];\n    }\n    this.rowStores[y] = new SelectionStore();\n    // build cross linking type to position\n    this.storesByType[type] = y;\n    this.storesYToType[y] = type;\n    return this.rowStores[y];\n  }\n\n  /**\n   * Cross store proxy, based on multiple dimensions\n   */\n  register({ x, y }: Cell): SelectionStore {\n    // if hidden just create store\n    if (isHiddenStore(x) || isHiddenStore(y)) {\n      return new SelectionStore();\n    }\n    if (!this.stores[y]) {\n      this.stores[y] = {};\n    }\n    if (this.stores[y][x]) {\n      // Store already registered. Do not register twice\n      return this.stores[y][x];\n    }\n    this.stores[y][x] = new SelectionStore();\n    // proxy update, column store trigger only range area\n    this.stores[y][x]?.onChange('range', c => {\n      this.columnStores[x].setRangeArea(c);\n      this.rowStores[y].setRangeArea(c);\n    });\n    // clean up on remove\n    this.stores[y][x]?.store.on('dispose', () => this.destroy(x, y));\n    return this.stores[y][x];\n  }\n\n  private destroy(x: number, y: number) {\n    this.columnStores[x]?.dispose();\n    this.rowStores[y]?.dispose();\n\n    delete this.rowStores[y];\n    delete this.columnStores[x];\n    // clear x cross-link\n    if (this.storesXToType[x]) {\n      const type = this.storesXToType[x];\n      delete this.storesXToType[x];\n      delete this.storesByType[type];\n    }\n    // clear y cross-link\n    if (this.storesYToType[y]) {\n      const type = this.storesYToType[y];\n      delete this.storesYToType[y];\n      delete this.storesByType[type];\n    }\n    if (this.stores[y]) {\n      delete this.stores[y][x];\n    }\n    // clear empty rows\n    if (!Object.keys(this.stores[y] || {}).length) {\n      delete this.stores[y];\n    }\n  }\n\n  setEditByCell<T extends Cell>(storePos: T, editCell: T) {\n    this.focusByCell(storePos, editCell, editCell);\n    this.setEdit('');\n  }\n\n  /**\n   * Sets the next focus cell before the current one.\n   * \n   * @param focus - The cell to set as the next focus.\n   */\n  beforeNextFocusCell(focus: Cell) {\n    // If there is no focused store, return early.\n    if (!this.focusedStore) {\n      return;\n    }\n\n    // Get the next store based on the current focus and the last cell.\n    const lastCell = this.focusedStore.entity.store.get('lastCell');\n    const next = lastCell && this.getNextStore(focus, this.focusedStore.position, lastCell);\n\n    // Set the next focus cell in the store.\n    next?.store?.setNextFocus({ ...focus, ...next.item });\n  }\n\n  focusByCell<T extends Cell>(storePos: T, start: T, end: T) {\n    const store = this.stores[storePos.y][storePos.x];\n    this.focus(store, { focus: start, end });\n  }\n\n  focus(store: SelectionStore, { focus, end }: { focus: Cell; end: Cell }) {\n    const currentStorePointer = this.getCurrentStorePointer(store);\n    if (!currentStorePointer) {\n      return null;\n    }\n\n    // check for the focus in nearby store/viewport\n    const lastCell = store.store.get('lastCell');\n    const next = lastCell && this.getNextStore(focus, currentStorePointer, lastCell);\n\n    // if next store present - update\n    if (next?.store) {\n      const item = { ...focus, ...next.item };\n      this.focus(next.store, { focus: item, end: item });\n      return null;\n    }\n\n    if (lastCell) {\n      focus = cropCellToMax(focus, lastCell);\n      end = cropCellToMax(end, lastCell);  \n    }\n    store.setFocus(focus, end);\n    return focus;\n  }\n\n  /**\n   * Retrieves the current store pointer based on the active store.\n   * Clears focus from all stores except the active one.\n   */\n  getCurrentStorePointer(store: SelectionStore) {\n    let currentStorePointer: Cell | undefined;\n\n    // Iterate through all stores\n    for (let y in this.stores) {\n      for (let x in this.stores[y]) {\n        const s = this.stores[y][x];\n\n        // Clear focus from stores other than the active one\n        if (s !== store) {\n          s.clearFocus();\n        } else {\n          // Update the current store pointer with the active store coordinates\n          currentStorePointer = { \n            x: parseInt(x, 10), \n            y: parseInt(y, 10) \n          };\n        }\n      }\n    }\n\n    return currentStorePointer;\n  }\n\n  /**\n   * Retrieves the next store based on the focus cell and current store pointer.\n   * If the next store exists, returns an object with the next store and the item in the new store.\n   * If the next store does not exist, returns null.\n   */\n  getNextStore(\n    focus: Cell,\n    currentStorePointer: Cell,\n    lastCell: Cell,\n  ) {\n    // item in new store\n    const nextItem: Partial<Cell> | null = nextCell(focus, lastCell);\n\n    let nextStore: SelectionStore | undefined;\n    if (nextItem) {\n      Object.entries(nextItem).forEach(([type, nextItemCoord]: [keyof Cell, number]) => {\n        let stores;\n        switch (type) {\n          case 'x':\n            // Get the X stores for the current Y coordinate of the current store pointer\n            stores = this.getXStores(currentStorePointer.y);\n            break;\n          case 'y':\n            // Get the Y stores for the current X coordinate of the current store pointer\n            stores = this.getYStores(currentStorePointer.x);\n            break;\n        }\n\n        // Get the next store based on the item in the new store\n        if (nextItemCoord >= 0) {\n          nextStore = stores[++currentStorePointer[type]];\n        } else {\n          nextStore = stores[--currentStorePointer[type]];\n          const nextLastCell = nextStore?.store.get('lastCell');\n          if (nextLastCell) {\n            nextItem[type] = nextLastCell[type] + nextItemCoord;\n          }\n        }\n    });\n  }\n    return {\n      store: nextStore,\n      item: nextItem,\n    };\n  }\n\n  clearAll() {\n    for (let y in this.stores) {\n      for (let x in this.stores[y]) {\n        this.stores[y][x]?.clearFocus();\n      }\n    }\n  }\n\n  setEdit(val?: string | boolean) {\n    if (!this.focusedStore) {\n      return;\n    }\n    this.focusedStore.entity.setEdit(val);\n  }\n\n  /**\n   * Select all cells across all stores\n   */\n  selectAll() {\n    for (let y in this.stores) {\n      for (let x in this.stores[y]) {\n        const store = this.stores[y][x];\n        if (!store) {\n          continue;\n        }\n        const lastCell = store.store.get('lastCell');\n        if (lastCell) {\n          store.setRange(\n            { x: 0, y: 0 },\n            { x: lastCell.x - 1, y: lastCell.y - 1 },\n          );\n        }\n      }\n    }\n  }\n\n  private getXStores(y: number) {\n    return this.stores[y];\n  }\n\n  private getYStores(x: number) {\n    const stores: { [p: number]: SelectionStore } = {};\n    for (let i in this.stores) {\n      stores[i] = this.stores[i][x];\n    }\n    return stores;\n  }\n}\n", "import { EMPTY_INDEX } from '../../services/selection.store.connector';\nimport { Cell, RangeArea } from '@type';\n\nexport function isHiddenStore(pos: number) {\n  return pos === EMPTY_INDEX;\n}\n\nexport function nextCell(cell: Cell, lastCell: Cell): Partial<Cell> | null {\n  const nextItem: Partial<Cell> = {};\n  let types: (keyof Cell)[] = ['x', 'y'];\n\n  // previous item check\n  for (let t of types) {\n    if (cell[t] < 0) {\n      nextItem[t] = cell[t];\n      return nextItem;\n    }\n  }\n  // next item check\n  for (let t of types) {\n    if (cell[t] >= lastCell[t]) {\n      nextItem[t] = cell[t] - lastCell[t];\n      return nextItem;\n    }\n  }\n  return null;\n}\n\nexport function cropCellToMax(cell: Cell, lastCell: Cell): Cell {\n  const croppedCell: Cell = { ...cell };\n  const cellCoordinates: (keyof Cell)[] = ['x', 'y'];\n\n  for (const coordinate of cellCoordinates) {\n    if (cell[coordinate] < 0) {\n      croppedCell[coordinate] = 0;\n    } else if (cell[coordinate] >= lastCell[coordinate]) {\n      croppedCell[coordinate] = lastCell[coordinate] - 1;\n    }\n  }\n\n  return croppedCell;\n}\n\nexport function getRange(start?: Cell | null, end?: Cell | null): RangeArea | null {\n  return start && end\n    ? {\n        x: Math.min(start.x, end.x),\n        y: Math.min(start.y, end.y),\n        x1: Math.max(start.x, end.x),\n        y1: Math.max(start.y, end.y),\n      }\n    : null;\n}\n\nexport function isRangeSingleCell(a: RangeArea): boolean {\n  return a.x === a.x1 && a.y === a.y1;\n}\n", "import type { DimensionCols, DimensionRows } from '@type';\n\nexport * from './dataSource';\nexport * from './dimension';\nexport * from './selection';\nexport * from './vp';\n\nexport const rowTypes: DimensionRows[] = ['rowPinStart', 'rgRow', 'rowPinEnd'];\nexport const columnTypes: DimensionCols[] = [\n  'colPinStart',\n  'rgCol',\n  'colPinEnd',\n];\n\nexport function isRowType(type: DimensionRows | any): type is DimensionRows {\n  return rowTypes.indexOf(type) > -1;\n}\n", "import { DimensionRows } from '@type';\nimport { GRID_INTERNALS } from '../../utils/consts';\n\nexport const GROUP_DEPTH = `${GRID_INTERNALS}-depth`;\nexport const PSEUDO_GROUP_ITEM = `${GRID_INTERNALS}-name`;\nexport const PSEUDO_GROUP_ITEM_ID = `${GRID_INTERNALS}-id`;\nexport const PSEUDO_GROUP_ITEM_VALUE = `${GRID_INTERNALS}-value`;\nexport const PSEUDO_GROUP_COLUMN = `${GRID_INTERNALS}-column`;\nexport const GROUP_EXPANDED = `${GRID_INTERNALS}-expanded`;\nexport const GROUP_COLUMN_PROP = `${GRID_INTERNALS}-prop`;\nexport const GROUP_ORIGINAL_INDEX = `${GRID_INTERNALS}-original-index`;\nexport const GROUP_EXPAND_BTN = `group-expand`;\nexport const GRO<PERSON>_EXPAND_EVENT = `groupexpandclick`;\nexport const GROUPING_ROW_TYPE: DimensionRows = 'rgRow';\n", "import type { DataType, ColumnProp, ColumnRegular } from '@type';\nimport {\n  GROUP_DEPTH,\n  GROUP_EXPANDED,\n  PSEUDO_GROUP_COLUMN,\n  PSEUDO_GROUP_ITEM,\n  PSEUDO_GROUP_ITEM_ID,\n  PSEUDO_GROUP_ITEM_VALUE,\n  GROUP_ORIGINAL_INDEX,\n  GROUP_COLUMN_PROP,\n} from './grouping.const';\nimport type { ExpandedOptions } from './grouping.row.types';\n\ntype GroupedData = Map<string, GroupedData | DataType[]>;\ntype SourceGather = {\n  source: DataType[];\n  prevExpanded: Record<string, boolean>;\n  oldNewIndexes?: Record<number, number>;\n};\n\nfunction getGroupValueDefault(item: DataType, prop: string | number) {\n  return item[prop] || null;\n}\n\n// get source based on proxy item collection to preserve rgRow order\nexport function getSource(\n  source: DataType[],\n  items: number[],\n  withoutGrouping = false,\n) {\n  let index = 0;\n  const result: Required<SourceGather> = {\n    source: [],\n    prevExpanded: {},\n    oldNewIndexes: {},\n  };\n  // order important here, expected parent is first, then others\n  items.forEach(i => {\n    const model = source[i];\n    if (!withoutGrouping) {\n      result.source.push(model);\n      return;\n    }\n\n    // grouping filter\n    if (isGrouping(model)) {\n      if (getExpanded(model)) {\n        result.prevExpanded[model[PSEUDO_GROUP_ITEM_VALUE]] = true;\n      }\n    } else {\n      result.source.push(model);\n      result.oldNewIndexes[i] = index;\n      index++;\n    }\n  });\n  return result;\n}\n\nexport function getExpanded(model: DataType = {}) {\n  return model[GROUP_EXPANDED];\n}\n\nfunction flattenGroupMaps({\n  groupedValues,\n  parentIds,\n  isExpanded,\n  itemIndex,\n  expandedAll,\n  prevExpanded,\n  columnProps,\n}: {\n  groupedValues: GroupedData;\n  parentIds: string[];\n  isExpanded: boolean;\n  itemIndex: number;\n  expandedAll: boolean;\n  prevExpanded: Record<string, boolean>;\n  columnProps: ColumnProp[];\n}) {\n  const depth = parentIds.length;\n  const sourceWithGroups: DataType[] = [];\n  // collapse all groups in the beginning\n  let trimmed: Record<number, boolean> = {};\n\n  // index mapping\n  let oldNewIndexMap: Record<number, number> = {};\n\n  groupedValues.forEach((innerGroupedValues, groupId) => {\n    const levelIds = [...parentIds, groupId];\n    const mergedIds = levelIds.join(',');\n    const isGroupExpanded =\n      isExpanded && (!!expandedAll || !!prevExpanded[mergedIds]);\n    sourceWithGroups.push({\n      [PSEUDO_GROUP_ITEM]: groupId,\n      [GROUP_DEPTH]: depth,\n      [PSEUDO_GROUP_ITEM_ID]: JSON.stringify(levelIds),\n      [PSEUDO_GROUP_ITEM_VALUE]: mergedIds,\n      [GROUP_EXPANDED]: isGroupExpanded,\n      [GROUP_COLUMN_PROP]: columnProps[depth],\n      [columnProps[depth]]: groupId,\n    });\n    itemIndex += 1;\n    // If parent group is collapsed, mark all items as hidden\n    if (!isExpanded && depth) {\n      trimmed[itemIndex] = true;\n    }\n    if (Array.isArray(innerGroupedValues)) {\n      // This branch handles leaf nodes (actual data items)\n      innerGroupedValues.forEach(value => {\n        itemIndex += 1;\n        if (!isGroupExpanded) {\n          trimmed[itemIndex] = true; // Mark items as hidden if group is collapsed\n        }\n        oldNewIndexMap[value[GROUP_ORIGINAL_INDEX]] = itemIndex; // Keep track of new positions\n      });\n      sourceWithGroups.push(...innerGroupedValues);\n    } else {\n      // This branch handles nested groups (further subgroups)\n      const children = flattenGroupMaps({\n        groupedValues: innerGroupedValues,\n        parentIds: levelIds,\n        isExpanded: isGroupExpanded,\n        itemIndex,\n        expandedAll,\n        prevExpanded,\n        columnProps,\n      }); // Recursively process subgroups\n      sourceWithGroups.push(...children.source);\n      trimmed = { ...children.trimmed, ...trimmed };\n      oldNewIndexMap = { ...children.oldNewIndexMap, ...oldNewIndexMap };\n      itemIndex = children.itemIndex;\n    }\n  });\n  return {\n    source: sourceWithGroups,\n    oldNewIndexMap,\n    trimmed,\n    itemIndex,\n  };\n}\n\n/**\n * Gather data for grouping\n * @param array - flat data array\n * @param columnProps - ids of groups\n * @param expanded - potentially expanded items if present\n */\nexport function gatherGrouping(\n  array: DataType[],\n  columnProps: ColumnProp[],\n  {\n    prevExpanded = {},\n    expandedAll = false,\n    getGroupValue = getGroupValueDefault,\n  }: ExpandedOptions,\n) {\n  const groupedItems: GroupedData = new Map();\n  \n  array.forEach((item, originalIndex) => {\n    const groupLevelValues = columnProps.map(groupId => getGroupValue(item, groupId));\n    const lastLevelValue = groupLevelValues.pop();\n    let currentGroupLevel = groupedItems;\n    groupLevelValues.forEach(value => {\n      if (!currentGroupLevel.has(value)) {\n        currentGroupLevel.set(value, new Map());\n      }\n      currentGroupLevel = currentGroupLevel.get(value) as GroupedData;\n    });\n    if (!currentGroupLevel.has(lastLevelValue)) {\n      const groupItems: DataType[] = [];\n      currentGroupLevel.set(lastLevelValue, groupItems);\n    }\n    const lastLevelItems = currentGroupLevel.get(lastLevelValue) as DataType[];\n    lastLevelItems.push({\n      ...item,\n      [GROUP_ORIGINAL_INDEX]: originalIndex,\n    });\n  });\n\n  const groupingDepth = columnProps.length;\n\n  const { source: sourceWithGroups, trimmed, oldNewIndexMap } = flattenGroupMaps({\n    groupedValues: groupedItems,\n    parentIds: [],\n    isExpanded: true,\n    itemIndex: -1,\n    expandedAll,\n    prevExpanded,\n    columnProps\n  });\n\n  return {\n    sourceWithGroups, // updates source mirror\n    depth: groupingDepth, // largest depth for grouping\n    trimmed, // used for expand/collapse grouping values\n    oldNewIndexMap, // used for mapping old values to new\n  };\n}\n\nexport function getGroupingName(rgRow?: DataType) {\n  return rgRow?.[PSEUDO_GROUP_ITEM];\n}\n\ntype GroupingItem = {\n  [PSEUDO_GROUP_ITEM]: string;\n  [GROUP_EXPANDED]: boolean;\n  [PSEUDO_GROUP_ITEM_VALUE]: string;\n  [GROUP_DEPTH]: number;\n  [GROUP_COLUMN_PROP]: ColumnProp;\n};\n\nexport function isGrouping(rgRow?: DataType): rgRow is GroupingItem {\n  return typeof rgRow?.[PSEUDO_GROUP_ITEM] !== 'undefined';\n}\n\nexport function isGroupingColumn(column?: ColumnRegular) {\n  return typeof column?.[PSEUDO_GROUP_COLUMN] !== 'undefined';\n}\n\nexport function measureEqualDepth<T>(groupA: T[], groupB: T[]) {\n  const ln = groupA.length;\n  let i = 0;\n  for (; i < ln; i++) {\n    if (groupA[i] !== groupB[i]) {\n      return i;\n    }\n  }\n  return i;\n}\n\nexport function getParsedGroup(id: string) {\n  const parseGroup = JSON.parse(id);\n  // extra precaution and type safeguard\n  if (!Array.isArray(parseGroup)) {\n    return null;\n  }\n  return parseGroup;\n}\n\n// check if items is child of current clicked group\nexport function isSameGroup(\n  currentGroup: any[],\n  currentModel: DataType,\n  nextModel: DataType,\n) {\n  const nextGroup = getParsedGroup(nextModel[PSEUDO_GROUP_ITEM_ID]);\n  if (!nextGroup) {\n    return false;\n  }\n\n  const depth = measureEqualDepth(currentGroup, nextGroup);\n  return currentModel[GROUP_DEPTH] < depth;\n}\n", "/**\n * The base implementation of `_.slice` without an iteratee call guard.\n *\n * @private\n * @param {Array} array The array to slice.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the slice of `array`.\n */\nfunction baseSlice(array, start, end) {\n  var index = -1,\n      length = array.length;\n\n  if (start < 0) {\n    start = -start > length ? 0 : (length + start);\n  }\n  end = end > length ? length : end;\n  if (end < 0) {\n    end += length;\n  }\n  length = start > end ? 0 : ((end - start) >>> 0);\n  start >>>= 0;\n\n  var result = Array(length);\n  while (++index < length) {\n    result[index] = array[index + start];\n  }\n  return result;\n}\n\nexport default baseSlice;\n", "import baseSlice from './_baseSlice.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a slice of `array` from `start` up to, but not including, `end`.\n *\n * **Note:** This method is used instead of\n * [`Array#slice`](https://mdn.io/Array/slice) to ensure dense arrays are\n * returned.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to slice.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the slice of `array`.\n */\nfunction slice(array, start, end) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  if (end && typeof end != 'number' && isIterateeCall(array, start, end)) {\n    start = 0;\n    end = length;\n  }\n  else {\n    start = start == null ? 0 : toInteger(start);\n    end = end === undefined ? length : toInteger(end);\n  }\n  return baseSlice(array, start, end);\n}\n\nexport default slice;\n", "import { DSourceState, getSourceItem, getVisibleSourceItem } from '@store';\nimport {\n  Observable,\n  CELL_CLASS,\n  DISABLED_CLASS,\n  getCellRaw,\n  getCellData,\n} from '../../utils';\nimport { getRange } from '@store';\n\nimport { isGroupingColumn } from '../../plugins/groupingRow/grouping.service';\nimport slice from 'lodash/slice';\nimport {\n  DimensionCols,\n  DimensionRows,\n  RowDrag,\n  ColumnRegular,\n  DataType,\n  CellProps,\n  ColumnDataSchemaModel,\n  ColumnProp,\n  DataLookup,\n  DataFormat,\n  ChangedRange,\n  OldNewRangeMapping,\n  Cell,\n  RangeArea,\n  BeforeSaveDataDetails,\n  EditorCtr,\n  Editors,\n  CellTemplateProp,\n} from '@type';\nimport { JSXBase } from '@stencil/core/internal';\n\nexport type ColumnStores = {\n  [T in DimensionCols]: Observable<DSourceState<ColumnRegular, DimensionCols>>;\n};\nexport type RowStores = {\n  [T in DimensionRows]: Observable<DSourceState<DataType, DimensionRows>>;\n};\n\nexport function getCellEditor(\n  column: Pick<ColumnRegular, 'editor'> | undefined,\n  editors: Editors = {},\n): EditorCtr | undefined {\n  const editor = column?.editor;\n  if (!editor) {\n    return undefined;\n  }\n  // reference\n  if (typeof editor === 'string') {\n    return editors[editor];\n  }\n  return editor;\n}\n\nexport default class ColumnService {\n  private unsubscribe: { (): void }[] = [];\n  get columns(): ColumnRegular[] {\n    return getVisibleSourceItem(this.source);\n  }\n\n  hasGrouping = false;\n  type: DimensionCols;\n\n  constructor(\n    private dataStore: Observable<DSourceState<DataType, DimensionRows>>,\n    private source: Observable<DSourceState<ColumnRegular, DimensionCols>>,\n  ) {\n    this.unsubscribe.push(\n      source.onChange('source', s => this.checkGrouping(s)),\n    );\n    this.checkGrouping(source.get('source'));\n    this.type = source.get('type');\n  }\n\n  private checkGrouping(cols: ColumnRegular[]) {\n    for (let rgCol of cols) {\n      if (isGroupingColumn(rgCol)) {\n        this.hasGrouping = true;\n        return;\n      }\n      this.hasGrouping = false;\n    }\n  }\n\n  isReadOnly(r: number, c: number) {\n    const readOnly = this.columns[c]?.readonly;\n    if (typeof readOnly === 'function') {\n      const data = this.rowDataModel(r, c);\n      return readOnly(data);\n    }\n    return !!readOnly;\n  }\n\n  mergeProperties(\n    r: number,\n    c: number,\n    defaultProps: CellProps,\n    schemaModel: CellTemplateProp,\n  ): CellProps {\n    const props: CellProps = {\n      ...defaultProps,\n    };\n    props.class = {\n      ...(typeof props.class === 'string'\n        ? { [props.class]: true }\n        : props.class),\n      [CELL_CLASS]: true,\n      [DISABLED_CLASS]: this.isReadOnly(r, c),\n    };\n\n    const extra = schemaModel.column?.cellProperties?.(schemaModel);\n    if (!extra) {\n      return props;\n    }\n    return doPropMerge(props, extra);\n  }\n\n  getRowClass(r: number, prop: string): string {\n    const model = getSourceItem(this.dataStore, r) || {};\n    return model[prop] || '';\n  }\n\n  getSaveData(\n    rowIndex: number,\n    colIndex: number,\n    val?: string,\n  ): BeforeSaveDataDetails {\n    const data = this.rowDataModel(rowIndex, colIndex);\n    if (typeof val === 'undefined') {\n      val = getCellData(data.value);\n    }\n    return {\n      ...data,\n      val,\n    };\n  }\n\n  /**\n   * Get cell data model for given rowIndex and colIndex\n   * Used to pass data to editor/renderer\n   */\n  rowDataModel(rowIndex: number, colIndex: number): ColumnDataSchemaModel {\n    const column = this.columns[colIndex];\n    const prop = column?.prop;\n    const model = getSourceItem(this.dataStore, rowIndex) || {};\n    const type = this.dataStore.get('type');\n    return {\n      prop,\n      model,\n      data: this.dataStore.get('source'),\n      column,\n      rowIndex,\n      colIndex,\n      colType: this.type,\n      type,\n      value: getCellRaw(model, column),\n    };\n  }\n\n  getRangeData(\n    d: ChangedRange,\n    columns: ColumnRegular[],\n  ): {\n    changed: DataLookup;\n    mapping: OldNewRangeMapping;\n  } {\n    const changed: DataLookup = {};\n\n    // get original length sizes\n    const copyColLength = d.oldRange.x1 - d.oldRange.x + 1;\n    const copyRowLength = d.oldRange.y1 - d.oldRange.y + 1;\n    const mapping: OldNewRangeMapping = {};\n\n    // rows\n    for (\n      let rowIndex = d.newRange.y, i = 0;\n      rowIndex < d.newRange.y1 + 1;\n      rowIndex++, i++\n    ) {\n      // copy original data link\n      const oldRowIndex = d.oldRange.y + (i % copyRowLength);\n      const copyRow = getSourceItem(this.dataStore, oldRowIndex) || {};\n\n      // columns\n      for (\n        let colIndex = d.newRange.x, j = 0;\n        colIndex < d.newRange.x1 + 1;\n        colIndex++, j++\n      ) {\n        // check if old range area\n        if (\n          rowIndex >= d.oldRange.y &&\n          rowIndex <= d.oldRange.y1 &&\n          colIndex >= d.oldRange.x &&\n          colIndex <= d.oldRange.x1\n        ) {\n          continue;\n        }\n\n        // requested column beyond range\n        if (!this.columns[colIndex]) {\n          continue;\n        }\n        const prop = this.columns[colIndex]?.prop;\n        const copyColIndex = d.oldRange.x + (j % copyColLength);\n        const copyColumnProp = columns[copyColIndex].prop;\n\n        /** if can write */\n        if (!this.isReadOnly(rowIndex, colIndex)) {\n          /** to show before save */\n          if (!changed[rowIndex]) {\n            changed[rowIndex] = {};\n          }\n          changed[rowIndex][prop] = copyRow[copyColumnProp];\n          /** Generate mapping object */\n          if (!mapping[rowIndex]) {\n            mapping[rowIndex] = {};\n          }\n          mapping[rowIndex][prop] = {\n            colIndex: copyColIndex,\n            colProp: copyColumnProp,\n            rowIndex: oldRowIndex,\n          };\n        }\n      }\n    }\n    return {\n      changed,\n      mapping,\n    };\n  }\n\n  getTransformedDataToApply(\n    start: Cell,\n    data: DataFormat[][],\n  ): {\n    changed: DataLookup;\n    range: RangeArea | null;\n  } {\n    const changed: DataLookup = {};\n    const copyRowLength = data.length;\n    const colLength = this.columns.length;\n    const rowLength = this.dataStore.get('items').length;\n    // rows\n    let rowIndex = start.y;\n    let maxCol = 0;\n    for (\n      let i = 0;\n      rowIndex < rowLength && i < copyRowLength;\n      rowIndex++, i++\n    ) {\n      // copy original data link\n      const copyRow = data[i % copyRowLength];\n      const copyColLength = copyRow?.length || 0;\n      // columns\n      let colIndex = start.x;\n      for (\n        let j = 0;\n        colIndex < colLength && j < copyColLength;\n        colIndex++, j++\n      ) {\n        const p = this.columns[colIndex].prop;\n        const currentCol = j % colLength;\n\n        /** if can write */\n        if (!this.isReadOnly(rowIndex, colIndex)) {\n          /** to show before save */\n          if (!changed[rowIndex]) {\n            changed[rowIndex] = {};\n          }\n          changed[rowIndex][p] = copyRow[currentCol];\n        }\n      }\n      maxCol = Math.max(maxCol, colIndex - 1);\n    }\n    const range = getRange(start, {\n      y: rowIndex - 1,\n      x: maxCol,\n    });\n    return {\n      changed,\n      range,\n    };\n  }\n\n  getRangeStaticData(d: RangeArea, value: DataFormat): DataLookup {\n    const changed: DataLookup = {};\n\n    // rows\n    for (let rowIndex = d.y, i = 0; rowIndex < d.y1 + 1; rowIndex++, i++) {\n      // columns\n      for (let colIndex = d.x, j = 0; colIndex < d.x1 + 1; colIndex++, j++) {\n        // requested column beyond range\n        if (!this.columns[colIndex]) {\n          continue;\n        }\n        const p = this.columns[colIndex].prop;\n\n        /** if can write */\n        if (!this.isReadOnly(rowIndex, colIndex)) {\n          /** to show before save */\n          if (!changed[rowIndex]) {\n            changed[rowIndex] = {};\n          }\n          changed[rowIndex][p] = value;\n        }\n      }\n    }\n    return changed;\n  }\n\n  getRangeTransformedToProps(\n    d: RangeArea,\n    store: Observable<DSourceState<DataType, DimensionRows>>,\n  ) {\n    const area: {\n      prop: ColumnProp;\n      rowIndex: number;\n      colIndex: number;\n      model?: DataType;\n      colType: DimensionCols;\n      type: DimensionRows;\n    }[] = [];\n\n    const type = this.dataStore.get('type');\n    // rows\n    for (let rowIndex = d.y, i = 0; rowIndex < d.y1 + 1; rowIndex++, i++) {\n      // columns\n      for (let colIndex = d.x, j = 0; colIndex < d.x1 + 1; colIndex++, j++) {\n        const prop = this.columns[colIndex]?.prop;\n        area.push({\n          prop,\n          rowIndex,\n          colIndex,\n          model: getSourceItem(store, rowIndex),\n          type,\n          colType: this.type,\n        });\n      }\n    }\n    return area;\n  }\n\n  copyRangeArray(\n    range: RangeArea,\n    store: Observable<DSourceState<DataType, DimensionRows>>,\n  ) {\n    const cols = [...this.columns];\n    const props = slice(cols, range.x, range.x1 + 1).map(v => v.prop);\n    const toCopy: DataFormat[][] = [];\n    const mapping: { [rowIndex: number]: { [colProp: ColumnProp]: any } } = {};\n\n    // rows indexes\n    for (let i = range.y; i <= range.y1; i++) {\n      const rgRow: DataFormat[] = [];\n      mapping[i] = {};\n\n      // columns indexes\n      for (let prop of props) {\n        const item = getSourceItem(store, i);\n\n        // if no item - skip\n        if (!item) {\n          continue;\n        }\n        const val = item[prop];\n        rgRow.push(val);\n        mapping[i][prop] = val;\n      }\n\n      toCopy.push(rgRow);\n    }\n    return {\n      data: toCopy,\n      mapping,\n    };\n  }\n\n  destroy() {\n    this.unsubscribe.forEach(f => f());\n  }\n}\n\n/**\n * Checks if the given rowDrag is a service for dragging rows.\n */\nexport function isRowDragService(\n  rowDrag: RowDrag,\n  model: ColumnDataSchemaModel,\n): boolean {\n  if (typeof rowDrag === 'function') {\n    return rowDrag(model);\n  }\n  return !!rowDrag;\n}\n\nfunction mergeClasses(\n  class1: JSXBase.HTMLAttributes<HTMLElement>['class'] = {},\n  class2: JSXBase.HTMLAttributes<HTMLElement>['class'] = {},\n) {\n  if (typeof class1 === 'string') {\n    class1 = { [class1]: true };\n  }\n  if (typeof class2 === 'string') {\n    class2 = { [class2]: true };\n  }\n  return { ...class1, ...class2 };\n}\n\nexport function doPropMerge(existing: CellProps, extra: CellProps) {\n  // if className is provided - remove it from props it messing with stencil\n  if (extra.className) {\n    extra.class = mergeClasses(extra.class, extra.className);\n    delete extra.className;\n  }\n  let props: CellProps = { ...extra, ...existing };\n  // extend existing props\n  if (extra.class) {\n    props.class = mergeClasses(props.class, extra.class);\n  }\n  if (extra.style) {\n    props.style = { ...extra.style, ...props.style };\n  }\n  return props;\n}\n"], "version": 3}