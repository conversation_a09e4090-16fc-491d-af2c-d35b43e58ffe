/*!
 * Built by Revolist OU ❤️
 */
const MIN_COL_SIZE = 30;
const RESIZE_INTERVAL = 40;
const DATA_COL = 'data-rgCol';
const DATA_ROW = 'data-rgRow';
const DISABLED_CLASS = 'disabled';
const CELL_CLASS = 'rgCell';
const ROW_HEADER_TYPE = 'rowHeaders';
const HEADER_CLASS = 'rgHeaderCell';
const HEADER_SORTABLE_CLASS = 'sortable';
const HEADER_ROW_CLASS = 'header-rgRow';
const HEADER_ACTUAL_ROW_CLASS = 'actual-rgRow';
const DRAG_ICON_CLASS = 'revo-drag-icon';
const DRAGGABLE_CLASS = 'revo-draggable';
const FOCUS_CLASS = 'focused-cell';
const SELECTION_BORDER_CLASS = 'selection-border-range';
const MOBILE_CLASS = 'mobile-handler';
const TMP_SELECTION_BG_CLASS = 'temp-bg-range';
const CELL_HANDLER_CLASS = 'autofill-handle';
const EDIT_INPUT_WR = 'edit-input-wrapper';
const DRAGG_TEXT = 'Draggable item';
const GRID_INTERNALS = '__rvgr';
const ROW_FOCUSED_CLASS = 'focused-rgRow';

export { CELL_CLASS as C, DATA_COL as D, EDIT_INPUT_WR as E, FOCUS_CLASS as F, GRID_INTERNALS as G, HEADER_CLASS as H, MIN_COL_SIZE as M, RESIZE_INTERVAL as R, SELECTION_BORDER_CLASS as S, TMP_SELECTION_BG_CLASS as T, DATA_ROW as a, DISABLED_CLASS as b, ROW_HEADER_TYPE as c, HEADER_SORTABLE_CLASS as d, HEADER_ROW_CLASS as e, HEADER_ACTUAL_ROW_CLASS as f, DRAG_ICON_CLASS as g, DRAGGABLE_CLASS as h, MOBILE_CLASS as i, CELL_HANDLER_CLASS as j, DRAGG_TEXT as k, ROW_FOCUSED_CLASS as l };
//# sourceMappingURL=consts.js.map

//# sourceMappingURL=consts.js.map