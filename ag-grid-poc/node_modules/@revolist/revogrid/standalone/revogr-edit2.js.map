{"file": "revogr-edit2.js", "mappings": ";;;;;;;;AAIM,SAAU,SAAS,CAAC,IAAY,EAAA;AACpC,IAAA,MAAM,IAAI,GAAmB;AAC3B,QAAA,YAAY,CAAC,UAAU;AACvB,QAAA,YAAY,CAAC,QAAQ;AACrB,QAAA,YAAY,CAAC,UAAU;AACvB,QAAA,YAAY,CAAC,WAAW;AACxB,QAAA,YAAY,CAAC,IAAI;AACjB,QAAA,YAAY,CAAC,GAAG;AAChB,QAAA,YAAY,CAAC,MAAM;AACnB,QAAA,YAAY,CAAC,SAAS;AACtB,QAAA,YAAY,CAAC,EAAE;AACf,QAAA,YAAY,CAAC,EAAE;AACf,QAAA,YAAY,CAAC,EAAE;AACf,QAAA,YAAY,CAAC,EAAE;AACf,QAAA,YAAY,CAAC,EAAE;AACf,QAAA,YAAY,CAAC,EAAE;AACf,QAAA,YAAY,CAAC,EAAE;AACf,QAAA,YAAY,CAAC,EAAE;AACf,QAAA,YAAY,CAAC,EAAE;AACf,QAAA,YAAY,CAAC,GAAG;AAChB,QAAA,YAAY,CAAC,GAAG;AAChB,QAAA,YAAY,CAAC,GAAG;AAChB,QAAA,YAAY,CAAC,GAAG;AAChB,QAAA,YAAY,CAAC,SAAS;AACtB,QAAA,YAAY,CAAC,OAAO;AACpB,QAAA,YAAY,CAAC,KAAK;AAClB,QAAA,YAAY,CAAC,MAAM;AACnB,QAAA,YAAY,CAAC,KAAK;AAClB,QAAA,YAAY,CAAC,SAAS;AACtB,QAAA,YAAY,CAAC,GAAG;KACjB;IAED,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;AAClC;AAEA;AACgB,SAAA,SAAS,CAAC,IAAY,EAAE,QAAgB,EAAA;IACtD,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QACrC,OAAO;AACL,YAAA,YAAY,CAAC,YAAY;AACzB,YAAA,YAAY,CAAC,aAAa;AAC1B,YAAA,YAAY,CAAC,eAAe;AAC7B,SAAA,CAAC,QAAQ,CAAC,IAAI,CAAC;;AAGlB,IAAA,OAAO,IAAI,KAAK,YAAY,CAAC,OAAO;AACtC;AAEM,SAAU,aAAa,CAAC,IAAkB,EAAA;IAC9C,OAAO;AACL,QAAA,YAAY,CAAC,OAAO;AACpB,QAAA,YAAY,CAAC,YAAY;AACzB,QAAA,YAAY,CAAC,aAAa;AAC1B,QAAA,YAAY,CAAC,eAAe;AAC7B,KAAA,CAAC,QAAQ,CAAC,IAAI,CAAC;AAClB;AAEM,SAAU,OAAO,CAAC,IAAY,EAAA;IAClC,OAAO,WAAW,CAAC,SAAS,KAAK,IAAI,IAAI,WAAW,CAAC,MAAM,KAAK,IAAI;AACtE;AAEM,SAAU,KAAK,CAAC,IAAY,EAAA;AAChC,IAAA,OAAO,WAAW,CAAC,GAAG,KAAK,IAAI;AACjC;AACM,SAAU,aAAa,CAAC,GAAW,EAAA;AACvC,IAAA,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG;AAC9B;AAEM,SAAU,eAAe,CAAC,GAAW,EAAA;AACzC,IAAA,OAAO,SAAS,CAAC,KAAK,KAAK,GAAG;AAChC;AAEM,SAAU,KAAK,CAAC,KAAoB,EAAA;AACxC,IAAA,QACE,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM;AACvC,SAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,EACxC;AACJ;AACM,SAAU,MAAM,CAAC,KAAoB,EAAA;AACzC,IAAA,QACE,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM;AACvC,SAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,EACxC;AACJ;AACM,SAAU,OAAO,CAAC,KAAoB,EAAA;AAC1C,IAAA,QACE,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM;AACvC,SAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,EACxC;AACJ;AACM,SAAU,KAAK,CAAC,KAAoB,EAAA;AACxC,IAAA,QACE,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM;AACvC,SAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,EACxC;AACJ;;MChFa,UAAU,CAAA;IAMrB,WACS,CAAA,IAA2B,EAC1B,YAA2B,EAAA;QAD5B,IAAI,CAAA,IAAA,GAAJ,IAAI;QACH,IAAY,CAAA,YAAA,GAAZ,YAAY;QAPtB,IAAS,CAAA,SAAA,GAA4B,IAAI;QAEzC,IAAO,CAAA,OAAA,GAAmB,IAAI;QAC9B,IAAQ,CAAA,QAAA,GAAc,SAAS;;AAO/B;;AAEG;AACH,IAAA,MAAM,kBAAkB,GAAA;;AACtB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,OAAO,EAAE;AACf,YAAA,CAAA,EAAA,GAAA,IAAI,CAAC,SAAS,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK,EAAE;;;AAI3B,IAAA,SAAS,CAAC,CAAgB,EAAA;QACxB,MAAM,OAAO,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC;QACtC,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAE7B,QAAA,IACE,CAAC,QAAQ,IAAI,OAAO;AACpB,YAAA,CAAC,CAAC,MAAM;AACR,YAAA,IAAI,CAAC,YAAY;AACjB,YAAA,CAAC,CAAC,CAAC,WAAW,EACd;;YAEA,IAAI,CAAC,gBAAgB,EAAE;;YAEvB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC;;;AAIhD;;AAEG;IACH,gBAAgB,GAAA;;AACd,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,SAAS,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAI,EAAE;;AAGxB;;AAEG;IACH,QAAQ,GAAA;;AACN,QAAA,OAAO,MAAA,IAAI,CAAC,SAAS,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,KAAK;;AAG9B;;;;;;AAMG;IACH,MAAM,CAAC,CAAuB,EAAE,eAAoB,EAAA;;QAClD,OAAO,CAAC,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,EAAE,MAAM;AACZ,YAAA,YAAY,EAAE,OAAO;;YAErB,KAAK,EAAE,MAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAI,EAAE;;AAE/B,YAAA,GAAG,EAAE,CAAC,EAA2B,KAAI;AACnC,gBAAA,IAAI,CAAC,SAAS,GAAG,EAAE;aACpB;;YAED,SAAS,EAAE,CAAC,CAAgB,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AACnD,SAAA,CAAC;;AAEL;;ACxFD;AACM,SAAU,WAAW,CAAC,EAAgB,EAAA;AAC1C,IAAA,OAAO,CAAC,EAAC,EAAE,KAAA,IAAA,IAAF,EAAE,KAAF,MAAA,GAAA,MAAA,GAAA,EAAE,CAAE,OAAO,CAAC,CAAI,CAAA,EAAA,aAAa,CAAE,CAAA,CAAC,CAAA;AAC3C;AAGA;AACM,SAAU,wBAAwB,CACtC,MAAW,EAAA;IAEX,OAAO,OAAO,MAAM,KAAK,UAAU,IAAI,OAAO,MAAM,CAAC,SAAS,KAAK,QAAQ;AAC7E;;ACfA,MAAM,kBAAkB,GAAG,isMAAisM;;MC+B/sM,QAAQ,iBAAAA,kBAAA,CAAA,MAAA,QAAA,SAAA,WAAA,CAAA;AAJrB,IAAA,WAAA,GAAA;;;;;AAmBE;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAG,KAAK;AAqBnB,QAAA,IAAa,CAAA,aAAA,GAAsB,IAAI;AACvC,QAAA,IAAkB,CAAA,kBAAA,GAAG,KAAK;AAwInC;AAtIC;;AAEG;AACO,IAAA,MAAM,aAAa,GAAA;AAC3B,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;;AAGhC;;;AAGG;AACO,IAAA,MAAM,gBAAgB,GAAA;;QAC9B,CAAA,EAAA,GAAA,MAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,gBAAgB,kDAAI;;IAG1C,UAAU,GAAA;;AACR,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;QAC9B,MAAM,GAAG,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAI;;;QAG5C,IAAI,MAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,cAAc,EAAE;YACtC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC;AACtD,YAAA,IAAI,OAAO,KAAK,KAAK,EAAE;gBACrB;;;AAGJ,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;;AAGxB;;;;AAIG;IACH,MAAM,CAAC,GAAS,EAAE,YAAsB,EAAA;AACtC,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;AAC9B,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtB,gBAAA,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtB,gBAAA,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;AACxB,gBAAA,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;gBACxB,GAAG;gBACH,YAAY;AACb,aAAA,CAAC;;;IAIN,mBAAmB,GAAA;;QAEjB,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACtC;;AAEF,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;;;;AAK/B,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;;AAEf,YAAA,IAAI,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACzC,IAAI,CAAC,aAAa,GAAG,IAAI,IAAI,CAAC,MAAM,CAClC,IAAI,CAAC,MAAM;;AAEX,gBAAA,CAAC,CAAC,EAAE,YAAY,KAAI;AAClB,oBAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC;iBAC7B;;AAED,gBAAA,SAAS,IAAG;AACV,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;AAC9B,oBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;AAChC,iBAAC,CACF;;;iBAEI;gBACL,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAC9B,IAAI,CAAC,MAAM;;AAEX,gBAAA,CAAC,CAAC,EAAE,YAAY,KAAI;AAClB,oBAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC;iBAC7B;;AAED,gBAAA,SAAS,IAAG;AACV,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;AAC9B,oBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;AAChC,iBAAC,CACF;;YAEH;;;QAGF,IAAI,CAAC,aAAa,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,YAAY,KAC/D,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC,CAC7B;;IAGH,kBAAkB,GAAA;;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB;;QAEF,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB;AAC3D,QAAA,CAAA,EAAA,GAAA,MAAA,IAAI,CAAC,aAAa,EAAC,kBAAkB,kDAAI;;IAG3C,oBAAoB,GAAA;;AAClB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;;;AAGpB,YAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC5B,IAAI,CAAC,UAAU,EAAE;;;AAIrB,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB;;AAGF,QAAA,CAAA,EAAA,GAAA,MAAA,IAAI,CAAC,aAAa,EAAC,oBAAoB,kDAAI;AAC3C,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI;AACjC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;IAG3B,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ;YAC3C,QACE,EAAC,IAAI,EAAA,EAAC,KAAK,EAAE,aAAa,EACvB,EAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAC7C;;AAGX,QAAA,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/utils/key.utils.ts", "src/components/editors/text-editor.ts", "src/components/editors/edit.utils.ts", "src/components/editors/revogr-edit-style.scss?tag=revogr-edit", "src/components/editors/revogr-edit.tsx"], "sourcesContent": ["import KeyCodes<PERSON>num, { codesLetter, keyValues } from './key.codes';\nimport OsPlatform from './platform';\n\n\nexport function isMetaKey(code: number): boolean {\n  const keys: KeyCodesEnum[] = [\n    KeyCodesEnum.ARROW_DOWN,\n    KeyCodesEnum.ARROW_UP,\n    KeyCodesEnum.ARROW_LEFT,\n    KeyCodesEnum.ARROW_RIGHT,\n    KeyCodesEnum.HOME,\n    KeyCodesEnum.END,\n    KeyCodesEnum.DELETE,\n    KeyCodesEnum.BACKSPACE,\n    KeyCodesEnum.F1,\n    KeyCodesEnum.F2,\n    KeyCodesEnum.F3,\n    KeyCodesEnum.F4,\n    KeyCodesEnum.F5,\n    KeyCodesEnum.F6,\n    KeyCodesEnum.F7,\n    KeyCodesEnum.F8,\n    KeyCodesEnum.F9,\n    KeyCodesEnum.F10,\n    KeyCodesEnum.F11,\n    KeyCodesEnum.F12,\n    KeyCodesEnum.TAB,\n    KeyCodesEnum.PAGE_DOWN,\n    KeyCodesEnum.PAGE_UP,\n    KeyCodesEnum.ENTER,\n    KeyCodesEnum.ESCAPE,\n    KeyCodesEnum.SHIFT,\n    KeyCodesEnum.CAPS_LOCK,\n    KeyCodesEnum.ALT,\n  ];\n\n  return keys.indexOf(code) !== -1;\n}\n\n// navigator.platform\nexport function isCtrlKey(code: number, platform: string): boolean {\n  if (platform.includes(OsPlatform.mac)) {\n    return [\n      KeyCodesEnum.COMMAND_LEFT,\n      KeyCodesEnum.COMMAND_RIGHT,\n      KeyCodesEnum.COMMAND_FIREFOX,\n    ].includes(code);\n  }\n\n  return code === KeyCodesEnum.CONTROL;\n}\n\nexport function isCtrlMetaKey(code: KeyCodesEnum): boolean {\n  return [\n    KeyCodesEnum.CONTROL,\n    KeyCodesEnum.COMMAND_LEFT,\n    KeyCodesEnum.COMMAND_RIGHT,\n    KeyCodesEnum.COMMAND_FIREFOX,\n  ].includes(code);\n}\n\nexport function isClear(code: string): boolean {\n  return codesLetter.BACKSPACE === code || codesLetter.DELETE === code;\n}\n\nexport function isTab(code: string): boolean {\n  return codesLetter.TAB === code;\n}\nexport function isTabKeyValue(key: string): boolean {\n  return keyValues.TAB === key;\n}\n\nexport function isEnterKeyValue(key: string): boolean {\n  return keyValues.ENTER === key;\n}\n\nexport function isCut(event: KeyboardEvent): boolean {\n  return (\n    (event.ctrlKey && event.code === 'KeyX') || // Ctrl + X on Windows\n    (event.metaKey && event.code === 'KeyX')\n  ); // Cmd + X on Mac\n}\nexport function isCopy(event: KeyboardEvent): boolean {\n  return (\n    (event.ctrlKey && event.code === 'KeyC') || // Ctrl + C on Windows\n    (event.metaKey && event.code === 'KeyC')\n  ); // Cmd + C on Mac\n}\nexport function isPaste(event: KeyboardEvent): boolean {\n  return (\n    (event.ctrlKey && event.code === 'KeyV') || // Ctrl + V on Windows\n    (event.metaKey && event.code === 'KeyV')\n  ); // Cmd + V on Mac\n}\nexport function isAll(event: KeyboardEvent): boolean {\n  return (\n    (event.ctrlKey && event.code === 'KeyA') || // Ctrl + A on Windows\n    (event.metaKey && event.code === 'KeyA')\n  ); // Cmd + A on Mac\n}\n", "import { type VNode, h as createElement } from '@stencil/core';\nimport { isEnterKeyValue, isTab } from '../../utils/key.utils';\nimport { timeout } from '../../utils';\nimport type { EditCell, EditorBase, ColumnDataSchemaModel } from '@type';\n\n/**\n * Represents a cell editor in a grid.\n *\n * It's a good place to start with your own editor.\n * It manages the editing of cells by handling events, saving data, rendering the editor UI, and managing the lifecycle of the editor instance.\n */\n\n/**\n * Callback triggered on cell editor save\n * Closes editor when called\n * @param preventFocus - if true editor will not be closed and next cell will not be focused\n */\nexport type SaveCallback = (value: any, preventFocus: boolean) => void;\n\nexport class TextEditor implements EditorBase {\n  editInput: HTMLInputElement | null = null;\n\n  element: Element | null = null;\n  editCell?: EditCell = undefined;\n\n  constructor(\n    public data: ColumnDataSchemaModel,\n    private saveCallback?: SaveCallback,\n  ) {}\n\n  /**\n   * Callback triggered on cell editor render\n   */\n  async componentDidRender(): Promise<void> {\n    if (this.editInput) {\n      await timeout();\n      this.editInput?.focus();\n    }\n  }\n\n  onKeyDown(e: KeyboardEvent) {\n    const isEnter = isEnterKeyValue(e.key);\n    const isKeyTab = isTab(e.key);\n\n    if (\n      (isKeyTab || isEnter) &&\n      e.target &&\n      this.saveCallback &&\n      !e.isComposing\n    ) {\n      // blur is needed to avoid autoscroll\n      this.beforeDisconnect();\n      // request callback which will close cell after all\n      this.saveCallback(this.getValue(), isKeyTab);\n    }\n  }\n\n  /**\n   * IMPORTANT: Prevent scroll glitches when editor is closed and focus is on current input element.\n   */\n  beforeDisconnect() {\n    this.editInput?.blur();\n  }\n\n  /**\n   * Get value from input\n   */\n  getValue() {\n    return this.editInput?.value;\n  }\n\n  /**\n   * Render method for Editor plugin.\n   * Renders input element with passed data from cell.\n   * @param {Function} h - h function from stencil render.\n   * @param {Object} _additionalData - additional data from plugin.\n   * @returns {VNode} - input element.\n   */\n  render(h: typeof createElement, _additionalData: any): VNode | VNode[] {\n    return h('input', {\n      type: 'text',\n      enterKeyHint: 'enter',\n      // set input value from cell data\n      value: this.editCell?.val ?? '',\n      // save input element as ref for further usage\n      ref: (el: HTMLInputElement | null) => {\n        this.editInput = el;\n      },\n      // listen to keydown event on input element\n      onKeyDown: (e: KeyboardEvent) => this.onKeyDown(e),\n    });\n  }\n}\n", "import { EDIT_INPUT_WR } from '../../utils/consts';\nimport {\n  EditorCtrConstructible,\n} from '@type';\n// is edit input\nexport function isEditInput(el?: HTMLElement) {\n  return !!el?.closest(`.${EDIT_INPUT_WR}`);\n}\n\n\n// Type guard for EditorCtrConstructible\nexport function isEditorCtrConstructible(\n  editor: any,\n): editor is EditorCtrConstructible {\n  return typeof editor === 'function' && typeof editor.prototype === 'object';\n}\n", "revogr-edit {\n  display: block;\n  position: absolute;\n  background-color: #fff;\n\n  input {\n    height: 100%;\n    width: 100%;\n    box-sizing: border-box;\n  }\n\n  revo-dropdown {\n    height: 100%;\n\n    &.shrink {\n      fieldset legend > span {\n        display: none;\n      }\n    }\n  }\n}\n", "import {\n  Component,\n  Event,\n  EventEmitter,\n  Prop,\n  h,\n  Element,\n  Host,\n  Method,\n} from '@stencil/core';\nimport { EDIT_INPUT_WR } from '../../utils/consts';\nimport { TextEditor } from './text-editor';\nimport { ColumnDataSchemaModel } from '@type';\nimport {\n  EditCell,\n  EditorCtr,\n  SaveDataDetails,\n  EditorBase,\n} from '@type';\n\nimport { isEditorCtrConstructible } from './edit.utils';\n\n/**\n * Represents a cell editor in a grid.\n * It manages the editing of cells by handling events, saving data, rendering the editor UI,\n * and managing the lifecycle of the editor instance.\n */\n@Component({\n  tag: 'revogr-edit',\n  styleUrl: 'revogr-edit-style.scss',\n})\nexport class RevoEdit {\n  /**\n   * Cell to edit data.\n   */\n  @Prop() editCell: EditCell;\n\n  /**\n   * Column data for editor.\n   */\n  @Prop() column: ColumnDataSchemaModel | null;\n  /**\n   * Custom editors register\n   */\n  @Prop() editor: Editor<PERSON><PERSON> | null;\n\n  /**\n   * Save on editor close. Defines if data should be saved on editor close.\n   */\n  @Prop() saveOnClose = false;\n  /**\n   * Additional data to pass to renderer\n   */\n  @Prop() additionalData: any;\n\n  /**\n   * Cell edit event initiator, first in the cellEdit event chain\n   */\n  @Event({ eventName: 'celleditinit' }) cellEdit: EventEmitter<SaveDataDetails>;\n\n  /**\n   * Close editor event\n   * pass true if requires focus next\n   */\n  @Event({ eventName: 'closeedit' }) closeEdit: EventEmitter<\n    boolean | undefined\n  >;\n\n  /** Edit session editor */\n  @Element() element: HTMLElement;\n  private currentEditor: EditorBase | null = null;\n  private preventSaveOnClose = false;\n\n  /**\n   * Cancel pending changes flag. Editor will be closed without autosave.\n   */\n  @Method() async cancelChanges() {\n    this.preventSaveOnClose = true;\n  }\n\n  /**\n   * Before editor got disconnected.\n   * Can be triggered multiple times before actual disconnect.\n   */\n  @Method() async beforeDisconnect() {\n    this.currentEditor?.beforeDisconnect?.();\n  }\n\n  onAutoSave() {\n    this.preventSaveOnClose = true;\n    const val = this.currentEditor?.getValue?.();\n    // For Editor plugin internal usage.\n    // When you want to prevent save and use custom save of your own.\n    if (this.currentEditor?.beforeAutoSave) {\n      const canSave = this.currentEditor.beforeAutoSave(val);\n      if (canSave === false) {\n        return;\n      }\n    }\n    this.onSave(val, true);\n  }\n\n  /**\n   * Callback triggered when cell editor saved.\n   * Closes editor when called.\n   * @param preventFocus - if true, editor will not be closed & next cell will not be focused.\n   */\n  onSave(val?: any, preventFocus?: boolean) {\n    this.preventSaveOnClose = true;\n    if (this.editCell) {\n      this.cellEdit.emit({\n        rgCol: this.editCell.x,\n        rgRow: this.editCell.y,\n        type: this.editCell.type,\n        prop: this.editCell.prop,\n        val,\n        preventFocus,\n      });\n    }\n  }\n\n  componentWillRender() {\n    // Active editor present and not yet closed.\n    if (this.currentEditor || !this.column) {\n      return;\n    }\n    this.preventSaveOnClose = false;\n\n    // Custom editor usage.\n    // Start with TextEditor (editors/text.tsx) for Custom editor.\n    // It can be class or function\n    if (this.editor) {\n      // if editor is constructible\n      if (isEditorCtrConstructible(this.editor)) {\n        this.currentEditor = new this.editor(\n          this.column,\n          // save callback\n          (e, preventFocus) => {\n            this.onSave(e, preventFocus);\n          },\n          // cancel callback\n          focusNext => {\n            this.preventSaveOnClose = true;\n            this.closeEdit.emit(focusNext);\n          },\n        );\n      // if editor is function\n      } else {\n        this.currentEditor = this.editor(\n          this.column,\n          // save callback\n          (e, preventFocus) => {\n            this.onSave(e, preventFocus);\n          },\n          // cancel callback\n          focusNext => {\n            this.preventSaveOnClose = true;\n            this.closeEdit.emit(focusNext);\n          },\n        );\n      }\n      return;\n    }\n    // Default text editor usage\n    this.currentEditor = new TextEditor(this.column, (e, preventFocus) =>\n      this.onSave(e, preventFocus),\n    );\n  }\n\n  componentDidRender() {\n    if (!this.currentEditor) {\n      return;\n    }\n    this.currentEditor.element = this.element.firstElementChild;\n    this.currentEditor.componentDidRender?.();\n  }\n\n  disconnectedCallback() {\n    if (this.saveOnClose) {\n      // Can not be cancelled by `preventSaveOnClose` prop.\n      // Editor requires `getValue` to be able to save.\n      if (!this.preventSaveOnClose) {\n        this.onAutoSave();\n      }\n    }\n\n    this.preventSaveOnClose = false;\n    if (!this.currentEditor) {\n      return;\n    }\n\n    this.currentEditor.disconnectedCallback?.();\n    this.currentEditor.element = null;\n    this.currentEditor = null;\n  }\n\n  render() {\n    if (this.currentEditor) {\n      this.currentEditor.editCell = this.editCell;\n      return (\n        <Host class={EDIT_INPUT_WR}>\n          {this.currentEditor.render(h, this.additionalData)}\n        </Host>\n      );\n    }\n    return '';\n  }\n}\n"], "version": 3}