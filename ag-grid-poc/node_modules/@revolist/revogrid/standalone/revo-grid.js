/*!
 * Built by Revolist OU ❤️
 */
import { h, proxyCustomElement, HTMLElement as HTMLElement$1, createEvent, Host } from '@stencil/core/internal/client';
import { K as reduce, L as baseEach, D as getColumnType, c as columnTypes, M as toInteger, v as isGrouping, u as getGroupingName, r as rowTypes, C as getCellDataParsed, B as getCellRaw, J as getColumnByProp, j as GROUP_EXPANDED, y as getParsedGroup, z as isSameGroup, G as GROUP_DEPTH, f as PSEUDO_GROUP_ITEM_VALUE, e as PSEUDO_GROUP_ITEM_ID, p as GROUPING_ROW_TYPE, q as getSource, h as PSEUDO_GROUP_COLUMN, t as gatherGrouping, o as GROUP_EXPAND_EVENT, w as isGroupingColumn, s as getExpanded, F as isColGrouping, i as isRowType, E as getColumnSizes, N as EMPTY_INDEX, H as getColumns, O as SelectionStoreConnector } from './column.service.js';
import { j as createStore, i as setStore, k as identity, l as isArray, b as getSourceItem, m as baseProperty, n as isArrayLike, o as getTag, q as baseKeys, g as getPhysical, e as setItems, D as DataStore, f as getSourceItemVirtualIndexByProp, d as setSourceByPhysicalIndex, s as setSourceByVirtualIndex, a as getVisibleSourceItem, h as gatherTrimmedItems } from './data.store.js';
import { d as debounce } from './debounce.js';
import { R as RESIZE_INTERVAL } from './consts.js';
import { c as calculateDimensionData, g as getItemByPosition, a as getItemByIndex } from './dimension.helpers.js';
import './platform.js';
import { f as calculateRowHeaderSize, V as ViewportStore, h as defineCustomElement$6 } from './revogr-row-headers2.js';
import { g as getScrollbarSize, t as timeout } from './index2.js';
import { b as FILTER_PROP, i as isFilterBtn } from './filter.button.js';
import { i as isObjectLike, b as baseGetTag } from './toNumber.js';
import { v as viewportDataPartition, F as FOOTER_SLOT, C as CONTENT_SLOT, H as HEADER_SLOT, D as DATA_SLOT, d as defineCustomElement$3 } from './revogr-viewport-scroll2.js';
import { O as ON_COLUMN_CLICK, d as dispatch, b as defineCustomElement$9 } from './revogr-header2.js';
import { g as getPropertyFromEvent } from './selection.utils.js';
import { d as defineCustomElement$f } from './revogr-attribution2.js';
import { d as defineCustomElement$e } from './revogr-clipboard2.js';
import { d as defineCustomElement$d } from './revogr-data2.js';
import { n as defineCustomElement$c } from './revogr-edit2.js';
import { d as defineCustomElement$b } from './revogr-extra2.js';
import { d as defineCustomElement$a } from './revogr-focus2.js';
import { d as defineCustomElement$8 } from './revogr-order-editor2.js';
import { d as defineCustomElement$7 } from './revogr-overlay-selection2.js';
import { d as defineCustomElement$5 } from './revogr-scroll-virtual2.js';
import { d as defineCustomElement$4 } from './revogr-temp-range2.js';
import { d as defineCustomElement$2 } from './vnode-converter.js';

class ThemeCompact {
    constructor() {
        this.defaultRowSize = 32;
    }
}

class ThemeDefault {
    constructor() {
        this.defaultRowSize = 27;
    }
}

class ThemeMaterial {
    constructor() {
        this.defaultRowSize = 42;
    }
}

const DEFAULT_THEME = 'default';
const allowedThemes = [
    DEFAULT_THEME,
    'material',
    'compact',
    'darkMaterial',
    'darkCompact',
];
class ThemeService {
    get theme() {
        return this.currentTheme;
    }
    get rowSize() {
        return this.customRowSize || this.currentTheme.defaultRowSize;
    }
    set rowSize(size) {
        this.customRowSize = size;
    }
    constructor(cfg) {
        this.customRowSize = 0;
        this.customRowSize = cfg.rowSize;
        this.register('default');
    }
    register(theme) {
        const parsedTheme = getTheme(theme);
        switch (parsedTheme) {
            case 'material':
            case 'darkMaterial':
                this.currentTheme = new ThemeMaterial();
                break;
            case 'compact':
            case 'darkCompact':
                this.currentTheme = new ThemeCompact();
                break;
            default:
                this.currentTheme = new ThemeDefault();
                break;
        }
    }
}
function getTheme(theme) {
    if (theme && allowedThemes.indexOf(theme) > -1) {
        return theme;
    }
    return DEFAULT_THEME;
}

/**
 * Plugin which recalculates realSize on changes of sizes, originItemSize and count
 */
const recalculateRealSizePlugin = (storeService) => {
    /**
     * Recalculates realSize if size, origin size or count changes
     */
    return {
        /**
         * Reacts on changes of count, sizes and originItemSize
         */
        set(k) {
            switch (k) {
                case 'count':
                case 'sizes':
                case 'originItemSize': {
                    // recalculate realSize
                    let realSize = 0;
                    const count = storeService.store.get('count');
                    for (let i = 0; i < count; i++) {
                        realSize +=
                            storeService.store.get('sizes')[i] ||
                                storeService.store.get('originItemSize');
                    }
                    storeService.setStore({ realSize });
                    break;
                }
            }
        },
    };
};

/**
 * Plugin for trimming
 *
 * 1.a. Retrieves the previous sizes value. Saves the resulting trimmed data as a new sizes value.
 * 1.b. Stores a reference to the trimmed data to prevent further changes.
 * 2. Removes multiple and shifts the data based on the trimmed value.
 */
const trimmedPlugin = (storeService) => {
    let trimmingObject = null;
    let trimmedPreviousSizes = null;
    return {
        set(key, val) {
            switch (key) {
                case 'sizes': {
                    // prevent changes after trimming
                    if (trimmingObject && trimmingObject === val) {
                        trimmingObject = null;
                        return;
                    }
                    trimmedPreviousSizes = null;
                    break;
                }
                case 'trimmed': {
                    const trim = val;
                    if (!trimmedPreviousSizes) {
                        trimmedPreviousSizes = storeService.store.get('sizes');
                    }
                    trimmingObject = removeMultipleAndShift(trimmedPreviousSizes, trim || {});
                    // save a reference to the trimmed object to prevent changes after trimming
                    storeService.setSizes(trimmingObject);
                    break;
                }
            }
        },
    };
};
function removeMultipleAndShift(items, toRemove) {
    const newItems = {};
    const sortedIndexes = Object.keys(items || {})
        .map(Number)
        .sort((a, b) => a - b);
    const lastIndex = sortedIndexes[sortedIndexes.length - 1];
    let shift = 0;
    for (let i = 0; i <= lastIndex; i++) {
        if (toRemove[i] !== undefined) {
            shift++;
            // skip already removed
            if (items[i] !== undefined) {
                continue;
            }
        }
        if (items[i] !== undefined) {
            newItems[i - shift] = items[i];
        }
    }
    return newItems;
}

/**
 * Storing pre-calculated
 * Dimension information and sizes
 */
function initialBase() {
    return {
        indexes: [],
        count: 0,
        // hidden items
        trimmed: null,
        // virtual item index to size
        sizes: {},
        // order in indexes[] to coordinate
        positionIndexToItem: {},
        // initial element to coordinate ^
        indexToItem: {},
        positionIndexes: [],
    };
}
function initialState() {
    return Object.assign(Object.assign({}, initialBase()), { 
        // size which all items can take
        realSize: 0, 
        // initial item size if it wasn't changed
        originItemSize: 0 });
}
class DimensionStore {
    constructor(type) {
        this.type = type;
        this.store = createStore(initialState());
        this.store.use(trimmedPlugin({
            store: this.store,
            setSizes: this.setDimensionSize.bind(this),
        }));
        this.store.use(recalculateRealSizePlugin({
            store: this.store,
            setStore: this.setStore.bind(this),
        }));
    }
    getCurrentState() {
        const state = initialState();
        const keys = Object.keys(state);
        return reduce(keys, (r, k) => {
            const data = this.store.get(k);
            r[k] = data;
            return r;
        }, state);
    }
    dispose() {
        setStore(this.store, initialState());
    }
    setStore(data) {
        setStore(this.store, data);
    }
    drop() {
        setStore(this.store, initialBase());
    }
    /**
     * Set custom dimension sizes and overwrite old
     * Generates new indexes based on sizes
     * @param sizes - sizes to set
     */
    setDimensionSize(sizes = {}) {
        const dimensionData = calculateDimensionData(this.store.get('originItemSize'), sizes);
        setStore(this.store, Object.assign(Object.assign({}, dimensionData), { sizes }));
    }
    updateSizesPositionByIndexes(newItemsOrder, prevItemsOrder = []) {
        // Move custom sizes to new order
        const customSizes = Object.assign({}, this.store.get('sizes'));
        if (!Object.keys(customSizes).length) {
            return;
        }
        // Step 1: Create a map of original indices, but allow duplicates by storing arrays of indices
        const originalIndices = {};
        prevItemsOrder.forEach((physIndex, virtIndex) => {
            if (!originalIndices[physIndex]) {
                originalIndices[physIndex] = [];
            }
            originalIndices[physIndex].push(virtIndex); // Store all indices for each value
        });
        // Step 2: Create new sizes based on new item order
        const newSizes = {};
        newItemsOrder.forEach((physIndex, virtIndex) => {
            const indices = originalIndices[physIndex]; // Get all original indices for this value
            if (indices && indices.length > 0) {
                const originalIndex = indices.shift(); // Get the first available original index
                if (originalIndex !== undefined && originalIndex !== virtIndex && customSizes[originalIndex]) {
                    newSizes[virtIndex] = customSizes[originalIndex];
                    delete customSizes[originalIndex];
                }
            }
        });
        // Step 3: Set new sizes if there are changes
        if (Object.keys(newSizes).length) {
            this.setDimensionSize(Object.assign(Object.assign({}, customSizes), newSizes));
        }
    }
}

/**
 * Base layer for plugins
 * Provide minimal starting core for plugins to work
 * Extend this class to create plugin
 */
class BasePlugin {
    constructor(revogrid, providers) {
        this.revogrid = revogrid;
        this.providers = providers;
        this.h = h;
        this.subscriptions = {};
    }
    /**
     *
     * @param eventName - event name to subscribe to in revo-grid component (e.g. 'beforeheaderclick')
     * @param callback - callback function for event
     */
    addEventListener(eventName, callback) {
        this.revogrid.addEventListener(eventName, callback);
        this.subscriptions[eventName] = callback;
    }
    /**
     * Subscribe to property change in revo-grid component
     * You can return false in callback to prevent default value set
     *
     * @param prop - property name
     * @param callback - callback function
     * @param immediate - trigger callback immediately with current value
     */
    watch(prop, callback, { immediate } = { immediate: false }) {
        const nativeValueDesc = Object.getOwnPropertyDescriptor(this.revogrid, prop) ||
            Object.getOwnPropertyDescriptor(this.revogrid.constructor.prototype, prop);
        // Overwrite property descriptor for this instance
        Object.defineProperty(this.revogrid, prop, {
            set(val) {
                var _a;
                const keepDefault = callback(val);
                if (keepDefault === false) {
                    return;
                }
                // Continue with native behavior
                return (_a = nativeValueDesc === null || nativeValueDesc === void 0 ? void 0 : nativeValueDesc.set) === null || _a === void 0 ? void 0 : _a.call(this, val);
            },
            get() {
                var _a;
                // Continue with native behavior
                return (_a = nativeValueDesc === null || nativeValueDesc === void 0 ? void 0 : nativeValueDesc.get) === null || _a === void 0 ? void 0 : _a.call(this);
            },
        });
        if (immediate) {
            callback(nativeValueDesc === null || nativeValueDesc === void 0 ? void 0 : nativeValueDesc.value);
        }
    }
    /**
     * Remove event listener
     * @param eventName
     */
    removeEventListener(eventName) {
        this.revogrid.removeEventListener(eventName, this.subscriptions[eventName]);
        delete this.subscriptions[eventName];
    }
    /**
     * Emit event from revo-grid component
     * Event can be cancelled by calling event.preventDefault() in callback
     */
    emit(eventName, detail) {
        const event = new CustomEvent(eventName, { detail, cancelable: true });
        this.revogrid.dispatchEvent(event);
        return event;
    }
    /**
     * Clear all subscriptions
     */
    clearSubscriptions() {
        for (let type in this.subscriptions) {
            this.removeEventListener(type);
        }
    }
    /**
     * Destroy plugin and clear all subscriptions
     */
    destroy() {
        this.clearSubscriptions();
    }
}

/**
 * A specialized version of `_.forEach` for arrays without support for
 * iteratee shorthands.
 *
 * @private
 * @param {Array} [array] The array to iterate over.
 * @param {Function} iteratee The function invoked per iteration.
 * @returns {Array} Returns `array`.
 */
function arrayEach(array, iteratee) {
  var index = -1,
      length = array == null ? 0 : array.length;

  while (++index < length) {
    if (iteratee(array[index], index, array) === false) {
      break;
    }
  }
  return array;
}

/**
 * Casts `value` to `identity` if it's not a function.
 *
 * @private
 * @param {*} value The value to inspect.
 * @returns {Function} Returns cast function.
 */
function castFunction(value) {
  return typeof value == 'function' ? value : identity;
}

/**
 * Iterates over elements of `collection` and invokes `iteratee` for each element.
 * The iteratee is invoked with three arguments: (value, index|key, collection).
 * Iteratee functions may exit iteration early by explicitly returning `false`.
 *
 * **Note:** As with other "Collections" methods, objects with a "length"
 * property are iterated like arrays. To avoid this behavior use `_.forIn`
 * or `_.forOwn` for object iteration.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @alias each
 * @category Collection
 * @param {Array|Object} collection The collection to iterate over.
 * @param {Function} [iteratee=_.identity] The function invoked per iteration.
 * @returns {Array|Object} Returns `collection`.
 * @see _.forEachRight
 * @example
 *
 * _.forEach([1, 2], function(value) {
 *   console.log(value);
 * });
 * // => Logs `1` then `2`.
 *
 * _.forEach({ 'a': 1, 'b': 2 }, function(value, key) {
 *   console.log(key);
 * });
 * // => Logs 'a' then 'b' (iteration order is not guaranteed).
 */
function forEach(collection, iteratee) {
  var func = isArray(collection) ? arrayEach : baseEach;
  return func(collection, castFunction(iteratee));
}

/**
 * Plugin module for revo-grid grid system
 * Add support for automatic column resize
 */
const LETTER_BLOCK_SIZE = 7;
class AutoSizeColumnPlugin extends BasePlugin {
    constructor(revogrid, providers, config) {
        super(revogrid, providers);
        this.providers = providers;
        this.config = config;
        this.autoSizeColumns = null;
        /** for edge case when no columns defined before data */
        this.dataResolve = null;
        this.dataReject = null;
        this.letterBlockSize = (config === null || config === void 0 ? void 0 : config.letterBlockSize) || LETTER_BLOCK_SIZE;
        // create test container to check text width
        if (config === null || config === void 0 ? void 0 : config.preciseSize) {
            this.precsizeCalculationArea = this.initiatePresizeElement();
            revogrid.appendChild(this.precsizeCalculationArea);
        }
        const aftersourceset = ({ detail: { source }, }) => {
            this.setSource(source);
        };
        const beforecolumnsset = ({ detail: { columns }, }) => {
            this.columnSet(columns);
        };
        this.addEventListener('beforecolumnsset', beforecolumnsset);
        switch (config === null || config === void 0 ? void 0 : config.mode) {
            case "autoSizeOnTextOverlap" /* ColumnAutoSizeMode.autoSizeOnTextOverlap */:
                this.addEventListener('aftersourceset', aftersourceset);
                this.addEventListener('afteredit', ({ detail }) => {
                    this.afteredit(detail);
                });
                break;
            case "autoSizeAll" /* ColumnAutoSizeMode.autoSizeAll */:
                this.addEventListener('aftersourceset', aftersourceset);
                this.addEventListener('afteredit', ({ detail }) => {
                    this.afterEditAll(detail);
                });
                break;
            default:
                this.addEventListener('headerdblclick', ({ detail }) => {
                    const type = getColumnType(detail.column);
                    const size = this.getColumnSize(detail.index, type);
                    if (size) {
                        this.providers.dimension.setCustomSizes(type, {
                            [detail.index]: size,
                        }, true);
                    }
                });
                break;
        }
    }
    async setSource(source) {
        let autoSize = this.autoSizeColumns;
        if (this.dataReject) {
            this.dataReject();
            this.clearPromise();
        }
        /** If data set first and no column provided await until get one */
        if (!autoSize) {
            const request = new Promise((resolve, reject) => {
                this.dataResolve = resolve;
                this.dataReject = reject;
            });
            try {
                autoSize = await request;
            }
            catch (e) {
                return;
            }
        }
        // calculate sizes
        forEach(autoSize, (_v, type) => {
            const sizes = {};
            forEach(autoSize[type], rgCol => {
                // calculate size
                rgCol.size = sizes[rgCol.index] = source.reduce((prev, rgRow) => Math.max(prev, this.getLength(rgRow[rgCol.prop])), this.getLength(rgCol.name || ''));
            });
            this.providers.dimension.setCustomSizes(type, sizes, true);
        });
    }
    getLength(len) {
        var _a;
        const padding = 15;
        if (!len) {
            return 0;
        }
        try {
            const str = len.toString();
            /**if exact calculation required proxy with html element, slow operation */
            if ((_a = this.config) === null || _a === void 0 ? void 0 : _a.preciseSize) {
                this.precsizeCalculationArea.innerText = str;
                return this.precsizeCalculationArea.scrollWidth + padding * 2;
            }
            return str.length * this.letterBlockSize + padding * 2;
        }
        catch (e) {
            return 0;
        }
    }
    afteredit(e) {
        let data;
        if (this.isRangeEdit(e)) {
            data = e.data;
        }
        else {
            data = { 0: { [e.prop]: e.val } };
        }
        forEach(this.autoSizeColumns, (columns, type) => {
            const sizes = {};
            forEach(columns, rgCol => {
                var _a;
                // calculate size
                const size = reduce(data, (prev, rgRow) => {
                    if (typeof rgRow[rgCol.prop] === 'undefined') {
                        return prev;
                    }
                    return Math.max(prev || 0, this.getLength(rgRow[rgCol.prop]));
                }, undefined);
                if (size && ((_a = rgCol.size) !== null && _a !== void 0 ? _a : 0) < size) {
                    rgCol.size = sizes[rgCol.index] = size;
                }
            });
            this.providers.dimension.setCustomSizes(type, sizes, true);
        });
    }
    afterEditAll(e) {
        const props = {};
        if (this.isRangeEdit(e)) {
            forEach(e.data, r => forEach(r, (_v, p) => (props[p] = true)));
        }
        else {
            props[e.prop] = true;
        }
        forEach(this.autoSizeColumns, (columns, type) => {
            const sizes = {};
            forEach(columns, rgCol => {
                if (props[rgCol.prop]) {
                    const size = this.getColumnSize(rgCol.index, type);
                    if (size) {
                        sizes[rgCol.index] = size;
                    }
                }
            });
            this.providers.dimension.setCustomSizes(type, sizes, true);
        });
    }
    getColumnSize(index, type) {
        var _a, _b;
        const rgCol = (_b = (_a = this.autoSizeColumns) === null || _a === void 0 ? void 0 : _a[type]) === null || _b === void 0 ? void 0 : _b[index];
        if (!rgCol) {
            return 0;
        }
        return reduce(this.providers.data.stores, (r, s) => {
            const perStore = reduce(s.store.get('items'), (prev, _row, i) => {
                const item = getSourceItem(s.store, i);
                return Math.max(prev || 0, this.getLength(item === null || item === void 0 ? void 0 : item[rgCol.prop]));
            }, 0);
            return Math.max(r, perStore);
        }, rgCol.size || 0);
    }
    columnSet(columns) {
        var _a;
        for (let t of columnTypes) {
            const type = t;
            const cols = columns[type];
            for (let i in cols) {
                if (cols[i].autoSize || ((_a = this.config) === null || _a === void 0 ? void 0 : _a.allColumns)) {
                    if (!this.autoSizeColumns) {
                        this.autoSizeColumns = {};
                    }
                    if (!this.autoSizeColumns[type]) {
                        this.autoSizeColumns[type] = {};
                    }
                    this.autoSizeColumns[type][i] = Object.assign(Object.assign({}, cols[i]), { index: parseInt(i, 10) });
                }
            }
        }
        if (this.dataResolve) {
            this.dataResolve(this.autoSizeColumns || {});
            this.clearPromise();
        }
    }
    clearPromise() {
        this.dataResolve = null;
        this.dataReject = null;
    }
    isRangeEdit(e) {
        return !!e.data;
    }
    initiatePresizeElement() {
        var _a;
        const styleForFontTest = {
            position: 'absolute',
            fontSize: '14px',
            height: '0',
            width: '0',
            whiteSpace: 'nowrap',
            top: '0',
            overflowX: 'scroll',
            display: 'block',
        };
        const el = document.createElement('div');
        for (let s in styleForFontTest) {
            el.style[s] = (_a = styleForFontTest[s]) !== null && _a !== void 0 ? _a : '';
        }
        el.classList.add('revo-test-container');
        return el;
    }
    destroy() {
        var _a;
        super.destroy();
        (_a = this.precsizeCalculationArea) === null || _a === void 0 ? void 0 : _a.remove();
    }
}

class StretchColumn extends BasePlugin {
    constructor(revogrid, providers) {
        super(revogrid, providers);
        this.providers = providers;
        this.stretchedColumn = null;
        // calculate scroll bar size for current user session
        this.scrollSize = getScrollbarSize(document);
        // subscribe to column changes
        const beforecolumnapplied = ({ detail: { columns }, }) => this.applyStretch(columns);
        this.addEventListener('beforecolumnapplied', beforecolumnapplied);
    }
    setScroll({ type, hasScroll }) {
        var _a;
        if (type === 'rgRow' &&
            this.stretchedColumn &&
            ((_a = this.stretchedColumn) === null || _a === void 0 ? void 0 : _a.initialSize) === this.stretchedColumn.size) {
            if (hasScroll) {
                this.stretchedColumn.size -= this.scrollSize;
                this.apply();
                this.dropChanges();
            }
        }
    }
    activateChanges() {
        const setScroll = ({ detail }) => this.setScroll(detail);
        this.addEventListener('scrollchange', setScroll);
    }
    dropChanges() {
        this.stretchedColumn = null;
        this.removeEventListener('scrollchange');
    }
    apply() {
        if (!this.stretchedColumn) {
            return;
        }
        const type = 'rgCol';
        const sizes = this.providers.dimension.stores[type].store.get('sizes');
        this.providers.dimension.setCustomSizes(type, Object.assign(Object.assign({}, sizes), { [this.stretchedColumn.index]: this.stretchedColumn.size }), true);
    }
    /**
     * Apply stretch changes
     */
    applyStretch(columns) {
        // unsubscribe from all events
        this.dropChanges();
        // calculate grid size
        let sizeDifference = this.revogrid.clientWidth - 1;
        forEach(columns, (_, type) => {
            const realSize = this.providers.dimension.stores[type].store.get('realSize');
            sizeDifference -= realSize;
        });
        if (this.revogrid.rowHeaders) {
            const itemsLength = this.providers.data.stores.rgRow.store.get('source').length;
            const header = this.revogrid.rowHeaders;
            const rowHeaderSize = calculateRowHeaderSize(itemsLength, typeof header === 'object' ? header : undefined);
            if (rowHeaderSize) {
                sizeDifference -= rowHeaderSize;
            }
        }
        if (sizeDifference > 0) {
            // currently plugin accepts last column only
            const index = columns.rgCol.length - 1;
            const last = columns.rgCol[index];
            /**
             * has column
             * no auto size applied
             * size for column shouldn't be defined
             */
            const colSize = (last === null || last === void 0 ? void 0 : last.size) || this.revogrid.colSize || 0;
            const size = sizeDifference + colSize - 1;
            if (last && !last.autoSize && colSize < size) {
                this.stretchedColumn = {
                    initialSize: size,
                    index,
                    size,
                };
                this.apply();
                this.activateChanges();
            }
        }
    }
}
/**
 * Check plugin type is Stretch
 */
function isStretchPlugin(plugin) {
    return !!plugin.applyStretch;
}

/**
 * The base implementation of `_.clamp` which doesn't coerce arguments.
 *
 * @private
 * @param {number} number The number to clamp.
 * @param {number} [lower] The lower bound.
 * @param {number} upper The upper bound.
 * @returns {number} Returns the clamped number.
 */
function baseClamp(number, lower, upper) {
  if (number === number) {
    {
      number = number <= upper ? number : upper;
    }
    {
      number = number >= lower ? number : lower;
    }
  }
  return number;
}

/** Used as references for the maximum length and index of an array. */
var MAX_ARRAY_LENGTH = 4294967295;

/**
 * Converts `value` to an integer suitable for use as the length of an
 * array-like object.
 *
 * **Note:** This method is based on
 * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to convert.
 * @returns {number} Returns the converted integer.
 * @example
 *
 * _.toLength(3.2);
 * // => 3
 *
 * _.toLength(Number.MIN_VALUE);
 * // => 0
 *
 * _.toLength(Infinity);
 * // => 4294967295
 *
 * _.toLength('3.2');
 * // => 3
 */
function toLength(value) {
  return value ? baseClamp(toInteger(value), 0, MAX_ARRAY_LENGTH) : 0;
}

/**
 * The base implementation of `_.fill` without an iteratee call guard.
 *
 * @private
 * @param {Array} array The array to fill.
 * @param {*} value The value to fill `array` with.
 * @param {number} [start=0] The start position.
 * @param {number} [end=array.length] The end position.
 * @returns {Array} Returns `array`.
 */
function baseFill(array, value, start, end) {
  var length = array.length;

  start = toInteger(start);
  if (start < 0) {
    start = -start > length ? 0 : (length + start);
  }
  end = (end === undefined || end > length) ? length : toInteger(end);
  if (end < 0) {
    end += length;
  }
  end = start > end ? 0 : toLength(end);
  while (start < end) {
    array[start++] = value;
  }
  return array;
}

/**
 * Fills elements of `array` with `value` from `start` up to, but not
 * including, `end`.
 *
 * **Note:** This method mutates `array`.
 *
 * @static
 * @memberOf _
 * @since 3.2.0
 * @category Array
 * @param {Array} array The array to fill.
 * @param {*} value The value to fill `array` with.
 * @param {number} [start=0] The start position.
 * @param {number} [end=array.length] The end position.
 * @returns {Array} Returns `array`.
 * @example
 *
 * var array = [1, 2, 3];
 *
 * _.fill(array, 'a');
 * console.log(array);
 * // => ['a', 'a', 'a']
 *
 * _.fill(Array(3), 2);
 * // => [2, 2, 2]
 *
 * _.fill([4, 6, 8, 10], '*', 1, 3);
 * // => [4, '*', '*', 10]
 */
function fill(array, value, start, end) {
  var length = array == null ? 0 : array.length;
  if (!length) {
    return [];
  }
  return baseFill(array, value, start, end);
}

const INITIAL = {
    mime: 'text/csv',
    fileKind: 'csv',
    // BOM signature
    bom: true,
    columnDelimiter: ',',
    rowDelimiter: '\r\n',
    encoding: '',
};
// The ASCII character code 13 is called a Carriage Return or CR.
const CARRIAGE_RETURN = String.fromCharCode(13);
// Chr(13) followed by a Chr(10) that compose a proper CRLF.
const LINE_FEED = String.fromCharCode(10);
const DOUBLE_QT = String.fromCharCode(34);
const NO_BREAK_SPACE = String.fromCharCode(0xfeff);
const escapeRegex = new RegExp('"', 'g');
class ExportCsv {
    constructor(options = {}) {
        this.options = Object.assign(Object.assign({}, INITIAL), options);
    }
    doExport({ data, headers, props }) {
        let result = this.options.bom ? NO_BREAK_SPACE : '';
        // any header
        if ((headers === null || headers === void 0 ? void 0 : headers.length) > 0) {
            headers.forEach(header => {
                // ignore empty
                if (!header.length) {
                    return;
                }
                result += this.prepareHeader(header, this.options.columnDelimiter);
                result += this.options.rowDelimiter;
            });
        }
        data.forEach((rgRow, index) => {
            if (index > 0) {
                result += this.options.rowDelimiter;
            }
            // support grouping
            if (isGrouping(rgRow)) {
                result += this.parseCell(getGroupingName(rgRow), this.options.columnDelimiter);
                return;
            }
            result += props.map(p => this.parseCell(rgRow[p], this.options.columnDelimiter)).join(this.options.columnDelimiter);
        });
        return result;
    }
    prepareHeader(columnHeaders, columnDelimiter) {
        let result = '';
        const newColumnHeaders = columnHeaders.map(v => this.parseCell(v, columnDelimiter, true));
        result += newColumnHeaders.join(columnDelimiter);
        return result;
    }
    parseCell(value, columnDelimiter, force = false) {
        let escape = value;
        if (typeof value !== 'string') {
            escape = JSON.stringify(value);
        }
        const toEscape = [CARRIAGE_RETURN, DOUBLE_QT, LINE_FEED, columnDelimiter];
        if (typeof escape === 'undefined') {
            return '';
        }
        if (escape !== '' && (force || toEscape.some(i => escape.indexOf(i) >= 0))) {
            return `"${escape.replace(escapeRegex, '""')}"`;
        }
        return escape;
    }
}

var ExportTypes;
(function (ExportTypes) {
    ExportTypes["csv"] = "csv";
})(ExportTypes || (ExportTypes = {}));
class ExportFilePlugin extends BasePlugin {
    /** Exports string */
    async exportString(options = {}, t = ExportTypes.csv) {
        const data = await this.beforeexport();
        if (!data) {
            return null;
        }
        return this.formatter(t, options).doExport(data);
    }
    /** Exports Blob */
    async exportBlob(options = {}, t = ExportTypes.csv) {
        return await this.getBlob(this.formatter(t, options));
    }
    /** Export file */
    async exportFile(options = {}, t = ExportTypes.csv) {
        const formatter = this.formatter(t, options);
        // url
        const URL = window.URL || window.webkitURL;
        const a = document.createElement('a');
        const { filename, fileKind } = formatter.options;
        const name = `${filename}.${fileKind}`;
        const blob = await this.getBlob(formatter);
        const url = blob ? URL.createObjectURL(blob) : '';
        a.style.display = 'none';
        a.setAttribute('href', url);
        a.setAttribute('download', name);
        this.revogrid.appendChild(a);
        a.dispatchEvent(new MouseEvent('click'));
        this.revogrid.removeChild(a);
        // delay for revoke, correct for some browsers
        await timeout(120);
        URL.revokeObjectURL(url);
    }
    /** Blob object */
    async getBlob(formatter) {
        const type = `${formatter.options.mime};charset=${formatter.options.encoding}`;
        if (typeof Blob !== 'undefined') {
            const data = await this.beforeexport();
            if (!data) {
                return null;
            }
            return new Blob([formatter.doExport(data)], { type });
        }
        return null;
    }
    // before event
    async beforeexport() {
        let data = await this.getData();
        const event = this.emit('beforeexport', { data });
        if (event.defaultPrevented) {
            return null;
        }
        return event.detail.data;
    }
    async getData() {
        const data = await this.getSource();
        const colSource = [];
        const colPromises = [];
        columnTypes.forEach((t, i) => {
            colPromises.push(this.getColPerSource(t).then(s => (colSource[i] = s)));
        });
        await Promise.all(colPromises);
        const columns = {
            headers: [],
            props: [],
        };
        for (let source of colSource) {
            source.headers.forEach((h, i) => {
                if (!columns.headers[i]) {
                    columns.headers[i] = [];
                }
                columns.headers[i].push(...h);
            });
            columns.props.push(...source.props);
        }
        return Object.assign({ data }, columns);
    }
    async getColPerSource(t) {
        const store = await this.revogrid.getColumnStore(t);
        const source = store.get('source');
        const virtualIndexes = store.get('items');
        const depth = store.get('groupingDepth');
        const groups = store.get('groups');
        const colNames = [];
        const colProps = [];
        virtualIndexes.forEach((v) => {
            const prop = source[v].prop;
            colNames.push(source[v].name || '');
            colProps.push(prop);
        });
        const rows = this.getGroupHeaders(depth, groups, virtualIndexes);
        rows.push(colNames);
        return {
            headers: rows,
            props: colProps,
        };
    }
    getGroupHeaders(depth, groups, items) {
        const rows = [];
        const template = fill(new Array(items.length), '');
        for (let d = 0; d < depth; d++) {
            const rgRow = [...template];
            rows.push(rgRow);
            if (!groups[d]) {
                continue;
            }
            const levelGroups = groups[d];
            // add names of groups
            levelGroups.forEach((group) => {
                const minIndex = group.indexes[0];
                if (typeof minIndex === 'number') {
                    rgRow[minIndex] = group.name;
                }
            });
        }
        return rows;
    }
    async getSource() {
        const data = [];
        const promisesData = [];
        rowTypes.forEach(t => {
            const dataPart = [];
            data.push(dataPart);
            const promise = this.revogrid.getVisibleSource(t).then((d) => dataPart.push(...d));
            promisesData.push(promise);
        });
        await Promise.all(promisesData);
        return data.reduce((r, v) => {
            r.push(...v);
            return r;
        }, []);
    }
    // get correct class for future multiple types support
    formatter(type, options = {}) {
        switch (type) {
            case ExportTypes.csv:
                return new ExportCsv(options);
            default:
                throw new Error('Unknown format');
        }
    }
}

const eq = (value, extra) => {
    if (typeof value === 'undefined' || (value === null && !extra)) {
        return true;
    }
    if (typeof value !== 'string') {
        value = JSON.stringify(value);
    }
    const filterVal = extra === null || extra === void 0 ? void 0 : extra.toString().toLocaleLowerCase();
    if ((filterVal === null || filterVal === void 0 ? void 0 : filterVal.length) === 0) {
        return true;
    }
    return value.toLocaleLowerCase() === filterVal;
};
const notEq = (value, extra) => !eq(value, extra);
notEq.extra = 'input';
eq.extra = 'input';

const gtThan = function (value, extra) {
    let conditionValue;
    if (typeof value === 'number' && typeof extra !== 'undefined' && extra !== null) {
        conditionValue = parseFloat(extra === null || extra === void 0 ? void 0 : extra.toString());
        return value > conditionValue;
    }
    return false;
};
gtThan.extra = 'input';

const gtThanEq = function (value, extra) {
    return eq(value, extra) || gtThan(value, extra);
};
gtThanEq.extra = 'input';

const lt = function (value, extra) {
    let conditionValue;
    if (typeof value === 'number' && typeof extra !== 'undefined' && extra !== null) {
        conditionValue = parseFloat(extra.toString());
        return value < conditionValue;
    }
    else {
        return false;
    }
};
lt.extra = 'input';

const lsEq = function (value, extra) {
    return eq(value, extra) || lt(value, extra);
};
lsEq.extra = 'input';

const set = (value) => !(value === '' || value === null || value === void 0);
const notSet = (value) => !set(value);

const beginsWith = (value, extra) => {
    if (!value) {
        return false;
    }
    if (!extra) {
        return true;
    }
    if (typeof value !== 'string') {
        value = JSON.stringify(value);
    }
    if (typeof extra !== 'string') {
        extra = JSON.stringify(extra);
    }
    return value.toLocaleLowerCase().indexOf(extra.toLocaleLowerCase()) === 0;
};
beginsWith.extra = 'input';

const contains = (value, extra) => {
    if (!extra) {
        return true;
    }
    if (!value) {
        return false;
    }
    if (extra) {
        if (typeof value !== 'string') {
            value = JSON.stringify(value);
        }
        return value.toLocaleLowerCase().indexOf(extra.toString().toLowerCase()) > -1;
    }
    return true;
};
const notContains = (value, extra) => {
    return !contains(value, extra);
};
notContains.extra = 'input';
contains.extra = 'input';

// filter.indexed.ts
const filterCoreFunctionsIndexedByType = {
    none: () => true,
    empty: notSet,
    notEmpty: set,
    eq: eq,
    notEq: notEq,
    begins: beginsWith,
    contains: contains,
    notContains: notContains,
    eqN: eq,
    neqN: notEq,
    gt: gtThan,
    gte: gtThanEq,
    lt: lt,
    lte: lsEq,
};
const filterTypes = {
    string: ['notEmpty', 'empty', 'eq', 'notEq', 'begins', 'contains', 'notContains'],
    number: ['notEmpty', 'empty', 'eqN', 'neqN', 'gt', 'gte', 'lt', 'lte'],
};
const filterNames = {
    none: 'None',
    empty: 'Not set',
    notEmpty: 'Set',
    eq: 'Equal',
    notEq: 'Not equal',
    begins: 'Begins with',
    contains: 'Contains',
    notContains: 'Does not contain',
    eqN: '=',
    neqN: '!=',
    gt: '>',
    gte: '>=',
    lt: '<',
    lte: '<=',
};

// filter.plugin.tsx
const FILTER_TRIMMED_TYPE = 'filter';
const FILTER_CONFIG_CHANGED_EVENT = 'filterconfigchanged';
const FILTE_PANEL = 'revogr-filter-panel';
/**
 * @typedef ColumnFilterConfig
 * @type {object}
 *
 * @property {MultiFilterItem|undefined} multiFilterItems - data for multi filtering with relation
 *
 * @property {Record<ColumnProp, FilterCollectionItem>|undefined} collection - preserved filter data, relation for filters will be applied as 'and'
 *
 * @property {string[]|undefined} include - filters to be included, if defined everything else out of scope will be ignored
 *
 * @property {Record<string, CustomFilter>|undefined} customFilters - hash map of {FilterType:CustomFilter}.
 *
 * @property {FilterLocalization|undefined} localization - translation for filter popup captions.
 *
 * @property {boolean|undefined} disableDynamicFiltering - disables dynamic filtering. A way to apply filters on Save only.
 */
/**
 * @internal
 */
class FilterPlugin extends BasePlugin {
    constructor(revogrid, providers, config) {
        var _a;
        super(revogrid, providers);
        this.revogrid = revogrid;
        this.config = config;
        this.filterCollection = {};
        this.multiFilterItems = {};
        /**
         * Filter types
         * @example
         * {
         *    string: ['contains', 'beginswith'],
         *    number: ['eqN', 'neqN', 'gt']
         *  }
         */
        this.filterByType = Object.assign({}, filterTypes);
        this.filterNameIndexByType = Object.assign({}, filterNames);
        this.filterFunctionsIndexedByType = Object.assign({}, filterCoreFunctionsIndexedByType);
        this.filterProp = FILTER_PROP;
        if (config) {
            this.initConfig(config);
        }
        const existingNodes = this.revogrid.registerVNode.filter(n => typeof n === 'object' && n.$tag$ !== FILTE_PANEL);
        this.revogrid.registerVNode = [
            ...existingNodes,
            h("revogr-filter-panel", { filterNames: this.filterNameIndexByType, filterEntities: this.filterFunctionsIndexedByType, filterCaptions: (_a = config === null || config === void 0 ? void 0 : config.localization) === null || _a === void 0 ? void 0 : _a.captions, onFilterChange: e => this.onFilterChange(e.detail), onResetChange: e => this.onFilterReset(e.detail), disableDynamicFiltering: config === null || config === void 0 ? void 0 : config.disableDynamicFiltering, closeOnOutsideClick: config === null || config === void 0 ? void 0 : config.closeFilterPanelOnOutsideClick, ref: e => (this.pop = e) },
                ' ',
                this.extraContent()),
        ];
        const aftersourceset = async () => {
            const filterCollectionProps = Object.keys(this.filterCollection);
            if (filterCollectionProps.length > 0) {
                // handle old way of filtering by reworking FilterCollection to new MultiFilterItem
                filterCollectionProps.forEach((prop, index) => {
                    if (!this.multiFilterItems[prop]) {
                        this.multiFilterItems[prop] = [
                            {
                                id: index,
                                type: this.filterCollection[prop].type,
                                value: this.filterCollection[prop].value,
                                relation: 'and',
                            },
                        ];
                    }
                });
            }
            if (Object.keys(this.multiFilterItems).length === 0) {
                return;
            }
            await this.runFiltering(this.multiFilterItems);
        };
        this.addEventListener('headerclick', e => this.headerclick(e));
        this.addEventListener(FILTER_CONFIG_CHANGED_EVENT, ({ detail }) => {
            if (!detail ||
                (typeof detail === 'object' &&
                    (!detail.multiFilterItems ||
                        !Object.keys(detail.multiFilterItems).length))) {
                this.clearFiltering();
                return;
            }
            if (typeof detail === 'object') {
                this.initConfig(detail);
            }
            aftersourceset();
        });
        this.addEventListener('aftersourceset', aftersourceset);
        this.addEventListener('filter', ({ detail }) => this.onFilterChange(detail));
    }
    beforeshow(_) {
        // used as hook for filter panel
    }
    extraContent() {
        return null;
    }
    initConfig(config) {
        if (config.multiFilterItems) {
            this.multiFilterItems = Object.assign({}, config.multiFilterItems);
        }
        else {
            this.multiFilterItems = {};
        }
        // Add custom filters
        if (config.customFilters) {
            for (let customFilterType in config.customFilters) {
                const cFilter = config.customFilters[customFilterType];
                if (!this.filterByType[cFilter.columnFilterType]) {
                    this.filterByType[cFilter.columnFilterType] = [];
                }
                // add custom filter type
                this.filterByType[cFilter.columnFilterType].push(customFilterType);
                // add custom filter function
                this.filterFunctionsIndexedByType[customFilterType] = cFilter.func;
                // add custom filter name
                this.filterNameIndexByType[customFilterType] = cFilter.name;
            }
        }
        // Add filterProp if provided in config
        if (config.filterProp) {
            this.filterProp = config.filterProp;
        }
        /**
         * which filters has to be included/excluded
         * convenient way to exclude system filters
         */
        const cfgInlcude = config.include;
        if (cfgInlcude) {
            const filters = {};
            for (let t in this.filterByType) {
                // validate filters, if appropriate function present
                const newTypes = this.filterByType[t].filter(f => cfgInlcude.indexOf(f) > -1);
                if (newTypes.length) {
                    filters[t] = newTypes;
                }
            }
            // if any valid filters provided show them
            if (Object.keys(filters).length > 0) {
                this.filterByType = filters;
            }
        }
        if (config.collection) {
            const filtersWithFilterFunctionPresent = Object.entries(config.collection).filter(([, item]) => this.filterFunctionsIndexedByType[item.type]);
            this.filterCollection = Object.fromEntries(filtersWithFilterFunctionPresent);
        }
        else {
            this.filterCollection = {};
        }
        if (config.localization) {
            if (config.localization.filterNames) {
                Object.entries(config.localization.filterNames).forEach(([k, v]) => {
                    if (this.filterNameIndexByType[k] != void 0) {
                        this.filterNameIndexByType[k] = v;
                    }
                });
            }
        }
    }
    async headerclick(e) {
        var _a, _b;
        const el = (_a = e.detail.originalEvent) === null || _a === void 0 ? void 0 : _a.target;
        if (!isFilterBtn(el)) {
            return;
        }
        e.preventDefault();
        if (!this.pop) {
            return;
        }
        // filter button clicked, open filter dialog
        const gridPos = this.revogrid.getBoundingClientRect();
        const buttonPos = el.getBoundingClientRect();
        const prop = e.detail.prop;
        const data = Object.assign(Object.assign(Object.assign({}, e.detail), this.filterCollection[prop]), { x: buttonPos.x - gridPos.x, y: buttonPos.y - gridPos.y + buttonPos.height, autoCorrect: true, filterTypes: this.getColumnFilter(e.detail.filter), filterItems: this.multiFilterItems, extraContent: this.extraHyperContent });
        (_b = this.beforeshow) === null || _b === void 0 ? void 0 : _b.call(this, data);
        this.pop.show(data);
    }
    getColumnFilter(type) {
        let filterType = 'string';
        if (!type) {
            return { [filterType]: this.filterByType[filterType] };
        }
        // if custom column filter
        if (this.isValidType(type)) {
            filterType = type;
            // if multiple filters applied
        }
        else if (typeof type === 'object' && type.length) {
            return type.reduce((r, multiType) => {
                if (this.isValidType(multiType)) {
                    r[multiType] = this.filterByType[multiType];
                }
                return r;
            }, {});
        }
        return { [filterType]: this.filterByType[filterType] };
    }
    isValidType(type) {
        return !!(typeof type === 'string' && this.filterByType[type]);
    }
    /**
     * Called on internal component change
     */
    async onFilterChange(filterItems) {
        // store the filter items
        this.multiFilterItems = filterItems;
        // run the filtering when the items change
        this.runFiltering(this.multiFilterItems);
    }
    onFilterReset(prop) {
        delete this.multiFilterItems[prop !== null && prop !== void 0 ? prop : ''];
        this.onFilterChange(this.multiFilterItems);
    }
    /**
     * Triggers grid filtering
     */
    async doFiltering(collection, source, columns, filterItems) {
        const columnsToUpdate = [];
        /**
         * Loop through the columns and update the columns that need to be updated with the `hasFilter` property.
         */
        const columnByProp = {};
        columns.forEach(rgCol => {
            const column = Object.assign({}, rgCol);
            const hasFilter = filterItems[column.prop];
            columnByProp[column.prop] = column;
            /**
             * If the column has a filter and it's not already marked as filtered, update the column.
             */
            if (column[this.filterProp] && !hasFilter) {
                delete column[this.filterProp];
                columnsToUpdate.push(column);
            }
            /**
             * If the column does not have a filter and it's marked as filtered, update the column.
             */
            if (!column[this.filterProp] && hasFilter) {
                columnsToUpdate.push(column);
                column[this.filterProp] = true;
            }
        });
        const itemsToTrim = this.getRowFilter(source, filterItems, columnByProp);
        // check is filter event prevented
        const { defaultPrevented, detail } = this.emit('beforefiltertrimmed', {
            collection,
            itemsToFilter: itemsToTrim,
            source,
            filterItems,
        });
        if (defaultPrevented) {
            return;
        }
        this.providers.data.setTrimmed({ [FILTER_TRIMMED_TYPE]: detail.itemsToFilter });
        // applies the hasFilter to the columns to show filter icon
        this.providers.column.updateColumns(columnsToUpdate);
        this.emit('afterfilterapply', {
            multiFilterItems: filterItems,
            source,
            collection,
        });
    }
    async clearFiltering() {
        this.multiFilterItems = {};
        await this.runFiltering(this.multiFilterItems);
    }
    async runFiltering(multiFilterItems) {
        const collection = {};
        // handle old filterCollection to return the first filter only (if any) from multiFilterItems
        const filterProps = Object.keys(multiFilterItems);
        for (const prop of filterProps) {
            // check if we have any filter for a column
            if (multiFilterItems[prop].length > 0) {
                const firstFilterItem = multiFilterItems[prop][0];
                collection[prop] = {
                    type: firstFilterItem.type,
                    value: firstFilterItem.value,
                };
            }
        }
        this.filterCollection = collection;
        const columns = this.providers.column.getColumns();
        // run the filtering on the main source only
        const source = this.providers.data.stores['rgRow'].store.get('source');
        const { defaultPrevented, detail } = this.emit('beforefilterapply', {
            collection: this.filterCollection,
            source,
            columns,
            filterItems: this.multiFilterItems,
        });
        if (defaultPrevented) {
            return;
        }
        this.doFiltering(detail.collection, detail.source, detail.columns, detail.filterItems);
    }
    /**
     * Get trimmed rows based on filter
     */
    getRowFilter(rows, filterItems, columnByProp) {
        const propKeys = Object.keys(filterItems);
        const trimmed = {};
        // each rows
        for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
            // check filter by column properties
            for (const prop of propKeys) {
                // add to the list of removed/trimmed rows of filter condition is satisfied
                if (this.shouldTrimRow(filterItems[prop], prop, columnByProp[prop], rows[rowIndex])) {
                    trimmed[rowIndex] = true;
                }
            } // end of for-of propKeys
        }
        return trimmed;
    }
    shouldTrimRow(propFilters, prop, column, model = {}) {
        // reset the count of satisfied filters
        let propFilterSatisfiedCount = 0;
        // reset the array of last filter results
        let lastFilterResults = [];
        // testing each filter for a prop
        for (const [filterIndex, filterData] of propFilters.entries()) {
            // the filter LogicFunction based on the type
            const filterFunc = this.filterFunctionsIndexedByType[filterData.type];
            // THE MAGIC OF FILTERING IS HERE
            // If there is no column but user wants to filter by a property
            const value = column ? getCellDataParsed(model, column) : model[prop];
            // OR relation
            if (filterData.relation === 'or') {
                // reset the array of last filter results
                lastFilterResults = [];
                // if the filter is satisfied, continue to the next filter
                if (filterFunc(value, filterData.value)) {
                    continue;
                }
                // if the filter is not satisfied, count it
                propFilterSatisfiedCount++;
                // AND relation
            }
            else {
                // 'and' relation will need to know the next filter
                // so we save this current filter to include it in the next filter
                lastFilterResults.push(!filterFunc(value, filterData.value));
                if (isFinalAndFilter(filterIndex, propFilters)) {
                    // let's just continue since for sure propFilterSatisfiedCount cannot be satisfied
                    if (allAndConditionsSatisfied(lastFilterResults)) {
                        // reset the array of last filter results
                        lastFilterResults = [];
                        continue;
                    }
                    // we need to add all of the lastFilterResults since we need to satisfy all
                    propFilterSatisfiedCount += lastFilterResults.length;
                    // reset the array of last filter results
                    lastFilterResults = [];
                }
            }
        } // end of propFilters forEach
        return propFilterSatisfiedCount === propFilters.length;
    }
}
/**
 * Checks if the current filter is the final one in an AND sequence.
 * @param index - Current filter index in the list.
 * @param filters - Array of filters for the property.
 * @returns True if this is the last AND condition; false otherwise.
 */
function isFinalAndFilter(index, filters) {
    const nextFilter = filters[index + 1]; // Get the next filter in the list.
    // Return true if there's no next filter or if the next filter defined and is not part of the AND sequence.
    return !nextFilter || (!!nextFilter.relation && nextFilter.relation !== 'and');
}
/**
 * Determines if all conditions in an AND sequence are satisfied.
 * @param pendingResults - An array of results from the AND conditions.
 * @returns True if all conditions are satisfied; false otherwise.
 */
function allAndConditionsSatisfied(pendingResults) {
    // Check if there are any failed conditions in the pending results.
    return !pendingResults.includes(true);
}

/** `Object#toString` result references. */
var stringTag = '[object String]';

/**
 * Checks if `value` is classified as a `String` primitive or object.
 *
 * @static
 * @since 0.1.0
 * @memberOf _
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a string, else `false`.
 * @example
 *
 * _.isString('abc');
 * // => true
 *
 * _.isString(1);
 * // => false
 */
function isString(value) {
  return typeof value == 'string' ||
    (!isArray(value) && isObjectLike(value) && baseGetTag(value) == stringTag);
}

/**
 * Gets the size of an ASCII `string`.
 *
 * @private
 * @param {string} string The string inspect.
 * @returns {number} Returns the string size.
 */
var asciiSize = baseProperty('length');

/** Used to compose unicode character classes. */
var rsAstralRange$1 = '\\ud800-\\udfff',
    rsComboMarksRange$1 = '\\u0300-\\u036f',
    reComboHalfMarksRange$1 = '\\ufe20-\\ufe2f',
    rsComboSymbolsRange$1 = '\\u20d0-\\u20ff',
    rsComboRange$1 = rsComboMarksRange$1 + reComboHalfMarksRange$1 + rsComboSymbolsRange$1,
    rsVarRange$1 = '\\ufe0e\\ufe0f';

/** Used to compose unicode capture groups. */
var rsZWJ$1 = '\\u200d';

/** Used to detect strings with [zero-width joiners or code points from the astral planes](http://eev.ee/blog/2015/09/12/dark-corners-of-unicode/). */
var reHasUnicode = RegExp('[' + rsZWJ$1 + rsAstralRange$1  + rsComboRange$1 + rsVarRange$1 + ']');

/**
 * Checks if `string` contains Unicode symbols.
 *
 * @private
 * @param {string} string The string to inspect.
 * @returns {boolean} Returns `true` if a symbol is found, else `false`.
 */
function hasUnicode(string) {
  return reHasUnicode.test(string);
}

/** Used to compose unicode character classes. */
var rsAstralRange = '\\ud800-\\udfff',
    rsComboMarksRange = '\\u0300-\\u036f',
    reComboHalfMarksRange = '\\ufe20-\\ufe2f',
    rsComboSymbolsRange = '\\u20d0-\\u20ff',
    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,
    rsVarRange = '\\ufe0e\\ufe0f';

/** Used to compose unicode capture groups. */
var rsAstral = '[' + rsAstralRange + ']',
    rsCombo = '[' + rsComboRange + ']',
    rsFitz = '\\ud83c[\\udffb-\\udfff]',
    rsModifier = '(?:' + rsCombo + '|' + rsFitz + ')',
    rsNonAstral = '[^' + rsAstralRange + ']',
    rsRegional = '(?:\\ud83c[\\udde6-\\uddff]){2}',
    rsSurrPair = '[\\ud800-\\udbff][\\udc00-\\udfff]',
    rsZWJ = '\\u200d';

/** Used to compose unicode regexes. */
var reOptMod = rsModifier + '?',
    rsOptVar = '[' + rsVarRange + ']?',
    rsOptJoin = '(?:' + rsZWJ + '(?:' + [rsNonAstral, rsRegional, rsSurrPair].join('|') + ')' + rsOptVar + reOptMod + ')*',
    rsSeq = rsOptVar + reOptMod + rsOptJoin,
    rsSymbol = '(?:' + [rsNonAstral + rsCombo + '?', rsCombo, rsRegional, rsSurrPair, rsAstral].join('|') + ')';

/** Used to match [string symbols](https://mathiasbynens.be/notes/javascript-unicode). */
var reUnicode = RegExp(rsFitz + '(?=' + rsFitz + ')|' + rsSymbol + rsSeq, 'g');

/**
 * Gets the size of a Unicode `string`.
 *
 * @private
 * @param {string} string The string inspect.
 * @returns {number} Returns the string size.
 */
function unicodeSize(string) {
  var result = reUnicode.lastIndex = 0;
  while (reUnicode.test(string)) {
    ++result;
  }
  return result;
}

/**
 * Gets the number of symbols in `string`.
 *
 * @private
 * @param {string} string The string to inspect.
 * @returns {number} Returns the string size.
 */
function stringSize(string) {
  return hasUnicode(string)
    ? unicodeSize(string)
    : asciiSize(string);
}

/** `Object#toString` result references. */
var mapTag = '[object Map]',
    setTag = '[object Set]';

/**
 * Gets the size of `collection` by returning its length for array-like
 * values or the number of own enumerable string keyed properties for objects.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Collection
 * @param {Array|Object|string} collection The collection to inspect.
 * @returns {number} Returns the collection size.
 * @example
 *
 * _.size([1, 2, 3]);
 * // => 3
 *
 * _.size({ 'a': 1, 'b': 2 });
 * // => 2
 *
 * _.size('pebbles');
 * // => 7
 */
function size(collection) {
  if (collection == null) {
    return 0;
  }
  if (isArrayLike(collection)) {
    return isString(collection) ? stringSize(collection) : collection.length;
  }
  var tag = getTag(collection);
  if (tag == mapTag || tag == setTag) {
    return collection.size;
  }
  return baseKeys(collection).length;
}

function sortIndexByItems(indexes, source, sortingFunc = {}) {
    // if no sorting - return unsorted indexes
    if (Object.entries(sortingFunc).length === 0) {
        // Unsorted indexes
        return [...Array(indexes.length).keys()];
    }
    //
    /**
     * go through all indexes and align in new order
     * performs a multi-level sorting by applying multiple comparison functions to determine the order of the items based on different properties.
     */
    return indexes.sort((a, b) => {
        const itemA = source[a];
        const itemB = source[b];
        for (const [prop, cmp] of Object.entries(sortingFunc)) {
            if (isGrouping(itemA)) {
                if (itemA['__rvgr-prop'] !== prop) {
                    return 0;
                }
            }
            if (isGrouping(itemB)) {
                if (itemB['__rvgr-prop'] !== prop) {
                    return 0;
                }
            }
            /**
             * If the comparison function returns a non-zero value (sorted), it means that the items should be sorted based on the given property. In such a case, the function immediately returns the sorted value, indicating the order in which the items should be arranged.
             * If none of the comparison functions result in a non-zero value, indicating that the items are equal or should remain in the same order, the function eventually returns 0.
             */
            const sorted = cmp === null || cmp === void 0 ? void 0 : cmp(prop, itemA, itemB);
            if (sorted) {
                return sorted;
            }
        }
        return 0;
    });
}
function defaultCellCompare(prop, a, b) {
    const aRaw = this.column ? getCellRaw(a, this.column) : a === null || a === void 0 ? void 0 : a[prop];
    const bRaw = this.column ? getCellRaw(b, this.column) : b === null || b === void 0 ? void 0 : b[prop];
    const av = aRaw === null || aRaw === void 0 ? void 0 : aRaw.toString().toLowerCase();
    const bv = bRaw === null || bRaw === void 0 ? void 0 : bRaw.toString().toLowerCase();
    if (av === bv) {
        return 0;
    }
    if (av > bv) {
        return 1;
    }
    return -1;
}
function descCellCompare(cmp) {
    return (prop, a, b) => {
        return -1 * cmp(prop, a, b);
    };
}
function getNextOrder(currentOrder) {
    switch (currentOrder) {
        case undefined:
            return 'asc';
        case 'asc':
            return 'desc';
        case 'desc':
            return undefined;
    }
}
function getComparer(column, order) {
    var _a;
    const cellCmp = ((_a = column === null || column === void 0 ? void 0 : column.cellCompare) === null || _a === void 0 ? void 0 : _a.bind({ order })) || (defaultCellCompare === null || defaultCellCompare === void 0 ? void 0 : defaultCellCompare.bind({ column, order }));
    if (order == 'asc') {
        return cellCmp;
    }
    if (order == 'desc') {
        return descCellCompare(cellCmp);
    }
    return undefined;
}

/**
 * Lifecycle
 * 1. @event `beforesorting` - Triggered when sorting just starts. Nothing has happened yet. This can be triggered from a column or from the source. If the type is from rows, the column will be undefined.
 * 2. @event `beforesourcesortingapply` - Triggered before the sorting data is applied to the data source. You can prevent this event, and the data will not be sorted.
 * 3. @event `beforesortingapply` - Triggered before the sorting data is applied to the data source. You can prevent this event, and the data will not be sorted. This event is only called from a column sorting click.
 * 4. @event `aftersortingapply` - Triggered after sorting has been applied and completed. This event occurs for both row and column sorting.
 *
 * Note: If you prevent an event, it will not proceed to the subsequent steps.
 */
class SortingPlugin extends BasePlugin {
    constructor(revogrid, providers, config) {
        super(revogrid, providers);
        this.revogrid = revogrid;
        /**
         * Delayed sorting promise
         */
        this.sortingPromise = null;
        /**
         * We need to sort only so often
         */
        this.postponeSort = debounce((order, comparison, ignoreViewportUpdate) => this.runSorting(order, comparison, ignoreViewportUpdate), 50);
        const setConfig = (cfg) => {
            var _a;
            if (cfg) {
                const sortingFunc = {};
                const order = {};
                (_a = cfg.columns) === null || _a === void 0 ? void 0 : _a.forEach(col => {
                    sortingFunc[col.prop] = getComparer(col, col.order);
                    order[col.prop] = col.order;
                });
                if (cfg.additive) {
                    this.sorting = Object.assign(Object.assign({}, this.sorting), order);
                    this.sortingFunc = Object.assign(Object.assign({}, this.sortingFunc), sortingFunc);
                }
                else {
                    // // set sorting
                    this.sorting = order;
                    this.sortingFunc = sortingFunc;
                }
            }
        };
        setConfig(config);
        this.addEventListener('sortingconfigchanged', ({ detail }) => {
            config = detail;
            setConfig(detail);
            this.startSorting(this.sorting, this.sortingFunc);
        });
        this.addEventListener('beforeheaderrender', ({ detail, }) => {
            var _a;
            const { data: column } = detail;
            if (column.sortable) {
                detail.data = Object.assign(Object.assign({}, column), { order: (_a = this.sorting) === null || _a === void 0 ? void 0 : _a[column.prop] });
            }
        });
        this.addEventListener('beforeanysource', ({ detail: { type }, }) => {
            // if sorting was provided - sort data
            if (!!this.sorting && this.sortingFunc) {
                const event = this.emit('beforesourcesortingapply', { type, sorting: this.sorting });
                if (event.defaultPrevented) {
                    return;
                }
                this.startSorting(this.sorting, this.sortingFunc);
            }
        });
        this.addEventListener('aftercolumnsset', ({ detail: { order }, }) => {
            // if config provided - do nothing, read from config
            if (config) {
                return;
            }
            const columns = this.providers.column.getColumns();
            const sortingFunc = {};
            for (let prop in order) {
                const cmp = getComparer(getColumnByProp(columns, prop), order[prop]);
                sortingFunc[prop] = cmp;
            }
            // set sorting
            this.sorting = order;
            this.sortingFunc = order && sortingFunc;
        });
        this.addEventListener('beforeheaderclick', (e) => {
            var _a, _b, _c, _d;
            if (e.defaultPrevented) {
                return;
            }
            if (!((_b = (_a = e.detail) === null || _a === void 0 ? void 0 : _a.column) === null || _b === void 0 ? void 0 : _b.sortable)) {
                return;
            }
            this.headerclick(e.detail.column, (_d = (_c = e.detail) === null || _c === void 0 ? void 0 : _c.originalEvent) === null || _d === void 0 ? void 0 : _d.shiftKey);
        });
    }
    /**
     * Entry point for sorting, waits for all delayes, registers jobs
     */
    startSorting(order, sortingFunc, ignoreViewportUpdate) {
        if (!this.sortingPromise) {
            // add job before render
            this.revogrid.jobsBeforeRender.push(new Promise(resolve => {
                this.sortingPromise = resolve;
            }));
        }
        this.postponeSort(order, sortingFunc, ignoreViewportUpdate);
    }
    /**
     * Apply sorting to data on header click
     * If additive - add to existing sorting, multiple columns can be sorted
     */
    headerclick(column, additive) {
        var _a, _b, _c;
        const columnProp = column.prop;
        let order = getNextOrder((_a = this.sorting) === null || _a === void 0 ? void 0 : _a[columnProp]);
        const beforeEvent = this.emit('beforesorting', { column, order, additive });
        if (beforeEvent.defaultPrevented) {
            return;
        }
        order = beforeEvent.detail.order;
        // apply sort data
        const beforeApplyEvent = this.emit('beforesortingapply', {
            column: beforeEvent.detail.column,
            order,
            additive,
        });
        if (beforeApplyEvent.defaultPrevented) {
            return;
        }
        const cmp = getComparer(beforeApplyEvent.detail.column, beforeApplyEvent.detail.order);
        if (beforeApplyEvent.detail.additive && this.sorting) {
            const sorting = {};
            const sortingFunc = {};
            this.sorting = Object.assign(Object.assign({}, this.sorting), sorting);
            // extend sorting function with new sorting for multiple columns sorting
            this.sortingFunc = Object.assign(Object.assign({}, this.sortingFunc), sortingFunc);
            if (columnProp in sorting && size(sorting) > 1 && order === undefined) {
                delete sorting[columnProp];
                delete sortingFunc[columnProp];
            }
            else {
                sorting[columnProp] = order;
                sortingFunc[columnProp] = cmp;
            }
        }
        else {
            if (order) {
                // reset sorting
                this.sorting = { [columnProp]: order };
                this.sortingFunc = { [columnProp]: cmp };
            }
            else {
                (_b = this.sorting) === null || _b === void 0 ? true : delete _b[columnProp];
                (_c = this.sortingFunc) === null || _c === void 0 ? true : delete _c[columnProp];
            }
        }
        this.startSorting(this.sorting, this.sortingFunc);
    }
    runSorting(order, comparison, ignoreViewportUpdate) {
        var _a;
        this.sort(order, comparison, undefined, ignoreViewportUpdate);
        (_a = this.sortingPromise) === null || _a === void 0 ? void 0 : _a.call(this);
        this.sortingPromise = null;
    }
    /**
     * Sort items by sorting function
     * @requires proxyItems applied to row store
     * @requires source applied to row store
     *
     * @param sorting - per column sorting
     * @param data - this.stores['rgRow'].store.get('source')
     */
    sort(sorting, sortingFunc, types = rowTypes, ignoreViewportUpdate = false) {
        // if no sorting - reset
        if (!Object.keys(sorting || {}).length) {
            for (let type of types) {
                const storeService = this.providers.data.stores[type];
                // row data
                const source = storeService.store.get('source');
                // row indexes
                const proxyItems = storeService.store.get('proxyItems');
                // row indexes
                const newItemsOrder = Array.from({ length: source.length }, (_, i) => i); // recover indexes range(0, source.length)
                this.providers.dimension.updateSizesPositionByNewDataIndexes(type, newItemsOrder, proxyItems);
                storeService.setData({ proxyItems: newItemsOrder, source: [...source], });
            }
        }
        else {
            for (let type of types) {
                const storeService = this.providers.data.stores[type];
                // row data
                const source = storeService.store.get('source');
                // row indexes
                const proxyItems = storeService.store.get('proxyItems');
                const newItemsOrder = sortIndexByItems([...proxyItems], source, sortingFunc);
                // take row indexes before trim applied and proxy items
                const prevItems = storeService.store.get('items');
                storeService.setData({
                    proxyItems: newItemsOrder,
                    source: [...source],
                });
                // take currently visible row indexes
                const newItems = storeService.store.get('items');
                if (!ignoreViewportUpdate) {
                    this.providers.dimension
                        .updateSizesPositionByNewDataIndexes(type, newItems, prevItems);
                }
            }
        }
        // refresh columns to redraw column headers and show correct icon
        columnTypes.forEach((type) => {
            this.providers.column.dataSources[type].refresh();
        });
        this.emit('aftersortingapply');
    }
}

// provide collapse data
function doCollapse(pIndex, source) {
    const model = source[pIndex];
    const collapseValue = model[PSEUDO_GROUP_ITEM_VALUE];
    const trimmed = {};
    let i = pIndex + 1;
    const total = source.length;
    while (i < total) {
        const currentModel = source[i];
        if (isGrouping(currentModel)) {
            const currentValue = currentModel[PSEUDO_GROUP_ITEM_VALUE];
            if (!currentValue.length || !currentValue.startsWith(collapseValue + ',')) {
                break;
            }
            currentModel[GROUP_EXPANDED] = false;
        }
        trimmed[i++] = true;
    }
    model[GROUP_EXPANDED] = false;
    return { trimmed };
}
/**
 *
 * @param pIndex - physical index
 * @param vIndex - virtual index, need to update item collection
 * @param source - data source
 * @param rowItemsIndexes - rgRow indexes
 */
function doExpand(vIndex, source, rowItemsIndexes) {
    const physicalIndex = rowItemsIndexes[vIndex];
    const model = source[physicalIndex];
    const currentGroup = getParsedGroup(model[PSEUDO_GROUP_ITEM_ID]);
    const trimmed = {};
    // no group found
    if (!currentGroup) {
        return { trimmed };
    }
    const groupItems = [];
    model[GROUP_EXPANDED] = true;
    let i = physicalIndex + 1;
    const total = source.length;
    let groupLevelOnly = 0;
    // go through all rows
    while (i < total) {
        const currentModel = source[i];
        const isGroup = isGrouping(currentModel);
        // group found
        if (isGroup) {
            if (!isSameGroup(currentGroup, model, currentModel)) {
                break;
            }
            else if (!groupLevelOnly) {
                // if get group first it's group only level
                groupLevelOnly = currentModel[GROUP_DEPTH];
            }
        }
        // level 0 or same depth
        if (!groupLevelOnly || (isGroup && groupLevelOnly === currentModel[GROUP_DEPTH])) {
            trimmed[i] = false;
            groupItems.push(i);
        }
        i++;
    }
    const result = {
        trimmed,
    };
    if (groupItems.length) {
        const items = [...rowItemsIndexes];
        items.splice(vIndex + 1, 0, ...groupItems);
        result.items = items;
    }
    return result;
}

const TRIMMED_GROUPING = 'grouping';
/**
 * Prepare trimming updated indexes for grouping
 * @param initiallyTrimed
 * @param firstLevelMap
 * @param secondLevelMap
 */
function processDoubleConversionTrimmed(initiallyTrimed, firstLevelMap, secondLevelMap) {
    const trimemedOptionsToUpgrade = {};
    /**
     * go through all groups except grouping
     */
    for (let type in initiallyTrimed) {
        if (type === TRIMMED_GROUPING) {
            continue;
        }
        const items = initiallyTrimed[type];
        const newItems = {};
        for (let initialIndex in items) {
            /**
             * if item exists we find it in collection
             * we support 2 level of conversions
             */
            let newConversionIndex = firstLevelMap[initialIndex];
            if (secondLevelMap) {
                newConversionIndex = secondLevelMap[newConversionIndex];
            }
            /**
             * if item was trimmed previously
             * trimming makes sense to apply
             */
            if (items[initialIndex]) {
                newItems[newConversionIndex] = true;
                /**
                 * If changes present apply changes to new source
                 */
                if (newConversionIndex !== parseInt(initialIndex, 10)) {
                    trimemedOptionsToUpgrade[type] = newItems;
                }
            }
        }
    }
    return trimemedOptionsToUpgrade;
}

class GroupingRowPlugin extends BasePlugin {
    getStore(type = GROUPING_ROW_TYPE) {
        return this.providers.data.stores[type].store;
    }
    constructor(revogrid, providers) {
        super(revogrid, providers);
    }
    // befoce cell focus
    onFocus(e) {
        if (isGrouping(e.detail.model)) {
            e.preventDefault();
        }
    }
    // expand event triggered
    onExpand({ virtualIndex }) {
        const { source } = getSource(this.getStore().get('source'), this.getStore().get('proxyItems'));
        let newTrimmed = this.getStore().get('trimmed')[TRIMMED_GROUPING];
        let i = getPhysical(this.getStore(), virtualIndex);
        const isExpanded = getExpanded(source[i]);
        if (!isExpanded) {
            const { trimmed, items } = doExpand(virtualIndex, source, this.getStore().get('items'));
            newTrimmed = Object.assign(Object.assign({}, newTrimmed), trimmed);
            if (items) {
                setItems(this.getStore(), items);
            }
        }
        else {
            const { trimmed } = doCollapse(i, source);
            newTrimmed = Object.assign(Object.assign({}, newTrimmed), trimmed);
            this.revogrid.clearFocus();
        }
        this.getStore().set('source', source);
        this.revogrid.addTrimmed(newTrimmed, TRIMMED_GROUPING);
    }
    setColumnGrouping(cols) {
        // if 0 column as holder
        if (cols === null || cols === void 0 ? void 0 : cols.length) {
            cols[0][PSEUDO_GROUP_COLUMN] = true;
            return true;
        }
        return false;
    }
    setColumns({ columns }) {
        for (let type of columnTypes) {
            if (this.setColumnGrouping(columns[type])) {
                break;
            }
        }
    }
    // evaluate drag between groups
    onDrag(e) {
        const { from, to } = e.detail;
        const isDown = to - from >= 0;
        const { source } = getSource(this.getStore().get('source'), this.getStore().get('proxyItems'));
        const items = this.getStore().get('items');
        let i = isDown ? from : to;
        const end = isDown ? to : from;
        for (; i < end; i++) {
            const model = source[items[i]];
            const isGroup = isGrouping(model);
            if (isGroup) {
                e.preventDefault();
                return;
            }
        }
    }
    beforeTrimmedApply(trimmed, type) {
        /** Before filter apply remove grouping filtering */
        if (type === FILTER_TRIMMED_TYPE) {
            const source = this.getStore().get('source');
            for (let index in trimmed) {
                if (trimmed[index] && isGrouping(source[index])) {
                    trimmed[index] = false;
                }
            }
        }
    }
    isSortingRunning() {
        const sortingPlugin = this.providers.plugins.getByClass(SortingPlugin);
        return !!(sortingPlugin === null || sortingPlugin === void 0 ? void 0 : sortingPlugin.sortingPromise);
    }
    /**
     * Starts global source update with group clearing and applying new one
     * Initiated when need to reapply grouping
     */
    doSourceUpdate(options) {
        var _a;
        /**
         * Get source without grouping
         * @param newOldIndexMap - provides us mapping with new indexes vs old indexes, we would use it for trimmed mapping
         */
        const store = this.getStore();
        const { source, prevExpanded, oldNewIndexes } = getSource(store.get('source'), store.get('proxyItems'), true);
        const expanded = Object.assign({ prevExpanded }, options);
        /**
         * Group again
         * @param oldNewIndexMap - provides us mapping with new indexes vs old indexes
         */
        const { sourceWithGroups, depth, trimmed, oldNewIndexMap, } = gatherGrouping(source, ((_a = this.options) === null || _a === void 0 ? void 0 : _a.props) || [], expanded);
        const customRenderer = options === null || options === void 0 ? void 0 : options.groupLabelTemplate;
        // setup source
        this.providers.data.setData(sourceWithGroups, GROUPING_ROW_TYPE, this.revogrid.disableVirtualY, { depth, customRenderer }, true);
        this.updateTrimmed(trimmed, oldNewIndexes !== null && oldNewIndexes !== void 0 ? oldNewIndexes : {}, oldNewIndexMap);
    }
    /**
     * Apply grouping on data set
     * Clear grouping from source
     * If source came from other plugin
     */
    onDataSet(data) {
        var _a, _b;
        let preservedExpanded = {};
        if (((_a = this.options) === null || _a === void 0 ? void 0 : _a.preserveGroupingOnUpdate) !== false) {
            let { prevExpanded } = getSource(this.getStore().get('source'), this.getStore().get('proxyItems'), true);
            preservedExpanded = prevExpanded;
        }
        const source = data.source.filter(s => !isGrouping(s));
        const options = Object.assign(Object.assign({}, (this.revogrid.grouping || {})), { prevExpanded: preservedExpanded });
        const { sourceWithGroups, depth, trimmed, oldNewIndexMap, } = gatherGrouping(source, ((_b = this.options) === null || _b === void 0 ? void 0 : _b.props) || [], options);
        data.source = sourceWithGroups;
        this.providers.data.setGrouping({ depth });
        this.updateTrimmed(trimmed, oldNewIndexMap);
    }
    /**
     * External call to apply grouping. Called by revogrid when prop changed.
     */
    setGrouping(options) {
        var _a, _b;
        // unsubscribe from all events when group applied
        this.clearSubscriptions();
        this.options = options;
        // clear props, no grouping exists
        if (!((_b = (_a = this.options) === null || _a === void 0 ? void 0 : _a.props) === null || _b === void 0 ? void 0 : _b.length)) {
            this.clearGrouping();
            return;
        }
        // props exist and source initd
        const store = this.getStore();
        const { source } = getSource(store.get('source'), store.get('proxyItems'));
        if (source.length) {
            this.doSourceUpdate(Object.assign({}, options));
        }
        // props exist and columns initd
        for (let t of columnTypes) {
            if (this.setColumnGrouping(this.providers.column.getColumns(t))) {
                this.providers.column.refreshByType(t);
                break;
            }
        }
        // if has any grouping subscribe to events again
        /** if grouping present and new data source arrived */
        this.addEventListener('beforesourceset', ({ detail }) => {
            var _a, _b, _c;
            if (!(((_b = (_a = this.options) === null || _a === void 0 ? void 0 : _a.props) === null || _b === void 0 ? void 0 : _b.length) && ((_c = detail === null || detail === void 0 ? void 0 : detail.source) === null || _c === void 0 ? void 0 : _c.length))) {
                return;
            }
            // if sorting is running don't apply grouping, wait for sorting, then it'll apply in @aftersortingapply
            if (this.isSortingRunning()) {
                return;
            }
            this.onDataSet(detail);
        });
        this.addEventListener('beforecolumnsset', ({ detail }) => {
            this.setColumns(detail);
        });
        /**
         * filter applied need to clear grouping and apply again
         * based on new results can be new grouping
         */
        this.addEventListener('beforetrimmed', ({ detail: { trimmed, trimmedType } }) => this.beforeTrimmedApply(trimmed, trimmedType));
        /**
         * sorting applied need to clear grouping and apply again
         * based on new results whole grouping order will changed
         */
        this.addEventListener('aftersortingapply', () => {
            var _a, _b;
            if (!((_b = (_a = this.options) === null || _a === void 0 ? void 0 : _a.props) === null || _b === void 0 ? void 0 : _b.length)) {
                return;
            }
            this.doSourceUpdate(Object.assign({}, this.options));
        });
        /**
         * Apply logic for focus inside of grouping
         * We can't focus on grouping rows, navigation only inside of groups for now
         */
        this.addEventListener('beforecellfocus', e => this.onFocus(e));
        /**
         * Prevent rgRow drag outside the group
         */
        this.addEventListener('roworderchanged', e => this.onDrag(e));
        /**
         * When grouping expand icon was clicked
         */
        this.addEventListener(GROUP_EXPAND_EVENT, e => this.onExpand(e.detail));
    }
    // clear grouping
    clearGrouping() {
        // clear columns
        columnTypes.forEach(t => {
            const cols = this.providers.column.getColumns(t);
            let deleted = false;
            cols.forEach(c => {
                if (isGroupingColumn(c)) {
                    delete c[PSEUDO_GROUP_COLUMN];
                    deleted = true;
                }
            });
            // if column store had grouping clear and refresh
            if (deleted) {
                this.providers.column.refreshByType(t);
            }
        });
        // clear rows
        const { source, oldNewIndexes } = getSource(this.getStore().get('source'), this.getStore().get('proxyItems'), true);
        this.providers.data.setData(source, GROUPING_ROW_TYPE, this.revogrid.disableVirtualY, undefined, true);
        this.updateTrimmed(undefined, undefined, oldNewIndexes);
    }
    updateTrimmed(trimmedGroup = {}, firstLevelMap = {}, secondLevelMap) {
        // map previously trimmed data
        const trimemedOptionsToUpgrade = processDoubleConversionTrimmed(this.getStore().get('trimmed'), firstLevelMap, secondLevelMap);
        for (let type in trimemedOptionsToUpgrade) {
            this.revogrid.addTrimmed(trimemedOptionsToUpgrade[type], type);
        }
        // const emptyGroups = this.filterOutEmptyGroups(trimemedOptionsToUpgrade, childrenByGroup);
        // setup trimmed data for grouping
        this.revogrid.addTrimmed(Object.assign({}, trimmedGroup), TRIMMED_GROUPING);
    }
}

const COLUMN_DRAG_CLASS = 'column-drag-start';
class ColumnOrderHandler {
    constructor() {
        this.offset = 0;
    }
    renderAutoscroll(_, parent) {
        if (!parent) {
            return;
        }
        this.autoscrollEl = document.createElement('div');
        this.autoscrollEl.classList.add('drag-auto-scroll-y');
        parent.appendChild(this.autoscrollEl);
    }
    autoscroll(pos, dataContainerSize, direction = 'translateX') {
        if (!this.autoscrollEl) {
            return;
        }
        const helperOffset = 10;
        // calculate current y position inside of the grid active holder
        // 3 - size of element + border
        const maxScroll = Math.min(pos + helperOffset, dataContainerSize - 3);
        this.autoscrollEl.style.transform = `${direction}(${maxScroll}px)`;
        this.autoscrollEl.scrollIntoView({
            block: 'nearest',
            inline: 'nearest',
        });
    }
    start(e, { dataEl, gridRect, scrollEl, gridEl }, dir = 'left') {
        gridEl.classList.add(COLUMN_DRAG_CLASS);
        const scrollContainerRect = scrollEl.getBoundingClientRect();
        if (scrollContainerRect) {
            this.offset = scrollContainerRect[dir] - gridRect[dir];
        }
        this.renderAutoscroll(e, dataEl);
    }
    stop(gridEl) {
        var _a;
        gridEl.classList.remove(COLUMN_DRAG_CLASS);
        if (this.element) {
            this.element.hidden = true;
        }
        this.offset = 0;
        (_a = this.autoscrollEl) === null || _a === void 0 ? void 0 : _a.remove();
        this.autoscrollEl = undefined;
    }
    showHandler(pos, size, direction = 'translateX') {
        if (!this.element) {
            return;
        }
        // do not allow overcross top of the scrollable area, header excluded
        if (this.offset) {
            pos = Math.max(pos, this.offset);
        }
        // can not be bigger then grid end
        pos = Math.min(pos, size);
        this.element.style.transform = `${direction}(${pos}px)`;
        this.element.hidden = false;
    }
    render() {
        const el = this.element = document.createElement('div');
        el.classList.add('drag-position-y');
        el.hidden = true;
        return el;
    }
}

/**
 * Plugin for column manual move
 */
const COLUMN_CLICK = ON_COLUMN_CLICK;
const MOVE = 'columndragmousemove';
const DRAG_END = 'columndragend';
const BEFORE_DRAG_END = 'beforecolumndragend';
// use this event subscription to drop D&D for particular columns
const DRAG_START = 'columndragstart';
class ColumnMovePlugin extends BasePlugin {
    constructor(revogrid, providers) {
        super(revogrid, providers);
        this.revogrid = revogrid;
        this.providers = providers;
        this.moveFunc = debounce((e) => this.doMove(e), 5);
        this.staticDragData = null;
        this.dragData = null;
        this.localSubscriptions = {};
        this.orderUi = new ColumnOrderHandler();
        revogrid.appendChild(this.orderUi.render());
        revogrid.classList.add('column-draggable');
        // Register events
        this.localSubscriptions['mouseleave'] = {
            target: document,
            callback: (e) => this.onMouseOut(e),
        };
        this.localSubscriptions['mouseup'] = {
            target: document,
            callback: (e) => this.onMouseUp(e),
        };
        this.localSubscriptions['mousemove'] = {
            target: document,
            callback: (e) => this.move(e),
        };
        this.addEventListener(COLUMN_CLICK, ({ detail }) => this.dragStart(detail));
    }
    dragStart({ event, data }) {
        if (event.defaultPrevented) {
            return;
        }
        const { defaultPrevented } = dispatch(this.revogrid, DRAG_START, data);
        // check if allowed to drag particulat column
        if (defaultPrevented) {
            return;
        }
        this.clearOrder();
        const { mouseleave, mouseup, mousemove } = this.localSubscriptions;
        mouseleave.target.addEventListener('mouseleave', mouseleave.callback);
        mouseup.target.addEventListener('mouseup', mouseup.callback);
        const dataEl = event.target.closest('revogr-header');
        const scrollEl = event.target.closest('revogr-viewport-scroll');
        if (!dataEl || !scrollEl) {
            return;
        }
        // no grouping drag and no row header column drag
        if (isColGrouping(data) || data.providers.type === 'rowHeaders') {
            return;
        }
        const cols = this.getDimension(data.pin || 'rgCol');
        const gridRect = this.revogrid.getBoundingClientRect();
        const elRect = dataEl.getBoundingClientRect();
        const startItem = getItemByPosition(cols, getLeftRelative(event.x, gridRect.left, elRect.left - gridRect.left));
        this.staticDragData = {
            startPos: event.x,
            startItem,
            data,
            dataEl,
            scrollEl,
            gridEl: this.revogrid,
            cols,
        };
        this.dragData = this.getData(this.staticDragData);
        mousemove.target.addEventListener('mousemove', mousemove.callback);
        this.orderUi.start(event, Object.assign(Object.assign({}, this.dragData), this.staticDragData));
    }
    doMove(e) {
        if (!this.staticDragData) {
            return;
        }
        const dragData = (this.dragData = this.getData(this.staticDragData));
        if (!dragData) {
            return;
        }
        const start = this.staticDragData.startPos;
        if (Math.abs(start - e.x) > 10) {
            const x = getLeftRelative(e.x, this.dragData.gridRect.left, this.dragData.scrollOffset);
            const rgCol = getItemByPosition(this.staticDragData.cols, x);
            this.orderUi.autoscroll(x, dragData.elRect.width);
            // prevent position change if out of bounds
            if (rgCol.itemIndex >= this.staticDragData.cols.count) {
                return;
            }
            this.orderUi.showHandler(rgCol.end + dragData.scrollOffset, dragData.gridRect.width);
        }
    }
    move(e) {
        dispatch(this.revogrid, MOVE, e);
        // then do move
        this.moveFunc(e);
    }
    onMouseOut(_) {
        this.clearOrder();
    }
    onMouseUp(e) {
        // apply new positions
        if (this.dragData && this.staticDragData) {
            let relativePos = getLeftRelative(e.x, this.dragData.gridRect.left, this.dragData.scrollOffset);
            if (relativePos < 0) {
                relativePos = 0;
            }
            const newPosition = getItemByPosition(this.staticDragData.cols, relativePos);
            const store = this.providers.column.stores[this.dragData.type].store;
            const newItems = [...store.get('items')];
            // prevent position change if needed
            const { defaultPrevented: stopDrag } = dispatch(this.revogrid, BEFORE_DRAG_END, Object.assign(Object.assign({}, this.staticDragData), { startPosition: this.staticDragData.startItem, newPosition, newItem: store.get('source')[newItems[this.staticDragData.startItem.itemIndex]] }));
            if (!stopDrag) {
                const prevItems = [...newItems];
                // todo: if move item out of group remove item from group
                const toMove = newItems.splice(this.staticDragData.startItem.itemIndex, 1);
                newItems.splice(newPosition.itemIndex, 0, ...toMove);
                store.set('items', newItems);
                this.providers.dimension.updateSizesPositionByNewDataIndexes(this.dragData.type, newItems, prevItems);
            }
            dispatch(this.revogrid, DRAG_END, this.dragData);
        }
        this.clearOrder();
    }
    clearLocalSubscriptions() {
        forEach(this.localSubscriptions, ({ target, callback }, key) => target.removeEventListener(key, callback));
    }
    clearOrder() {
        this.staticDragData = null;
        this.dragData = null;
        this.clearLocalSubscriptions();
        this.orderUi.stop(this.revogrid);
    }
    /**
     * Clearing subscription
     */
    clearSubscriptions() {
        super.clearSubscriptions();
        this.clearLocalSubscriptions();
    }
    getData({ gridEl, dataEl, data, }) {
        const gridRect = gridEl.getBoundingClientRect();
        const elRect = dataEl.getBoundingClientRect();
        const scrollOffset = elRect.left - gridRect.left;
        return {
            elRect,
            gridRect,
            type: data.pin || 'rgCol',
            scrollOffset,
        };
    }
    getDimension(type) {
        return this.providers.dimension.stores[type].getCurrentState();
    }
}
function getLeftRelative(absoluteX, gridPos, offset) {
    return absoluteX - gridPos - offset;
}

class ColumnDataProvider {
    get stores() {
        return this.dataSources;
    }
    constructor() {
        this.collection = null;
        this.dataSources = columnTypes.reduce((sources, k) => {
            sources[k] = new DataStore(k);
            return sources;
        }, {});
    }
    column(c, type = 'rgCol') {
        return this.getColumn(c, type);
    }
    getColumn(virtualIndex, type) {
        return getSourceItem(this.dataSources[type].store, virtualIndex);
    }
    getRawColumns() {
        return reduce(this.dataSources, (result, item, type) => {
            result[type] = item.store.get('source');
            return result;
        }, {
            rgCol: [],
            colPinStart: [],
            colPinEnd: [],
        });
    }
    getColumns(type = 'all') {
        const columnsByType = this.getRawColumns();
        if (type !== 'all') {
            return columnsByType[type];
        }
        return columnTypes.reduce((r, t) => [...r, ...columnsByType[t]], []);
    }
    getColumnIndexByProp(prop, type) {
        return getSourceItemVirtualIndexByProp(this.dataSources[type].store, prop);
    }
    getColumnByProp(prop) {
        var _a;
        return (_a = this.collection) === null || _a === void 0 ? void 0 : _a.columnByProp[prop];
    }
    refreshByType(type) {
        this.dataSources[type].refresh();
    }
    /**
     * Main method to set columns
     */
    setColumns(data) {
        columnTypes.forEach(k => {
            // set columns data
            this.dataSources[k].updateData(data.columns[k], {
                // max depth level
                depth: data.maxLevel,
                // groups
                groups: data.columnGrouping[k].reduce((res, g) => {
                    if (!res[g.level]) {
                        res[g.level] = [];
                    }
                    res[g.level].push(g);
                    return res;
                }, {}),
            });
        });
        this.collection = data;
        return data;
    }
    /**
     * Used in plugins
     * Modify columns in store
     */
    updateColumns(updatedColumns) {
        // collect column by type and propert
        const columnByKey = updatedColumns.reduce((res, c) => {
            const type = getColumnType(c);
            if (!res[type]) {
                res[type] = {};
            }
            res[type][c.prop] = c;
            return res;
        }, {});
        // find indexes in source
        const colByIndex = {};
        for (const t in columnByKey) {
            if (!columnByKey.hasOwnProperty(t)) {
                continue;
            }
            const type = t;
            const colsToUpdate = columnByKey[type];
            const sourceItems = this.dataSources[type].store.get('source');
            colByIndex[type] = {};
            for (let i = 0; i < sourceItems.length; i++) {
                const column = sourceItems[i];
                const colToUpdateIfExists = colsToUpdate === null || colsToUpdate === void 0 ? void 0 : colsToUpdate[column.prop];
                // update column if exists in source
                if (colToUpdateIfExists) {
                    colByIndex[type][i] = colToUpdateIfExists;
                }
            }
        }
        for (const t in colByIndex) {
            if (!colByIndex.hasOwnProperty(t)) {
                continue;
            }
            const type = t;
            setSourceByPhysicalIndex(this.dataSources[type].store, colByIndex[type] || {});
        }
    }
    updateColumn(column, index) {
        const type = getColumnType(column);
        setSourceByVirtualIndex(this.dataSources[type].store, { [index]: column });
    }
}

/**
 * Data source provider
 *
 * @dependsOn DimensionProvider
 */
class DataProvider {
    constructor(dimensionProvider) {
        this.dimensionProvider = dimensionProvider;
        this.stores = reduce(rowTypes, (sources, k) => {
            sources[k] = new DataStore(k);
            return sources;
        }, {});
    }
    setData(data, type = 'rgRow', disableVirtualRows = false, grouping, silent = false) {
        // set rgRow data
        this.stores[type].updateData([...data], grouping, silent);
        // for pinned row no need virtual data
        const noVirtual = type !== 'rgRow' || disableVirtualRows;
        this.dimensionProvider.setData(data.length, type, noVirtual);
        return data;
    }
    getModel(virtualIndex, type = 'rgRow') {
        const store = this.stores[type].store;
        return getSourceItem(store, virtualIndex);
    }
    changeOrder({ rowType = 'rgRow', from, to }) {
        const storeService = this.stores[rowType];
        // take currently visible row indexes
        const newItemsOrder = [...storeService.store.get('proxyItems')];
        const prevItems = storeService.store.get('items');
        // take out
        const toMove = newItemsOrder.splice(newItemsOrder.indexOf(prevItems[from]), // get index in proxy
        1);
        // insert before
        newItemsOrder.splice(newItemsOrder.indexOf(prevItems[to]), // get index in proxy
        0, ...toMove);
        storeService.setData({
            proxyItems: newItemsOrder,
        });
        // take currently visible row indexes
        const newItems = storeService.store.get('items');
        this.dimensionProvider.updateSizesPositionByNewDataIndexes(rowType, newItems, prevItems);
    }
    setCellData({ type, rowIndex, prop, val }, mutate = true) {
        const model = this.getModel(rowIndex, type);
        model[prop] = val;
        this.stores[type].setSourceData({ [rowIndex]: model }, mutate);
    }
    setRangeData(data, type) {
        const items = {};
        for (let rowIndex in data) {
            const oldModel = (items[rowIndex] = getSourceItem(this.stores[type].store, parseInt(rowIndex, 10)));
            if (!oldModel) {
                continue;
            }
            for (let prop in data[rowIndex]) {
                oldModel[prop] = data[rowIndex][prop];
            }
        }
        this.stores[type].setSourceData(items);
    }
    refresh(type = 'all') {
        if (isRowType(type)) {
            this.refreshItems(type);
        }
        rowTypes.forEach((t) => this.refreshItems(t));
    }
    refreshItems(type = 'rgRow') {
        const items = this.stores[type].store.get('items');
        this.stores[type].setData({ items: [...items] });
    }
    setGrouping({ depth }, type = 'rgRow') {
        this.stores[type].setData({ groupingDepth: depth });
    }
    setTrimmed(trimmed, type = 'rgRow') {
        const store = this.stores[type];
        store.addTrimmed(trimmed);
        this.dimensionProvider.setTrimmed(trimmed, type);
        if (type === 'rgRow') {
            this.dimensionProvider.setData(getVisibleSourceItem(store.store).length, type);
        }
    }
}

/**
 * Dimension provider
 * Stores dimension information and custom sizes
 *
 * @dependsOn ViewportProvider
 */
class DimensionProvider {
    constructor(viewports, config) {
        this.viewports = viewports;
        const sizeChanged = debounce((k) => config.realSizeChanged(k), RESIZE_INTERVAL);
        this.stores = reduce([...rowTypes, ...columnTypes], (sources, t) => {
            sources[t] = new DimensionStore(t);
            sources[t].store.onChange('realSize', () => sizeChanged(t));
            return sources;
        }, {});
    }
    /**
     * Clear old sizes from dimension and viewports
     * @param type - dimension type
     * @param count - count of items
     */
    clearSize(t, count) {
        this.stores[t].drop();
        // after we done with drop trigger viewport recalculaction
        this.viewports.stores[t].setOriginalSizes(this.stores[t].store.get('originItemSize'));
        this.setItemCount(count, t);
    }
    /**
     * Apply new custom sizes to dimension and view port
     * @param type - dimension type
     * @param sizes - new custom sizes
     * @param keepOld - keep old sizes merge new with old
     */
    setCustomSizes(type, sizes, keepOld = false) {
        let newSizes = sizes;
        if (keepOld) {
            const oldSizes = this.stores[type].store.get('sizes');
            newSizes = Object.assign(Object.assign({}, oldSizes), sizes);
        }
        this.stores[type].setDimensionSize(newSizes);
        this.setViewPortCoordinate({
            type,
            force: true,
        });
    }
    setItemCount(realCount, type) {
        this.viewports.stores[type].setViewport({ realCount });
        this.stores[type].setStore({ count: realCount });
    }
    /**
     * Apply trimmed items
     * @param trimmed - trimmed items
     * @param type
     */
    setTrimmed(trimmed, type) {
        const allTrimmed = gatherTrimmedItems(trimmed);
        const dimStoreType = this.stores[type];
        dimStoreType.setStore({ trimmed: allTrimmed });
        this.setViewPortCoordinate({
            type,
            force: true,
        });
    }
    /**
     * Sets dimension data and viewport coordinate
     * @param itemCount
     * @param type - dimension type
     * @param noVirtual - disable virtual data
     */
    setData(itemCount, type, noVirtual = false) {
        this.setItemCount(itemCount, type);
        // Virtualization will get disabled
        if (noVirtual) {
            const dimension = this.stores[type].getCurrentState();
            this.viewports.stores[type].setViewport({
                virtualSize: dimension.realSize,
            });
        }
        this.setViewPortCoordinate({
            type,
        });
    }
    /**
     * Applies new columns to the dimension provider
     * @param columns - new columns data
     * @param disableVirtualX - disable virtual data for X axis
     */
    applyNewColumns(columns, disableVirtualX, keepOld = false) {
        // Apply new columns to dimension provider
        for (let type of columnTypes) {
            if (!keepOld) {
                // Clear existing data in the dimension provider
                this.stores[type].drop();
            }
            // Get the new columns for the current type
            const items = columns[type];
            // Determine if virtual data should be disabled for the current type
            const noVirtual = type !== 'rgCol' || disableVirtualX;
            // Set the items count in the dimension provider
            this.stores[type].setStore({ count: items.length });
            // Set the custom sizes for the columns
            const newSizes = getColumnSizes(items);
            this.stores[type].setDimensionSize(newSizes);
            // Update the viewport with new data
            const vpUpdate = {
                // This triggers drop on realCount change
                realCount: items.length,
            };
            // If virtual data is disabled, set the virtual size to the real size
            if (noVirtual) {
                vpUpdate.virtualSize = this.stores[type].getCurrentState().realSize;
            }
            // Update the viewport
            this.viewports.stores[type].setViewport(vpUpdate);
            this.setViewPortCoordinate({
                type,
            });
        }
    }
    /**
     * Gets the full size of the grid by summing up the sizes of all dimensions
     * Goes through all dimensions columnTypes (x) and rowTypes (y) and sums up their sizes
     */
    getFullSize() {
        var _a, _b;
        let x = 0;
        let y = 0;
        for (let type of columnTypes) {
            x += ((_a = this.stores[type]) === null || _a === void 0 ? void 0 : _a.store.get('realSize')) || 0;
        }
        for (let type of rowTypes) {
            y += ((_b = this.stores[type]) === null || _b === void 0 ? void 0 : _b.store.get('realSize')) || 0;
        }
        return { y, x };
    }
    setViewPortCoordinate({ type, coordinate = this.viewports.stores[type].lastCoordinate, force = false, }) {
        const dimension = this.stores[type].getCurrentState();
        this.viewports.stores[type].setViewPortCoordinate(coordinate, dimension, force);
    }
    getViewPortPos(e) {
        const dimension = this.stores[e.dimension].getCurrentState();
        const item = getItemByIndex(dimension, e.coordinate);
        return item.start;
    }
    setSettings(data, dimensionType) {
        let stores = [];
        switch (dimensionType) {
            case 'rgCol':
                stores = columnTypes;
                break;
            case 'rgRow':
                stores = rowTypes;
                break;
        }
        for (let s of stores) {
            this.stores[s].setStore(data);
        }
    }
    updateSizesPositionByNewDataIndexes(type, newItemsOrder, prevItemsOrder = []) {
        // Move custom sizes to new order
        this.stores[type].updateSizesPositionByIndexes(newItemsOrder, prevItemsOrder);
        this.setViewPortCoordinate({
            type,
            force: true,
        });
    }
}

class ViewportProvider {
    constructor() {
        this.stores = reduce([...rowTypes, ...columnTypes], (sources, k) => {
            sources[k] = new ViewportStore(k);
            return sources;
        }, {});
    }
    setViewport(type, data) {
        this.stores[type].setViewport(data);
    }
}

/** Collect Column data */
function gatherColumnData(data) {
    const colDimension = data.dimensions[data.colType].store;
    const realWidth = colDimension.get('realSize');
    const prop = {
        contentWidth: realWidth,
        class: data.colType,
        contentHeight: data.contentHeight,
        key: data.colType,
        colType: data.colType,
        onResizeviewport: data.onResizeviewport,
        // set viewport size to real size
        style: data.fixWidth ? { minWidth: `${realWidth}px` } : undefined,
    };
    const headerProp = {
        colData: getVisibleSourceItem(data.colStore),
        dimensionCol: colDimension,
        type: data.colType,
        groups: data.colStore.get('groups'),
        groupingDepth: data.colStore.get('groupingDepth'),
        resizeHandler: data.colType === 'colPinEnd' ? ['l'] : undefined,
        onHeaderresize: data.onHeaderresize,
    };
    return {
        prop,
        type: data.colType,
        position: data.position,
        headerProp,
        viewportCol: data.viewports[data.colType].store,
    };
}
class ViewportService {
    constructor(config, contentHeight) {
        var _a, _b;
        this.config = config;
        (_a = this.config.selectionStoreConnector) === null || _a === void 0 ? void 0 : _a.beforeUpdate();
        // ----------- Handle columns ----------- //
        // Transform data from stores and apply it to different components
        const columns = [];
        let x = 0; // we increase x only if column present
        columnTypes.forEach(val => {
            const colStore = config.columnProvider.stores[val].store;
            // only columns that have data show
            if (!colStore.get('items').length) {
                return;
            }
            const column = {
                colType: val,
                position: { x, y: 1 },
                contentHeight,
                // only central column has dynamic width
                fixWidth: val !== 'rgCol',
                viewports: config.viewportProvider.stores,
                dimensions: config.dimensionProvider.stores,
                rowStores: config.dataProvider.stores,
                colStore,
                onHeaderresize: e => this.onColumnResize(val, e, colStore),
            };
            if (val === 'rgCol') {
                column.onResizeviewport = (e) => {
                    var _a;
                    const vpState = {
                        clientSize: e.detail.size,
                    };
                    // virtual size will be handled by dimension provider if disabled
                    if ((e.detail.dimension === 'rgRow' && !config.disableVirtualY)
                        || (e.detail.dimension === 'rgCol' && !config.disableVirtualX)) {
                        vpState.virtualSize = e.detail.size;
                    }
                    (_a = config.viewportProvider) === null || _a === void 0 ? void 0 : _a.setViewport(e.detail.dimension, vpState);
                };
            }
            const colData = gatherColumnData(column);
            const columnSelectionStore = this.registerCol(colData.position.x, val);
            // render per each column data collections vertically
            const dataPorts = this.dataViewPort(column).reduce((r, rgRow) => {
                // register selection store for Segment
                const segmentSelection = this.registerSegment(rgRow.position);
                segmentSelection.setLastCell(rgRow.lastCell);
                // register selection store for Row
                const rowSelectionStore = this.registerRow(rgRow.position.y, rgRow.type);
                const rowDef = Object.assign(Object.assign({ colType: val }, rgRow), { rowSelectionStore, selectionStore: segmentSelection.store, ref: (e) => config.selectionStoreConnector.registerSection(e), onSetrange: e => {
                        segmentSelection.setRangeArea(e.detail);
                    }, onSettemprange: e => segmentSelection.setTempArea(e.detail), onFocuscell: e => {
                        // todo: multi focus
                        segmentSelection.clearFocus();
                        config.selectionStoreConnector.focus(segmentSelection, e.detail);
                    } });
                r.push(rowDef);
                return r;
            }, []);
            columns.push(Object.assign(Object.assign({}, colData), { columnSelectionStore,
                dataPorts }));
            x++;
        });
        this.columns = columns;
        // ----------- Handle columns end ----------- //
        (_b = this.config.scrollingService) === null || _b === void 0 ? void 0 : _b.unregister();
    }
    onColumnResize(type, { detail }, store) {
        var _a;
        // apply to dimension provider
        (_a = this.config.dimensionProvider) === null || _a === void 0 ? void 0 : _a.setCustomSizes(type, detail, true);
        // set resize event
        const changedItems = {};
        for (const [i, size] of Object.entries(detail || {})) {
            const virtualIndex = parseInt(i, 10);
            const item = getSourceItem(store, virtualIndex);
            if (item) {
                changedItems[virtualIndex] = Object.assign(Object.assign({}, item), { size });
            }
        }
        this.config.resize(changedItems);
    }
    /** register selection store for Segment */
    registerSegment(position) {
        return this.config.selectionStoreConnector.register(position);
    }
    /** register selection store for Row */
    registerRow(y, type) {
        return this.config.selectionStoreConnector.registerRow(y, type).store;
    }
    /** register selection store for Column */
    registerCol(x, type) {
        return this.config.selectionStoreConnector.registerColumn(x, type).store;
    }
    /** Collect Row data */
    dataViewPort(data) {
        const slots = {
            rowPinStart: HEADER_SLOT,
            rgRow: CONTENT_SLOT,
            rowPinEnd: FOOTER_SLOT,
        };
        // y position for selection
        let y = 0;
        return rowTypes.reduce((result, type) => {
            // filter out empty sources, we still need to return source to keep slot working
            const isPresent = data.viewports[type].store.get('realCount') || type === 'rgRow';
            const rgCol = Object.assign(Object.assign({}, data), { position: Object.assign(Object.assign({}, data.position), { y: isPresent ? y : EMPTY_INDEX }) });
            const partition = viewportDataPartition(rgCol, type, slots[type], type !== 'rgRow');
            result.push(partition);
            if (isPresent) {
                y++;
            }
            return result;
        }, []);
    }
    scrollToCell(cell) {
        for (let key in cell) {
            const coordinate = cell[key];
            if (typeof coordinate === 'number') {
                this.config.scrollingService.proxyScroll({
                    dimension: key === 'x' ? 'rgCol' : 'rgRow',
                    coordinate,
                });
            }
        }
    }
    /**
     * Clear current grid focus
     */
    clearFocused() {
        this.config.selectionStoreConnector.clearAll();
    }
    clearEdit() {
        this.config.selectionStoreConnector.setEdit(false);
    }
    /**
     * Collect focused element data
     */
    getFocused() {
        const focused = this.config.selectionStoreConnector.focusedStore;
        if (!focused) {
            return null;
        }
        // get column data
        const colType = this.config.selectionStoreConnector.storesXToType[focused.position.x];
        const column = this.config.columnProvider.getColumn(focused.cell.x, colType);
        // get row data
        const rowType = this.config.selectionStoreConnector.storesYToType[focused.position.y];
        const model = this.config.dataProvider.getModel(focused.cell.y, rowType);
        return {
            column,
            model,
            cell: focused.cell,
            colType,
            rowType,
        };
    }
    getStoreCoordinateByType(colType, rowType) {
        const stores = this.config.selectionStoreConnector.storesByType;
        if (typeof stores[colType] === 'undefined' || typeof stores[rowType] === 'undefined') {
            return;
        }
        return {
            x: stores[colType],
            y: stores[rowType],
        };
    }
    setFocus(colType, rowType, start, end) {
        var _a;
        const coordinate = this.getStoreCoordinateByType(colType, rowType);
        if (coordinate) {
            (_a = this.config.selectionStoreConnector) === null || _a === void 0 ? void 0 : _a.focusByCell(coordinate, start, end);
        }
    }
    getSelectedRange() {
        const focused = this.config.selectionStoreConnector.focusedStore;
        if (!focused) {
            return null;
        }
        // get column data
        const colType = this.config.selectionStoreConnector.storesXToType[focused.position.x];
        // get row data
        const rowType = this.config.selectionStoreConnector.storesYToType[focused.position.y];
        const range = focused.entity.store.get('range');
        if (!range) {
            return null;
        }
        return Object.assign(Object.assign({}, range), { colType,
            rowType });
    }
    setEdit(rowIndex, colIndex, colType, rowType) {
        var _a;
        const coordinate = this.getStoreCoordinateByType(colType, rowType);
        if (coordinate) {
            (_a = this.config.selectionStoreConnector) === null || _a === void 0 ? void 0 : _a.setEditByCell(coordinate, { x: colIndex, y: rowIndex });
        }
    }
}

class GridScrollingService {
    constructor(setViewport) {
        this.setViewport = setViewport;
        this.elements = {};
    }
    async proxyScroll(e, key) {
        var _a;
        let newEventPromise;
        let event = e;
        for (let elKey in this.elements) {
            // skip
            if (e.dimension === 'rgCol' && elKey === 'headerRow') {
                continue;
                // pinned column only
            }
            else if (this.isPinnedColumn(key) && e.dimension === 'rgCol') {
                if (elKey === key || !e.delta) {
                    continue;
                }
                for (let el of this.elements[elKey]) {
                    if (el.changeScroll) {
                        newEventPromise = el.changeScroll(e);
                    }
                }
            }
            else {
                for (let el of this.elements[elKey]) {
                    await ((_a = el.setScroll) === null || _a === void 0 ? void 0 : _a.call(el, e));
                }
            }
        }
        const newEvent = await newEventPromise;
        if (newEvent) {
            event = newEvent;
        }
        this.setViewport(event);
    }
    /**
     * Silent scroll update for mobile devices when we have negative scroll top
     */
    async scrollSilentService(e, key) {
        var _a;
        for (let elKey in this.elements) {
            // skip same element update
            if (elKey === key) {
                continue;
            }
            if (columnTypes.includes(key) &&
                (elKey === 'headerRow' ||
                    columnTypes.includes(elKey))) {
                for (let el of this.elements[elKey]) {
                    await ((_a = el.changeScroll) === null || _a === void 0 ? void 0 : _a.call(el, e, true));
                }
                continue;
            }
        }
    }
    isPinnedColumn(key) {
        return !!key && ['colPinStart', 'colPinEnd'].indexOf(key) > -1;
    }
    registerElements(els) {
        this.elements = els;
    }
    /**
     * Register new element for farther scroll support
     * @param el - can be null if holder removed
     * @param key - element key
     */
    registerElement(el, key) {
        if (!this.elements[key]) {
            this.elements[key] = [];
        }
        // new element added
        if (el) {
            this.elements[key].push(el);
        }
        else if (this.elements[key]) {
            // element removed
            delete this.elements[key];
        }
    }
    unregister() {
        this.elements = {};
    }
}

/**
 * Draw drag
 */
class OrdererService {
    constructor() {
        this.parentY = 0;
    }
    start(parent, { pos, text, event }) {
        var _a;
        const { top } = parent.getBoundingClientRect();
        this.parentY = top;
        if (this.text) {
            this.text.innerText = text;
        }
        this.move(pos);
        this.moveTip({ x: event.x, y: event.y });
        (_a = this.el) === null || _a === void 0 ? void 0 : _a.classList.remove('hidden');
    }
    end() {
        var _a;
        (_a = this.el) === null || _a === void 0 ? void 0 : _a.classList.add('hidden');
    }
    move(pos) {
        this.moveElement(pos.end - this.parentY);
    }
    moveTip({ x, y }) {
        if (!this.draggable) {
            return;
        }
        this.draggable.style.left = `${x}px`;
        this.draggable.style.top = `${y}px`;
    }
    moveElement(y) {
        if (!this.rgRow) {
            return;
        }
        this.rgRow.style.transform = `translateY(${y}px)`;
    }
}
const OrderRenderer = ({ ref }) => {
    const service = new OrdererService();
    ref(service);
    return (h("div", { class: "draggable-wrapper hidden", ref: e => (service.el = e) },
        h("div", { class: "draggable", ref: el => (service.draggable = el) },
            h("span", { class: "revo-alt-icon" }),
            h("span", { ref: e => (service.text = e) })),
        h("div", { class: "drag-position", ref: e => (service.rgRow = e) })));
};

const rowDefinitionByType = (newVal = []) => {
    const result = {};
    for (const v of newVal) {
        let rowDefs = result[v.type];
        if (!rowDefs) {
            rowDefs = result[v.type] = {};
        }
        if (v.size) {
            if (!rowDefs.sizes) {
                rowDefs.sizes = {};
            }
            rowDefs.sizes[v.index] = v.size;
        }
    }
    return result;
};
const rowDefinitionRemoveByType = (oldVal = []) => {
    const result = {};
    for (const v of oldVal) {
        let rowDefs = result[v.type];
        if (!rowDefs) {
            rowDefs = result[v.type] = [];
        }
        if (v.size) {
            rowDefs.push(v.index);
        }
    }
    return result;
};

function isMobileDevice() {
    return /Mobi/i.test(navigator.userAgent) || /Android/i.test(navigator.userAgent) || navigator.maxTouchPoints > 0;
}

/**
 * WCAG Plugin is responsible for enhancing the accessibility features of the RevoGrid component.
 * It ensures that the grid is fully compliant with Web Content Accessibility Guidelines (WCAG) 2.1.
 * This plugin should be the last plugin you add, as it modifies the grid's default behavior.
 *
 * The WCAG Plugin performs the following tasks:
 * - Sets the 'dir' attribute to 'ltr' for left-to-right text direction.
 * - Sets the 'role' attribute to 'treegrid' for treelike hierarchical structure.
 * - Sets the 'aria-keyshortcuts' attribute to 'Enter' and 'Esc' for keyboard shortcuts.
 * - Adds event listeners for keyboard navigation and editing.
 *
 * By default, the plugin adds ARIA roles and properties to the grid elements, providing semantic information
 * for assistive technologies. These roles include 'grid', 'row', and 'gridcell'. The plugin also sets
 * ARIA attributes such as 'aria-rowindex', 'aria-colindex', and 'aria-selected'.
 *
 * The WCAG Plugin ensures that the grid is fully functional and usable for users with various disabilities,
 * including visual impairments, deaf-blindness, and cognitive disabilities.
 *
 * Note: The WCAG Plugin should be added as the last plugin in the list of plugins, as it modifies the grid's
 * default behavior and may conflict with other plugins if added earlier.
 */
class WCAGPlugin extends BasePlugin {
    constructor(revogrid, providers) {
        super(revogrid, providers);
        revogrid.setAttribute('dir', 'ltr');
        revogrid.setAttribute('role', 'treegrid');
        revogrid.setAttribute('aria-keyshortcuts', 'Enter');
        revogrid.setAttribute('aria-multiselectable', 'true');
        revogrid.setAttribute('tabindex', '0');
        /**
         * Before Columns Set Event
         */
        this.addEventListener('beforecolumnsset', ({ detail }) => {
            const columns = [
                ...detail.columns.colPinStart,
                ...detail.columns.rgCol,
                ...detail.columns.colPinEnd,
            ];
            revogrid.setAttribute('aria-colcount', `${columns.length}`);
            columns.forEach((column, index) => {
                const { columnProperties, cellProperties } = column;
                column.columnProperties = (...args) => {
                    const result = (columnProperties === null || columnProperties === void 0 ? void 0 : columnProperties(...args)) || {};
                    result.role = 'columnheader';
                    result['aria-colindex'] = `${index}`;
                    return result;
                };
                column.cellProperties = (...args) => {
                    const wcagProps = {
                        ['role']: 'gridcell',
                        ['aria-colindex']: `${index}`,
                        ['aria-rowindex']: `${args[0].rowIndex}`,
                        ['tabindex']: -1,
                    };
                    const columnProps = (cellProperties === null || cellProperties === void 0 ? void 0 : cellProperties(...args)) || {};
                    return Object.assign(Object.assign({}, wcagProps), columnProps);
                };
            });
        });
        /**
         * Before Row Set Event
         */
        this.addEventListener('beforesourceset', ({ detail, }) => {
            revogrid.setAttribute('aria-rowcount', `${detail.source.length}`);
        });
        this.addEventListener('beforerowrender', ({ detail, }) => {
            detail.node.$attrs$ = Object.assign(Object.assign({}, detail.node.$attrs$), { role: 'row', ['aria-rowindex']: detail.item.itemIndex });
        });
        // focuscell
        this.addEventListener('afterfocus', async (e) => {
            if (e.defaultPrevented) {
                return;
            }
            const el = this.revogrid.querySelector(`revogr-data[type="${e.detail.rowType}"][col-type="${e.detail.colType}"] [data-rgrow="${e.detail.rowIndex}"][data-rgcol="${e.detail.colIndex}"]`);
            if (el instanceof HTMLElement) {
                el.focus();
            }
        });
    }
}

/**
 * Plugin service
 * Manages plugins
 */
class PluginService {
    constructor() {
        /**
         * Plugins
         * Define plugins collection
         */
        this.internalPlugins = [];
    }
    /**
     * Get all plugins
     */
    get() {
        return [...this.internalPlugins];
    }
    /**
     * Add plugin to collection
     */
    add(plugin) {
        this.internalPlugins.push(plugin);
    }
    /**
     * Add user plugins and create
     */
    addUserPluginsAndCreate(element, plugins = [], prevPlugins, pluginData) {
        if (!pluginData) {
            return;
        }
        // Step 1: Identify plugins to remove, compare new and old plugins
        const pluginsToRemove = (prevPlugins === null || prevPlugins === void 0 ? void 0 : prevPlugins.filter(prevPlugin => !plugins.some(userPlugin => userPlugin === prevPlugin))) || [];
        // Step 2: Remove old plugins
        pluginsToRemove.forEach(plugin => {
            var _a, _b;
            const index = this.internalPlugins.findIndex(createdPlugin => createdPlugin instanceof plugin);
            if (index !== -1) {
                (_b = (_a = this.internalPlugins[index]).destroy) === null || _b === void 0 ? void 0 : _b.call(_a);
                this.internalPlugins.splice(index, 1); // Remove the plugin
            }
        });
        // Step 3: Register user plugins
        plugins === null || plugins === void 0 ? void 0 : plugins.forEach(userPlugin => {
            // check if plugin already exists, if so, skip
            const existingPlugin = this.internalPlugins.find(createdPlugin => createdPlugin instanceof userPlugin);
            if (existingPlugin) {
                return;
            }
            this.add(new userPlugin(element, pluginData));
        });
    }
    /**
     * Get plugin by class
     */
    getByClass(pluginClass) {
        return this.internalPlugins.find(p => p instanceof pluginClass);
    }
    /**
     * Remove plugin
     */
    remove(plugin) {
        var _a, _b;
        const index = this.internalPlugins.indexOf(plugin);
        if (index > -1) {
            (_b = (_a = this.internalPlugins[index]).destroy) === null || _b === void 0 ? void 0 : _b.call(_a);
            this.internalPlugins.splice(index, 1);
        }
    }
    /**
     * Remove all plugins
     */
    destroy() {
        this.internalPlugins.forEach(p => { var _a; return (_a = p.destroy) === null || _a === void 0 ? void 0 : _a.call(p); });
        this.internalPlugins = [];
    }
}

const revoGridStyleCss = ".revo-drag-icon{width:11px;opacity:0.8}.revo-drag-icon::before{content:\"::\"}.revo-alt-icon{-webkit-mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");width:11px;height:11px;background-size:cover;background-repeat:no-repeat}.arrow-down{position:absolute;right:5px;top:0}.arrow-down svg{width:8px;margin-top:5px;margin-left:5px;opacity:0.4}.cell-value-wrapper{margin-right:10px;overflow:hidden;text-overflow:ellipsis}.revo-button{position:relative;overflow:hidden;color:#fff;background-color:#4545ff;height:32px;line-height:32px;padding:0 15px;outline:0;border:0;border-radius:7px;box-sizing:border-box;cursor:pointer}.revo-button.green{background-color:#009037}.revo-button.red{background-color:#E0662E}.revo-button:disabled,.revo-button[disabled]{cursor:not-allowed !important;filter:opacity(0.35) !important}.revo-button.outline{border:1px solid #dbdbdb;line-height:30px;background:none;color:#000;box-shadow:none}revo-grid[theme^=dark] .revo-button.outline{border:1px solid #404040;color:#d8d8d8}revo-grid[theme=default],revo-grid:not([theme]){border:1px solid var(--revo-grid-header-border);font-size:12px}revo-grid[theme=default] .rowHeaders revogr-header,revo-grid:not([theme]) .rowHeaders revogr-header{box-shadow:-1px 0 0 0 var(--revo-grid-header-border) inset}revo-grid[theme=default] revogr-header,revo-grid:not([theme]) revogr-header{text-align:center;line-height:30px;background-color:var(--revo-grid-header-bg)}revo-grid[theme=default] revogr-header .group-rgRow,revo-grid:not([theme]) revogr-header .group-rgRow{box-shadow:none}revo-grid[theme=default] revogr-header .group-rgRow .rgHeaderCell,revo-grid:not([theme]) revogr-header .group-rgRow .rgHeaderCell{box-shadow:-1px 0 0 0 var(--revo-grid-header-border), -1px 0 0 0 var(--revo-grid-header-border) inset, 0 -1px 0 0 var(--revo-grid-header-border), 0 -1px 0 0 var(--revo-grid-header-border) inset}revo-grid[theme=default] revogr-header .header-rgRow,revo-grid[theme=default] revogr-header .group-rgRow,revo-grid:not([theme]) revogr-header .header-rgRow,revo-grid:not([theme]) revogr-header .group-rgRow{text-transform:uppercase;font-size:12px;color:var(--revo-grid-header-color)}revo-grid[theme=default] revogr-header .header-rgRow,revo-grid:not([theme]) revogr-header .header-rgRow{height:30px;box-shadow:0 -1px 0 0 var(--revo-grid-header-border) inset}revo-grid[theme=default] revogr-header .rgHeaderCell,revo-grid:not([theme]) revogr-header .rgHeaderCell{box-shadow:-1px 0 0 0 var(--revo-grid-header-border) inset, 0 -1px 0 0 var(--revo-grid-header-border), 0 -1px 0 0 var(--revo-grid-header-border) inset}revo-grid[theme=default] .rowHeaders,revo-grid:not([theme]) .rowHeaders{background-color:var(--revo-grid-header-bg)}revo-grid[theme=default] .rowHeaders revogr-data .rgCell,revo-grid:not([theme]) .rowHeaders revogr-data .rgCell{color:var(--revo-grid-header-color)}revo-grid[theme=default] .rowHeaders revogr-data .rgCell:first-child,revo-grid:not([theme]) .rowHeaders revogr-data .rgCell:first-child{box-shadow:0 -1px 0 0 var(--revo-grid-header-border) inset}revo-grid[theme=default] .rowHeaders revogr-data .rgCell:not(:first-child),revo-grid:not([theme]) .rowHeaders revogr-data .rgCell:not(:first-child){box-shadow:0 -1px 0 0 var(--revo-grid-header-border) inset, 1px 0 0 0 var(--revo-grid-header-border) inset}revo-grid[theme=default] .rowHeaders revogr-data .rgCell:last-child,revo-grid:not([theme]) .rowHeaders revogr-data .rgCell:last-child{border-right:1px solid var(--revo-grid-header-border)}revo-grid[theme=default] .rowHeaders revogr-data revogr-header,revo-grid:not([theme]) .rowHeaders revogr-data revogr-header{box-shadow:0 -1px 0 0 var(--revo-grid-header-border) inset}revo-grid[theme=default] revogr-viewport-scroll.colPinStart revogr-data .rgRow .rgCell:last-child,revo-grid:not([theme]) revogr-viewport-scroll.colPinStart revogr-data .rgRow .rgCell:last-child{box-shadow:0 -1px 0 0 var(--revo-grid-cell-border) inset, -1px 0 0 0 var(--revo-grid-header-border) inset}revo-grid[theme=default] revogr-viewport-scroll.colPinStart .footer-wrapper revogr-data .rgRow:first-child .rgCell,revo-grid:not([theme]) revogr-viewport-scroll.colPinStart .footer-wrapper revogr-data .rgRow:first-child .rgCell{box-shadow:0 1px 0 0 var(--revo-grid-header-border) inset, -1px 0 0 0 var(--revo-grid-header-border) inset}revo-grid[theme=default] revogr-viewport-scroll.colPinEnd,revo-grid[theme=default] revogr-viewport-scroll.colPinEnd revogr-header,revo-grid:not([theme]) revogr-viewport-scroll.colPinEnd,revo-grid:not([theme]) revogr-viewport-scroll.colPinEnd revogr-header{box-shadow:1px 0 0 var(--revo-grid-header-border) inset}revo-grid[theme=default] .footer-wrapper revogr-data .rgRow:first-child .rgCell,revo-grid:not([theme]) .footer-wrapper revogr-data .rgRow:first-child .rgCell{box-shadow:0 1px 0 0 var(--revo-grid-cell-border) inset, -1px 0 0 0 var(--revo-grid-cell-border) inset, 0 -1px 0 0 var(--revo-grid-cell-border) inset}revo-grid[theme=default] revogr-data,revo-grid:not([theme]) revogr-data{text-align:center}revo-grid[theme=default] revogr-data .revo-draggable,revo-grid:not([theme]) revogr-data .revo-draggable{float:left}revo-grid[theme=default] revogr-data .rgRow,revo-grid:not([theme]) revogr-data .rgRow{line-height:27px}revo-grid[theme=default] revogr-data .rgCell,revo-grid:not([theme]) revogr-data .rgCell{box-shadow:0 -1px 0 0 var(--revo-grid-cell-border) inset, -1px 0 0 0 var(--revo-grid-cell-border) inset}revo-grid[theme=material]{font-family:Nunito, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"}revo-grid[theme=material] revogr-header{line-height:50px;font-weight:600;text-align:left}revo-grid[theme=material] revogr-header .rgHeaderCell{padding:0 15px;text-overflow:ellipsis}revo-grid[theme=material] revogr-header .header-rgRow{height:50px}revo-grid[theme=material] revogr-data{text-align:left}revo-grid[theme=material] revogr-data .rgRow{line-height:42px}revo-grid[theme=material] revogr-data .rgCell{padding:0 15px}revo-grid[theme=darkMaterial]{font-family:Nunito, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"}revo-grid[theme=darkMaterial] revogr-header{line-height:50px;font-weight:600;text-align:left}revo-grid[theme=darkMaterial] revogr-header .rgHeaderCell{padding:0 15px;text-overflow:ellipsis}revo-grid[theme=darkMaterial] revogr-header .header-rgRow{height:50px}revo-grid[theme=darkMaterial] revogr-data{text-align:left}revo-grid[theme=darkMaterial] revogr-data .rgRow{line-height:42px}revo-grid[theme=darkMaterial] revogr-data .rgCell{padding:0 15px}revo-grid[theme=darkCompact]{font-family:Nunito, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"}revo-grid[theme=darkCompact] revogr-header{line-height:45px;font-weight:600;text-align:left}revo-grid[theme=darkCompact] revogr-header .rgHeaderCell{padding:0 15px;text-overflow:ellipsis}revo-grid[theme=darkCompact] revogr-header .header-rgRow{height:45px}revo-grid[theme=darkCompact] revogr-data{text-align:left}revo-grid[theme=darkCompact] revogr-data .rgRow{line-height:32px}revo-grid[theme=darkCompact] revogr-data .rgCell{padding:0 15px}revo-grid[theme=compact]{font-family:Nunito, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"}revo-grid[theme=compact] revogr-header{line-height:45px;font-weight:600;text-align:left}revo-grid[theme=compact] revogr-header .rgHeaderCell{padding:0 15px;text-overflow:ellipsis}revo-grid[theme=compact] revogr-header .header-rgRow{height:45px}revo-grid[theme=compact] revogr-data{text-align:left}revo-grid[theme=compact] revogr-data .rgRow{line-height:32px}revo-grid[theme=compact] revogr-data .rgCell{padding:0 15px}revo-grid[theme=compact] revo-dropdown .rv-dr-root{padding:0px 9px}revo-grid{--revo-grid-primary:#266ae8;--revo-grid-primary-transparent:rgba(38, 106, 232, 0.9);--revo-grid-background:#fff;--revo-grid-foreground:black;--revo-grid-divider:gray;--revo-grid-shadow:rgba(0, 0, 0, 0.15);--revo-grid-text:black;--revo-grid-border:rgba(0, 0, 0, 0.2);--revo-grid-filter-panel-bg:#fff;--revo-grid-filter-panel-border:#d9d9d9;--revo-grid-filter-panel-shadow:rgba(0, 0, 0, 0.15);--revo-grid-filter-panel-input-bg:#eaeaeb;--revo-grid-filter-panel-divider:#d9d9d9;--revo-grid-filter-panel-select-border:transparent;--revo-grid-filter-panel-select-border-hover:transparent;--revo-grid-header-bg:#f8f9fa;--revo-grid-header-color:#000;--revo-grid-header-border:#cecece;--revo-grid-cell-border:#e2e3e3;--revo-grid-focused-bg:rgba(233, 234, 237, 0.5);--revo-grid-row-hover:#f1f1f1;--revo-grid-row-headers-bg:#f7faff;--revo-grid-row-headers-color:#757a82;--revo-grid-cell-disabled-bg:rgba(0, 0, 0, 0.07);display:flex !important;height:100%;min-height:300px;font-family:Helvetica, Arial, Sans-Serif, serif;font-size:14px;position:relative;color:var(--revo-grid-text);-webkit-touch-callout:none;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:flex;flex-direction:column;width:100%;height:100%}revo-grid[theme*=dark]{--revo-grid-background:#212529;--revo-grid-foreground:#fff;--revo-grid-text:rgba(255, 255, 255, 0.9);--revo-grid-divider:#505050;--revo-grid-border:rgba(255, 255, 255, 0.2);--revo-grid-filter-panel-bg:#212529;--revo-grid-filter-panel-border:#505050;--revo-grid-filter-panel-input-bg:#343a40;--revo-grid-filter-panel-divider:#505050;--revo-grid-header-bg:#343a40;--revo-grid-header-color:#fff;--revo-grid-header-border:#505050;--revo-grid-cell-border:#424242;--revo-grid-focused-bg:rgba(52, 58, 64, 0.5);--revo-grid-row-hover:rgba(80, 80, 80, 0.5);--revo-grid-row-headers-bg:rgba(52, 58, 64, 0.8);--revo-grid-row-headers-color:rgba(255, 255, 255, 0.8);--revo-grid-cell-disabled-bg:rgba(255, 255, 255, 0.07)}revo-grid revogr-header .header-rgRow.group{box-shadow:0 -1px 0 0 var(--revo-grid-header-border) inset}revo-grid revogr-header .header-rgRow:not(.group){box-shadow:0 -1px 0 0 var(--revo-grid-header-border), 0 -1px 0 0 var(--revo-grid-header-border) inset}revo-grid revogr-header .rgHeaderCell.sortable:hover{background-color:var(--revo-grid-row-hover)}revo-grid revogr-header .rgHeaderCell.focused-cell{background:var(--revo-grid-focused-bg)}revo-grid .footer-wrapper revogr-data{box-shadow:0 -1px 0 var(--revo-grid-cell-border)}revo-grid revogr-viewport-scroll.colPinStart{box-shadow:-1px 0 0 var(--revo-grid-cell-border) inset}revo-grid revogr-viewport-scroll.colPinEnd{box-shadow:-1px 0 0 var(--revo-grid-cell-border)}revo-grid revogr-data .rgRow{box-shadow:0 -1px 0 0 var(--revo-grid-cell-border) inset}revo-grid revogr-data .rgRow.focused-rgRow{background-color:var(--revo-grid-focused-bg)}revo-grid revogr-data .rgCell{color:var(--revo-grid-text)}revo-grid revogr-data .rgCell.disabled{background-color:var(--revo-grid-cell-disabled-bg)}revo-grid .attribution{position:absolute;bottom:0;left:0;right:0;z-index:1000;width:0;height:0;border-left:4px solid var(--revo-grid-primary-transparent);border-bottom:4px solid var(--revo-grid-primary-transparent);border-top:4px solid transparent;border-right:4px solid transparent;cursor:pointer}revo-grid .attribution .value{position:absolute;bottom:0;left:0;background-color:var(--revo-grid-background);padding:4px;border-radius:4px;box-shadow:0 1px 10px var(--revo-grid-border);white-space:nowrap;text-decoration:none;color:var(--revo-grid-text);letter-spacing:0.3px;font-size:11px;opacity:0;width:4px;overflow:hidden;transition:opacity 0.5s ease-in-out, width 0.3s ease-in-out}revo-grid .attribution:hover .value{width:63px;opacity:1}revo-grid.column-draggable.column-drag-start:hover,revo-grid.column-draggable.column-drag-start *:hover{cursor:grabbing}revo-grid .footer-wrapper,revo-grid .header-wrapper{width:100%}revo-grid .footer-wrapper revogr-data,revo-grid .header-wrapper revogr-data{z-index:3}revo-grid revo-dropdown{width:100%}revo-grid revo-dropdown .rv-dr-root{max-height:100%}revo-grid revo-dropdown.shrink label{opacity:0}revo-grid .viewports{max-width:100%;display:flex;flex-direction:row;align-items:flex-start;flex-grow:1}revo-grid .main-viewport{flex-grow:1;height:0;display:flex;justify-content:space-between;flex-direction:row}revo-grid .draggable{position:fixed;height:30px;line-height:30px;background:var(--revo-grid-background);border-radius:3px;display:block;z-index:100;margin-top:5px;margin-right:-20px;box-shadow:0 4px 20px 0 var(--revo-grid-shadow);padding-left:20px;padding-right:5px}revo-grid .draggable.hidden{display:none}revo-grid .draggable .revo-alt-icon{background-color:var(--revo-grid-foreground);position:absolute;left:5px;top:10px}revo-grid .draggable-wrapper.hidden{display:none}revo-grid .drag-position{position:absolute;left:0;right:0;height:1px;z-index:2;background:var(--revo-grid-divider);pointer-events:none}revo-grid .drag-position-y{position:absolute;top:0;left:0;bottom:0;width:1px;z-index:2;background:var(--revo-grid-divider);pointer-events:none}revo-grid .drag-auto-scroll-y{pointer-events:none;position:absolute;left:0;top:0;height:50px;width:1px}revo-grid .clipboard{position:absolute;left:0;top:0}revo-grid revogr-scroll-virtual{position:relative}revo-grid revogr-scroll-virtual.vertical,revo-grid revogr-scroll-virtual.horizontal{z-index:3}";

const RevoGridComponent = /*@__PURE__*/ proxyCustomElement(class RevoGridComponent extends HTMLElement$1 {
    constructor() {
        super();
        this.__registerHost();
        this.contentsizechanged = createEvent(this, "contentsizechanged", 7);
        this.beforeedit = createEvent(this, "beforeedit", 7);
        this.beforerangeedit = createEvent(this, "beforerangeedit", 7);
        this.afteredit = createEvent(this, "afteredit", 7);
        this.beforeautofill = createEvent(this, "beforeautofill", 7);
        this.beforerange = createEvent(this, "beforerange", 7);
        this.afterfocus = createEvent(this, "afterfocus", 7);
        this.roworderchanged = createEvent(this, "roworderchanged", 7);
        this.beforesorting = createEvent(this, "beforesorting", 7);
        this.beforesourcesortingapply = createEvent(this, "beforesourcesortingapply", 7);
        this.beforesortingapply = createEvent(this, "beforesortingapply", 7);
        this.rowdragstart = createEvent(this, "rowdragstart", 7);
        this.headerclick = createEvent(this, "headerclick", 7);
        this.beforecellfocus = createEvent(this, "beforecellfocus", 7);
        this.beforefocuslost = createEvent(this, "beforefocuslost", 7);
        this.beforesourceset = createEvent(this, "beforesourceset", 7);
        this.beforeanysource = createEvent(this, "beforeanysource", 7);
        this.aftersourceset = createEvent(this, "aftersourceset", 7);
        this.afteranysource = createEvent(this, "afteranysource", 7);
        this.beforecolumnsset = createEvent(this, "beforecolumnsset", 7);
        this.beforecolumnapplied = createEvent(this, "beforecolumnapplied", 7);
        this.aftercolumnsset = createEvent(this, "aftercolumnsset", 7);
        this.beforefilterapply = createEvent(this, "beforefilterapply", 7);
        this.beforefiltertrimmed = createEvent(this, "beforefiltertrimmed", 7);
        this.beforetrimmed = createEvent(this, "beforetrimmed", 7);
        this.aftertrimmed = createEvent(this, "aftertrimmed", 7);
        this.viewportscroll = createEvent(this, "viewportscroll", 7);
        this.beforeexport = createEvent(this, "beforeexport", 7);
        this.beforeeditstart = createEvent(this, "beforeeditstart", 7);
        this.aftercolumnresize = createEvent(this, "aftercolumnresize", 7);
        this.beforerowdefinition = createEvent(this, "beforerowdefinition", 7);
        this.filterconfigchanged = createEvent(this, "filterconfigchanged", 7);
        this.sortingconfigchanged = createEvent(this, "sortingconfigchanged", 7);
        this.rowheaderschanged = createEvent(this, "rowheaderschanged", 7);
        this.beforegridrender = createEvent(this, "beforegridrender", 7);
        this.aftergridrender = createEvent(this, "aftergridrender", 7);
        this.aftergridinit = createEvent(this, "aftergridinit", 7);
        this.additionaldatachanged = createEvent(this, "additionaldatachanged", 7);
        this.afterthemechanged = createEvent(this, "afterthemechanged", 7);
        this.created = createEvent(this, "created", 7);
        /**
         * Defines how many rows/columns should be rendered outside visible area.
         */
        this.frameSize = 1;
        /**
         * Indicates default rgRow size.
         * By default 0, means theme package size will be applied
         *
         * Alternatively you can use `rowSize` to reset viewport
         */
        this.rowSize = 0;
        /** Indicates default column size. */
        this.colSize = 100;
        /** When true, user can range selection. */
        this.range = false;
        /** When true, grid in read only mode. */
        this.readonly = false;
        /** When true, columns are resizable. */
        this.resize = false;
        /** When true cell focus appear. */
        this.canFocus = true;
        /** When true enable clipboard. */
        this.useClipboard = true;
        /**
         * Columns - defines an array of grid columns.
         * Can be column or grouped column.
         */
        this.columns = [];
        /**
         * Source - defines main data source.
         * Can be an Object or 2 dimensional array([][]);
         * Keys/indexes referenced from columns Prop.
         */
        this.source = [];
        /** Pinned top Source: {[T in ColumnProp]: any} - defines pinned top rows data source. */
        this.pinnedTopSource = [];
        /** Pinned bottom Source: {[T in ColumnProp]: any} - defines pinned bottom rows data source. */
        this.pinnedBottomSource = [];
        /** Custom row properies to be applied. See `RowDefinition` for more info. */
        this.rowDefinitions = [];
        /** Custom editors register. */
        this.editors = {};
        /**
         * Apply changes in editor when closed except 'Escape' cases.
         * If custom editor in use method getValue required.
         * Check interfaces.d.ts `EditorBase` for more info.
         */
        this.applyOnClose = false;
        /**
         * Custom grid plugins. Can be added or removed at runtime.
         * Every plugin should be inherited from BasePlugin class.
         *
         * For more details check [Plugin guide](https://rv-grid.com/guide/plugin/)
         */
        this.plugins = [];
        /**
         * Column Types Format.
         * Every type represent multiple column properties.
         * Types will be merged but can be replaced with column properties.
         * Types were made as separate objects to be reusable per multiple columns.
         */
        this.columnTypes = {};
        /** Theme name. */
        this.theme = 'default';
        /**
         * Row class property mapping.
         * Map custom classes to rows from row object data.
         * Define this property in rgRow object and this will be mapped as rgRow class.
         */
        this.rowClass = '';
        /**
         * Autosize config.
         * Enables columns autoSize.
         * For more details check `autoSizeColumn` plugin.
         * By default disabled, hence operation is not performance efficient.
         * `true` to enable with default params (double header separator click for autosize).
         * Or define config. See `AutoSizeColumnConfig` for more details.
         */
        this.autoSizeColumn = false;
        /**
         * Enables filter plugin.
         * Can be boolean.
         * Or can be filter collection See `FilterCollection` for more info.
         */
        this.filter = false;
        /**
         * Enable column move plugin.
         */
        this.canMoveColumns = false;
        /**
         * Trimmed rows.
         * Functionality which allows to hide rows from main data set.
         * `trimmedRows` are physical `rgRow` indexes to hide.
         */
        this.trimmedRows = {};
        /**
         * Enable export plugin.
         */
        this.exporting = false;
        /**
         * Stretch strategy for columns by `StretchColumn` plugin.
         * For example if there are more space on the right last column size would be increased.
         */
        this.stretch = false;
        /**
         * Additional data to be passed to plugins, renders or editors.
         * For example if you need to pass Vue component instance.
         */
        this.additionalData = {};
        /**
         * Disable lazy rendering mode for the `X axis`.
         * Use when not many columns present and you don't need rerenader cells during scroll.
         * Can be used for initial rendering performance improvement.
         */
        this.disableVirtualX = false;
        /**
         * Disable lazy rendering mode for the `Y axis`.
         * Use when not many rows present and you don't need rerenader cells during scroll.
         * Can be used for initial rendering performance improvement.
         */
        this.disableVirtualY = false;
        /**
         * Please only hide the attribution if you are subscribed to Pro version
         */
        this.hideAttribution = false;
        /**
         * Prevent rendering until job is done.
         * Can be used for initial rendering performance improvement.
         * When several plugins require initial rendering this will prevent double initial rendering.
         */
        this.jobsBeforeRender = [];
        /**
         * Register new virtual node inside of grid.
         * Used for additional items creation such as plugin elements.
         * Should be set before grid render inside of plugins.
         * Can return VNode result of h() function or a function that returns VNode.
         * Function can be used for performance improvement and additional renders.
         */
        this.registerVNode = [];
        /**
         * Enable accessibility. If disabled, the grid will not be accessible.
         * @default true
         */
        this.accessible = true;
        /**
         * Disable native drag&drop plugin.
         */
        this.canDrag = true;
        this.extraElements = [];
        this.pluginService = new PluginService();
        this.viewport = null;
        this.isInited = false;
    }
    // #endregion
    // #region Methods
    /**
     * Refreshes data viewport.
     * Can be specific part as rgRow or pinned rgRow or 'all' by default.
     */
    async refresh(type = 'all') {
        if (!this.dataProvider) {
            throw new Error('Not connected');
        }
        this.dataProvider.refresh(type);
    }
    /**
     * Refreshes data at specified cell.
     * Useful for performance optimization.
     * No viewport update will be triggered.
     *
     * @example
     * const grid = document.querySelector('revo-grid');
     * grid.setDataAt({ row: 0, col: 0, val: 'test' }); // refresh
     */
    async setDataAt({ row, col, colType = 'rgCol', rowType = 'rgRow', val, skipDataUpdate = false }) {
        var _a;
        if (this.dataProvider && this.columnProvider && !skipDataUpdate) {
            const columnProp = (_a = this.columnProvider.getColumn(col, colType)) === null || _a === void 0 ? void 0 : _a.prop;
            if (typeof columnProp !== 'undefined') {
                this.dataProvider.setCellData({
                    type: rowType,
                    rowIndex: row,
                    prop: columnProp,
                    val,
                }, false);
            }
        }
        const dataElement = this.element.querySelector(`revogr-data[type="${rowType}"][col-type="${colType}"]`);
        return dataElement === null || dataElement === void 0 ? void 0 : dataElement.updateCell({
            row,
            col,
        });
    }
    /**
     * Scrolls viewport to specified row by index.
     */
    async scrollToRow(coordinate = 0) {
        if (!this.dimensionProvider) {
            throw new Error('Not connected');
        }
        const y = this.dimensionProvider.getViewPortPos({
            coordinate,
            dimension: 'rgRow',
        });
        await this.scrollToCoordinate({ y });
    }
    /**
     * Scrolls viewport to specified column by index.
     */
    async scrollToColumnIndex(coordinate = 0) {
        if (!this.dimensionProvider) {
            throw new Error('Not connected');
        }
        const x = this.dimensionProvider.getViewPortPos({
            coordinate,
            dimension: 'rgCol',
        });
        await this.scrollToCoordinate({ x });
    }
    /**
     * Scrolls viewport to specified column by prop
     */
    async scrollToColumnProp(prop, dimension = 'rgCol') {
        if (!this.dimensionProvider || !this.columnProvider) {
            throw new Error('Not connected');
        }
        const coordinate = this.columnProvider.getColumnIndexByProp(prop, dimension);
        if (coordinate < 0) {
            // already on the screen
            return;
        }
        const x = this.dimensionProvider.getViewPortPos({
            coordinate,
            dimension,
        });
        await this.scrollToCoordinate({ x });
    }
    /** Update columns */
    async updateColumns(cols) {
        var _a;
        (_a = this.columnProvider) === null || _a === void 0 ? void 0 : _a.updateColumns(cols);
    }
    /** Add trimmed by type */
    async addTrimmed(trimmed, trimmedType = 'external', type = 'rgRow') {
        if (!this.dataProvider) {
            throw new Error('Not connected');
        }
        const event = this.beforetrimmed.emit({
            trimmed,
            trimmedType,
            type,
        });
        if (event.defaultPrevented) {
            return event;
        }
        this.dataProvider.setTrimmed({ [trimmedType]: event.detail.trimmed }, type);
        this.aftertrimmed.emit();
        return event;
    }
    /**  Scrolls view port to coordinate */
    async scrollToCoordinate(cell) {
        var _a;
        (_a = this.viewport) === null || _a === void 0 ? void 0 : _a.scrollToCell(cell);
    }
    /**  Open editor for cell. */
    async setCellEdit(rgRow, prop, rowSource = 'rgRow') {
        var _a;
        const rgCol = getColumnByProp(this.columns, prop);
        if (!rgCol) {
            return;
        }
        await timeout();
        const colGroup = rgCol.pin || 'rgCol';
        if (!this.columnProvider) {
            throw new Error('Not connected');
        }
        (_a = this.viewport) === null || _a === void 0 ? void 0 : _a.setEdit(rgRow, this.columnProvider.getColumnIndexByProp(prop, colGroup), colGroup, rowSource);
    }
    /**  Set focus range. */
    async setCellsFocus(cellStart = { x: 0, y: 0 }, cellEnd = { x: 0, y: 0 }, colType = 'rgCol', rowType = 'rgRow') {
        var _a;
        (_a = this.viewport) === null || _a === void 0 ? void 0 : _a.setFocus(colType, rowType, cellStart, cellEnd);
    }
    /**  Get data from source */
    async getSource(type = 'rgRow') {
        if (!this.dataProvider) {
            throw new Error('Not connected');
        }
        return this.dataProvider.stores[type].store.get('source');
    }
    /**
     * Get data from visible part of source
     * Trimmed/filtered rows will be excluded
     * @param type - type of source
     */
    async getVisibleSource(type = 'rgRow') {
        if (!this.dataProvider) {
            throw new Error('Not connected');
        }
        return getVisibleSourceItem(this.dataProvider.stores[type].store);
    }
    /**
     * Provides access to rows internal store observer
     * Can be used for plugin support
     * @param type - type of source
     */
    async getSourceStore(type = 'rgRow') {
        if (!this.dataProvider) {
            throw new Error('Not connected');
        }
        return this.dataProvider.stores[type].store;
    }
    /**
     * Provides access to column internal store observer
     * Can be used for plugin support
     * @param type - type of column
     */
    async getColumnStore(type = 'rgCol') {
        if (!this.columnProvider) {
            throw new Error('Not connected');
        }
        return this.columnProvider.stores[type].store;
    }
    /**
     * Update column sorting
     * @param column - column prop and cellCompare
     * @param order - order to apply
     * @param additive - if false will replace current order
     *
     * later passed to SortingPlugin
     */
    async updateColumnSorting(column, order, additive) {
        this.sortingconfigchanged.emit({
            columns: [{
                    prop: column.prop,
                    order,
                    cellCompare: column.cellCompare,
                }],
            additive,
        });
    }
    /**
     * Clears column sorting
     */
    async clearSorting() {
        this.sortingconfigchanged.emit({
            columns: [],
        });
    }
    /**
     * Receive all columns in data source
     */
    async getColumns() {
        if (!this.columnProvider) {
            throw new Error('Not connected');
        }
        return this.columnProvider.getColumns();
    }
    /**
     * Clear current grid focus. Grid has no longer focus on it.
     */
    async clearFocus() {
        var _a, _b;
        const focused = (_a = this.viewport) === null || _a === void 0 ? void 0 : _a.getFocused();
        const event = this.beforefocuslost.emit(focused);
        if (event.defaultPrevented) {
            return;
        }
        (_b = this.selectionStoreConnector) === null || _b === void 0 ? void 0 : _b.clearAll();
    }
    /**
     * Get all active plugins instances
     */
    async getPlugins() {
        return this.pluginService.get();
    }
    /**
     * Get the currently focused cell.
     */
    async getFocused() {
        var _a, _b;
        return (_b = (_a = this.viewport) === null || _a === void 0 ? void 0 : _a.getFocused()) !== null && _b !== void 0 ? _b : null;
    }
    /**
     * Get size of content
     * Including all pinned data
     */
    async getContentSize() {
        var _a;
        if (!this.dimensionProvider) {
            throw new Error('Not connected');
        }
        return (_a = this.dimensionProvider) === null || _a === void 0 ? void 0 : _a.getFullSize();
    }
    /**
     * Get the currently selected Range.
     */
    async getSelectedRange() {
        var _a, _b;
        return (_b = (_a = this.viewport) === null || _a === void 0 ? void 0 : _a.getSelectedRange()) !== null && _b !== void 0 ? _b : null;
    }
    /**
     * Refresh extra elements. Triggers re-rendering of extra elements and functions.
     * Part of extraElements and registerVNode methods.
     * Useful for plugins.
     */
    async refreshExtraElements() {
        var _a;
        (_a = this.extraService) === null || _a === void 0 ? void 0 : _a.refresh();
    }
    /**
     * Get all providers for grid
     * Useful for external grid integration
     */
    async getProviders() {
        return this.getPluginData();
    }
    mousedownHandle(event) {
        const screenX = getPropertyFromEvent(event, 'screenX');
        const screenY = getPropertyFromEvent(event, 'screenY');
        if (screenX === null || screenY === null) {
            return;
        }
        this.clickTrackForFocusClear = screenX + screenY;
    }
    /**
     * To keep your elements from losing focus use mouseup/touchend e.preventDefault();
     */
    async mouseupHandle(event) {
        var _a;
        const screenX = getPropertyFromEvent(event, 'screenX');
        const screenY = getPropertyFromEvent(event, 'screenY');
        if (screenX === null || screenY === null) {
            return;
        }
        if (event.defaultPrevented) {
            return;
        }
        const pos = screenX + screenY;
        // detect if mousemove then do nothing
        if (Math.abs(((_a = this.clickTrackForFocusClear) !== null && _a !== void 0 ? _a : 0) - pos) > 10) {
            return;
        }
        // Check if action finished inside the document
        // if event prevented, or it is current table don't clear focus
        const path = event.composedPath();
        if (!path.includes(this.element) &&
            !(this.element.shadowRoot && path.includes(this.element.shadowRoot))) {
            // Perform actions if the click is outside the component
            await this.clearFocus();
        }
    }
    // #endregion
    // #region Listeners
    /** Drag events */
    onRowDragStarted(e) {
        var _a;
        const dragStart = this.rowdragstart.emit(e.detail);
        if (dragStart.defaultPrevented) {
            e.preventDefault();
            return;
        }
        (_a = this.orderService) === null || _a === void 0 ? void 0 : _a.start(this.element, Object.assign(Object.assign({}, e.detail), dragStart.detail));
    }
    onRowDragEnd() {
        var _a;
        (_a = this.orderService) === null || _a === void 0 ? void 0 : _a.end();
    }
    onRowOrderChange(e) {
        var _a;
        (_a = this.dataProvider) === null || _a === void 0 ? void 0 : _a.changeOrder(e.detail);
    }
    onRowDrag({ detail }) {
        var _a;
        (_a = this.orderService) === null || _a === void 0 ? void 0 : _a.move(detail);
    }
    onRowMouseMove(e) {
        var _a;
        (_a = this.orderService) === null || _a === void 0 ? void 0 : _a.moveTip(e.detail);
    }
    async onCellEdit(e) {
        var _a;
        const { defaultPrevented, detail } = this.beforeedit.emit(e.detail);
        await timeout();
        // apply data
        if (!defaultPrevented) {
            (_a = this.dataProvider) === null || _a === void 0 ? void 0 : _a.setCellData(detail);
            // @feature: incrimental update for cells
            // this.dataProvider.setCellData(detail, false);
            // await this.setDataAt({
            //   row: detail.rowIndex,
            //   col: detail.colIndex,
            //   rowType: detail.type,
            //   colType: detail.colType,
            // });
            this.afteredit.emit(detail);
        }
    }
    onRangeEdit(e) {
        if (!this.dataProvider) {
            throw new Error('Not connected');
        }
        const { defaultPrevented, detail } = this.beforerangeedit.emit(e.detail);
        if (defaultPrevented) {
            e.preventDefault();
            return;
        }
        this.dataProvider.setRangeData(detail.data, detail.type);
        this.afteredit.emit(detail);
    }
    onRangeChanged(e) {
        const beforeange = this.beforerange.emit(e.detail);
        if (beforeange.defaultPrevented) {
            e.preventDefault();
        }
        const beforeFill = this.beforeautofill.emit(beforeange.detail);
        if (beforeFill.defaultPrevented) {
            e.preventDefault();
        }
    }
    onRowDropped(e) {
        // e.cancelBubble = true;
        const { defaultPrevented } = this.roworderchanged.emit(e.detail);
        if (defaultPrevented) {
            e.preventDefault();
        }
    }
    onHeaderClick(e) {
        const { defaultPrevented } = this.headerclick.emit(Object.assign(Object.assign({}, e.detail.column), { originalEvent: e.detail.originalEvent }));
        if (defaultPrevented) {
            e.preventDefault();
        }
    }
    onCellFocus(e) {
        const { defaultPrevented } = this.beforecellfocus.emit(e.detail);
        if (!this.canFocus || defaultPrevented) {
            e.preventDefault();
        }
    }
    // #endregion
    // #region Watchers
    columnTypesChanged() {
        // Column format change will trigger column structure update
        this.columnChanged(this.columns);
    }
    columnChanged(newVal = [], _prevVal = undefined, __watchName = 'columns', init = false) {
        if (!this.dimensionProvider || !this.columnProvider) {
            return;
        }
        const columnGather = getColumns(newVal, 0, this.columnTypes);
        const beforeSetEvent = this.beforecolumnsset.emit(columnGather);
        if (beforeSetEvent.defaultPrevented) {
            return;
        }
        this.dimensionProvider.applyNewColumns(beforeSetEvent.detail.columns, this.disableVirtualX, init);
        const beforeApplyEvent = this.beforecolumnapplied.emit(columnGather);
        if (beforeApplyEvent.defaultPrevented) {
            return;
        }
        const columns = this.columnProvider.setColumns(beforeApplyEvent.detail);
        this.aftercolumnsset.emit({
            columns,
            order: Object.entries(beforeApplyEvent.detail.sort).reduce((acc, [prop, column]) => {
                acc[prop] = column.order;
                return acc;
            }, {}),
        });
    }
    disableVirtualXChanged(newVal = false, prevVal = false) {
        if (newVal === prevVal) {
            return;
        }
        this.columnChanged(this.columns);
    }
    rowSizeChanged(s) {
        if (!this.dimensionProvider) {
            return;
        }
        // clear existing data
        this.dimensionProvider.setSettings({ originItemSize: s }, 'rgRow');
        this.rowDefChanged(this.rowDefinitions, this.rowDefinitions, 'rowSize', true);
    }
    themeChanged(t, _, __ = 'theme', init = false) {
        if (!this.dimensionProvider) {
            return;
        }
        this.themeService.register(t);
        this.dimensionProvider.setSettings({ originItemSize: this.themeService.rowSize }, 'rgRow');
        this.dimensionProvider.setSettings({ originItemSize: this.colSize }, 'rgCol');
        // if theme change we need to reapply row size and reset viewport
        if (!init) {
            // clear existing data
            this.dimensionProvider.setSettings({ originItemSize: this.themeService.rowSize }, 'rgRow');
            this.rowDefChanged(
            // for cases when some custom size present and not
            this.rowDefinitions, this.rowDefinitions, 'theme', true);
        }
        this.afterthemechanged.emit(t);
    }
    dataSourceChanged(newVal = [], _, watchName) {
        if (!this.dataProvider) {
            return;
        }
        let type = 'rgRow';
        switch (watchName) {
            case 'pinnedBottomSource':
                type = 'rowPinEnd';
                break;
            case 'pinnedTopSource':
                type = 'rowPinStart';
                break;
            case 'source':
                type = 'rgRow';
                /**
                 * Applied for source only for cross compatability between plugins
                 */
                const beforesourceset = this.beforesourceset.emit({
                    type,
                    source: newVal,
                });
                newVal = beforesourceset.detail.source;
                break;
        }
        const beforesourceset = this.beforeanysource.emit({
            type,
            source: newVal,
        });
        const newSource = [...beforesourceset.detail.source];
        this.dataProvider.setData(newSource, type, this.disableVirtualY);
        /**
         * Applied for source only for cross compatability between plugins
         */
        if (watchName === 'source') {
            this.aftersourceset.emit({
                type,
                source: newVal,
            });
        }
        this.afteranysource.emit({
            type,
            source: newVal,
        });
    }
    disableVirtualYChanged(newVal = false, prevVal = false) {
        if (newVal === prevVal) {
            return;
        }
        this.dataSourceChanged(this.source, this.source, 'source');
    }
    rowDefChanged(after, before, _watchName, forceUpdate = true) {
        // in firefox it's triggered before init
        if (!this.dimensionProvider || !this.dataProvider) {
            return;
        }
        const { detail: { vals: newVal, oldVals: oldVal }, } = this.beforerowdefinition.emit({
            vals: after,
            oldVals: before,
        });
        // apply new values
        const newRows = rowDefinitionByType(newVal);
        // clear current defs
        if (oldVal) {
            const remove = rowDefinitionRemoveByType(oldVal);
            // clear all old data and drop sizes
            for (const t in remove) {
                if (remove.hasOwnProperty(t)) {
                    const type = t;
                    const store = this.dataProvider.stores[type];
                    const sourceLength = store.store.get('source').length;
                    this.dimensionProvider.clearSize(type, sourceLength);
                }
            }
        }
        // set new sizes
        rowTypes.forEach((t) => {
            var _a;
            const newSizes = newRows[t];
            // apply new sizes or force update
            if (newSizes || forceUpdate) {
                (_a = this.dimensionProvider) === null || _a === void 0 ? void 0 : _a.setCustomSizes(t, (newSizes === null || newSizes === void 0 ? void 0 : newSizes.sizes) || {});
            }
        });
    }
    trimmedRowsChanged(newVal = {}) {
        this.addTrimmed(newVal);
    }
    /**
     * Grouping
     */
    groupingChanged(newVal = {}) {
        var _a;
        (_a = this.pluginService.getByClass(GroupingRowPlugin)) === null || _a === void 0 ? void 0 : _a.setGrouping(newVal || {});
    }
    /**
     * Stretch Plugin Apply
     */
    applyStretch(isStretch) {
        if (!this.dimensionProvider || !this.dataProvider || !this.columnProvider || !this.viewportProvider) {
            return;
        }
        if (isStretch === 'false') {
            isStretch = false;
        }
        const pluginData = this.getPluginData();
        if (!pluginData) {
            return;
        }
        const stretch = this.pluginService.getByClass(StretchColumn);
        if ((typeof isStretch === 'boolean' && isStretch) || isStretch === 'true') {
            if (!stretch) {
                this.pluginService.add(new StretchColumn(this.element, pluginData));
            }
            else if (isStretchPlugin(stretch)) {
                stretch.applyStretch(this.columnProvider.getRawColumns());
            }
        }
        else if (stretch) {
            this.pluginService.remove(stretch);
        }
    }
    applyFilter(cfg) {
        this.filterconfigchanged.emit(cfg);
    }
    applySorting(cfg) {
        this.sortingconfigchanged.emit(cfg);
    }
    rowHeadersChange(rowHeaders) {
        this.rowheaderschanged.emit(rowHeaders);
    }
    /**
     * Register external VNodes
     */
    registerOutsideVNodes(elements = []) {
        this.extraElements = elements;
    }
    additionalDataChanged(data) {
        this.additionaldatachanged.emit(data);
    }
    /**
     * User can add plugins via plugins property
     */
    pluginsChanged(plugins = [], prevPlugins) {
        this.pluginService.addUserPluginsAndCreate(this.element, plugins, prevPlugins, this.getPluginData());
    }
    // #endregion
    // #region Plugins
    setPlugins() {
        // remove old plugins if any
        this.removePlugins();
        // pass data provider to plugins
        const pluginData = this.getPluginData();
        if (!pluginData) {
            return;
        }
        // register system plugins
        this.setCorePlugins(pluginData);
        // register user plugins
        this.pluginsChanged(this.plugins);
    }
    setCorePlugins(pluginData) {
        if (this.accessible) {
            this.pluginService.add(new WCAGPlugin(this.element, pluginData));
        }
        // register auto size plugin
        if (this.autoSizeColumn) {
            this.pluginService.add(new AutoSizeColumnPlugin(this.element, pluginData, typeof this.autoSizeColumn === 'object'
                ? this.autoSizeColumn
                : undefined));
        }
        // register filter plugin
        if (this.filter) {
            this.pluginService.add(new FilterPlugin(this.element, pluginData, typeof this.filter === 'object' ? this.filter : undefined));
        }
        // register export plugin
        if (this.exporting) {
            this.pluginService.add(new ExportFilePlugin(this.element, pluginData));
        }
        // register sorting plugin
        this.pluginService.add(new SortingPlugin(this.element, pluginData));
        // register grouping plugin
        this.pluginService.add(new GroupingRowPlugin(this.element, pluginData));
        if (this.canMoveColumns) {
            this.pluginService.add(new ColumnMovePlugin(this.element, pluginData));
        }
    }
    getPluginData() {
        if (!this.dimensionProvider || !this.dataProvider || !this.columnProvider || !this.viewportProvider || !this.selectionStoreConnector) {
            return;
        }
        // pass data provider to plugins
        const pluginData = {
            data: this.dataProvider,
            column: this.columnProvider,
            dimension: this.dimensionProvider,
            viewport: this.viewportProvider,
            selection: this.selectionStoreConnector,
            plugins: this.pluginService,
        };
        return pluginData;
    }
    removePlugins() {
        this.pluginService.destroy();
    }
    // #endregion
    // if reconnect to dom we need to set up plugins
    connectedCallback() {
        if (this.isInited) {
            this.setPlugins();
        }
        this.created.emit();
    }
    /**
     * Called once just after the component is first connected to the DOM.
     * Since this method is only called once, it's a good place to load data asynchronously and to setup the state
     * without triggering extra re-renders.
     * A promise can be returned, that can be used to wait for the first render().
     */
    componentWillLoad() {
        var _a;
        // #region Setup Providers
        this.viewportProvider = new ViewportProvider();
        this.themeService = new ThemeService({
            rowSize: this.rowSize,
        });
        this.dimensionProvider = new DimensionProvider(this.viewportProvider, {
            realSizeChanged: (k) => this.contentsizechanged.emit(k),
        });
        this.columnProvider = new ColumnDataProvider();
        this.selectionStoreConnector = new SelectionStoreConnector();
        this.dataProvider = new DataProvider(this.dimensionProvider);
        // #endregion
        this.registerOutsideVNodes(this.registerVNode);
        // init plugins
        this.setPlugins();
        // set data
        this.applyStretch(this.stretch);
        this.themeChanged(this.theme, undefined, undefined, true);
        this.columnChanged(this.columns, undefined, undefined, true);
        this.dataSourceChanged(this.source, undefined, 'source');
        this.dataSourceChanged(this.pinnedTopSource, undefined, 'pinnedTopSource');
        this.dataSourceChanged(this.pinnedBottomSource, undefined, 'pinnedBottomSource');
        if (Object.keys((_a = this.trimmedRows) !== null && _a !== void 0 ? _a : {}).length > 0) {
            this.trimmedRowsChanged(this.trimmedRows);
        }
        this.rowDefChanged(this.rowDefinitions);
        // init grouping
        if (this.grouping && Object.keys(this.grouping).length > 0) {
            this.groupingChanged(this.grouping);
        }
        // init scrolling service
        this.scrollingService = new GridScrollingService((e) => {
            var _a;
            (_a = this.dimensionProvider) === null || _a === void 0 ? void 0 : _a.setViewPortCoordinate({
                coordinate: e.coordinate,
                type: e.dimension,
            });
            this.viewportscroll.emit(e);
        });
        this.aftergridinit.emit();
        // set inited flag for connectedCallback
        this.isInited = true;
    }
    componentWillRender() {
        const event = this.beforegridrender.emit();
        if (event.defaultPrevented) {
            return false;
        }
        return Promise.all(this.jobsBeforeRender);
    }
    componentDidRender() {
        this.aftergridrender.emit();
    }
    render() {
        if (!this.dimensionProvider || !this.dataProvider || !this.columnProvider || !this.viewportProvider || !this.selectionStoreConnector) {
            return;
        }
        const contentHeight = this.dimensionProvider.stores['rgRow'].store.get('realSize');
        // init viewport service helpers
        this.viewport = new ViewportService({
            columnProvider: this.columnProvider,
            dataProvider: this.dataProvider,
            dimensionProvider: this.dimensionProvider,
            viewportProvider: this.viewportProvider,
            scrollingService: this.scrollingService,
            orderService: this.orderService,
            selectionStoreConnector: this.selectionStoreConnector,
            disableVirtualX: this.disableVirtualX,
            disableVirtualY: this.disableVirtualY,
            resize: c => this.aftercolumnresize.emit(c),
        }, contentHeight);
        // #region ViewportSections
        /**
         * The code renders a viewport divided into sections.
         * It starts by rendering the pinned start, data, and pinned end sections.
         * Within each section, it renders columns along with their headers, pinned top, center data, and pinned bottom.
         * The code iterates over the columns and their data to generate the view port's HTML structure.
         */
        const viewportSections = [];
        // Row headers setting
        if (this.rowHeaders && this.viewport.columns.length) {
            const anyView = this.viewport.columns[0];
            viewportSections.push(h("revogr-row-headers", { additionalData: this.additionalData, height: contentHeight, rowClass: this.rowClass, resize: this.resize, dataPorts: anyView.dataPorts, headerProp: anyView.headerProp, jobsBeforeRender: this.jobsBeforeRender, rowHeaderColumn: typeof this.rowHeaders === 'object' ? this.rowHeaders : undefined, onScrollview: ({ detail: e }) => this.scrollingService.proxyScroll(e, 'headerRow'), onRef: ({ detail: e }) => this.scrollingService.registerElement(e, 'headerRow') }));
        }
        // Viewport section render
        const isMobile = isMobileDevice();
        const viewPortHtml = [];
        // Render viewports column(horizontal sections)
        for (let view of this.viewport.columns) {
            const headerProperties = Object.assign(Object.assign({}, view.headerProp), { type: view.type, additionalData: this.additionalData, viewportCol: view.viewportCol, selectionStore: view.columnSelectionStore, canResize: this.resize, readonly: this.readonly, columnFilter: !!this.filter });
            // Column headers
            const dataViews = [
                h("revogr-header", Object.assign({}, headerProperties, { slot: HEADER_SLOT })),
            ];
            // Render viewport data (vertical sections)
            view.dataPorts.forEach(data => {
                const key = `${data.type}_${view.type}`;
                const dataView = (h("revogr-overlay-selection", Object.assign({}, data, { canDrag: this.canDrag && data.canDrag, isMobileDevice: isMobile, onSelectall: () => { var _a; return (_a = this.selectionStoreConnector) === null || _a === void 0 ? void 0 : _a.selectAll(); }, editors: this.editors, readonly: this.readonly, range: this.range, useClipboard: this.useClipboard, applyChangesOnClose: this.applyOnClose, additionalData: this.additionalData, slot: data.slot, onBeforenextvpfocus: (e) => { var _a; return (_a = this.selectionStoreConnector) === null || _a === void 0 ? void 0 : _a.beforeNextFocusCell(e.detail); }, onCanceledit: () => { var _a; return (_a = this.selectionStoreConnector) === null || _a === void 0 ? void 0 : _a.setEdit(false); }, onSetedit: ({ detail }) => {
                        var _a;
                        const event = this.beforeeditstart.emit(detail);
                        if (!event.defaultPrevented) {
                            (_a = this.selectionStoreConnector) === null || _a === void 0 ? void 0 : _a.setEdit(detail.val);
                        }
                    } }), h("revogr-data", Object.assign({}, data, { colType: view.type, key: key, readonly: this.readonly, range: this.range, rowClass: this.rowClass, rowSelectionStore: data.rowSelectionStore, additionalData: this.additionalData, jobsBeforeRender: this.jobsBeforeRender, slot: DATA_SLOT }), h("slot", { name: `data-${view.type}-${data.type}` })), h("revogr-temp-range", { selectionStore: data.selectionStore, dimensionRow: data.dimensionRow, dimensionCol: data.dimensionCol }), h("revogr-focus", { colData: data.colData, dataStore: data.dataStore, focusTemplate: this.focusTemplate, rowType: data.type, colType: view.type, selectionStore: data.selectionStore, dimensionRow: data.dimensionRow, dimensionCol: data.dimensionCol }, h("slot", { name: `focus-${view.type}-${data.type}` }))));
                dataViews.push(dataView);
            });
            // Add viewport scroll in the end
            viewPortHtml.push(h("revogr-viewport-scroll", Object.assign({}, view.prop, { ref: el => this.scrollingService.registerElement(el, `${view.prop.key}`), onScrollviewport: e => this.scrollingService.proxyScroll(e.detail, `${view.prop.key}`), onScrollviewportsilent: e => this.scrollingService.scrollSilentService(e.detail, `${view.prop.key}`) }), dataViews));
        }
        viewportSections.push(viewPortHtml);
        // #endregion
        const typeRow = 'rgRow';
        const typeCol = 'rgCol';
        const viewports = this.viewportProvider.stores;
        const dimensions = this.dimensionProvider.stores;
        const verticalScroll = (h("revogr-scroll-virtual", { class: "vertical", dimension: typeRow, clientSize: viewports[typeRow].store.get('clientSize'), virtualSize: viewports[typeRow].store.get('virtualSize'), realSize: dimensions[typeRow].store.get('realSize'), ref: el => this.scrollingService.registerElement(el, 'rowScroll'), onScrollvirtual: e => this.scrollingService.proxyScroll(e.detail) }));
        const horizontalScroll = (h("revogr-scroll-virtual", { class: "horizontal", dimension: typeCol, clientSize: viewports[typeCol].store.get('clientSize'), virtualSize: viewports[typeCol].store.get('virtualSize'), realSize: dimensions[typeCol].store.get('realSize'), ref: el => this.scrollingService.registerElement(el, 'colScroll'), onScrollvirtual: e => this.scrollingService.proxyScroll(e.detail) }));
        return (h(Host, null, this.hideAttribution ? null : (h("revogr-attribution", { class: "attribution" })), h("slot", { name: "header" }), h("div", { class: "main-viewport", onClick: (e) => {
                var _a;
                if (e.currentTarget === e.target) {
                    (_a = this.viewport) === null || _a === void 0 ? void 0 : _a.clearEdit();
                }
            } }, h("div", { class: "viewports" }, h("slot", { name: "viewport" }), viewportSections, verticalScroll, h(OrderRenderer, { ref: e => (this.orderService = e) }))), horizontalScroll, h("revogr-extra", { ref: el => (this.extraService = el), nodes: this.extraElements }), h("slot", { name: "footer" })));
    }
    disconnectedCallback() {
        // Remove all plugins, to avoid memory leaks
        // and unexpected behaviour when the component is removed
        this.removePlugins();
    }
    get element() { return this; }
    static get watchers() { return {
        "columnTypes": ["columnTypesChanged"],
        "columns": ["columnChanged"],
        "disableVirtualX": ["disableVirtualXChanged"],
        "rowSize": ["rowSizeChanged"],
        "theme": ["themeChanged"],
        "source": ["dataSourceChanged"],
        "pinnedBottomSource": ["dataSourceChanged"],
        "pinnedTopSource": ["dataSourceChanged"],
        "disableVirtualY": ["disableVirtualYChanged"],
        "rowDefinitions": ["rowDefChanged"],
        "trimmedRows": ["trimmedRowsChanged"],
        "grouping": ["groupingChanged"],
        "stretch": ["applyStretch"],
        "filter": ["applyFilter"],
        "sorting": ["applySorting"],
        "rowHeaders": ["rowHeadersChange"],
        "registerVNode": ["registerOutsideVNodes"],
        "additionalData": ["additionalDataChanged"],
        "plugins": ["pluginsChanged"]
    }; }
    static get style() { return revoGridStyleCss; }
}, [4, "revo-grid", {
        "rowHeaders": [4, "row-headers"],
        "frameSize": [2, "frame-size"],
        "rowSize": [2, "row-size"],
        "colSize": [2, "col-size"],
        "range": [4],
        "readonly": [4],
        "resize": [4],
        "canFocus": [4, "can-focus"],
        "useClipboard": [4, "use-clipboard"],
        "columns": [16],
        "source": [16],
        "pinnedTopSource": [16, "pinned-top-source"],
        "pinnedBottomSource": [16, "pinned-bottom-source"],
        "rowDefinitions": [16, "row-definitions"],
        "editors": [16],
        "applyOnClose": [4, "apply-on-close"],
        "plugins": [16],
        "columnTypes": [16, "column-types"],
        "theme": [1537],
        "rowClass": [513, "row-class"],
        "autoSizeColumn": [4, "auto-size-column"],
        "filter": [4],
        "sorting": [16],
        "focusTemplate": [16, "focus-template"],
        "canMoveColumns": [4, "can-move-columns"],
        "trimmedRows": [16, "trimmed-rows"],
        "exporting": [4],
        "grouping": [16],
        "stretch": [8],
        "additionalData": [16, "additional-data"],
        "disableVirtualX": [4, "disable-virtual-x"],
        "disableVirtualY": [4, "disable-virtual-y"],
        "hideAttribution": [4, "hide-attribution"],
        "jobsBeforeRender": [16, "jobs-before-render"],
        "registerVNode": [16, "register-v-node"],
        "accessible": [4],
        "canDrag": [4, "can-drag"],
        "refresh": [64],
        "setDataAt": [64],
        "scrollToRow": [64],
        "scrollToColumnIndex": [64],
        "scrollToColumnProp": [64],
        "updateColumns": [64],
        "addTrimmed": [64],
        "scrollToCoordinate": [64],
        "setCellEdit": [64],
        "setCellsFocus": [64],
        "getSource": [64],
        "getVisibleSource": [64],
        "getSourceStore": [64],
        "getColumnStore": [64],
        "updateColumnSorting": [64],
        "clearSorting": [64],
        "getColumns": [64],
        "clearFocus": [64],
        "getPlugins": [64],
        "getFocused": [64],
        "getContentSize": [64],
        "getSelectedRange": [64],
        "refreshExtraElements": [64],
        "getProviders": [64]
    }, [[5, "touchstart", "mousedownHandle"], [5, "mousedown", "mousedownHandle"], [5, "touchend", "mouseupHandle"], [5, "mouseup", "mouseupHandle"], [0, "rowdragstartinit", "onRowDragStarted"], [0, "rowdragendinit", "onRowDragEnd"], [0, "roworderchange", "onRowOrderChange"], [0, "rowdragmoveinit", "onRowDrag"], [0, "rowdragmousemove", "onRowMouseMove"], [0, "celleditapply", "onCellEdit"], [0, "rangeeditapply", "onRangeEdit"], [0, "selectionchangeinit", "onRangeChanged"], [0, "rowdropinit", "onRowDropped"], [0, "beforeheaderclick", "onHeaderClick"], [0, "beforecellfocusinit", "onCellFocus"]], {
        "columnTypes": ["columnTypesChanged"],
        "columns": ["columnChanged"],
        "disableVirtualX": ["disableVirtualXChanged"],
        "rowSize": ["rowSizeChanged"],
        "theme": ["themeChanged"],
        "source": ["dataSourceChanged"],
        "pinnedBottomSource": ["dataSourceChanged"],
        "pinnedTopSource": ["dataSourceChanged"],
        "disableVirtualY": ["disableVirtualYChanged"],
        "rowDefinitions": ["rowDefChanged"],
        "trimmedRows": ["trimmedRowsChanged"],
        "grouping": ["groupingChanged"],
        "stretch": ["applyStretch"],
        "filter": ["applyFilter"],
        "sorting": ["applySorting"],
        "rowHeaders": ["rowHeadersChange"],
        "registerVNode": ["registerOutsideVNodes"],
        "additionalData": ["additionalDataChanged"],
        "plugins": ["pluginsChanged"]
    }]);
function defineCustomElement$1() {
    if (typeof customElements === "undefined") {
        return;
    }
    const components = ["revo-grid", "revogr-attribution", "revogr-clipboard", "revogr-data", "revogr-edit", "revogr-extra", "revogr-focus", "revogr-header", "revogr-order-editor", "revogr-overlay-selection", "revogr-row-headers", "revogr-scroll-virtual", "revogr-temp-range", "revogr-viewport-scroll", "vnode-html"];
    components.forEach(tagName => { switch (tagName) {
        case "revo-grid":
            if (!customElements.get(tagName)) {
                customElements.define(tagName, RevoGridComponent);
            }
            break;
        case "revogr-attribution":
            if (!customElements.get(tagName)) {
                defineCustomElement$f();
            }
            break;
        case "revogr-clipboard":
            if (!customElements.get(tagName)) {
                defineCustomElement$e();
            }
            break;
        case "revogr-data":
            if (!customElements.get(tagName)) {
                defineCustomElement$d();
            }
            break;
        case "revogr-edit":
            if (!customElements.get(tagName)) {
                defineCustomElement$c();
            }
            break;
        case "revogr-extra":
            if (!customElements.get(tagName)) {
                defineCustomElement$b();
            }
            break;
        case "revogr-focus":
            if (!customElements.get(tagName)) {
                defineCustomElement$a();
            }
            break;
        case "revogr-header":
            if (!customElements.get(tagName)) {
                defineCustomElement$9();
            }
            break;
        case "revogr-order-editor":
            if (!customElements.get(tagName)) {
                defineCustomElement$8();
            }
            break;
        case "revogr-overlay-selection":
            if (!customElements.get(tagName)) {
                defineCustomElement$7();
            }
            break;
        case "revogr-row-headers":
            if (!customElements.get(tagName)) {
                defineCustomElement$6();
            }
            break;
        case "revogr-scroll-virtual":
            if (!customElements.get(tagName)) {
                defineCustomElement$5();
            }
            break;
        case "revogr-temp-range":
            if (!customElements.get(tagName)) {
                defineCustomElement$4();
            }
            break;
        case "revogr-viewport-scroll":
            if (!customElements.get(tagName)) {
                defineCustomElement$3();
            }
            break;
        case "vnode-html":
            if (!customElements.get(tagName)) {
                defineCustomElement$2();
            }
            break;
    } });
}

const RevoGrid = RevoGridComponent;
const defineCustomElement = defineCustomElement$1;

export { AutoSizeColumnPlugin as A, BasePlugin as B, ColumnMovePlugin as C, DimensionStore as D, ExportFilePlugin as E, FILTER_TRIMMED_TYPE as F, GroupingRowPlugin as G, RevoGrid, StretchColumn as S, ExportCsv as a, FILTER_CONFIG_CHANGED_EVENT as b, FILTE_PANEL as c, FilterPlugin as d, defineCustomElement, filterTypes as e, filterCoreFunctionsIndexedByType as f, getTheme as g, filterNames as h, isStretchPlugin as i, doCollapse as j, doExpand as k, getLeftRelative as l, SortingPlugin as m, defaultCellCompare as n, descCellCompare as o, getNextOrder as p, getComparer as q, sortIndexByItems as s };
//# sourceMappingURL=revo-grid.js.map

//# sourceMappingURL=revo-grid.js.map