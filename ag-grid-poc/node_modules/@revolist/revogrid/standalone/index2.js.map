{"file": "index2.js", "mappings": ";;;;;;AASA;AACG;SACa,KAAK,CAAC,IAAY,EAAE,OAAO,GAAG,CAAC,EAAA;IAC7C,MAAM,GAAG,GAAa,EAAE;AACxB,IAAA,MAAM,GAAG,GAAG,OAAO,GAAG,IAAI;AAC1B,IAAA,KAAK,IAAI,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClC,QAAA,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;;AAEb,IAAA,OAAO,GAAG;AACZ;AAEA;AACgB,SAAA,mBAAmB,CAAe,EAAK,EAAE,SAAoC,EAAA;IAC3F,OAAO,CAAC,UAAU,GAAG,EAAA;QACnB,IAAI,CAAC,GAAG,CAAC;AACT,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC;AAEtB,QAAA,OAAO,CAAC,IAAI,CAAC,EAAE;YACb,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;YACtB,MAAM,GAAG,GAAG,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAEjC,YAAA,IAAI,GAAG,GAAG,CAAC,EAAE;AACX,gBAAA,CAAC,GAAG,CAAC,GAAG,CAAC;;AACJ,iBAAA,IAAI,GAAG,GAAG,CAAC,EAAE;AAClB,gBAAA,CAAC,GAAG,CAAC,GAAG,CAAC;;iBACJ;AACL,gBAAA,OAAO,CAAC;;;AAIZ,QAAA,OAAO,CAAC,CAAC,GAAG,CAAC;AACf,KAAC,EAAE,IAAI,CAAC;AACV;AAEA;;AAEG;SACa,UAAU,CAAI,GAAQ,EAAE,EAAK,EAAE,EAA6B,EAAA;AAC1E,IAAA,GAAG,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;AACxD,IAAA,OAAO,GAAG;AACZ;AAEA;AACA,SAAS,aAAa,CAAI,GAAM,EAAE,GAAM,EAAA;IACtC,OAAO,GAAG,GAAG,GAAG;AAClB;AAEA;;AAEG;AACG,SAAU,gBAAgB,CAAI,IAAS,EAAE,IAAS,EAAE,YAAwC,aAAa,EAAA;IAC7G,MAAM,MAAM,GAAQ,EAAE;IACtB,IAAI,MAAM,GAAG,CAAC;IACd,IAAI,MAAM,GAAG,CAAC;IACd,IAAI,OAAO,GAAG,CAAC;IAEf,OAAO,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;AAC1C,QAAA,IAAI,cAAc,GAAG,MAAM,IAAI,IAAI,CAAC,MAAM;AAC1C,QAAA,IAAI,cAAc,GAAG,MAAM,IAAI,IAAI,CAAC,MAAM;QAE1C,IAAI,CAAC,cAAc,KAAK,cAAc,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;YAChF,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AAC9B,YAAA,MAAM,EAAE;;aACH;YACL,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AAC9B,YAAA,MAAM,EAAE;;AAGV,QAAA,OAAO,EAAE;;AAGX,IAAA,OAAO,MAAM;AACf;AAEA;;AAEG;AACG,SAAU,gBAAgB,CAAC,QAAkB,EAAA;;IAEjD,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;;AAG/C,IAAA,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ;IACnC,SAAS,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AACtC,IAAA,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU;IACrC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC;IAChC,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;IAC/B,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;;AAGhC,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;;IAGpC,MAAM,cAAc,GAAG,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW;;AAGpE,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;;AAGpC,IAAA,OAAO,cAAc;AACvB;AAEA;;;;;;;;;;;AAWG;SACa,UAAU,CAAC,KAAa,EAAE,IAAsB,EAAE,EAAoB,EAAA;AACpF,IAAA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC5E;AAEA;;AAEG;AACI,eAAe,OAAO,CAAC,KAAK,GAAG,CAAC,EAAA;AACrC,IAAA,MAAM,IAAI,OAAO,CAAC,CAAC,CAAoB,KAAI;QACzC,UAAU,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC;AAC9B,KAAC,CAAC;AACJ;AAEA;;AAEG;AACa,SAAA,WAAW,CAAC,WAAgB,EAAE,YAAmB,EAAA;AAC/D,IAAA,YAAY,CAAC,OAAO,CAAC,QAAQ,IAAG;AAC9B,QAAA,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,IAAG;YAC5D,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,wBAAwB,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtI,SAAC,CAAC;AACJ,KAAC,CAAC;AACJ;;;;", "names": [], "sources": ["src/utils/index.ts"], "sourcesContent": ["export * from './store.utils';\nexport * from './store.types';\nexport * from './column.utils';\nexport * from './consts';\nexport * from './key.utils';\nexport * from './key.codes';\nexport * from './row-header-utils';\n\n\n/* Generate range on size\n */\nexport function range(size: number, startAt = 0): number[] {\n  const res: number[] = [];\n  const end = startAt + size;\n  for (let i = startAt; i < end; i++) {\n    res.push(i);\n  }\n  return res;\n}\n\n/* Find index position in array */\nexport function findPositionInArray<T>(this: T[], el: T, compareFn: (el: T, el2: T) => number): number {\n  return (function (arr): number {\n    let m = 0;\n    let n = arr.length - 1;\n\n    while (m <= n) {\n      const k = (n + m) >> 1;\n      const cmp = compareFn(el, arr[k]);\n\n      if (cmp > 0) {\n        m = k + 1;\n      } else if (cmp < 0) {\n        n = k - 1;\n      } else {\n        return k;\n      }\n    }\n\n    return -m - 1;\n  })(this);\n}\n\n/**\n * Sorted push\n */\nexport function pushSorted<T>(arr: T[], el: T, fn: (el: T, el2: T) => number): T[] {\n  arr.splice(findPositionInArray.bind(arr)(el, fn), 0, el);\n  return arr;\n}\n\n// (arr1[index1] < arr2[index2])\nfunction simpleCompare<T>(el1: T, el2: T): boolean {\n  return el1 < el2;\n}\n\n/**\n * Merge sorted array helper function\n */\nexport function mergeSortedArray<T>(arr1: T[], arr2: T[], compareFn: (el: T, el2: T) => boolean = simpleCompare): T[] {\n  const merged: T[] = [];\n  let index1 = 0;\n  let index2 = 0;\n  let current = 0;\n\n  while (current < arr1.length + arr2.length) {\n    let isArr1Depleted = index1 >= arr1.length;\n    let isArr2Depleted = index2 >= arr2.length;\n\n    if (!isArr1Depleted && (isArr2Depleted || compareFn(arr1[index1], arr2[index2]))) {\n      merged[current] = arr1[index1];\n      index1++;\n    } else {\n      merged[current] = arr2[index2];\n      index2++;\n    }\n\n    current++;\n  }\n\n  return merged;\n}\n\n/**\n * Calculate system scrollbar size\n */\nexport function getScrollbarSize(document: Document): number {\n  // Create a temporary div container and append it to the body\n  const container = document.createElement('div');\n\n  // Apply styling to ensure the div is scrollable\n  container.style.overflow = 'scroll';\n  container.style.visibility = 'hidden'; // make sure the container isn't visible\n  container.style.position = 'absolute';\n  container.style.top = '-9999px'; // move it out of the screen\n  container.style.width = '50px'; // arbitrary width\n  container.style.height = '50px'; // arbitrary height\n\n  // Append the div to the body\n  document.body.appendChild(container);\n\n  // Calculate the width of the scrollbar\n  const scrollbarWidth = container.offsetWidth - container.clientWidth;\n\n  // Remove the div from the body after calculation\n  document.body.removeChild(container);\n\n  // Return the calculated width of the scrollbar\n  return scrollbarWidth;\n}\n\n/* Scale a value between 2 ranges\n *\n * Sample:\n * // 55 from a 0-100 range to a 0-1000 range (Ranges don't have to be positive)\n * const n = scaleValue(55, [0,100], [0,1000]);\n *\n * Ranges of two values\n * @from\n * @to\n *\n * ~~ return value does the equivalent of Math.floor but faster.\n */\nexport function scaleValue(value: number, from: [number, number], to: [number, number]): number {\n  return ((to[1] - to[0]) * (value - from[0])) / (from[1] - from[0]) + to[0];\n}\n\n/**\n * Async timeout\n */\nexport async function timeout(delay = 0): Promise<void> {\n  await new Promise((r: (v?: any) => void) => {\n    setTimeout(() => r(), delay);\n  });\n}\n\n/**\n * Type script mixins\n */\nexport function applyMixins(derivedCtor: any, constructors: any[]) {\n  constructors.forEach(baseCtor => {\n    Object.getOwnPropertyNames(baseCtor.prototype).forEach(name => {\n      Object.defineProperty(derivedCtor.prototype, name, Object.getOwnPropertyDescriptor(baseCtor.prototype, name) || Object.create(null));\n    });\n  });\n}\n"], "version": 3}