/*!
 * Built by Revolist OU ❤️
 */
import { setMode } from '@stencil/core/internal/client';
export { getAssetPath, h, setAssetPath, setNonce, setPlatformOptions } from '@stencil/core/internal/client';
import { g as getTheme, B as BasePlugin } from './revo-grid.js';
export { A as AutoSizeColumnPlugin, C as ColumnMovePlugin, D as DimensionStore, a as ExportCsv, E as ExportFilePlugin, b as FILTER_CONFIG_CHANGED_EVENT, F as FILTER_TRIMMED_TYPE, c as FILTE_PANEL, d as FilterPlugin, G as GroupingRowPlugin, RevoGrid, m as SortingPlugin, S as StretchColumn, n as defaultCellCompare, defineCustomElement as defineCustomElementRevoGrid, o as descCellCompare, j as doCollapse, k as doExpand, f as filterCoreFunctionsIndexedByType, h as filterNames, e as filterTypes, q as getComparer, l as getLeftRelative, p as getNextOrder, i as isStretchPlugin, s as sortIndexByItems } from './revo-grid.js';
export { p as GROUPING_ROW_TYPE, k as GROUP_COLUMN_PROP, G as GROUP_DEPTH, j as GROUP_EXPANDED, m as GROUP_EXPAND_BTN, o as GROUP_EXPAND_EVENT, l as GROUP_ORIGINAL_INDEX, h as PSEUDO_GROUP_COLUMN, P as PSEUDO_GROUP_ITEM, e as PSEUDO_GROUP_ITEM_ID, f as PSEUDO_GROUP_ITEM_VALUE, S as SelectionStore, c as columnTypes, b as cropCellToMax, I as gatherGroup, t as gatherGrouping, A as getCellData, C as getCellDataParsed, B as getCellRaw, J as getColumnByProp, E as getColumnSizes, D as getColumnType, H as getColumns, s as getExpanded, u as getGroupingName, y as getParsedGroup, g as getRange, q as getSource, F as isColGrouping, v as isGrouping, w as isGroupingColumn, a as isHiddenStore, d as isRangeSingleCell, i as isRowType, z as isSameGroup, x as measureEqualDepth, n as nextCell, r as rowTypes } from './column.service.js';
export { S as SortingSign, d as dispatch, a as dispatchByEvent } from './revogr-header2.js';
export { a as applyMixins, f as findPositionInArray, g as getScrollbarSize, m as mergeSortedArray, p as pushSorted, r as range, s as scaleValue, t as timeout } from './index2.js';
export { C as CellRenderer, G as GroupingRowRenderer, e as expandEvent, a as expandSvgIconVNode } from './revogr-data2.js';
export { T as TextEditor, k as isAll, c as isClear, h as isCopy, a as isCtrlKey, b as isCtrlMetaKey, g as isCut, l as isEditInput, m as isEditorCtrConstructible, f as isEnterKeyValue, i as isMetaKey, j as isPaste, d as isTab, e as isTabKeyValue } from './revogr-edit2.js';
export { RevogrAttribution, defineCustomElement as defineCustomElementRevogrAttribution } from './revogr-attribution.js';
export { RevogrClipboard, defineCustomElement as defineCustomElementRevogrClipboard } from './revogr-clipboard.js';
export { RevogrData, defineCustomElement as defineCustomElementRevogrData } from './revogr-data.js';
export { RevogrEdit, defineCustomElement as defineCustomElementRevogrEdit } from './revogr-edit.js';
export { RevogrExtra, defineCustomElement as defineCustomElementRevogrExtra } from './revogr-extra.js';
export { RevogrFilterPanel, defineCustomElement as defineCustomElementRevogrFilterPanel } from './revogr-filter-panel.js';
export { RevogrFocus, defineCustomElement as defineCustomElementRevogrFocus } from './revogr-focus.js';
export { RevogrHeader, defineCustomElement as defineCustomElementRevogrHeader } from './revogr-header.js';
export { RevogrOrderEditor, defineCustomElement as defineCustomElementRevogrOrderEditor } from './revogr-order-editor.js';
export { RevogrOverlaySelection, defineCustomElement as defineCustomElementRevogrOverlaySelection } from './revogr-overlay-selection.js';
export { RevogrRowHeaders, defineCustomElement as defineCustomElementRevogrRowHeaders } from './revogr-row-headers.js';
export { RevogrScrollVirtual, defineCustomElement as defineCustomElementRevogrScrollVirtual } from './revogr-scroll-virtual.js';
export { RevogrTempRange, defineCustomElement as defineCustomElementRevogrTempRange } from './revogr-temp-range.js';
export { RevogrViewportScroll, defineCustomElement as defineCustomElementRevogrViewportScroll } from './revogr-viewport-scroll.js';
export { VnodeHtml, defineCustomElement as defineCustomElementVnodeHtml } from './vnode-html.js';
export { D as DataStore, h as gatherTrimmedItems, g as getPhysical, b as getSourceItem, f as getSourceItemVirtualIndexByProp, c as getSourcePhysicalIndex, a as getVisibleSourceItem, p as proxyPlugin, e as setItems, d as setSourceByPhysicalIndex, s as setSourceByVirtualIndex, i as setStore, t as trimmedPlugin } from './data.store.js';
export { c as calculateDimensionData, a as getItemByIndex, g as getItemByPosition } from './dimension.helpers.js';
export { V as ViewportStore, a as addMissingItems, f as calculateRowHeaderSize, d as getFirstItem, b as getItems, e as getLastItem, g as getUpdatedItemsByPosition, i as isActiveRange, c as isActiveRangeOutsideLastItem, r as recombineByOffset, s as setItemSizes, u as updateMissingAndRange } from './revogr-row-headers2.js';
export { A as AND_OR_BUTTON, e as AndOrButton, a as FILTER_BUTTON_ACTIVE, F as FILTER_BUTTON_CLASS, b as FILTER_PROP, c as FilterButton, T as TRASH_BUTTON, d as TrashButton, i as isFilterBtn } from './filter.button.js';
export { C as CELL_CLASS, j as CELL_HANDLER_CLASS, D as DATA_COL, a as DATA_ROW, b as DISABLED_CLASS, h as DRAGGABLE_CLASS, k as DRAGG_TEXT, g as DRAG_ICON_CLASS, E as EDIT_INPUT_WR, F as FOCUS_CLASS, G as GRID_INTERNALS, f as HEADER_ACTUAL_ROW_CLASS, H as HEADER_CLASS, e as HEADER_ROW_CLASS, d as HEADER_SORTABLE_CLASS, M as MIN_COL_SIZE, i as MOBILE_CLASS, R as RESIZE_INTERVAL, l as ROW_FOCUSED_CLASS, c as ROW_HEADER_TYPE, S as SELECTION_BORDER_CLASS, T as TMP_SELECTION_BG_CLASS } from './consts.js';
export { c as codesLetter, k as keyValues } from './platform.js';

setMode(elm => {
    let theme = elm.theme || elm.getAttribute('theme');
    if (typeof theme === 'string') {
        theme = theme.trim();
    }
    const parsedTheme = getTheme(theme);
    if (parsedTheme !== theme) {
        elm.setAttribute('theme', parsedTheme);
    }
    return parsedTheme;
});

const REVOGRID_EVENTS = new Map([
    ['contentsizechanged', 'contentsizechanged'],
    ['beforeedit', 'beforeedit'],
    ['beforerangeedit', 'beforerangeedit'],
    ['afteredit', 'afteredit'],
    ['beforeautofill', 'beforeautofill'],
    ['beforerange', 'beforerange'],
    ['afterfocus', 'afterfocus'],
    ['roworderchanged', 'roworderchanged'],
    ['beforesorting', 'beforesorting'],
    ['beforesourcesortingapply', 'beforesourcesortingapply'],
    ['beforesortingapply', 'beforesortingapply'],
    ['rowdragstart', 'rowdragstart'],
    ['headerclick', 'headerclick'],
    ['beforecellfocus', 'beforecellfocus'],
    ['beforefocuslost', 'beforefocuslost'],
    ['beforesourceset', 'beforesourceset'],
    ['beforeanysource', 'beforeanysource'],
    ['aftersourceset', 'aftersourceset'],
    ['afteranysource', 'afteranysource'],
    ['beforecolumnsset', 'beforecolumnsset'],
    ['beforecolumnapplied', 'beforecolumnapplied'],
    ['aftercolumnsset', 'aftercolumnsset'],
    ['beforefilterapply', 'beforefilterapply'],
    ['beforefiltertrimmed', 'beforefiltertrimmed'],
    ['beforetrimmed', 'beforetrimmed'],
    ['aftertrimmed', 'aftertrimmed'],
    ['viewportscroll', 'viewportscroll'],
    ['beforeexport', 'beforeexport'],
    ['beforeeditstart', 'beforeeditstart'],
    ['aftercolumnresize', 'aftercolumnresize'],
    ['beforerowdefinition', 'beforerowdefinition'],
    ['filterconfigchanged', 'filterconfigchanged'],
    ['sortingconfigchanged', 'sortingconfigchanged'],
    ['rowheaderschanged', 'rowheaderschanged'],
    ['beforegridrender', 'beforegridrender'],
    ['aftergridrender', 'aftergridrender'],
    ['aftergridinit', 'aftergridinit'],
    ['additionaldatachanged', 'additionaldatachanged'],
    ['afterthemechanged', 'afterthemechanged'],
    ['created', 'created'],
    ['beforepaste', 'beforepaste'],
    ['beforepasteapply', 'beforepasteapply'],
    ['pasteregion', 'pasteregion'],
    ['afterpasteapply', 'afterpasteapply'],
    ['beforecut', 'beforecut'],
    ['clearregion', 'clearregion'],
    ['beforecopy', 'beforecopy'],
    ['beforecopyapply', 'beforecopyapply'],
    ['copyregion', 'copyregion'],
    ['beforerowrender', 'beforerowrender'],
    ['afterrender', 'afterrender'],
    ['beforecellrender', 'beforecellrender'],
    ['beforedatarender', 'beforedatarender'],
    ['dragstartcell', 'dragstartcell'],
    ['celleditinit', 'celleditinit'],
    ['closeedit', 'closeedit'],
    ['filterChange', 'filterChange'],
    ['resetChange', 'resetChange'],
    ['beforefocusrender', 'beforefocusrender'],
    ['beforescrollintoview', 'beforescrollintoview'],
    ['afterfocus', 'afterfocus'],
    ['beforeheaderclick', 'beforeheaderclick'],
    ['headerresize', 'headerresize'],
    ['beforeheaderresize', 'beforeheaderresize'],
    ['headerdblclick', 'headerdblclick'],
    ['beforeheaderrender', 'beforeheaderrender'],
    ['beforegroupheaderrender', 'beforegroupheaderrender'],
    ['afterheaderrender', 'afterheaderrender'],
    ['rowdragstartinit', 'rowdragstartinit'],
    ['rowdragendinit', 'rowdragendinit'],
    ['rowdragmoveinit', 'rowdragmoveinit'],
    ['rowdragmousemove', 'rowdragmousemove'],
    ['rowdropinit', 'rowdropinit'],
    ['roworderchange', 'roworderchange'],
    ['beforecopyregion', 'beforecopyregion'],
    ['beforepasteregion', 'beforepasteregion'],
    ['celleditapply', 'celleditapply'],
    ['beforecellfocusinit', 'beforecellfocusinit'],
    ['beforenextvpfocus', 'beforenextvpfocus'],
    ['setedit', 'setedit'],
    ['beforeapplyrange', 'beforeapplyrange'],
    ['beforesetrange', 'beforesetrange'],
    ['setrange', 'setrange'],
    ['beforeeditrender', 'beforeeditrender'],
    ['selectall', 'selectall'],
    ['canceledit', 'canceledit'],
    ['settemprange', 'settemprange'],
    ['beforesettemprange', 'beforesettemprange'],
    ['applyfocus', 'applyfocus'],
    ['focuscell', 'focuscell'],
    ['beforerangedataapply', 'beforerangedataapply'],
    ['selectionchangeinit', 'selectionchangeinit'],
    ['beforerangecopyapply', 'beforerangecopyapply'],
    ['rangeeditapply', 'rangeeditapply'],
    ['clipboardrangecopy', 'clipboardrangecopy'],
    ['clipboardrangepaste', 'clipboardrangepaste'],
    ['beforekeydown', 'beforekeydown'],
    ['beforekeyup', 'beforekeyup'],
    ['beforecellsave', 'beforecellsave'],
    ['celledit', 'celledit'],
    ['scrollview', 'scrollview'],
    ['ref', 'ref'],
    ['scrollvirtual', 'scrollvirtual'],
    ['scrollviewport', 'scrollviewport'],
    ['resizeviewport', 'resizeviewport'],
    ['scrollchange', 'scrollchange'],
    ['scrollviewportsilent', 'scrollviewportsilent'],
    ['html', 'html']
]);

/**
 * Automatically adds new rows when pasted data is larger than current rows
 * @event newRows - is triggered when new rows are added. Data of new rows can be filled with default values. If the event is prevented, no rows will be added
 */
class AutoAddRowsPlugin extends BasePlugin {
    constructor(revogrid, providers) {
        super(revogrid, providers);
        this.addEventListener('beforepasteapply', evt => this.handleBeforePasteApply(evt));
    }
    handleBeforePasteApply(event) {
        const start = this.providers.selection.focused;
        const isEditing = this.providers.selection.edit != null;
        if (!start || isEditing) {
            return;
        }
        const rowLength = this.providers.data.stores.rgRow.store.get('items').length;
        const endRow = start.y + event.detail.parsed.length;
        if (rowLength < endRow) {
            const count = endRow - rowLength;
            const newRows = Array.from({ length: count }, (_, i) => ({
                index: rowLength + i,
                data: {},
            }));
            const event = this.emit('newRows', { newRows: newRows });
            if (event.defaultPrevented) {
                return;
            }
            const items = [
                ...this.providers.data.stores.rgRow.store.get('source'),
                ...event.detail.newRows.map(j => j.data),
            ];
            this.providers.data.setData(items);
        }
    }
}

export { AutoAddRowsPlugin, BasePlugin, REVOGRID_EVENTS };
//# sourceMappingURL=index.js.map

//# sourceMappingURL=index.js.map