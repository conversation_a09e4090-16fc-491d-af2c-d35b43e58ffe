/*!
 * Built by Revolist OU ❤️
 */
import { proxyCustomElement, HTMLElement, h, Host } from '@stencil/core/internal/client';

const Attribution = /*@__PURE__*/ proxyCustomElement(class Attribution extends HTMLElement {
    constructor() {
        super();
        this.__registerHost();
    }
    render() {
        return (h(Host, { key: '3d66475a019010c24b6c610ccc047e01c35178f9' }, h("a", { key: '777afddffef0309a697b9c14ee73c0001ac22b71', href: "https://rv-grid.com/guide/attribution", target: "_blank", rel: "noopener noreferrer", title: "Made with \u2764\uFE0F by Revolist OU Team", class: "value" }, "RevoGrid")));
    }
}, [0, "revogr-attribution"]);
function defineCustomElement() {
    if (typeof customElements === "undefined") {
        return;
    }
    const components = ["revogr-attribution"];
    components.forEach(tagName => { switch (tagName) {
        case "revogr-attribution":
            if (!customElements.get(tagName)) {
                customElements.define(tagName, Attribution);
            }
            break;
    } });
}

export { Attribution as A, defineCustomElement as d };
//# sourceMappingURL=revogr-attribution2.js.map

//# sourceMappingURL=revogr-attribution2.js.map