{"file": "revogr-order-editor2.js", "mappings": ";;;;;;;;;;AAQc,MAAO,eAAe,CAAA;AAIlC,IAAA,WAAA,CAAoB,MAAc,EAAA;QAAd,IAAM,CAAA,MAAA,GAAN,MAAM;QAHlB,IAAW,CAAA,WAAA,GAAgB,IAAI;QAC/B,IAAW,CAAA,WAAA,GAAkB,IAAI;;;IAKzC,QAAQ,CAAC,CAAa,EAAE,IAAe,EAAA;AACrC,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE;YAC7B;;QAEF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;;QAGpC,IAAI,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE;;AAEnC,YAAA,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;AAChB,gBAAA,MAAM,CAAC,CAAC,GAAG,CAAC;;;iBAGT,IAAI,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE;gBACtC,MAAM,CAAC,CAAC,EAAE;;AAEZ,YAAA,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;QAE3D,IAAI,CAAC,KAAK,EAAE;;;IAId,UAAU,CAAC,CAAa,EAAE,IAAe,EAAA;QACvC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;QACxC,OAAO,IAAI,CAAC,WAAW;;IAGzB,IAAI,CAAC,CAAS,EAAE,IAAe,EAAA;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;;AAElC,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,GAAG,EAAE,EAAE;AAChE,YAAA,OAAO,IAAI;;AAEb,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,SAAS;AAClC,QAAA,OAAO,KAAK;;;IAId,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;AACvB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;;AAIzB,IAAA,MAAM,CAAC,CAAS,EAAE,EAAE,EAAE,EAAE,IAAI,EAAa,EAAA;QACvC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,qBAAqB,EAAE;AAC1C,QAAA,MAAM,WAAW,GAAG,CAAC,GAAG,GAAG;QAC3B,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,EAAE,WAAW,CAAC;AAClD,QAAA,MAAM,gBAAgB,GAAG;YACvB,SAAS,EAAE,KAAK,CAAC,SAAS;AAC1B,YAAA,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,GAAG;AACxB,YAAA,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG;SACrB;AACD,QAAA,OAAO,gBAAgB;;;AAIzB,IAAA,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAQ,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAa,EAAA;QACnD,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,qBAAqB,EAAE;AAChD,QAAA,MAAM,WAAW,GAAG,CAAC,GAAG,GAAG;AAC3B,QAAA,MAAM,YAAY,GAAG,CAAC,GAAG,IAAI;QAC7B,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,EAAE,WAAW,CAAC;QAClD,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,EAAE,YAAY,CAAC;AACnD,QAAA,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,EAAE;;AAEpD;;MC3DY,WAAW,iBAAAA,kBAAA,CAAA,MAAA,WAAA,SAAA,WAAA,CAAA;AADxB,IAAA,WAAA,GAAA;;;;;;;;;AAqDU,QAAA,IAAM,CAAA,MAAA,GAGR,EAAE;QACA,IAAA,CAAA,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAS,KAAI;AAC3C,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AAC1D,YAAA,IAAI,KAAK,KAAK,IAAI,EAAE;gBAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CACZ,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAK,CACR,EAAA,EAAA,OAAO,EAAE,IAAI,CAAC,OAAO,IACrB;;SAEL,EAAE,CAAC,CAAC;AA4FN;;;IAxFW,MAAM,SAAS,CAAC,CAAiB,EAAA;AACzC,QAAA,CAAC,CAAC,aAAa,CAAC,cAAc,EAAE;;AAGhC,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACtB,IAAI,CAAC,UAAU,EAAE;;AAGnB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE;AAC3B,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC;AACnE,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC;AAChE,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YAC5C,IAAI;AACJ,YAAA,IAAI,EAAE,UAAU;YAChB,GAAG;YACH,KAAK,EAAE,CAAC,CAAC,aAAa;YACtB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC;AACpD,SAAA,CAAC;AACF,QAAA,IAAI,cAAc,CAAC,gBAAgB,EAAE;YACnC;;AAGF,QAAA,MAAM,QAAQ,GAAG,CAAC,CAAa,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAChD,QAAA,MAAM,OAAO,GAAG,CAAC,CAAa,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE;AAE1C,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACd;AACE,YAAA,IAAI,EAAE,WAAW;AACjB,YAAA,QAAQ,EAAE,QAAQ;SACnB,EACD;AACE,YAAA,IAAI,EAAE,SAAS;AACf,YAAA,QAAQ,EAAE,OAAO;SAClB,EACD;AACE,YAAA,IAAI,EAAE,YAAY;AAClB,YAAA,QAAQ,EAAE,UAAU;AACrB,SAAA,CACF;AACD,QAAA,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC;;AAEhD,QAAA,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC;AAC7C,QAAA,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,CAAC;;IAG3C,MAAM,QAAQ,CAAC,CAAa,EAAA;AACpC,QAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;QAChD,IAAI,CAAC,UAAU,EAAE;;AAGT,IAAA,MAAM,UAAU,GAAA;AACxB,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;QAC5B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC1E,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;;;AAIjD,IAAA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAA4B,EAAA;AACrC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AACvD,QAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;IAGrB,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC;AACzC,YAAA,eAAe,EAAE,CAAC,IAAY,EAAE,EAAU,KAAI;AAC5C,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;oBACrC,IAAI;oBACJ,EAAE;oBACF,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,iBAAA,CAAC;AACF,gBAAA,IAAI,SAAS,CAAC,gBAAgB,EAAE;oBAC9B;;gBAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;aAC3C;AACF,SAAA,CAAC;;IAGI,OAAO,GAAA;QACb,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,MAAM;AACf,YAAA,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK;AAC7B,YAAA,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK;SAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/components/order/order-row.service.ts", "src/components/order/revogr-order-editor.tsx"], "sourcesContent": ["import { getItemByPosition } from '@store';\nimport { DimensionSettingsState, PositionItem, Cell } from '@type';\n\ntype EventData = { el: HTMLElement; rows: DimensionSettingsState; cols: DimensionSettingsState };\ninterface Config {\n  positionChanged(from: number, to: number): void;\n}\n\nexport default class RowOrderService {\n  private currentCell: Cell | null = null;\n  private previousRow: number | null = null;\n\n  constructor(private config: Config) {}\n\n  /** Drag finished, calculate and apply changes */\n  endOrder(e: MouseEvent, data: EventData) {\n    if (this.currentCell === null) {\n      return;\n    }\n    const newRow = this.getCell(e, data);\n\n    // if position changed\n    if (newRow.y !== this.currentCell.y) {\n      // rgRow dragged out table\n      if (newRow.y < 0) {\n        newRow.y = 0;\n      }\n      // rgRow dragged to the top\n      else if (newRow.y < this.currentCell.y) {\n        newRow.y++;\n      }\n      this.config.positionChanged(this.currentCell.y, newRow.y);\n    }\n    this.clear();\n  }\n\n  /** Drag started, reserve initial cell for farther use */\n  startOrder(e: MouseEvent, data: EventData): Cell {\n    this.currentCell = this.getCell(e, data);\n    return this.currentCell;\n  }\n\n  move(y: number, data: EventData): PositionItem | null {\n    const rgRow = this.getRow(y, data);\n    // if rgRow same as previous or below range (-1 = 0) do nothing\n    if (this.previousRow === rgRow.itemIndex || rgRow.itemIndex < -1) {\n      return null;\n    }\n    this.previousRow = rgRow.itemIndex;\n    return rgRow;\n  }\n\n  /** Drag stopped, probably cursor outside of document area */\n  clear() {\n    this.currentCell = null;\n    this.previousRow = null;\n  }\n\n  /** Calculate cell based on x, y position */\n  getRow(y: number, { el, rows }: EventData): PositionItem {\n    const { top } = el.getBoundingClientRect();\n    const topRelative = y - top;\n    const rgRow = getItemByPosition(rows, topRelative);\n    const absolutePosition = {\n      itemIndex: rgRow.itemIndex,\n      start: rgRow.start + top,\n      end: rgRow.end + top,\n    };\n    return absolutePosition;\n  }\n\n  /** Calculate cell based on x, y position */\n  getCell({ x, y }: Cell, { el, rows, cols }: EventData): Cell {\n    const { top, left } = el.getBoundingClientRect();\n    const topRelative = y - top;\n    const leftRelative = x - left;\n    const rgRow = getItemByPosition(rows, topRelative);\n    const rgCol = getItemByPosition(cols, leftRelative);\n    return { x: rgCol.itemIndex, y: rgRow.itemIndex };\n  }\n}\n", "import { Component, Method, Event, EventEmitter, Prop } from '@stencil/core';\nimport debounce from 'lodash/debounce';\n\nimport { type DSourceState, getSourceItem } from '@store';\nimport { DRAGG_TEXT } from '../../utils/consts';\nimport RowOrderService from './order-row.service';\nimport type {\n  DimensionRows,\n  DataType,\n  DimensionSettingsState,\n  DragStartEvent,\n  PositionItem,\n  Cell,\n  RowDragStartDetails,\n} from '@type';\nimport type { Observable } from '../../utils';\n\n/**\n * Component for handling row order editor.\n */\n@Component({ tag: 'revogr-order-editor' })\nexport class OrderEditor {\n  // #region Properties\n  /** Parent element */\n  @Prop() parent: HTMLElement;\n  /** Dimension settings Y */\n  @Prop() dimensionRow: Observable<DimensionSettingsState>;\n  /** Dimension settings X */\n  @Prop() dimensionCol: Observable<DimensionSettingsState>;\n\n  /** Static stores, not expected to change during component lifetime */\n  @Prop() dataStore: Observable<DSourceState<DataType, DimensionRows>>;\n\n  @Prop() rowType: DimensionRows;\n  // #endregion\n\n  // #region Events\n  /** Row drag started */\n  @Event({ eventName: 'rowdragstartinit', cancelable: true })\n  rowDragStart: EventEmitter<RowDragStartDetails>;\n\n  /** Row drag ended started */\n  @Event({ eventName: 'rowdragendinit' })\n  rowDragEnd: EventEmitter<{ rowType: DimensionRows }>;\n\n  /** Row move started */\n  @Event({ eventName: 'rowdragmoveinit', cancelable: true })\n  rowDrag: EventEmitter<PositionItem & { rowType: DimensionRows }>;\n\n  /** Row mouse move started */\n  @Event({ eventName: 'rowdragmousemove', cancelable: true })\n  rowMouseMove: EventEmitter<Cell & { rowType: DimensionRows }>;\n\n  /** Row dragged, new range ready to be applied */\n  @Event({ eventName: 'rowdropinit', cancelable: true })\n  rowDropped: EventEmitter<{\n    from: number;\n    to: number;\n    rowType: DimensionRows;\n  }>;\n\n  /** Row drag ended finished. Time to apply data */\n  @Event({ eventName: 'roworderchange' })\n  rowOrderChange: EventEmitter<{\n    from: number;\n    to: number;\n    rowType: DimensionRows;\n  }>;\n\n  // #endregion\n\n  // #region Private\n  private rowOrderService: RowOrderService;\n  private events: {\n    name: keyof DocumentEventMap;\n    listener: (e: MouseEvent) => void;\n  }[] = [];\n  private rowMoveFunc = debounce((y: number) => {\n    const rgRow = this.rowOrderService.move(y, this.getData());\n    if (rgRow !== null) {\n      this.rowDrag.emit({\n        ...rgRow,\n        rowType: this.rowType,\n      });\n    }\n  }, 5);\n  // #endregion\n\n  // #region Methods\n  @Method() async dragStart(e: DragStartEvent) {\n    e.originalEvent.preventDefault();\n\n    // extra check if previous ended\n    if (this.events.length) {\n      this.clearOrder();\n    }\n\n    const data = this.getData();\n    const cell = this.rowOrderService.startOrder(e.originalEvent, data);\n    const pos = this.rowOrderService.getRow(e.originalEvent.y, data);\n    const dragStartEvent = this.rowDragStart.emit({\n      cell,\n      text: DRAGG_TEXT,\n      pos,\n      event: e.originalEvent,\n      rowType: this.rowType,\n      model: getSourceItem(this.dataStore, pos.itemIndex),\n    });\n    if (dragStartEvent.defaultPrevented) {\n      return;\n    }\n\n    const moveMove = (e: MouseEvent) => this.move(e);\n    const mouseUp = (e: MouseEvent) => this.endOrder(e);\n    const mouseLeave = () => this.clearOrder();\n\n    this.events.push(\n      {\n        name: 'mousemove',\n        listener: moveMove,\n      },\n      {\n        name: 'mouseup',\n        listener: mouseUp,\n      },\n      {\n        name: 'mouseleave',\n        listener: mouseLeave,\n      },\n    );\n    document.addEventListener('mousemove', moveMove);\n    // Action finished inside of the document\n    document.addEventListener('mouseup', mouseUp);\n    document.addEventListener('mouseleave', mouseLeave);\n  }\n\n  @Method() async endOrder(e: MouseEvent) {\n    this.rowOrderService.endOrder(e, this.getData());\n    this.clearOrder();\n  }\n\n  @Method() async clearOrder() {\n    this.rowOrderService.clear();\n    this.events.forEach(v => document.removeEventListener(v.name, v.listener));\n    this.events.length = 0;\n    this.rowDragEnd.emit({ rowType: this.rowType });\n  }\n  // #endregion\n\n  move({ x, y }: { x: number; y: number }) {\n    this.rowMouseMove.emit({ x, y, rowType: this.rowType });\n    this.rowMoveFunc(y);\n  }\n\n  connectedCallback() {\n    this.rowOrderService = new RowOrderService({\n      positionChanged: (from: number, to: number) => {\n        const dropEvent = this.rowDropped.emit({\n          from,\n          to,\n          rowType: this.rowType,\n        });\n        if (dropEvent.defaultPrevented) {\n          return;\n        }\n        this.rowOrderChange.emit(dropEvent.detail);\n      },\n    });\n  }\n\n  private getData() {\n    return {\n      el: this.parent,\n      rows: this.dimensionRow.state,\n      cols: this.dimensionCol.state,\n    };\n  }\n}\n"], "version": 3}