/*!
 * Built by Revolist OU ❤️
 */
import { proxyCustomElement, HTMLElement, h } from '@stencil/core/internal/client';

const RevoGridExtra = /*@__PURE__*/ proxyCustomElement(class RevoGridExtra extends HTMLElement {
    constructor() {
        super();
        this.__registerHost();
        /**
         * Nodes to render
         */
        this.nodes = [];
        /**
         * Force component to re-render
         */
        this.update = 1;
    }
    /**
     * Refreshes the extra component. Useful if you want to manually
     * force the component to re-render.
     */
    async refresh() {
        this.update *= -1;
    }
    render() {
        var _a;
        return (_a = this.nodes) === null || _a === void 0 ? void 0 : _a.map(node => {
            // Check if node is a function or a stencil component
            // If function wrap it in a stencil component with the refresh function
            if (typeof node === 'function') {
                const config = {};
                const getNodes = () => [node({ refresh: () => { var _a; return (_a = config.refresh) === null || _a === void 0 ? void 0 : _a.call(config); } })];
                return (h("revogr-extra", { nodes: getNodes(), ref: (el) => {
                        if (el) {
                            // Update exclusively for current node
                            config.refresh = () => {
                                el.nodes = getNodes();
                            };
                        }
                    } }));
            }
            return node;
        });
    }
}, [0, "revogr-extra", {
        "nodes": [16],
        "update": [32],
        "refresh": [64]
    }]);
function defineCustomElement() {
    if (typeof customElements === "undefined") {
        return;
    }
    const components = ["revogr-extra", "revogr-extra"];
    components.forEach(tagName => { switch (tagName) {
        case "revogr-extra":
            if (!customElements.get(tagName)) {
                customElements.define(tagName, RevoGridExtra);
            }
            break;
        case "revogr-extra":
            if (!customElements.get(tagName)) {
                defineCustomElement();
            }
            break;
    } });
}

export { RevoGridExtra as R, defineCustomElement as d };
//# sourceMappingURL=revogr-extra2.js.map

//# sourceMappingURL=revogr-extra2.js.map