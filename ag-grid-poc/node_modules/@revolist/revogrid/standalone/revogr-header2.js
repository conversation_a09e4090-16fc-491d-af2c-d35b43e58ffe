/*!
 * Built by Revolist OU ❤️
 */
import { h, proxyCustomElement, HTMLElement, createEvent } from '@stencil/core/internal/client';
import { r as getNative, u as baseIteratee, l as isArray } from './data.store.js';
import { U as doPropMerge, L as baseEach } from './column.service.js';
import { M as MIN_COL_SIZE, D as DATA_COL, d as HEADER_SORTABLE_CLASS, H as HEADER_CLASS, F as FOCUS_CLASS, e as HEADER_ROW_CLASS, f as HEADER_ACTUAL_ROW_CLASS } from './consts.js';
import './platform.js';
import { a as getItemByIndex } from './dimension.helpers.js';
import { c as FilterButton } from './filter.button.js';

/**
 * Dispatches a custom event to a specified target element.
 *
 * @param target - The target element to dispatch the event to.
 * @param eventName - The name of the custom event.
 * @param detail - Optional. The detail of the custom event.
 * @returns The custom event that was dispatched.
 */
function dispatch(target, eventName, detail) {
    // Create a new CustomEvent with the specified event name and detail.
    const event = new CustomEvent(eventName, {
        detail,
        cancelable: true, // Indicates whether the event can be canceled.
        bubbles: true, // Indicates whether the event bubbles up through the DOM.
    });
    // Dispatch the event on the target element.
    target === null || target === void 0 ? void 0 : target.dispatchEvent(event);
    // Return the custom event that was dispatched.
    return event;
}
/**
 * Dispatches a custom event based on an existing event object and prevents the default behavior of the original event.
 *
 * @param e - The original event object containing the target and preventDefault method.
 * @param eventName - The name of the custom event.
 * @param detail - Optional. The detail of the custom event.
 * @returns The custom event that was dispatched.
 */
function dispatchByEvent(e, // The original event object containing the target and preventDefault method.
eventName, // The name of the custom event.
detail) {
    // Prevent the default behavior of the original event.
    e.preventDefault();
    // Dispatch the custom event to the target element specified in the original event object.
    return dispatch(e.target, eventName, detail);
}

const SortingSign = ({ column }) => {
    var _a;
    return h("i", { class: (_a = column === null || column === void 0 ? void 0 : column.order) !== null && _a !== void 0 ? _a : 'sort-off' });
};

var ResizeEvents;
(function (ResizeEvents) {
    ResizeEvents["start"] = "resize:start";
    ResizeEvents["move"] = "resize:move";
    ResizeEvents["end"] = "resize:end";
})(ResizeEvents || (ResizeEvents = {}));
const RESIZE_MASK = {
    'resizable-r': { bit: 0b0001, cursor: 'ew-resize' },
    'resizable-rb': { bit: 0b0011, cursor: 'se-resize' },
    'resizable-b': { bit: 0b0010, cursor: 's-resize' },
    'resizable-lb': { bit: 0b0110, cursor: 'sw-resize' },
    'resizable-l': { bit: 0b0100, cursor: 'w-resize' },
    'resizable-lt': { bit: 0b1100, cursor: 'nw-resize' },
    'resizable-t': { bit: 0b1000, cursor: 'n-resize' },
    'resizable-rt': { bit: 0b1001, cursor: 'ne-resize' },
};
const DISABLE_MASK = {
    w: 0b0100,
    h: 0b1000,
};
const defaultProps = (props) => {
    return Object.assign(Object.assign({}, props), { fitParent: props.fitParent || false, active: props.active || [], disableAttributes: props.disableAttributes || [], minWidth: props.minWidth || 0, minHeight: props.minHeight || 0 });
};
class ResizeDirective {
    constructor(initialProps, $event) {
        var _a, _b;
        this.initialProps = initialProps;
        this.$event = $event;
        this.mouseX = 0;
        this.mouseY = 0;
        this.width = 0;
        this.height = 0;
        this.changeX = 0;
        this.changeY = 0;
        this.disableCalcMap = 0b1111;
        this.props = defaultProps(initialProps);
        this.mouseMoveFunc = this.handleMove.bind(this);
        this.mouseUpFunc = this.handleUp.bind(this);
        this.minW = this.props.minWidth;
        this.minH = this.props.minHeight;
        this.maxW = (_a = this.props.maxWidth) !== null && _a !== void 0 ? _a : 0;
        this.maxH = (_b = this.props.maxHeight) !== null && _b !== void 0 ? _b : 0;
        this.parent = { width: 0, height: 0 };
        this.resizeState = 0;
    }
    set($el) {
        this.$el = $el;
        this.props.disableAttributes.forEach(attr => {
            switch (attr) {
                case 'l':
                    this.disableCalcMap &= -2;
                    break;
                case 't':
                    this.disableCalcMap &= -3;
                    break;
                case 'w':
                    this.disableCalcMap &= -5;
                    break;
                case 'h':
                    this.disableCalcMap &= -9;
            }
        });
    }
    emitEvent(eventName, additionalOptions) {
        var _a;
        if (!this.$event) {
            return;
        }
        const isLeft = (_a = this.activeResizer) === null || _a === void 0 ? void 0 : _a.classList.contains('resizable-l');
        this.$event(Object.assign({ eventName, width: this.width + this.changeX * (isLeft ? -1 : 1), height: this.height + this.changeY, changedX: this.changeX, changedY: this.changeY }, additionalOptions));
    }
    static isTouchEvent(e) {
        var _a;
        const event = e;
        return ((_a = event.touches) === null || _a === void 0 ? void 0 : _a.length) >= 0;
    }
    handleMove(event) {
        var _a;
        if (!this.resizeState) {
            return;
        }
        let eventY, eventX;
        if (ResizeDirective.isTouchEvent(event)) {
            eventY = event.touches[0].clientY;
            eventX = event.touches[0].clientX;
        }
        else {
            eventY = event.clientY;
            eventX = event.clientX;
        }
        let isX = this.resizeState & RESIZE_MASK['resizable-r'].bit ||
            this.resizeState & RESIZE_MASK['resizable-l'].bit;
        let isY = this.resizeState & RESIZE_MASK['resizable-t'].bit ||
            this.resizeState & RESIZE_MASK['resizable-b'].bit;
        if (isY && this.disableCalcMap & DISABLE_MASK.h) {
            let diffY = eventY - this.mouseY;
            let changedY = this.changeY + diffY;
            const newHeight = this.height + changedY;
            // if overcrossed min height
            if (newHeight < this.minH) {
                changedY = -(this.height - this.minH);
            }
            // if overcrossed max heiht
            if (this.maxH && newHeight > this.maxH) {
                changedY = this.maxH - this.height;
            }
            this.changeY = changedY;
            this.mouseY = eventY;
            if (this.activeResizer) {
                this.activeResizer.style.bottom = `${-this.changeY}px`;
            }
        }
        if (isX && this.disableCalcMap & DISABLE_MASK.w) {
            const isLeft = (_a = this.activeResizer) === null || _a === void 0 ? void 0 : _a.classList.contains('resizable-l');
            let diffX = eventX - this.mouseX;
            let changedX = this.changeX + diffX;
            const newWidth = this.width + changedX * (isLeft ? -1 : 1);
            // if overcrossed min width
            if (newWidth < this.minW) {
                changedX = -(this.width - this.minW);
            }
            // if overcrossed max width
            if (this.maxW && newWidth > this.maxW) {
                changedX = this.maxW - this.width;
            }
            this.changeX = changedX;
            this.mouseX = eventX;
            if (this.activeResizer) {
                if (!isLeft) {
                    this.activeResizer.style.right = `${-this.changeX}px`;
                }
                else {
                    this.activeResizer.style.left = `${this.changeX}px`;
                }
            }
        }
        this.emitEvent(ResizeEvents.move);
    }
    handleDown(event) {
        if (event.defaultPrevented) {
            return;
        }
        // stop other events if resize in progress
        event.preventDefault();
        this.dropInitial();
        for (let elClass in RESIZE_MASK) {
            const target = event.target;
            if (this.$el.contains(target) && (target === null || target === void 0 ? void 0 : target.classList.contains(elClass))) {
                document.body.style.cursor = RESIZE_MASK[elClass].cursor;
                if (ResizeDirective.isTouchEvent(event)) {
                    this.setInitials(event.touches[0], target);
                }
                else {
                    event.preventDefault && event.preventDefault();
                    this.setInitials(event, target);
                }
                this.resizeState = RESIZE_MASK[elClass].bit;
                const eventName = ResizeEvents.start;
                this.emitEvent(eventName);
                break;
            }
        }
        this.bindMove();
    }
    handleUp(e) {
        e.preventDefault();
        if (this.resizeState !== 0) {
            this.resizeState = 0;
            document.body.style.cursor = '';
            const eventName = ResizeEvents.end;
            this.emitEvent(eventName);
        }
        this.dropInitial();
        this.unbindMove();
    }
    setInitials({ clientX, clientY }, target) {
        var _a, _b, _c, _d;
        const computedStyle = getComputedStyle(this.$el);
        this.$el.classList.add('active');
        this.activeResizer = target;
        if (this.disableCalcMap & DISABLE_MASK.w) {
            this.mouseX = clientX;
            this.width = this.$el.clientWidth;
            this.parent.width = (_b = (_a = this.$el.parentElement) === null || _a === void 0 ? void 0 : _a.clientWidth) !== null && _b !== void 0 ? _b : 0;
            // min width
            const minPaddingX = parseFloat(computedStyle.paddingLeft) +
                parseFloat(computedStyle.paddingRight);
            this.minW = Math.max(minPaddingX, this.initialProps.minWidth || 0);
            // max width
            if (this.initialProps.maxWidth) {
                this.maxW = Math.max(this.width, this.initialProps.maxWidth);
            }
        }
        if (this.disableCalcMap & DISABLE_MASK.h) {
            this.mouseY = clientY;
            this.height = this.$el.clientHeight;
            this.parent.height = (_d = (_c = this.$el.parentElement) === null || _c === void 0 ? void 0 : _c.clientHeight) !== null && _d !== void 0 ? _d : 0;
            // min height
            const minPaddingY = parseFloat(computedStyle.paddingTop) +
                parseFloat(computedStyle.paddingBottom);
            this.minH = Math.max(minPaddingY, this.initialProps.minHeight || 0);
            // max height
            if (this.initialProps.maxHeight) {
                this.maxH = Math.max(this.height, this.initialProps.maxHeight);
            }
        }
    }
    dropInitial() {
        this.changeX = this.changeY = this.minW = this.minH;
        this.width = this.height = 0;
        if (this.activeResizer) {
            this.activeResizer.removeAttribute('style');
        }
        this.$el.classList.remove('active');
        this.activeResizer = undefined;
    }
    bindMove() {
        document.documentElement.addEventListener('mouseup', this.mouseUpFunc, true);
        document.documentElement.addEventListener('touchend', this.mouseUpFunc, true);
        document.documentElement.addEventListener('mousemove', this.mouseMoveFunc, true);
        document.documentElement.addEventListener('touchmove', this.mouseMoveFunc, true);
        document.documentElement.addEventListener('mouseleave', this.mouseUpFunc);
    }
    unbindMove() {
        document.documentElement.removeEventListener('mouseup', this.mouseUpFunc, true);
        document.documentElement.removeEventListener('touchend', this.mouseUpFunc, true);
        document.documentElement.removeEventListener('mousemove', this.mouseMoveFunc, true);
        document.documentElement.removeEventListener('touchmove', this.mouseMoveFunc, true);
        document.documentElement.removeEventListener('mouseleave', this.mouseUpFunc);
    }
}

const ResizableElement = (props, children) => {
    const resizeEls = [];
    const directive = (props.canResize &&
        new ResizeDirective(props, e => {
            var _a;
            if (e.eventName === ResizeEvents.end) {
                (_a = props.onResize) === null || _a === void 0 ? void 0 : _a.call(props, e);
            }
        })) ||
        null;
    if (props.active) {
        if (props.canResize) {
            for (let p in props.active) {
                resizeEls.push(h("div", { onClick: e => e.preventDefault(), onDblClick: e => {
                        var _a;
                        e.preventDefault();
                        (_a = props.onDblClick) === null || _a === void 0 ? void 0 : _a.call(props, e);
                    }, onMouseDown: (e) => directive === null || directive === void 0 ? void 0 : directive.handleDown(e), onTouchStart: (e) => directive === null || directive === void 0 ? void 0 : directive.handleDown(e), class: `resizable resizable-${props.active[p]}` }));
            }
        }
        else {
            for (let p in props.active) {
                resizeEls.push(h("div", { onClick: e => e.preventDefault(), onTouchStart: (e) => e.preventDefault(), onDblClick: e => {
                        var _a;
                        e.preventDefault();
                        (_a = props.onDblClick) === null || _a === void 0 ? void 0 : _a.call(props, e);
                    }, class: `no-resize resizable resizable-${props.active[p]}` }));
            }
        }
    }
    return (h("div", Object.assign({}, props, { ref: e => e && (directive === null || directive === void 0 ? void 0 : directive.set(e)) }),
        children,
        resizeEls));
};

const ON_COLUMN_CLICK = 'columnclick';
const HeaderCellRenderer = ({ data, props, additionalData }, children) => {
    let colTemplate = (data === null || data === void 0 ? void 0 : data.name) || '';
    let cellProps = props;
    if (data === null || data === void 0 ? void 0 : data.columnTemplate) {
        colTemplate = data.columnTemplate(h, data, additionalData);
    }
    if (data === null || data === void 0 ? void 0 : data.columnProperties) {
        const extra = data.columnProperties(data);
        if (extra) {
            cellProps = doPropMerge(props, extra);
        }
    }
    const resizeProps = Object.assign(Object.assign({}, cellProps), { onMouseDown(e) {
            dispatch(e.currentTarget, ON_COLUMN_CLICK, {
                data,
                event: e,
            });
        } });
    return (h(ResizableElement, Object.assign({}, resizeProps),
        h("div", { class: "header-content" }, colTemplate),
        children));
};

var defineProperty = (function() {
  try {
    var func = getNative(Object, 'defineProperty');
    func({}, '', {});
    return func;
  } catch (e) {}
}());

/**
 * The base implementation of `assignValue` and `assignMergeValue` without
 * value checks.
 *
 * @private
 * @param {Object} object The object to modify.
 * @param {string} key The key of the property to assign.
 * @param {*} value The value to assign.
 */
function baseAssignValue(object, key, value) {
  if (key == '__proto__' && defineProperty) {
    defineProperty(object, key, {
      'configurable': true,
      'enumerable': true,
      'value': value,
      'writable': true
    });
  } else {
    object[key] = value;
  }
}

/**
 * A specialized version of `baseAggregator` for arrays.
 *
 * @private
 * @param {Array} [array] The array to iterate over.
 * @param {Function} setter The function to set `accumulator` values.
 * @param {Function} iteratee The iteratee to transform keys.
 * @param {Object} accumulator The initial aggregated object.
 * @returns {Function} Returns `accumulator`.
 */
function arrayAggregator(array, setter, iteratee, accumulator) {
  var index = -1,
      length = array == null ? 0 : array.length;

  while (++index < length) {
    var value = array[index];
    setter(accumulator, value, iteratee(value), array);
  }
  return accumulator;
}

/**
 * Aggregates elements of `collection` on `accumulator` with keys transformed
 * by `iteratee` and values set by `setter`.
 *
 * @private
 * @param {Array|Object} collection The collection to iterate over.
 * @param {Function} setter The function to set `accumulator` values.
 * @param {Function} iteratee The iteratee to transform keys.
 * @param {Object} accumulator The initial aggregated object.
 * @returns {Function} Returns `accumulator`.
 */
function baseAggregator(collection, setter, iteratee, accumulator) {
  baseEach(collection, function(value, key, collection) {
    setter(accumulator, value, iteratee(value), collection);
  });
  return accumulator;
}

/**
 * Creates a function like `_.groupBy`.
 *
 * @private
 * @param {Function} setter The function to set accumulator values.
 * @param {Function} [initializer] The accumulator object initializer.
 * @returns {Function} Returns the new aggregator function.
 */
function createAggregator(setter, initializer) {
  return function(collection, iteratee) {
    var func = isArray(collection) ? arrayAggregator : baseAggregator,
        accumulator = {};

    return func(collection, setter, baseIteratee(iteratee), accumulator);
  };
}

/**
 * Creates an object composed of keys generated from the results of running
 * each element of `collection` thru `iteratee`. The corresponding value of
 * each key is the last element responsible for generating the key. The
 * iteratee is invoked with one argument: (value).
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Collection
 * @param {Array|Object} collection The collection to iterate over.
 * @param {Function} [iteratee=_.identity] The iteratee to transform keys.
 * @returns {Object} Returns the composed aggregate object.
 * @example
 *
 * var array = [
 *   { 'dir': 'left', 'code': 97 },
 *   { 'dir': 'right', 'code': 100 }
 * ];
 *
 * _.keyBy(array, function(o) {
 *   return String.fromCharCode(o.code);
 * });
 * // => { 'a': { 'dir': 'left', 'code': 97 }, 'd': { 'dir': 'right', 'code': 100 } }
 *
 * _.keyBy(array, 'dir');
 * // => { 'left': { 'dir': 'left', 'code': 97 }, 'right': { 'dir': 'right', 'code': 100 } }
 */
var keyBy = createAggregator(function(result, value, key) {
  baseAssignValue(result, key, value);
});

const HeaderRenderer = (p) => {
    var _a, _b, _c, _d, _e;
    const cellClass = {
        [HEADER_CLASS]: true,
        [HEADER_SORTABLE_CLASS]: !!((_a = p.data) === null || _a === void 0 ? void 0 : _a.sortable),
    };
    if ((_b = p.data) === null || _b === void 0 ? void 0 : _b.order) {
        cellClass[p.data.order] = true;
    }
    const dataProps = {
        [DATA_COL]: p.column.itemIndex,
        canResize: p.canResize,
        minWidth: ((_c = p.data) === null || _c === void 0 ? void 0 : _c.minSize) || MIN_COL_SIZE,
        maxWidth: (_d = p.data) === null || _d === void 0 ? void 0 : _d.maxSize,
        active: p.active || ['r'],
        class: cellClass,
        style: {
            width: `${p.column.size}px`,
            transform: `translateX(${p.column.start}px)`,
        },
        onResize: p.onResize,
        onDblClick(originalEvent) {
            var _a;
            (_a = p.onDblClick) === null || _a === void 0 ? void 0 : _a.call(p, {
                column: p.data,
                index: p.column.itemIndex,
                originalEvent,
                providers: p.data.providers,
            });
        },
        onClick(originalEvent) {
            if (originalEvent.defaultPrevented || !p.onClick) {
                return;
            }
            p.onClick({
                column: p.data,
                index: p.column.itemIndex,
                originalEvent,
                providers: p.data.providers,
            });
        },
    };
    if (p.range) {
        if (p.column.itemIndex >= p.range.x && p.column.itemIndex <= p.range.x1) {
            if (typeof dataProps.class === 'object') {
                dataProps.class[FOCUS_CLASS] = true;
            }
        }
    }
    return (h(HeaderCellRenderer, { data: p.data, props: dataProps, additionalData: p.additionalData },
        h(SortingSign, { column: p.data }),
        p.canFilter && ((_e = p.data) === null || _e === void 0 ? void 0 : _e.filter) !== false ? (h(FilterButton, { column: p.data })) : ('')));
};

const HeaderGroupRenderer = (p) => {
    const groupProps = {
        canResize: p.canResize,
        minWidth: p.group.indexes.length * MIN_COL_SIZE,
        maxWidth: 0,
        active: p.active || ['r'],
        class: {
            [HEADER_CLASS]: true,
        },
        style: {
            transform: `translateX(${p.start}px)`,
            width: `${p.end - p.start}px`,
        },
        onResize: p.onResize,
    };
    return (h(HeaderCellRenderer, { data: Object.assign(Object.assign({}, p.group), { prop: '', providers: p.providers, index: p.start }), props: groupProps, additionalData: p.additionalData }));
};

const revogrHeaderStyleCss = "@charset \"UTF-8\";.revo-drag-icon{width:11px;opacity:0.8}.revo-drag-icon::before{content:\"::\"}.revo-alt-icon{-webkit-mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");width:11px;height:11px;background-size:cover;background-repeat:no-repeat}.arrow-down{position:absolute;right:5px;top:0}.arrow-down svg{width:8px;margin-top:5px;margin-left:5px;opacity:0.4}.cell-value-wrapper{margin-right:10px;overflow:hidden;text-overflow:ellipsis}.revo-button{position:relative;overflow:hidden;color:#fff;background-color:#4545ff;height:32px;line-height:32px;padding:0 15px;outline:0;border:0;border-radius:7px;box-sizing:border-box;cursor:pointer}.revo-button.green{background-color:#009037}.revo-button.red{background-color:#E0662E}.revo-button:disabled,.revo-button[disabled]{cursor:not-allowed !important;filter:opacity(0.35) !important}.revo-button.outline{border:1px solid #dbdbdb;line-height:30px;background:none;color:#000;box-shadow:none}revo-grid[theme^=dark] .revo-button.outline{border:1px solid #404040;color:#d8d8d8}revogr-header{position:relative;z-index:5;display:block}revogr-header .rgHeaderCell{display:flex}revogr-header .rgHeaderCell.align-center{text-align:center}revogr-header .rgHeaderCell.align-left{text-align:left}revogr-header .rgHeaderCell.align-right{text-align:right}revogr-header .rgHeaderCell.sortable{cursor:pointer}revogr-header .rgHeaderCell i.asc:after,revogr-header .rgHeaderCell i.desc:after{font-size:13px}revogr-header .rgHeaderCell i.asc:after{content:\"↑\"}revogr-header .rgHeaderCell i.desc:after{content:\"↓\"}revogr-header .rgHeaderCell,revogr-header .grouped-cell{position:absolute;box-sizing:border-box;height:100%;z-index:1}revogr-header .header-rgRow{display:block;position:relative}revogr-header .header-rgRow.group{z-index:0}revogr-header .group-rgRow{position:relative}revogr-header .rgHeaderCell.active{z-index:10}revogr-header .rgHeaderCell.active .resizable{background-color:deepskyblue}revogr-header .rgHeaderCell .header-content{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex-grow:1}revogr-header .rgHeaderCell .resizable{display:block;position:absolute;z-index:90;touch-action:none;user-select:none}revogr-header .rgHeaderCell .resizable:hover{background-color:deepskyblue}revogr-header .rgHeaderCell>.resizable-r{cursor:ew-resize;width:6px;right:0;top:0;height:100%}revogr-header .rgHeaderCell>.resizable-rb{cursor:se-resize;width:6px;height:6px;right:0;bottom:0}revogr-header .rgHeaderCell>.resizable-b{cursor:s-resize;height:6px;bottom:0;width:100%;left:0}revogr-header .rgHeaderCell>.resizable-lb{cursor:sw-resize;width:6px;height:6px;left:0;bottom:0}revogr-header .rgHeaderCell>.resizable-l{cursor:w-resize;width:6px;left:0;height:100%;top:0}revogr-header .rgHeaderCell>.resizable-lt{cursor:nw-resize;width:6px;height:6px;left:0;top:0}revogr-header .rgHeaderCell>.resizable-t{cursor:n-resize;height:6px;top:0;width:100%;left:0}revogr-header .rgHeaderCell>.resizable-rt{cursor:ne-resize;width:6px;height:6px;right:0;top:0}revogr-header .rv-filter{visibility:hidden}";

const RevogrHeaderComponent = /*@__PURE__*/ proxyCustomElement(class RevogrHeaderComponent extends HTMLElement {
    constructor() {
        super();
        this.__registerHost();
        this.initialHeaderClick = createEvent(this, "beforeheaderclick", 7);
        this.headerresize = createEvent(this, "headerresize", 7);
        this.beforeResize = createEvent(this, "beforeheaderresize", 7);
        this.headerdblClick = createEvent(this, "headerdblclick", 7);
        this.beforeHeaderRender = createEvent(this, "beforeheaderrender", 7);
        this.beforeGroupHeaderRender = createEvent(this, "beforegroupheaderrender", 7);
        this.afterHeaderRender = createEvent(this, "afterheaderrender", 7);
        /**
         * Grouping depth, how many levels of grouping
         */
        this.groupingDepth = 0;
        /**
         * Extra properties to pass into header renderer, such as vue or react components to handle parent
         */
        this.additionalData = {};
    }
    onResize({ width }, index) {
        const col = this.colData[index];
        const event = this.beforeResize.emit([
            Object.assign(Object.assign({}, col), { size: width || undefined }),
        ]);
        if (event.defaultPrevented) {
            return;
        }
        this.headerresize.emit({ [index]: width || 0 });
    }
    onResizeGroup(changedX, startIndex, endIndex) {
        const sizes = {};
        const cols = keyBy(this.viewportCol.get('items'), 'itemIndex');
        const change = changedX / (endIndex - startIndex + 1);
        for (let i = startIndex; i <= endIndex; i++) {
            const item = cols[i];
            if (item) {
                sizes[i] = item.size + change;
            }
        }
        this.headerresize.emit(sizes);
    }
    componentDidRender() {
        this.afterHeaderRender.emit(this.providers);
    }
    render() {
        var _a;
        const cols = this.viewportCol.get('items');
        const range = (_a = this.selectionStore) === null || _a === void 0 ? void 0 : _a.get('range');
        const { cells } = this.renderHeaderColumns(cols, range);
        const groupRow = this.renderGroupingColumns();
        return [
            h("div", { key: 'a8d57270f4758b454fb4900808453e0c4663c91c', class: "group-rgRow" }, groupRow),
            h("div", { key: '41a270ff6bde9943a81d9c46482f2a40312afb50', class: `${HEADER_ROW_CLASS} ${HEADER_ACTUAL_ROW_CLASS}` }, cells),
        ];
    }
    renderHeaderColumns(cols, range) {
        const cells = [];
        for (let rgCol of cols) {
            const colData = this.colData[rgCol.itemIndex];
            const props = {
                range,
                column: rgCol,
                data: Object.assign(Object.assign({}, colData), { index: rgCol.itemIndex, providers: this.providers }),
                canFilter: !!this.columnFilter,
                canResize: this.canResize,
                active: this.resizeHandler,
                additionalData: this.additionalData,
                onResize: e => this.onResize(e, rgCol.itemIndex),
                onDblClick: e => this.headerdblClick.emit(e),
                onClick: e => this.initialHeaderClick.emit(e),
            };
            const event = this.beforeHeaderRender.emit(props);
            if (!event.defaultPrevented) {
                cells.push(h(HeaderRenderer, Object.assign({}, event.detail)));
            }
        }
        return { cells };
    }
    renderGroupingColumns() {
        var _a;
        const groupRow = [];
        for (let i = 0; i < this.groupingDepth; i++) {
            if (this.groups[i]) {
                for (let group of this.groups[i]) {
                    const groupStartIndex = (_a = group.indexes[0]) !== null && _a !== void 0 ? _a : -1;
                    if (groupStartIndex > -1) {
                        const groupEndIndex = groupStartIndex + group.indexes.length - 1;
                        const groupStart = getItemByIndex(this.dimensionCol.state, groupStartIndex).start;
                        const groupEnd = getItemByIndex(this.dimensionCol.state, groupEndIndex).end;
                        const props = {
                            providers: this.providers,
                            start: groupStart,
                            end: groupEnd,
                            group,
                            active: this.resizeHandler,
                            canResize: this.canResize,
                            additionalData: this.additionalData,
                            onResize: e => {
                                var _a;
                                return this.onResizeGroup((_a = e.changedX) !== null && _a !== void 0 ? _a : 0, groupStartIndex, groupEndIndex);
                            },
                        };
                        const event = this.beforeGroupHeaderRender.emit(props);
                        if (!event.defaultPrevented) {
                            groupRow.push(h(HeaderGroupRenderer, Object.assign({}, event.detail)));
                        }
                    }
                }
            }
            groupRow.push(h("div", { class: `${HEADER_ROW_CLASS} group` }));
        }
        return groupRow;
    }
    get providers() {
        return {
            type: this.type,
            readonly: this.readonly,
            data: this.colData,
            viewport: this.viewportCol,
            dimension: this.dimensionCol,
            selection: this.selectionStore,
        };
    }
    get element() { return this; }
    static get style() { return revogrHeaderStyleCss; }
}, [0, "revogr-header", {
        "viewportCol": [16, "viewport-col"],
        "dimensionCol": [16, "dimension-col"],
        "selectionStore": [16, "selection-store"],
        "groups": [16],
        "groupingDepth": [2, "grouping-depth"],
        "readonly": [4],
        "canResize": [4, "can-resize"],
        "resizeHandler": [16, "resize-handler"],
        "colData": [16, "col-data"],
        "columnFilter": [4, "column-filter"],
        "type": [1],
        "additionalData": [8, "additional-data"]
    }]);
function defineCustomElement() {
    if (typeof customElements === "undefined") {
        return;
    }
    const components = ["revogr-header"];
    components.forEach(tagName => { switch (tagName) {
        case "revogr-header":
            if (!customElements.get(tagName)) {
                customElements.define(tagName, RevogrHeaderComponent);
            }
            break;
    } });
}

export { ON_COLUMN_CLICK as O, RevogrHeaderComponent as R, SortingSign as S, dispatchByEvent as a, defineCustomElement as b, dispatch as d };
//# sourceMappingURL=revogr-header2.js.map

//# sourceMappingURL=revogr-header2.js.map