{"file": "revogr-row-headers2.js", "mappings": ";;;;;;;;;;;;;AAEA,MAAM,iBAAiB,GAAG,EAAE;AACrB,MAAM,sBAAsB,GAAG,CACpC,WAAmB,EACnB,eAA4B,EAC5B,QAAQ,GAAG,EAAE,KACX;IACF,QACE,CAAA,eAAe,KAAA,IAAA,IAAf,eAAe,KAAf,MAAA,GAAA,MAAA,GAAA,eAAe,CAAE,IAAI;AACrB,QAAA,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,EAAE,QAAQ,CAAC;AAE/E;;ACUA;;;;AAIG;AACa,SAAA,yBAAyB,CACvC,GAAW;AACX,KAAQ,EACR,SAAiB,EACjB,WAAmB,EACnB,SAAgC,EAAA;IAEhC,MAAM,UAAU,GAAiB,iBAAiB,CAAC,SAAS,EAAE,GAAG,CAAC;AAClE,IAAA,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC;AACrC,IAAA,IAAI,QAAmC;;IAEvC,IAAI,SAAS,EAAE;AACb,QAAA,IAAI,kBAAkB,GAAG,UAAU,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,IAAI,CAAC,CAAC;;QAE1E,IAAI,kBAAkB,EAAE;;YAEtB,QAAQ,GAAG,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EACvD,iBAAiB,EAAE,kBAAkB,GAAG,EAAE,IACvC,SAAS,CAAA,EACT,KAAK,CAAA,CACR;;;AAIN,IAAA,MAAM,kBAAkB,GAAG,iBAAiB,CAC1C,WAAW,EACX,SAAS,CAAC,QAAQ,EAClB,UAAU,CACX;;IAED,IAAI,QAAQ,EAAE;AACZ,QAAA,MAAM,KAAK,GAAG,eAAe,CAC3B,UAAU,EACV,SAAS,EACT,kBAAkB,EAClB,QAAQ,EACR,SAAS,CACV;AACD,QAAA,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,qBAAqB,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC;;;;IAK1D,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,KAAK,GAAG,QAAQ,CAAC;YACrB,cAAc,EAAE,UAAU,CAAC,KAAK;YAChC,cAAc,EAAE,UAAU,CAAC,SAAS;YACpC,QAAQ,EAAE,SAAS,CAAC,cAAc;AAClC,YAAA,OAAO,EAAE,kBAAkB;AAC3B,YAAA,QAAQ,EAAE,SAAS;YACnB,KAAK,EAAE,SAAS,CAAC,KAAK;AACvB,SAAA,CAAC;;AAGF,QAAA,QAAQ,GAAG;YACT,KAAK;AACL,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,GAAG,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC;SACtB;;AAEH,IAAA,OAAO,QAAQ;AACjB;AAEA;AACA;AACA;AACA,SAAS,iBAAiB,CACxB,WAAmB,EACnB,QAAgB,EAChB,UAAwB,EAAA;AAExB,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,IAAI,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC;AAC9E;SAEgB,qBAAqB,CACnC,KAA4B,EAC5B,OAA8B,EAC9B,KAAY,EAAA;AAEZ,IAAA,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC;;AAE1C,IAAA,IACE,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG;AACxB,QAAA,EAAE,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,EACjD;AACA,QAAA,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM;;AAE/B,IAAA,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;AAC7B;AAEA;;;AAGG;AACG,SAAU,eAAe,CAC7B,SAAuB,EACvB,SAAiB,EACjB,WAAmB,EACnB,kBAAqB,EACrB,SAAmE,EAAA;AAEnE,IAAA,MAAM,QAAQ,GAAwB,WAAW,CAAC,kBAAkB,CAAC;IACrE,MAAM,KAAK,GAAG,QAAQ,CAAC;QACrB,KAAK,EAAE,SAAS,CAAC,KAAK;QACtB,cAAc,EAAE,QAAQ,CAAC,GAAG;AAC5B,QAAA,cAAc,EAAE,QAAQ,CAAC,SAAS,GAAG,CAAC;QACtC,QAAQ,EAAE,SAAS,CAAC,cAAc;QAClC,OAAO,EAAE,WAAW,IAAI,QAAQ,CAAC,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC;AACvD,QAAA,QAAQ,EAAE,SAAS;AACpB,KAAA,CAAC;AACF,IAAA,OAAO,KAAK;AACd;AAEA;;;AAGG;SACa,QAAQ,CACtB,GAOC,EACD,WAAW,GAAG,CAAC,EAAA;IAEf,MAAM,KAAK,GAA0B,EAAE;AAEvC,IAAA,IAAI,KAAK,GAAG,GAAG,CAAC,cAAc;IAC9B,IAAI,IAAI,GAAG,WAAW;;AAGtB,IAAA,OAAO,IAAI,IAAI,GAAG,CAAC,OAAO,IAAI,KAAK,GAAG,GAAG,CAAC,QAAQ,EAAE;AAClD,QAAA,MAAM,OAAO,GAAG,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,QAAQ,CAAC;QAC3D,KAAK,CAAC,IAAI,CAAC;AACT,YAAA,KAAK,EAAE,GAAG,CAAC,cAAc,GAAG,IAAI;AAChC,YAAA,GAAG,EAAE,GAAG,CAAC,cAAc,GAAG,IAAI,GAAG,OAAO;AACxC,YAAA,SAAS,EAAE,KAAK;AAChB,YAAA,IAAI,EAAE,OAAO;AACd,SAAA,CAAC;QACF,IAAI,IAAI,OAAO;AACf,QAAA,KAAK,EAAE;;AAET,IAAA,OAAO,KAAK;AACd;AAcgB,SAAA,iBAAiB,CAC/B,MAAc,EACd,IAAyB,EAAA;;IAEzB,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AAChC,IAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AAClC,IAAA,IAAI,QAAQ,GAAG;QACb,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,GAAG,EAAE,IAAI,CAAC,GAAG;KACd;;AAGD,IAAA,IAAI,MAAM,GAAG,UAAU,EAAE;AACvB,QAAA,OAAO,SAAS;;;AAIlB,IAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;;AAE1B,QAAA,IAAI,QAAQ,GAAwB,WAAW,CAAC,IAAI,CAAC;AAErD,QAAA,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK;AACtB,QAAA,MAAM,MAAM,GAAG,CAAC,GAAG,MAAM;AACzB,QAAA,OAAO,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AACtB,YAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,GAAG,CAAC;AACvC,YAAA,MAAM,IAAI,GAAG,WAAW,CACtB,QAAQ,EACR,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,cAAc,CACpB;;YAGD,IAAI,QAAQ,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE;gBACvC;;;AAIF,YAAA,IAAI,MAAM,GAAG,CAAC,GAAG,UAAU;;AAG3B,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACrB,gBAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC;;;AAIpC,YAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,GAAG;gBAC5B,KAAK,EAAE,QAAQ,CAAC,GAAG;AACnB,gBAAA,GAAG,EAAE,QAAQ,CAAC,GAAG,GAAG,IAAI;AACxB,gBAAA,SAAS,EAAE,QAAQ;AACnB,gBAAA,IAAI,EAAE,IAAI;aACX;;YAED,QAAQ,CAAC,KAAK,EAAE;AAChB,YAAA,QAAQ,CAAC,GAAG,GAAG,MAAM;;;;SAIlB;;AAEL,QAAA,IAAI,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC;AAElC,QAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG;AACxB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/B,YAAA,MAAM,QAAQ,GAAG,CAAC,CAAA,EAAA,GAAA,SAAS,aAAT,SAAS,KAAA,MAAA,GAAA,MAAA,GAAT,SAAS,CAAE,SAAS,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,CAAC,IAAI,CAAC;AAChD,YAAA,MAAM,IAAI,GAAG,WAAW,CACtB,QAAQ,EACR,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,cAAc,CACpB;;AAGD,YAAA,IAAI,QAAQ,GAAG,GAAG,GAAG,CAAC;AACtB,YAAA,QAAQ,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,UAAU,GAAG,QAAQ,GAAG,QAAQ,IAAI,UAAU;;AAGzE,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACvB,gBAAA,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC;gBAChC;;;AAIF,YAAA,MAAM,cAAc,GAAG,CAAA,EAAA,GAAA,SAAS,KAAT,IAAA,IAAA,SAAS,KAAT,MAAA,GAAA,MAAA,GAAA,SAAS,CAAE,KAAK,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,CAAC;AAC5C,YAAA,QAAQ,CAAC,QAAQ,CAAC,GAAG,SAAS,GAAG;gBAC/B,KAAK,EAAE,cAAc,GAAG,IAAI;AAC5B,gBAAA,GAAG,EAAE,cAAc;AACnB,gBAAA,SAAS,EAAE,QAAQ;AACnB,gBAAA,IAAI,EAAE,IAAI;aACX;;AAED,YAAA,QAAQ,CAAC,KAAK,GAAG,QAAQ;YACzB,QAAQ,CAAC,GAAG,EAAE;;;AAGlB,IAAA,MAAM,KAAK,GAAG;QACZ,KAAK,EACH,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,GAAG,UAAU,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;YAClE,UAAU;QACZ,GAAG,EACD,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG;YAC5D,UAAU;KACb;AACD,IAAA,OAAA,MAAA,CAAA,MAAA,CAAA,EACE,KAAK,EAAE,QAAQ,EAAA,EACZ,KAAK,CACR;AACJ;AAEA,SAAS,WAAW,CAClB,KAAa,EACb,KAA2B,EAC3B,QAAQ,GAAG,CAAC,EAAA;AAEZ,IAAA,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;AACzB,QAAA,OAAO,KAAK,CAAC,KAAK,CAAC;;AAErB,IAAA,OAAO,QAAQ;AACjB;AAEA;;AAEG;AACG,SAAU,aAAa,CAC3B,GAAW,EACX,QAAgB,EAChB,KAAoB,EACpB,IAAmB,EAAA;AAEnB,IAAA,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;AACnB,QAAA,OAAO,KAAK;;;;AAId,IAAA,QACE,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG;AACvC,SAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,KAAK,QAAQ,CAAC;AAE9C;AAEM,SAAU,4BAA4B,CAC1C,GAAW,EACX,WAAmB,EACnB,SAAwB,EACxB,QAAuB,EAAA;;;IAGvB,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,OAAO,KAAK;;AAEd,IAAA,OAAO,WAAW,GAAG,GAAG,IAAI,MAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAA,MAAA,GAAA,MAAA,GAAR,QAAQ,CAAE,GAAG,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,CAAC,CAAC;AACjD;AAEM,SAAU,YAAY,CAC1B,CAAgB,EAAA;IAEhB,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;AACzB;AAEM,SAAU,WAAW,CAAC,CAAgB,EAAA;IAC1C,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AACvB;AAEA;;;;;;;AAOG;AACG,SAAU,YAAY,CAC1B,OAA8B,EAC9B,YAAoB,EACpB,IAAY,EACZ,cAAsB,EAAA;AAEtB,IAAA,MAAM,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC;AAC1B,IAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM;IAE1B,IAAI,GAAG,GAAG,cAAc;IACxB,IAAI,CAAC,GAAG,CAAC;IACT,IAAI,KAAK,GAAG,YAAY;;IAGxB,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,OAAO,EAAE;;;AAGX,IAAA,OAAO,CAAC,GAAG,KAAK,EAAE;AAChB,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;AACzB,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG;AAChB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI;QAChB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI;AAC5B,QAAA,GAAG,GAAG,IAAI,CAAC,GAAG;;AAEd,QAAA,KAAK,EAAE;AACP,QAAA,CAAC,EAAE;;AAGH,QAAA,IAAI,KAAK,KAAK,KAAK,EAAE;YACnB,KAAK,GAAG,CAAC;;;AAGb,IAAA,OAAO,KAAK;AACd;;ACnXA;;;;AAIG;AACH,SAAS,YAAY,GAAA;IACnB,OAAO;;AAEL,QAAA,KAAK,EAAE,EAAE;;AAET,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,GAAG,EAAE,CAAC;;AAGN,QAAA,WAAW,EAAE,CAAC;;AAGd,QAAA,SAAS,EAAE,CAAC;;AAGZ,QAAA,UAAU,EAAE,CAAC;KACd;AACH;AAEA;;AAEG;MACU,aAAa,CAAA;AAMxB,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,eAAe;;IAE7B,IAAI,cAAc,CAAC,KAAa,EAAA;AAC9B,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK;;AAE9B,IAAA,WAAA,CAAqB,IAAwB,EAAA;QAAxB,IAAI,CAAA,IAAA,GAAJ,IAAI;;QARjB,IAAe,CAAA,eAAA,GAAG,CAAC;QASzB,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;;AAG1C;;;;AAIG;AACH,IAAA,qBAAqB,CACnB,QAAgB,EAChB,SAAgC,EAChC,KAAK,GAAG,KAAK,EAAA;QAEb,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC;;QAElD,IAAI,CAAC,YAAY,EAAE;YACjB;;QAGF,MAAM,WAAW,GAAG,CAAC;AACrB,QAAA,MAAM,gBAAgB,GAAG,SAAS,CAAC,cAAc,GAAG,WAAW;;AAE/D,QAAA,MAAM,OAAO,GAAG,gBAAgB,GAAG,CAAC;;AAEpC,QAAA,MAAM,WAAW,GAAG,YAAY,GAAG,OAAO;;QAG1C,IAAI,aAAa,GAAG,CAAC;;AAErB,QAAA,IAAI,SAAS,CAAC,QAAQ,GAAG,YAAY,EAAE;;YAErC,aAAa,GAAG,SAAS,CAAC,QAAQ,GAAG,YAAY,GAAG,gBAAgB;;QAGtE,IAAI,GAAG,GAAG,QAAQ;;AAElB,QAAA,IAAI,GAAG,GAAG,CAAC,EAAE;YACX,GAAG,GAAG,CAAC;;AACF,aAAA,IAAI,GAAG,GAAG,aAAa,EAAE;YAC9B,GAAG,GAAG,aAAa;;;AAIrB,QAAA,IAAI,CAAC,cAAc,GAAG,GAAG;;QAGzB,GAAG,IAAI,gBAAgB;QACvB,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,aAAa,GAAG,GAAG,GAAG,aAAa;AAE7D,QAAA,IAAI,QAAuB;;QAE3B,IAAI,KAAK,EAAE;AACT,YAAA,QAAQ,GAAG;AACT,gBAAA,KAAK,EAAE,EAAE;AACT,gBAAA,KAAK,EAAE,CAAC;AACR,gBAAA,GAAG,EAAE,CAAC;aACP;;aACI;AACL,YAAA,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE;;AAG5B,QAAA,MAAM,SAAS,GAAoC,YAAY,CAAC,QAAQ,CAAC;AACzE,QAAA,MAAM,QAAQ,GAAoC,WAAW,CAAC,QAAQ,CAAC;QAEvE,IAAI,QAAQ,GAA2B,EAAE;;;AAIzC,QAAA,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE;YAChE,QAAQ,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACH,QAAQ,CACR,EAAA,yBAAyB,CAC1B,GAAG,EACH,QAAQ,EACR,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,EAC3B,WAAW,EACX,SAAS,CACV,CACF;AACD,YAAA,IAAI,CAAC,WAAW,CAAM,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,QAAQ,EAAG;;;aAE5B,IACL,4BAA4B,CAAC,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,CAAC,EACnE;YACA,MAAM,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC;;YAEjC,MAAM,OAAO,GAAG,eAAe,CAC7B,SAAS,EACT,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,EAC3B,WAAW,GAAG,GAAG,GAAG,SAAS,CAAC,KAAK,EACnC,QAAQ,EACR;gBACE,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,cAAc,EAAE,SAAS,CAAC,cAAc;AACzC,aAAA,CACF;;AAGD,YAAA,IAAI,OAAO,CAAC,MAAM,EAAE;AAClB,gBAAA,MAAM,KAAK,GAAG;oBACZ,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC9B,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;iBAC3B;AACD,gBAAA,qBAAqB,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC;AAC5C,gBAAA,QAAQ,GACH,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,QAAQ,CACX,EAAA,EAAA,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAA,CAAA,EACd,KAAK,CACT;AACD,gBAAA,IAAI,CAAC,WAAW,CAAM,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,QAAQ,EAAG;;;;AAKvC;;AAEG;AACH,IAAA,gBAAgB,CAAC,IAAY,EAAA;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;AACrC,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM;;QAE1B,IAAI,CAAC,KAAK,EAAE;YACV;;AAGF,QAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;AACnB,YAAA,KAAK,EAAE,YAAY,CACjB,KAAK,EACL,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EACvB,IAAI,EACJ,IAAI,CAAC,cAAc,CACpB;AACF,SAAA,CAAC;;IAGJ,QAAQ,GAAA;QACN,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;YAC9B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;YAC9B,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;SAC3B;;AAGH,IAAA,WAAW,CAAC,IAA4B,EAAA;;;AAGtC,QAAA,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;YAC9E,IAAI,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,IAAI,CAAA,EAAA,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE,EAAA,CAAE;;AAE7C,QAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC;;AAE7B;;AC7MM,MAAM,eAAe,GAAiB,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC;;MCqBnE,gBAAgB,iBAAAA,kBAAA,CAAA,MAAA,gBAAA,SAAA,WAAA,CAAA;AAD7B,IAAA,WAAA,GAAA;;;;;AAkCE;;;;AAIG;AACK,QAAA,IAAgB,CAAA,gBAAA,GAAmB,EAAE;AAkG9C;IApFC,MAAM,GAAA;QACJ,MAAM,SAAS,GAAkB,EAAE;AACnC,QAAA,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,aAAa,CAAC;;QAGjD,IAAI,WAAW,GAAG,CAAC;;AAEnB,QAAA,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;AAC/B,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM;;YAGpD,MAAM,SAAS,GAAG,IAAI,SAAS,CAA0B,IAAI,CAAC,IAAI,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAC7D,IAAI,CAAC,SAAS,CAAC,KAAK,EACvB;;AAGF,YAAA,MAAM,OAAO,GAAG,IAAI,SAAS,CAC3B,aAAa,CACd;AACD,YAAA,MAAM,MAAM,GAAA,MAAA,CAAA,MAAA,CAAA,EACV,YAAY,EAAE,eAAe,CAAC,WAAW,CAAC,EACvC,EAAA,IAAI,CAAC,eAAe,CACxB;AACD,YAAA,OAAO,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC;AAC5B,YAAA,SAAS,CAAC,IAAI,CACZ,CAAA,CAAA,aAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACM,IAAI,EACR,EAAA,OAAO,EAAC,YAAY,EACpB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EACvC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,SAAS,EAAE,SAAS,CAAC,KAAK,EAC1B,OAAO,EAAE,OAAO,CAAC,KAAK,EACtB,WAAW,EAAE,QAAQ,CAAC,KAAK,EAC3B,QAAQ,EAAE,IAAI,EACd,KAAK,EAAE,KAAK,EACZ,CAAA,CAAA,CACH;YACD,WAAW,IAAI,SAAS;;QAG1B,MAAM,OAAO,GAAG,sBAAsB,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC;QACzE,QAAQ,CAAC,WAAW,CAAC;AACnB,YAAA,SAAS,EAAE,CAAC;AACZ,YAAA,WAAW,EAAE,CAAC;AACd,YAAA,KAAK,EAAE;AACL,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,KAAK,EAAE,CAAC;AACR,oBAAA,GAAG,EAAE,OAAO;AACZ,oBAAA,SAAS,EAAE,CAAC;AACb,iBAAA;AACF,aAAA;AACF,SAAA,CAAC;AAEF,QAAA,MAAM,cAAc,GACwC;YAC1D,aAAa,EAAE,IAAI,CAAC,MAAM;AAC1B,YAAA,YAAY,EAAE,CAAC;AACf,YAAA,KAAK,EAAE,EAAE,QAAQ,EAAE,CAAG,EAAA,OAAO,IAAI,EAAE;AACnC,YAAA,OAAO,EAAE,YAAY;AACrB,YAAA,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;AAC1C,YAAA,gBAAgB,EAAE,CAAC,CAAc,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;SACzE;AACD,QAAA,MAAM,cAAc,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACf,IAAI,CAAC,UAAU,CAAA,EAAA;;AAElB,YAAA,MAAM,EAAE,EAAE,EACV,OAAO,EACL,OAAO,IAAI,CAAC,eAAe,KAAK,QAAQ,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,EACxE,WAAW,EAAE,QAAQ,CAAC,KAAK,EAC3B,SAAS,EAAE,KAAK,EAChB,IAAI,EAAE,eAAe;;AAErB,YAAA,IAAI,EAAE;AAAW,SAAA,CAClB;AACD,QAAA,QACE,CAAC,CAAA,IAAI,EAAC,EAAA,KAAK,EAAE,EAAE,CAAC,eAAe,GAAG,IAAI,EAAE,EAAE,GAAG,EAAE,eAAe,EAAA,EAC5D,CAA4B,CAAA,wBAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EAAA,cAAc,kBAAc,IAAI,EAAA,CAAA,EAC1D,CAAA,CAAA,eAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EAAmB,cAAc,CAAI,CAAA,EACpC,SAAS,CACa,CACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/utils/row-header-utils.ts", "src/store/vp/viewport.helpers.ts", "src/store/vp/viewport.store.ts", "src/components/rowHeaders/row-header-render.tsx", "src/components/rowHeaders/revogr-row-headers.tsx"], "sourcesContent": ["import { RowHeaders } from '..';\n\nconst LETTER_BLOCK_SIZE = 10;\nexport const calculateRowHeaderSize = (\n  itemsLength: number,\n  rowHeaderColumn?: RowHeaders,\n  minWidth = 50,\n) => {\n  return (\n    rowHeaderColumn?.size ||\n    Math.max((itemsLength.toString().length + 1) * LETTER_BLOCK_SIZE, minWidth)\n  );\n};\n", "import type {\n  DimensionSettingsState,\n  PositionItem,\n  ViewSettingSizeProp,\n  ViewportStateItems,\n  VirtualPositionItem,\n  Range,\n} from '@type';\n\nimport { getItemByPosition } from '../dimension/dimension.helpers';\n\nexport type DimensionDataViewport = Pick<\n  DimensionSettingsState,\n  | 'indexes'\n  | 'positionIndexes'\n  | 'positionIndexToItem'\n  | 'sizes'\n  | 'originItemSize'\n  | 'realSize'\n>;\n\nexport type ItemsToUpdate = Pick<ViewportStateItems, 'items' | 'start' | 'end'>;\n/**\n * Update items based on new scroll position\n * If viewport wasn't changed fully simple recombination of positions\n * Otherwise rebuild viewport items\n */\nexport function getUpdatedItemsByPosition<T extends ItemsToUpdate>(\n  pos: number, // coordinate\n  items: T,\n  realCount: number,\n  virtualSize: number,\n  dimension: DimensionDataViewport,\n): ItemsToUpdate {\n  const activeItem: PositionItem = getItemByPosition(dimension, pos);\n  const firstItem = getFirstItem(items);\n  let toUpdate: ItemsToUpdate | undefined;\n  // do simple position recombination if items already present in viewport\n  if (firstItem) {\n    let changedOffsetStart = activeItem.itemIndex - (firstItem.itemIndex || 0);\n    // if item changed\n    if (changedOffsetStart) {\n      // simple recombination\n      toUpdate = recombineByOffset(Math.abs(changedOffsetStart), {\n        positiveDirection: changedOffsetStart > -1,\n        ...dimension,\n        ...items,\n      });\n    }\n  }\n\n  const maxSizeVirtualSize = getMaxVirtualSize(\n    virtualSize,\n    dimension.realSize,\n    activeItem,\n  );\n  // if partial recombination add items if revo-viewport has some space left\n  if (toUpdate) {\n    const extra = addMissingItems(\n      activeItem,\n      realCount,\n      maxSizeVirtualSize,\n      toUpdate,\n      dimension,\n    );\n    if (extra.length) {\n      updateMissingAndRange(toUpdate.items, extra, toUpdate);\n    }\n  }\n\n  // new collection if no items after replacement full replacement\n  if (!toUpdate) {\n    const items = getItems({\n      firstItemStart: activeItem.start,\n      firstItemIndex: activeItem.itemIndex,\n      origSize: dimension.originItemSize,\n      maxSize: maxSizeVirtualSize,\n      maxCount: realCount,\n      sizes: dimension.sizes,\n    });\n\n    // range now comes from 0 to length - 1\n    toUpdate = {\n      items,\n      start: 0,\n      end: items.length - 1,\n    };\n  }\n  return toUpdate;\n}\n\n// virtual size can differ based on scroll position if some big items are present\n// scroll can be in the middle of item and virtual size will be larger\n// so we need to exclude this part from virtual size hence it's already passed\nfunction getMaxVirtualSize(\n  virtualSize: number,\n  realSize: number,\n  activeItem: PositionItem,\n) {\n  return Math.min(virtualSize + (activeItem.end - activeItem.start), realSize);\n}\n\nexport function updateMissingAndRange(\n  items: VirtualPositionItem[],\n  missing: VirtualPositionItem[],\n  range: Range,\n) {\n  items.splice(range.end + 1, 0, ...missing);\n  // update range if start larger after recombination\n  if (\n    range.start >= range.end &&\n    !(range.start === range.end && range.start === 0)\n  ) {\n    range.start += missing.length;\n  }\n  range.end += missing.length;\n}\n\n/**\n * If partial replacement\n * this function adds items if viewport has some space left\n */\nexport function addMissingItems<T extends ItemsToUpdate>(\n  firstItem: PositionItem,\n  realCount: number,\n  virtualSize: number,\n  existingCollection: T,\n  dimension: Pick<DimensionSettingsState, 'sizes' | 'originItemSize'>,\n): VirtualPositionItem[] {\n  const lastItem: VirtualPositionItem = getLastItem(existingCollection);\n  const items = getItems({\n    sizes: dimension.sizes,\n    firstItemStart: lastItem.end,\n    firstItemIndex: lastItem.itemIndex + 1,\n    origSize: dimension.originItemSize,\n    maxSize: virtualSize - (lastItem.end - firstItem.start),\n    maxCount: realCount,\n  });\n  return items;\n}\n\n/**\n * Get wiewport items parameters\n * caching position and calculating items count in viewport\n */\nexport function getItems(\n  opt: {\n    firstItemIndex: number;\n    firstItemStart: number;\n    origSize: number;\n    maxSize: number; // virtual size\n    maxCount: number; // real item count, where the last item\n    sizes?: ViewSettingSizeProp;\n  },\n  currentSize = 0,\n) {\n  const items: VirtualPositionItem[] = [];\n\n  let index = opt.firstItemIndex;\n  let size = currentSize;\n\n  // max size or max count\n  while (size <= opt.maxSize && index < opt.maxCount) {\n    const newSize = getItemSize(index, opt.sizes, opt.origSize);\n    items.push({\n      start: opt.firstItemStart + size,\n      end: opt.firstItemStart + size + newSize,\n      itemIndex: index,\n      size: newSize,\n    });\n    size += newSize;\n    index++;\n  }\n  return items;\n}\n\n/**\n * Do batch items recombination\n * If items not overlapped with existing viewport returns null\n */\ntype RecombindDimensionData = Pick<\n  DimensionSettingsState,\n  'sizes' | 'realSize' | 'originItemSize'\n>;\ntype RecombineOffsetData = {\n  positiveDirection: boolean;\n} & ItemsToUpdate &\n  RecombindDimensionData;\nexport function recombineByOffset(\n  offset: number,\n  data: RecombineOffsetData,\n): ItemsToUpdate | undefined {\n  const newItems = [...data.items];\n  const itemsCount = newItems.length;\n  let newRange = {\n    start: data.start,\n    end: data.end,\n  };\n\n  // if offset out of revo-viewport, makes sense whole redraw\n  if (offset > itemsCount) {\n    return undefined;\n  }\n\n  // is direction of scroll positive\n  if (data.positiveDirection) {\n    // push item to the end\n    let lastItem: VirtualPositionItem = getLastItem(data);\n\n    let i = newRange.start;\n    const length = i + offset;\n    for (; i < length; i++) {\n      const newIndex = lastItem.itemIndex + 1;\n      const size = getItemSize(\n        newIndex,\n        data.sizes,\n        data.originItemSize,\n      );\n\n      // if item overlapped limit break a loop\n      if (lastItem.end + size > data.realSize) {\n        break;\n      }\n\n      // new item index to recombine\n      let newEnd = i % itemsCount;\n\n      // item should always present, we do not create new item, we recombine them\n      if (!newItems[newEnd]) {\n        throw new Error('incorrect index');\n      }\n\n      // do recombination\n      newItems[newEnd] = lastItem = {\n        start: lastItem.end,\n        end: lastItem.end + size,\n        itemIndex: newIndex,\n        size: size,\n      };\n      // update range\n      newRange.start++;\n      newRange.end = newEnd;\n    }\n\n    // direction is negative\n  } else {\n    // push item to the start\n    let firstItem = getFirstItem(data);\n\n    const end = newRange.end;\n    for (let i = 0; i < offset; i++) {\n      const newIndex = (firstItem?.itemIndex ?? 0) - 1;\n      const size = getItemSize(\n        newIndex,\n        data.sizes,\n        data.originItemSize,\n      );\n\n      // new item index to recombine\n      let newStart = end - i;\n      newStart = (newStart < 0 ? itemsCount + newStart : newStart) % itemsCount;\n\n      // item should always present, we do not create new item, we recombine them\n      if (!newItems[newStart]) {\n        console.error('incorrect index');\n        break;\n      }\n\n      // do recombination\n      const firstItemStart = firstItem?.start ?? 0;\n      newItems[newStart] = firstItem = {\n        start: firstItemStart - size,\n        end: firstItemStart,\n        itemIndex: newIndex,\n        size: size,\n      };\n      // update range\n      newRange.start = newStart;\n      newRange.end--;\n    }\n  }\n  const range = {\n    start:\n      (newRange.start < 0 ? itemsCount + newRange.start : newRange.start) %\n      itemsCount,\n    end:\n      (newRange.end < 0 ? itemsCount + newRange.end : newRange.end) %\n      itemsCount,\n  };\n  return {\n    items: newItems,\n    ...range,\n  };\n}\n\nfunction getItemSize(\n  index: number,\n  sizes?: ViewSettingSizeProp,\n  origSize = 0,\n): number {\n  if (sizes && sizes[index]) {\n    return sizes[index];\n  }\n  return origSize;\n}\n\n/**\n * Verify if position is in range of the PositionItem, start and end are included\n */\nexport function isActiveRange(\n  pos: number,\n  realSize: number,\n  first?: PositionItem,\n  last?: PositionItem,\n): boolean {\n  if (!first || !last) {\n    return false;\n  }\n  // if position is in range of first item\n  // or position is after first item and last item is the last item in real size\n  return (\n    (pos >= first.start && pos <= first.end) ||\n    (pos > first.end && last.end === realSize)\n  );\n}\n\nexport function isActiveRangeOutsideLastItem(\n  pos: number,\n  virtualSize: number,\n  firstItem?: PositionItem,\n  lastItem?: PositionItem,\n) {\n  // if no first item, means no items in viewport\n  if (!firstItem) {\n    return false;\n  }\n  return virtualSize + pos > (lastItem?.end ?? 0);\n}\n\nexport function getFirstItem(\n  s: ItemsToUpdate,\n) {\n  return s.items[s.start];\n}\n\nexport function getLastItem(s: ItemsToUpdate): VirtualPositionItem {\n  return s.items[s.end];\n}\n\n/**\n * Set items sizes from start index to end\n * @param vpItems\n * @param start\n * @param size\n * @param lastCoordinate\n * @returns\n */\nexport function setItemSizes(\n  vpItems: VirtualPositionItem[],\n  initialIndex: number,\n  size: number,\n  lastCoordinate: number,\n) {\n  const items = [...vpItems];\n  const count = items.length;\n\n  let pos = lastCoordinate;\n  let i = 0;\n  let start = initialIndex;\n\n  // viewport not inited\n  if (!count) {\n    return [];\n  }\n  // loop through array from initial item after recombination\n  while (i < count) {\n    const item = items[start];\n    item.start = pos;\n    item.size = size;\n    item.end = item.start + size;\n    pos = item.end;\n    // loop by start index\n    start++;\n    i++;\n\n    // if start index out of array, reset it\n    if (start === count) {\n      start = 0;\n    }\n  }\n  return items;\n}\n", "import {\n  DimensionDataViewport,\n  addMissingItems,\n  getFirstItem,\n  getLastItem,\n  getUpdatedItemsByPosition,\n  isActiveRange,\n  setItemSizes,\n  updateMissingAndRange,\n  isActiveRangeOutsideLastItem,\n  ItemsToUpdate,\n} from './viewport.helpers';\nimport { createStore } from '@stencil/store';\nimport { type Observable, setStore } from '../../utils';\nimport type {\n  VirtualPositionItem,\n  ViewportState,\n  MultiDimensionType,\n} from '@type';\n\n/**\n * Viewport store\n * Used for virtualization (process of rendering only visible part of data)\n * Redraws viewport based on position and dimension\n */\nfunction initialState(): ViewportState {\n  return {\n    // virtual item information per rendered item\n    items: [],\n    // virtual dom item order to render\n    start: 0,\n    end: 0,\n\n    // size of virtual viewport in px\n    virtualSize: 0,\n\n    // total number of items\n    realCount: 0,\n\n    // size of viewport in px\n    clientSize: 0,\n  };\n}\n\n/**\n * Viewport store class\n */\nexport class ViewportStore {\n  readonly store: Observable<ViewportState>;\n\n  // last coordinate for store position restore\n  private lastKnownScroll = 0;\n\n  get lastCoordinate() {\n    return this.lastKnownScroll;\n  }\n  set lastCoordinate(value: number) {\n    this.lastKnownScroll = value;\n  }\n  constructor(readonly type: MultiDimensionType) {\n    this.store = createStore(initialState());\n  }\n\n  /**\n   * Render viewport based on coordinate\n   * It's the main method for draw\n   * Use force if you want to re-render viewport\n   */\n  setViewPortCoordinate(\n    position: number,\n    dimension: DimensionDataViewport,\n    force = false,\n  ) {\n    const viewportSize = this.store.get('virtualSize');\n    // no visible data to calculate\n    if (!viewportSize) {\n      return;\n    }\n\n    const frameOffset = 1;\n    const singleOffsetInPx = dimension.originItemSize * frameOffset;\n    // add offset to virtual size from both sides\n    const outsize = singleOffsetInPx * 2;\n    // math virtual size is based on visible area + 2 items outside of visible area\n    const virtualSize = viewportSize + outsize;\n\n    // expected no scroll if real size less than virtual size, position is 0\n    let maxCoordinate = 0;\n    // if there is nodes outside of viewport, max coordinate has to be adjusted\n    if (dimension.realSize > viewportSize) {\n      // max coordinate is real size minus virtual/rendered space\n      maxCoordinate = dimension.realSize - viewportSize - singleOffsetInPx;\n    }\n\n    let pos = position;\n    // limit position to max and min coordinates\n    if (pos < 0) {\n      pos = 0;\n    } else if (pos > maxCoordinate) {\n      pos = maxCoordinate;\n    }\n\n    // store last coordinate for further restore on redraw\n    this.lastCoordinate = pos;\n\n    // actual position is less than first item start based on offset\n    pos -= singleOffsetInPx;\n    pos = pos < 0 ? 0 : pos < maxCoordinate ? pos : maxCoordinate;\n\n    let allItems: ItemsToUpdate;\n    // if force clear all items and start from 0\n    if (force) {\n      allItems = {\n        items: [],\n        start: 0,\n        end: 0,\n      };\n    } else {\n      allItems = this.getItems();\n    }\n\n    const firstItem: VirtualPositionItem | undefined = getFirstItem(allItems);\n    const lastItem: VirtualPositionItem | undefined = getLastItem(allItems);\n\n    let toUpdate: Partial<ViewportState> = {};\n\n    // left position changed\n    // verify if new position is in range of previously rendered first item\n    if (!isActiveRange(pos, dimension.realSize, firstItem, lastItem)) {\n      toUpdate = {\n        ...toUpdate,\n        ...getUpdatedItemsByPosition(\n          pos,\n          allItems,\n          this.store.get('realCount'),\n          virtualSize,\n          dimension,\n        ),\n      };\n      this.setViewport({ ...toUpdate });\n      // verify is render area is outside of last item\n    } else if (\n      isActiveRangeOutsideLastItem(pos, virtualSize, firstItem, lastItem)\n    ) {\n      const items = [...allItems.items];\n      // check is any item missing for fulfill content\n      const missing = addMissingItems(\n        firstItem,\n        this.store.get('realCount'),\n        virtualSize + pos - firstItem.start,\n        allItems,\n        {\n          sizes: dimension.sizes,\n          originItemSize: dimension.originItemSize,\n        },\n      );\n\n      // update missing items\n      if (missing.length) {\n        const range = {\n          start: this.store.get('start'),\n          end: this.store.get('end'),\n        };\n        updateMissingAndRange(items, missing, range);\n        toUpdate = {\n          ...toUpdate,\n          items: [...items],\n          ...range,\n        };\n        this.setViewport({ ...toUpdate });\n      }\n    }\n  }\n\n  /**\n   * Set sizes for existing items\n   */\n  setOriginalSizes(size: number) {\n    const items = this.store.get('items');\n    const count = items.length;\n    // viewport not inited\n    if (!count) {\n      return;\n    }\n\n    setStore(this.store, {\n      items: setItemSizes(\n        items,\n        this.store.get('start'),\n        size,\n        this.lastCoordinate,\n      ),\n    });\n  }\n\n  getItems(): ItemsToUpdate {\n    return {\n      items: this.store.get('items'),\n      start: this.store.get('start'),\n      end: this.store.get('end'),\n    };\n  }\n\n  setViewport(data: Partial<ViewportState>) {\n    // drop items on virtual size change, require a new item set\n    // drop items on real size change, require a new item set\n    if (typeof data.realCount === 'number' || typeof data.virtualSize === 'number') {\n      data = { ...data, items: data.items || [] };\n    }\n    setStore(this.store, data);\n  }\n}\n", "import type { VNode } from '@stencil/core';\nimport type { HyperFunc } from '@type';\n\ntype HeaderRender = {\n  (start: number): (h: HyperFunc<VNode>, e: { rowIndex: number }) => number;\n};\nexport const RowHeaderRender: HeaderRender = s => (__, { rowIndex: i }) => s + i;\n", "import { h, Host, Component, Prop, Event, EventEmitter } from '@stencil/core';\nimport type { JSXBase } from '@stencil/core/internal';\n\nimport { ViewportStore, DataStore } from '@store';\nimport type {\n  RowHeaders,\n  ViewPortScrollEvent,\n  DataType,\n  ColumnRegular,\n  ViewportData,\n  ElementScroll,\n  DimensionRows,\n  DimensionCols,\n} from '@type';\n\nimport { ROW_HEADER_TYPE } from '../../utils/consts';\nimport { RowHeaderRender } from './row-header-render';\nimport { calculateRowHeaderSize } from '../../utils/row-header-utils';\nimport { HEADER_SLOT } from '../revoGrid/viewport.helpers';\nimport { type JSX } from '../../components';\n\n/**\n * Row headers component\n * Visible on the left side of the table\n */\n\n@Component({ tag: 'revogr-row-headers' })\nexport class RevogrRowHeaders {\n  // #region Properties\n  /**\n   * Header height to setup row headers\n   */\n  @Prop() height: number;\n\n  /**\n   * Viewport data\n   */\n  @Prop() dataPorts: ViewportData[];\n  /**\n   * Header props\n   */\n  @Prop() headerProp: Record<string, any>;\n\n  /**\n   * Row class\n   */\n  @Prop() rowClass: string;\n\n  /**\n   * Enable resize\n   */\n  @Prop() resize: boolean;\n  /**\n   * Row header column\n   */\n  @Prop() rowHeaderColumn: RowHeaders;\n  /**\n   * Additional data to pass to renderer\n   */\n  @Prop() additionalData: any;\n  /**\n   * Prevent rendering until job is done.\n   * Can be used for initial rendering performance improvement.\n   * When several plugins require initial rendering this will prevent double initial rendering.\n   */\n  @Prop() jobsBeforeRender: Promise<any>[] = [];\n  // #endregion\n\n  /**\n   * Scroll viewport\n   */\n  @Event({ eventName: 'scrollview', bubbles: false })\n  scrollViewport: EventEmitter<ViewPortScrollEvent>;\n  /**\n   * Register element to scroll\n   */\n  @Event({ eventName: 'ref', bubbles: false })\n  elementToScroll: EventEmitter<ElementScroll>;\n\n  render() {\n    const dataViews: HTMLElement[] = [];\n    const viewport = new ViewportStore('colPinStart');\n\n    /** render viewports rows */\n    let totalLength = 1;\n    // todo: this part could be optimized to avoid to often re-render dataPorts can be cached\n    for (let data of this.dataPorts) {\n      const itemCount = data.dataStore.get('items').length;\n\n      // initiate row data\n      const dataStore = new DataStore<DataType, DimensionRows>(data.type, {\n        ...data.dataStore.state,\n      });\n\n      // initiate column data\n      const colData = new DataStore<ColumnRegular, DimensionCols>(\n        'colPinStart',\n      );\n      const column: ColumnRegular = {\n        cellTemplate: RowHeaderRender(totalLength),\n        ...this.rowHeaderColumn,\n      };\n      colData.updateData([column]);\n      dataViews.push(\n        <revogr-data\n          {...data}\n          colType=\"rowHeaders\"\n          jobsBeforeRender={this.jobsBeforeRender}\n          rowClass={this.rowClass}\n          dataStore={dataStore.store}\n          colData={colData.store}\n          viewportCol={viewport.store}\n          readonly={true}\n          range={false}\n        />,\n      );\n      totalLength += itemCount;\n    }\n\n    const colSize = calculateRowHeaderSize(totalLength, this.rowHeaderColumn);\n    viewport.setViewport({\n      realCount: 1,\n      virtualSize: 0,\n      items: [\n        {\n          size: colSize,\n          start: 0,\n          end: colSize,\n          itemIndex: 0,\n        },\n      ],\n    });\n\n    const viewportScroll: JSX.RevogrViewportScroll &\n      JSXBase.HTMLAttributes<HTMLRevogrViewportScrollElement> = {\n      contentHeight: this.height,\n      contentWidth: 0,\n      style: { minWidth: `${colSize}px` },\n      colType: 'rowHeaders',\n      ref: (el) => this.elementToScroll.emit(el),\n      onScrollviewport: (e: CustomEvent) => this.scrollViewport.emit(e.detail),\n    };\n    const viewportHeader: JSX.RevogrHeader & { slot: string } = {\n      ...this.headerProp,\n      // groups not present on row headers\n      groups: [],\n      colData:\n        typeof this.rowHeaderColumn === 'object' ? [this.rowHeaderColumn] : [],\n      viewportCol: viewport.store,\n      canResize: false,\n      type: ROW_HEADER_TYPE,\n      // parent,\n      slot: HEADER_SLOT,\n    };\n    return (\n      <Host class={{ [ROW_HEADER_TYPE]: true }} key={ROW_HEADER_TYPE}>\n        <revogr-viewport-scroll {...viewportScroll} row-header={true}>\n          <revogr-header {...viewportHeader} />\n          {dataViews}\n        </revogr-viewport-scroll>\n      </Host>\n    );\n  }\n}\n"], "version": 3}