/*!
 * Built by Revolist OU ❤️
 */
import { proxyCustomElement, HTMLElement, createEvent, h, Host } from '@stencil/core/internal/client';
import { E as EDIT_INPUT_WR } from './consts.js';
import { K as KeyCodesEnum, O as OsPlatform, c as codesLetter, k as keyValues } from './platform.js';
import { t as timeout } from './index2.js';

function isMetaKey(code) {
    const keys = [
        KeyCodesEnum.ARROW_DOWN,
        KeyCodesEnum.ARROW_UP,
        KeyCodesEnum.ARROW_LEFT,
        KeyCodesEnum.ARROW_RIGHT,
        KeyCodesEnum.HOME,
        KeyCodesEnum.END,
        KeyCodesEnum.DELETE,
        KeyCodesEnum.BACKSPACE,
        KeyCodesEnum.F1,
        KeyCodesEnum.F2,
        KeyCodesEnum.F3,
        KeyCodesEnum.F4,
        KeyCodesEnum.F5,
        KeyCodesEnum.F6,
        KeyCodesEnum.F7,
        KeyCodesEnum.F8,
        KeyCodesEnum.F9,
        KeyCodesEnum.F10,
        KeyCodesEnum.F11,
        KeyCodesEnum.F12,
        KeyCodesEnum.TAB,
        KeyCodesEnum.PAGE_DOWN,
        KeyCodesEnum.PAGE_UP,
        KeyCodesEnum.ENTER,
        KeyCodesEnum.ESCAPE,
        KeyCodesEnum.SHIFT,
        KeyCodesEnum.CAPS_LOCK,
        KeyCodesEnum.ALT,
    ];
    return keys.indexOf(code) !== -1;
}
// navigator.platform
function isCtrlKey(code, platform) {
    if (platform.includes(OsPlatform.mac)) {
        return [
            KeyCodesEnum.COMMAND_LEFT,
            KeyCodesEnum.COMMAND_RIGHT,
            KeyCodesEnum.COMMAND_FIREFOX,
        ].includes(code);
    }
    return code === KeyCodesEnum.CONTROL;
}
function isCtrlMetaKey(code) {
    return [
        KeyCodesEnum.CONTROL,
        KeyCodesEnum.COMMAND_LEFT,
        KeyCodesEnum.COMMAND_RIGHT,
        KeyCodesEnum.COMMAND_FIREFOX,
    ].includes(code);
}
function isClear(code) {
    return codesLetter.BACKSPACE === code || codesLetter.DELETE === code;
}
function isTab(code) {
    return codesLetter.TAB === code;
}
function isTabKeyValue(key) {
    return keyValues.TAB === key;
}
function isEnterKeyValue(key) {
    return keyValues.ENTER === key;
}
function isCut(event) {
    return ((event.ctrlKey && event.code === 'KeyX') || // Ctrl + X on Windows
        (event.metaKey && event.code === 'KeyX')); // Cmd + X on Mac
}
function isCopy(event) {
    return ((event.ctrlKey && event.code === 'KeyC') || // Ctrl + C on Windows
        (event.metaKey && event.code === 'KeyC')); // Cmd + C on Mac
}
function isPaste(event) {
    return ((event.ctrlKey && event.code === 'KeyV') || // Ctrl + V on Windows
        (event.metaKey && event.code === 'KeyV')); // Cmd + V on Mac
}
function isAll(event) {
    return ((event.ctrlKey && event.code === 'KeyA') || // Ctrl + A on Windows
        (event.metaKey && event.code === 'KeyA')); // Cmd + A on Mac
}

class TextEditor {
    constructor(data, saveCallback) {
        this.data = data;
        this.saveCallback = saveCallback;
        this.editInput = null;
        this.element = null;
        this.editCell = undefined;
    }
    /**
     * Callback triggered on cell editor render
     */
    async componentDidRender() {
        var _a;
        if (this.editInput) {
            await timeout();
            (_a = this.editInput) === null || _a === void 0 ? void 0 : _a.focus();
        }
    }
    onKeyDown(e) {
        const isEnter = isEnterKeyValue(e.key);
        const isKeyTab = isTab(e.key);
        if ((isKeyTab || isEnter) &&
            e.target &&
            this.saveCallback &&
            !e.isComposing) {
            // blur is needed to avoid autoscroll
            this.beforeDisconnect();
            // request callback which will close cell after all
            this.saveCallback(this.getValue(), isKeyTab);
        }
    }
    /**
     * IMPORTANT: Prevent scroll glitches when editor is closed and focus is on current input element.
     */
    beforeDisconnect() {
        var _a;
        (_a = this.editInput) === null || _a === void 0 ? void 0 : _a.blur();
    }
    /**
     * Get value from input
     */
    getValue() {
        var _a;
        return (_a = this.editInput) === null || _a === void 0 ? void 0 : _a.value;
    }
    /**
     * Render method for Editor plugin.
     * Renders input element with passed data from cell.
     * @param {Function} h - h function from stencil render.
     * @param {Object} _additionalData - additional data from plugin.
     * @returns {VNode} - input element.
     */
    render(h, _additionalData) {
        var _a, _b;
        return h('input', {
            type: 'text',
            enterKeyHint: 'enter',
            // set input value from cell data
            value: (_b = (_a = this.editCell) === null || _a === void 0 ? void 0 : _a.val) !== null && _b !== void 0 ? _b : '',
            // save input element as ref for further usage
            ref: (el) => {
                this.editInput = el;
            },
            // listen to keydown event on input element
            onKeyDown: (e) => this.onKeyDown(e),
        });
    }
}

// is edit input
function isEditInput(el) {
    return !!(el === null || el === void 0 ? void 0 : el.closest(`.${EDIT_INPUT_WR}`));
}
// Type guard for EditorCtrConstructible
function isEditorCtrConstructible(editor) {
    return typeof editor === 'function' && typeof editor.prototype === 'object';
}

const revogrEditStyleCss = ".revo-drag-icon{width:11px;opacity:0.8}.revo-drag-icon::before{content:\"::\"}.revo-alt-icon{-webkit-mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");width:11px;height:11px;background-size:cover;background-repeat:no-repeat}.arrow-down{position:absolute;right:5px;top:0}.arrow-down svg{width:8px;margin-top:5px;margin-left:5px;opacity:0.4}.cell-value-wrapper{margin-right:10px;overflow:hidden;text-overflow:ellipsis}.revo-button{position:relative;overflow:hidden;color:#fff;background-color:#4545ff;height:32px;line-height:32px;padding:0 15px;outline:0;border:0;border-radius:7px;box-sizing:border-box;cursor:pointer}.revo-button.green{background-color:#009037}.revo-button.red{background-color:#E0662E}.revo-button:disabled,.revo-button[disabled]{cursor:not-allowed !important;filter:opacity(0.35) !important}.revo-button.outline{border:1px solid #dbdbdb;line-height:30px;background:none;color:#000;box-shadow:none}revo-grid[theme^=dark] .revo-button.outline{border:1px solid #404040;color:#d8d8d8}revogr-edit{display:block;position:absolute;background-color:#fff}revogr-edit input{height:100%;width:100%;box-sizing:border-box}revogr-edit revo-dropdown{height:100%}revogr-edit revo-dropdown.shrink fieldset legend>span{display:none}";

const RevoEdit = /*@__PURE__*/ proxyCustomElement(class RevoEdit extends HTMLElement {
    constructor() {
        super();
        this.__registerHost();
        this.cellEdit = createEvent(this, "celleditinit", 7);
        this.closeEdit = createEvent(this, "closeedit", 7);
        /**
         * Save on editor close. Defines if data should be saved on editor close.
         */
        this.saveOnClose = false;
        this.currentEditor = null;
        this.preventSaveOnClose = false;
    }
    /**
     * Cancel pending changes flag. Editor will be closed without autosave.
     */
    async cancelChanges() {
        this.preventSaveOnClose = true;
    }
    /**
     * Before editor got disconnected.
     * Can be triggered multiple times before actual disconnect.
     */
    async beforeDisconnect() {
        var _a, _b;
        (_b = (_a = this.currentEditor) === null || _a === void 0 ? void 0 : _a.beforeDisconnect) === null || _b === void 0 ? void 0 : _b.call(_a);
    }
    onAutoSave() {
        var _a, _b, _c;
        this.preventSaveOnClose = true;
        const val = (_b = (_a = this.currentEditor) === null || _a === void 0 ? void 0 : _a.getValue) === null || _b === void 0 ? void 0 : _b.call(_a);
        // For Editor plugin internal usage.
        // When you want to prevent save and use custom save of your own.
        if ((_c = this.currentEditor) === null || _c === void 0 ? void 0 : _c.beforeAutoSave) {
            const canSave = this.currentEditor.beforeAutoSave(val);
            if (canSave === false) {
                return;
            }
        }
        this.onSave(val, true);
    }
    /**
     * Callback triggered when cell editor saved.
     * Closes editor when called.
     * @param preventFocus - if true, editor will not be closed & next cell will not be focused.
     */
    onSave(val, preventFocus) {
        this.preventSaveOnClose = true;
        if (this.editCell) {
            this.cellEdit.emit({
                rgCol: this.editCell.x,
                rgRow: this.editCell.y,
                type: this.editCell.type,
                prop: this.editCell.prop,
                val,
                preventFocus,
            });
        }
    }
    componentWillRender() {
        // Active editor present and not yet closed.
        if (this.currentEditor || !this.column) {
            return;
        }
        this.preventSaveOnClose = false;
        // Custom editor usage.
        // Start with TextEditor (editors/text.tsx) for Custom editor.
        // It can be class or function
        if (this.editor) {
            // if editor is constructible
            if (isEditorCtrConstructible(this.editor)) {
                this.currentEditor = new this.editor(this.column, 
                // save callback
                (e, preventFocus) => {
                    this.onSave(e, preventFocus);
                }, 
                // cancel callback
                focusNext => {
                    this.preventSaveOnClose = true;
                    this.closeEdit.emit(focusNext);
                });
                // if editor is function
            }
            else {
                this.currentEditor = this.editor(this.column, 
                // save callback
                (e, preventFocus) => {
                    this.onSave(e, preventFocus);
                }, 
                // cancel callback
                focusNext => {
                    this.preventSaveOnClose = true;
                    this.closeEdit.emit(focusNext);
                });
            }
            return;
        }
        // Default text editor usage
        this.currentEditor = new TextEditor(this.column, (e, preventFocus) => this.onSave(e, preventFocus));
    }
    componentDidRender() {
        var _a, _b;
        if (!this.currentEditor) {
            return;
        }
        this.currentEditor.element = this.element.firstElementChild;
        (_b = (_a = this.currentEditor).componentDidRender) === null || _b === void 0 ? void 0 : _b.call(_a);
    }
    disconnectedCallback() {
        var _a, _b;
        if (this.saveOnClose) {
            // Can not be cancelled by `preventSaveOnClose` prop.
            // Editor requires `getValue` to be able to save.
            if (!this.preventSaveOnClose) {
                this.onAutoSave();
            }
        }
        this.preventSaveOnClose = false;
        if (!this.currentEditor) {
            return;
        }
        (_b = (_a = this.currentEditor).disconnectedCallback) === null || _b === void 0 ? void 0 : _b.call(_a);
        this.currentEditor.element = null;
        this.currentEditor = null;
    }
    render() {
        if (this.currentEditor) {
            this.currentEditor.editCell = this.editCell;
            return (h(Host, { class: EDIT_INPUT_WR }, this.currentEditor.render(h, this.additionalData)));
        }
        return '';
    }
    get element() { return this; }
    static get style() { return revogrEditStyleCss; }
}, [0, "revogr-edit", {
        "editCell": [16, "edit-cell"],
        "column": [16],
        "editor": [16],
        "saveOnClose": [4, "save-on-close"],
        "additionalData": [8, "additional-data"],
        "cancelChanges": [64],
        "beforeDisconnect": [64]
    }]);
function defineCustomElement() {
    if (typeof customElements === "undefined") {
        return;
    }
    const components = ["revogr-edit"];
    components.forEach(tagName => { switch (tagName) {
        case "revogr-edit":
            if (!customElements.get(tagName)) {
                customElements.define(tagName, RevoEdit);
            }
            break;
    } });
}

export { RevoEdit as R, TextEditor as T, isCtrlKey as a, isCtrlMetaKey as b, isClear as c, isTab as d, isTabKeyValue as e, isEnterKeyValue as f, isCut as g, isCopy as h, isMetaKey as i, isPaste as j, isAll as k, isEditInput as l, isEditorCtrConstructible as m, defineCustomElement as n };
//# sourceMappingURL=revogr-edit2.js.map

//# sourceMappingURL=revogr-edit2.js.map