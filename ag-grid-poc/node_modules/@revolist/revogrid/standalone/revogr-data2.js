/*!
 * Built by Revolist OU ❤️
 */
import { h, Build, proxyCustomElement, HTMLElement as HTMLElement$1, createEvent, Host } from '@stencil/core/internal/client';
import { m as GROUP_EXPAND_BTN, o as GROUP_EXPAND_EVENT, G as GROUP_DEPTH, j as GROUP_EXPANDED, P as PSEUDO_GROUP_ITEM, T as isRowDragService, C as getCellDataParsed, Q as ColumnService, v as isGrouping } from './column.service.js';
import { a as DATA_ROW, h as DRAGGABLE_CLASS, g as DRAG_ICON_CLASS, l as ROW_FOCUSED_CLASS, D as DATA_COL } from './consts.js';
import { b as getSourceItem } from './data.store.js';
import './platform.js';
import { d as defineCustomElement$1 } from './vnode-converter.js';

const PADDING_DEPTH = 10;
const RowRenderer = ({ rowClass, index, size, start, depth }, cells) => {
    const props = Object.assign({ [DATA_ROW]: index });
    return (h("div", Object.assign({}, props, { class: `rgRow ${rowClass || ''}`, style: {
            height: `${size}px`,
            transform: `translateY(${start}px)`,
            paddingLeft: depth ? `${PADDING_DEPTH * depth}px` : undefined,
        } }), cells));
};

function expandEvent(e, model, virtualIndex) {
    var _a;
    const event = new CustomEvent(GROUP_EXPAND_EVENT, {
        detail: {
            model,
            virtualIndex,
        },
        cancelable: true,
        bubbles: true,
    });
    (_a = e.target) === null || _a === void 0 ? void 0 : _a.dispatchEvent(event);
}
const GroupingRowRenderer = (props) => {
    const { model, itemIndex, hasExpand, groupingCustomRenderer } = props;
    const name = model[PSEUDO_GROUP_ITEM];
    const expanded = model[GROUP_EXPANDED];
    const depth = parseInt(model[GROUP_DEPTH], 10) || 0;
    if (groupingCustomRenderer) {
        return (h(RowRenderer, Object.assign({}, props, { rowClass: "groupingRow", depth: depth }),
            h("div", { onClick: e => expandEvent(e, model, itemIndex) }, groupingCustomRenderer(h, Object.assign(Object.assign({}, props), { colType: props.providers.colType, name,
                expanded,
                depth })))));
    }
    return (h(RowRenderer, Object.assign({}, props, { rowClass: "groupingRow", depth: depth }), hasExpand && [
        h("button", { class: { [GROUP_EXPAND_BTN]: true }, onClick: e => expandEvent(e, model, itemIndex) }, expandSvgIconVNode(expanded)),
        name,
    ]));
};
const expandSvgIconVNode = (expanded = false) => {
    return (h("svg", { "aria-hidden": "true", style: { transform: `rotate(${!expanded ? -90 : 0}deg)` }, focusable: "false", viewBox: "0 0 448 512" },
        h("path", { fill: "currentColor", d: "M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z" })));
};

function renderCell(v) {
    var _a;
    const els = [];
    // #region Custom cell
    const template = (_a = v.schemaModel.column) === null || _a === void 0 ? void 0 : _a.cellTemplate;
    if (template) {
        els.push(template(h, v.schemaModel, v.additionalData));
    }
    // #endregion
    // #region Regular cell
    else {
        if (!v.schemaModel.column) {
            // something is wrong with data
            if (Build === null || Build === void 0 ? void 0 : Build.isDev) {
                console.error('Investigate column problem.', v.schemaModel);
            }
            return '';
        }
        // Row drag
        if (v.schemaModel.column.rowDrag &&
            isRowDragService(v.schemaModel.column.rowDrag, v.schemaModel)) {
            els.push(h("span", { class: DRAGGABLE_CLASS, onMouseDown: originalEvent => {
                    var _a;
                    return (_a = v.dragStartCell) === null || _a === void 0 ? void 0 : _a.emit({
                        originalEvent,
                        model: v.schemaModel,
                    });
                } },
                h("span", { class: DRAG_ICON_CLASS })));
        }
        els.push(`${getCellDataParsed(v.schemaModel.model, v.schemaModel.column)}`);
    }
    return els;
}
const CellRenderer = ({ renderProps, cellProps, }) => {
    const render = renderCell.bind(null, renderProps);
    return (h("div", Object.assign({}, cellProps, { redraw: render }), render()));
};

/**
 * Class is responsible for highlighting rows in a table.
 */
class RowHighlightPlugin {
    constructor() {
        this.currentRange = null;
    }
    selectionChange(e, renderedRows) {
        // clear previous range
        if (this.currentRange) {
            renderedRows.forEach((row, y) => {
                var _a;
                // skip current range
                if (e && y >= e.y && y <= e.y1) {
                    return;
                }
                // clear previous range
                if (row &&
                    row.$elm$ instanceof HTMLElement &&
                    row.$elm$.classList.contains(ROW_FOCUSED_CLASS)) {
                    row.$elm$.classList.remove(ROW_FOCUSED_CLASS);
                    if ((_a = row.$attrs$) === null || _a === void 0 ? void 0 : _a.class.includes(ROW_FOCUSED_CLASS)) {
                        row.$attrs$.class = row.$attrs$.class.replace(ROW_FOCUSED_CLASS, '');
                    }
                }
            });
        }
        // apply new range
        if (e) {
            for (let y = e.y; y <= e.y1; y++) {
                const row = renderedRows.get(y);
                if (row &&
                    row.$elm$ instanceof HTMLElement &&
                    !row.$elm$.classList.contains(ROW_FOCUSED_CLASS)) {
                    const attrs = (row.$attrs$ = row.$attrs$ || {});
                    attrs.class = (attrs.class || '') + ' ' + ROW_FOCUSED_CLASS;
                    row.$elm$.classList.add(ROW_FOCUSED_CLASS);
                }
            }
        }
        this.currentRange = e;
    }
    isRowFocused(y) {
        return (this.currentRange && y >= this.currentRange.y && y <= this.currentRange.y1);
    }
}

/**
 * Converts a VNode element into an HTML element and appends it to the specified parentHolder.
 */
function convertVNodeToHTML(parentHolder, redraw) {
    return new Promise(resolve => {
        const vnode = document.createElement('vnode-html');
        parentHolder.appendChild(vnode);
        vnode.redraw = redraw;
        vnode.addEventListener('html', e => {
            vnode.remove();
            resolve(e.detail);
        });
    });
}

const revogrDataStyleCss = ".revo-drag-icon{width:11px;opacity:0.8}.revo-drag-icon::before{content:\"::\"}.revo-alt-icon{-webkit-mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");width:11px;height:11px;background-size:cover;background-repeat:no-repeat}.arrow-down{position:absolute;right:5px;top:0}.arrow-down svg{width:8px;margin-top:5px;margin-left:5px;opacity:0.4}.cell-value-wrapper{margin-right:10px;overflow:hidden;text-overflow:ellipsis}.revo-button{position:relative;overflow:hidden;color:#fff;background-color:#4545ff;height:32px;line-height:32px;padding:0 15px;outline:0;border:0;border-radius:7px;box-sizing:border-box;cursor:pointer}.revo-button.green{background-color:#009037}.revo-button.red{background-color:#E0662E}.revo-button:disabled,.revo-button[disabled]{cursor:not-allowed !important;filter:opacity(0.35) !important}.revo-button.outline{border:1px solid #dbdbdb;line-height:30px;background:none;color:#000;box-shadow:none}revo-grid[theme^=dark] .revo-button.outline{border:1px solid #404040;color:#d8d8d8}revogr-data{display:block;width:100%;position:relative}revogr-data .rgRow{position:absolute;width:100%;left:0}revogr-data .rgRow.groupingRow{font-weight:600;text-align:left}revogr-data .rgRow.groupingRow .group-expand{width:25px;height:100%;max-height:25px;margin-right:2px;background-color:transparent;border-color:transparent;vertical-align:middle;padding-left:5px;display:inline-flex}revogr-data .rgRow.groupingRow .group-expand svg{width:7px}revogr-data .revo-draggable{border:none;height:32px;display:inline-flex;outline:0;padding:0;font-size:0.8125rem;box-sizing:border-box;align-items:center;white-space:nowrap;vertical-align:middle;justify-content:center;text-decoration:none;width:24px;height:100%;cursor:pointer;display:inline-flex}revogr-data .revo-draggable:hover>.revo-drag-icon{opacity:1;zoom:1.2;font-weight:600}revogr-data .revo-draggable>.revo-drag-icon{pointer-events:none;transition:opacity 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, zoom 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms}revogr-data .rgCell{top:0;left:0;position:absolute;box-sizing:border-box;height:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;outline:none}revogr-data .rgCell.align-center{text-align:center}revogr-data .rgCell.align-left{text-align:left}revogr-data .rgCell.align-right{text-align:right}";

const RevogrData = /*@__PURE__*/ proxyCustomElement(class RevogrData extends HTMLElement$1 {
    constructor() {
        super();
        this.__registerHost();
        this.beforerowrender = createEvent(this, "beforerowrender", 7);
        this.afterrender = createEvent(this, "afterrender", 7);
        this.beforeCellRender = createEvent(this, "beforecellrender", 7);
        this.beforeDataRender = createEvent(this, "beforedatarender", 7);
        this.dragStartCell = createEvent(this, "dragstartcell", 7);
        /**
         * Prevent rendering until job is done.
         * Can be used for initial rendering performance improvement.
         * When several plugins require initial rendering this will prevent double initial rendering.
         */
        this.jobsBeforeRender = [];
        /**
         * Rendered rows - virtual index vs vnode
         */
        this.renderedRows = new Map();
    }
    /**
     * Pointed cell update.
     */
    async updateCell(e) {
        var _a, _b, _c;
        // Stencil tweak to update cell content
        const cell = (_b = (_a = this.renderedRows.get(e.row)) === null || _a === void 0 ? void 0 : _a.$children$) === null || _b === void 0 ? void 0 : _b[e.col];
        if ((_c = cell === null || cell === void 0 ? void 0 : cell.$attrs$) === null || _c === void 0 ? void 0 : _c.redraw) {
            const children = await convertVNodeToHTML(this.element, cell.$attrs$.redraw);
            cell.$elm$.innerHTML = children.html;
            cell.$key$ = Math.random();
        }
    }
    onDataStoreChange() {
        this.onStoreChange();
    }
    onColDataChange() {
        this.onStoreChange();
    }
    onStoreChange() {
        var _a, _b;
        (_a = this.columnService) === null || _a === void 0 ? void 0 : _a.destroy();
        this.columnService = new ColumnService(this.dataStore, this.colData);
        // make sure we have correct data, before render
        this.providers = {
            type: this.type,
            colType: this.colType,
            readonly: this.readonly,
            data: this.dataStore,
            columns: this.colData,
            viewport: this.viewportCol,
            dimension: this.dimensionRow,
            selection: this.rowSelectionStore,
        };
        (_b = this.rangeUnsubscribe) === null || _b === void 0 ? void 0 : _b.call(this);
        this.rangeUnsubscribe = this.rowSelectionStore.onChange('range', (e) => this.rowHighlightPlugin.selectionChange(e, this.renderedRows));
    }
    connectedCallback() {
        this.rowHighlightPlugin = new RowHighlightPlugin();
        this.onStoreChange();
    }
    disconnectedCallback() {
        var _a, _b;
        (_a = this.columnService) === null || _a === void 0 ? void 0 : _a.destroy();
        (_b = this.rangeUnsubscribe) === null || _b === void 0 ? void 0 : _b.call(this);
    }
    async componentWillRender() {
        this.beforeDataRender.emit({
            rowType: this.type,
            colType: this.colType,
        });
        return Promise.all(this.jobsBeforeRender.map(p => typeof p === 'function' ? p() : p));
    }
    componentDidRender() {
        this.afterrender.emit({ type: this.type });
    }
    render() {
        this.renderedRows = new Map();
        const columnsData = this.columnService.columns;
        if (!columnsData.length) {
            return;
        }
        const rows = this.viewportRow.get('items');
        if (!rows.length) {
            return;
        }
        const cols = this.viewportCol.get('items');
        if (!cols.length) {
            return;
        }
        const rowsEls = [];
        const depth = this.dataStore.get('groupingDepth');
        const groupingCustomRenderer = this.dataStore.get('groupingCustomRenderer');
        const groupDepth = this.columnService.hasGrouping ? depth : 0;
        for (let rgRow of rows) {
            const dataItem = getSourceItem(this.dataStore, rgRow.itemIndex);
            // #region Grouping
            if (isGrouping(dataItem)) {
                const gmodel = Object.assign(Object.assign({}, rgRow), { index: rgRow.itemIndex, model: dataItem, groupingCustomRenderer,
                    // Only show expand button if grouping is enabled and not in row headers
                    hasExpand: this.columnService.hasGrouping && this.colType !== 'rowHeaders', columnItems: cols, providers: this.providers });
                rowsEls.push(h(GroupingRowRenderer, Object.assign({}, gmodel)));
                continue;
            }
            // #endregion
            const cells = [];
            // #region Cells
            for (let rgCol of cols) {
                const smodel = Object.assign(Object.assign({}, this.columnService.rowDataModel(rgRow.itemIndex, rgCol.itemIndex)), { providers: this.providers });
                // call before cell render
                const cellEvent = this.triggerBeforeCellRender(smodel, rgRow, rgCol);
                // if event was prevented
                if (cellEvent.defaultPrevented) {
                    continue;
                }
                const { detail: { column: columnProps, row: rowProps, model: schemaModel }, } = cellEvent;
                const defaultProps = {
                    [DATA_COL]: columnProps.itemIndex,
                    [DATA_ROW]: rowProps.itemIndex,
                    style: {
                        width: `${columnProps.size}px`,
                        transform: `translateX(${columnProps.start}px)`,
                        height: rowProps.size ? `${rowProps.size}px` : undefined,
                    },
                };
                /**
                 * For grouping, can be removed in the future and replaced with event
                 */
                if (groupDepth && !columnProps.itemIndex && defaultProps.style) {
                    defaultProps.style.paddingLeft = `${PADDING_DEPTH * groupDepth}px`;
                }
                const props = this.columnService.mergeProperties(rowProps.itemIndex, columnProps.itemIndex, defaultProps, schemaModel);
                // Never use webcomponent for cell render
                // It's very slow because of webcomponent initialization takes time
                const cellNode = h(CellRenderer, { renderProps: {
                        schemaModel,
                        additionalData: this.additionalData,
                        dragStartCell: this.dragStartCell,
                    }, cellProps: props });
                cells.push(cellNode);
            }
            // #endregion
            // #region Rows
            let rowClass = this.rowClass
                ? this.columnService.getRowClass(rgRow.itemIndex, this.rowClass)
                : '';
            if (this.rowHighlightPlugin.isRowFocused(rgRow.itemIndex)) {
                rowClass += ` ${ROW_FOCUSED_CLASS}`;
            }
            const row = (h(RowRenderer, { index: rgRow.itemIndex, rowClass: rowClass, size: rgRow.size, start: rgRow.start }, cells));
            this.beforerowrender.emit({
                node: row,
                item: rgRow,
                model: dataItem,
                colType: this.columnService.type,
                rowType: this.type,
            });
            rowsEls.push(row);
            this.renderedRows.set(rgRow.itemIndex, row);
            // #endregion
        }
        return (h(Host, null, h("slot", null), rowsEls));
    }
    triggerBeforeCellRender(model, row, column) {
        const detail = {
            column,
            row,
            model,
            rowType: model.type,
            colType: model.colType,
        };
        return this.beforeCellRender.emit(detail);
    }
    get element() { return this; }
    static get watchers() { return {
        "dataStore": ["onDataStoreChange"],
        "colData": ["onColDataChange"]
    }; }
    static get style() { return revogrDataStyleCss; }
}, [4, "revogr-data", {
        "readonly": [4],
        "range": [4],
        "rowClass": [1, "row-class"],
        "additionalData": [8, "additional-data"],
        "rowSelectionStore": [16, "row-selection-store"],
        "viewportRow": [16, "viewport-row"],
        "viewportCol": [16, "viewport-col"],
        "dimensionRow": [16, "dimension-row"],
        "colData": [16, "col-data"],
        "dataStore": [16, "data-store"],
        "type": [513],
        "colType": [513, "col-type"],
        "jobsBeforeRender": [16, "jobs-before-render"],
        "providers": [32],
        "updateCell": [64]
    }, undefined, {
        "dataStore": ["onDataStoreChange"],
        "colData": ["onColDataChange"]
    }]);
function defineCustomElement() {
    if (typeof customElements === "undefined") {
        return;
    }
    const components = ["revogr-data", "vnode-html"];
    components.forEach(tagName => { switch (tagName) {
        case "revogr-data":
            if (!customElements.get(tagName)) {
                customElements.define(tagName, RevogrData);
            }
            break;
        case "vnode-html":
            if (!customElements.get(tagName)) {
                defineCustomElement$1();
            }
            break;
    } });
}

export { CellRenderer as C, GroupingRowRenderer as G, RevogrData as R, expandSvgIconVNode as a, defineCustomElement as d, expandEvent as e };
//# sourceMappingURL=revogr-data2.js.map

//# sourceMappingURL=revogr-data2.js.map