/*!
 * Built by Revolist OU ❤️
 */
import { proxyCustomElement, HTMLElement, createEvent, h, Host } from '@stencil/core/internal/client';

const VNodeToHtml = /*@__PURE__*/ proxyCustomElement(class VNodeToHtml extends HTMLElement {
    constructor() {
        super();
        this.__registerHost();
        this.html = createEvent(this, "html", 7);
        this.redraw = null;
        this.vnodes = [];
    }
    componentDidRender() {
        this.html.emit({
            html: this.el.innerHTML,
            vnodes: this.vnodes,
        });
    }
    render() {
        var _a, _b;
        this.vnodes = (_b = (_a = this.redraw) === null || _a === void 0 ? void 0 : _a.call(this)) !== null && _b !== void 0 ? _b : null;
        return (h(Host, { key: '11b76ca8a86ebf279add88bbd86ef9eb5149605a', style: { visibility: 'hidden', position: 'absolute' } }, this.vnodes));
    }
    get el() { return this; }
}, [0, "vnode-html", {
        "redraw": [16]
    }]);
function defineCustomElement() {
    if (typeof customElements === "undefined") {
        return;
    }
    const components = ["vnode-html"];
    components.forEach(tagName => { switch (tagName) {
        case "vnode-html":
            if (!customElements.get(tagName)) {
                customElements.define(tagName, VNodeToHtml);
            }
            break;
    } });
}

export { VNodeToHtml as V, defineCustomElement as d };
//# sourceMappingURL=vnode-converter.js.map

//# sourceMappingURL=vnode-converter.js.map