/*!
 * Built by Revolist OU ❤️
 */
import { a as isSymbol } from './toNumber.js';
import { k as identity } from './data.store.js';

/** Used as references for the maximum length and index of an array. */
var MAX_ARRAY_LENGTH$1 = 4294967295,
    MAX_ARRAY_INDEX = MAX_ARRAY_LENGTH$1 - 1;

/* Built-in method references for those with the same name as other `lodash` methods. */
var nativeFloor = Math.floor,
    nativeMin = Math.min;

/**
 * The base implementation of `_.sortedIndexBy` and `_.sortedLastIndexBy`
 * which invokes `iteratee` for `value` and each element of `array` to compute
 * their sort ranking. The iteratee is invoked with one argument; (value).
 *
 * @private
 * @param {Array} array The sorted array to inspect.
 * @param {*} value The value to evaluate.
 * @param {Function} iteratee The iteratee invoked per element.
 * @param {boolean} [retHighest] Specify returning the highest qualified index.
 * @returns {number} Returns the index at which `value` should be inserted
 *  into `array`.
 */
function baseSortedIndexBy(array, value, iteratee, retHighest) {
  var low = 0,
      high = array == null ? 0 : array.length;
  if (high === 0) {
    return 0;
  }

  value = iteratee(value);
  var valIsNaN = value !== value,
      valIsNull = value === null,
      valIsSymbol = isSymbol(value),
      valIsUndefined = value === undefined;

  while (low < high) {
    var mid = nativeFloor((low + high) / 2),
        computed = iteratee(array[mid]),
        othIsDefined = computed !== undefined,
        othIsNull = computed === null,
        othIsReflexive = computed === computed,
        othIsSymbol = isSymbol(computed);

    if (valIsNaN) {
      var setLow = othIsReflexive;
    } else if (valIsUndefined) {
      setLow = othIsReflexive && (othIsDefined);
    } else if (valIsNull) {
      setLow = othIsReflexive && othIsDefined && (!othIsNull);
    } else if (valIsSymbol) {
      setLow = othIsReflexive && othIsDefined && !othIsNull && (!othIsSymbol);
    } else if (othIsNull || othIsSymbol) {
      setLow = false;
    } else {
      setLow = (computed < value);
    }
    if (setLow) {
      low = mid + 1;
    } else {
      high = mid;
    }
  }
  return nativeMin(high, MAX_ARRAY_INDEX);
}

/** Used as references for the maximum length and index of an array. */
var MAX_ARRAY_LENGTH = 4294967295,
    HALF_MAX_ARRAY_LENGTH = MAX_ARRAY_LENGTH >>> 1;

/**
 * The base implementation of `_.sortedIndex` and `_.sortedLastIndex` which
 * performs a binary search of `array` to determine the index at which `value`
 * should be inserted into `array` in order to maintain its sort order.
 *
 * @private
 * @param {Array} array The sorted array to inspect.
 * @param {*} value The value to evaluate.
 * @param {boolean} [retHighest] Specify returning the highest qualified index.
 * @returns {number} Returns the index at which `value` should be inserted
 *  into `array`.
 */
function baseSortedIndex(array, value, retHighest) {
  var low = 0,
      high = array == null ? low : array.length;

  if (typeof value == 'number' && value === value && high <= HALF_MAX_ARRAY_LENGTH) {
    while (low < high) {
      var mid = (low + high) >>> 1,
          computed = array[mid];

      if (computed !== null && !isSymbol(computed) &&
          ((computed < value))) {
        low = mid + 1;
      } else {
        high = mid;
      }
    }
    return high;
  }
  return baseSortedIndexBy(array, value, identity);
}

/**
 * Uses a binary search to determine the lowest index at which `value`
 * should be inserted into `array` in order to maintain its sort order.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Array
 * @param {Array} array The sorted array to inspect.
 * @param {*} value The value to evaluate.
 * @returns {number} Returns the index at which `value` should be inserted
 *  into `array`.
 * @example
 *
 * _.sortedIndex([30, 50], 40);
 * // => 1
 */
function sortedIndex(array, value) {
  return baseSortedIndex(array, value);
}

/**
 * Pre-calculation
 * Dimension custom sizes for each cell
 * Keeps only changed sizes, skips origin size
 */
function calculateDimensionData(originItemSize, newSizes = {}) {
    const positionIndexes = [];
    const positionIndexToItem = {};
    const indexToItem = {};
    // prepare order sorted new sizes and calculate changed real size
    const newIndexes = Object.keys(newSizes).map(Number).sort((a, b) => a - b);
    let previous;
    for (let i = 0; i < newIndexes.length; i++) {
        const itemIndex = newIndexes[i];
        const newItem = {
            itemIndex,
            start: 0,
            end: 0,
        };
        // if previous item was changed too
        if (previous) {
            const itemsBetween = (itemIndex - previous.itemIndex - 1) * originItemSize;
            newItem.start = itemsBetween + previous.end;
        }
        else {
            newItem.start = itemIndex * originItemSize;
        }
        newItem.end = newItem.start + newSizes[itemIndex];
        positionIndexes.push(newItem.start);
        indexToItem[itemIndex] = positionIndexToItem[i] = newItem;
        previous = newItem;
    }
    return {
        indexes: newIndexes,
        positionIndexes: [...positionIndexes],
        positionIndexToItem: Object.assign({}, positionIndexToItem),
        indexToItem,
    };
}
/**
 * Calculate item by position
 */
const getItemByPosition = ({ indexes, positionIndexes, originItemSize, positionIndexToItem, }, pos) => {
    const item = {
        itemIndex: 0,
        start: 0,
        end: 0,
    };
    const currentPlace = indexes.length ? sortedIndex(positionIndexes, pos) : 0;
    // not found or first index
    if (!currentPlace) {
        item.itemIndex = Math.floor(pos / originItemSize);
        item.start = item.itemIndex * originItemSize;
        item.end = item.start + originItemSize;
        return item;
    }
    const positionItem = positionIndexToItem[currentPlace - 1];
    // if item has specified size
    if (positionItem.end > pos) {
        return positionItem;
    }
    // special size item was present before
    const relativePos = pos - positionItem.end;
    const relativeIndex = Math.floor(relativePos / originItemSize);
    item.itemIndex = positionItem.itemIndex + 1 + relativeIndex;
    item.start = positionItem.end + relativeIndex * originItemSize;
    item.end = item.start + originItemSize;
    return item;
};
function getItemByIndex(dimension, index) {
    let item = {
        itemIndex: index,
        start: 0,
        end: 0,
    };
    // if item has specified size
    if (dimension.indexToItem[index]) {
        return dimension.indexToItem[index];
    }
    const currentPlace = dimension.indexes.length
        ? sortedIndex(dimension.indexes, index)
        : 0;
    // not found or first index
    if (!currentPlace) {
        item.start = item.itemIndex * dimension.originItemSize;
        item.end = item.start + dimension.originItemSize;
        return item;
    }
    // special size item was present before
    const positionItem = dimension.indexToItem[dimension.indexes[currentPlace - 1]];
    item.start =
        positionItem.end +
            (index - positionItem.itemIndex - 1) * dimension.originItemSize;
    item.end = item.start + dimension.originItemSize;
    return item;
}

export { getItemByIndex as a, calculateDimensionData as c, getItemByPosition as g };
//# sourceMappingURL=dimension.helpers.js.map

//# sourceMappingURL=dimension.helpers.js.map