{"file": "filter.button.js", "mappings": ";;;;;AAGO,MAAM,mBAAmB,GAAG;AAC5B,MAAM,oBAAoB,GAAG;AAE7B,MAAM,WAAW,GAAG;AACpB,MAAM,aAAa,GAAG;AACtB,MAAM,YAAY,GAAG;MAKf,YAAY,GAAG,CAAC,EAAE,MAAM,EAAS,KAAI;AAChD,IAAA,QACE,CAAA,CAAA,MAAA,EAAA,IAAA;AACE,QAAA,CAAA,CAAA,QAAA,EAAA,EACE,KAAK,EAAE;gBACL,CAAC,mBAAmB,GAAG,IAAI;gBAC3B,CAAC,oBAAoB,GAAG,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC;AACxD,aAAA,EAAA;AAED,YAAA,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,YAAY,EAAC,OAAO,EAAC,WAAW,EAAA;gBACzC,CAAG,CAAA,GAAA,EAAA,EAAA,MAAM,EAAC,MAAM,EAAc,cAAA,EAAA,GAAG,EAAC,IAAI,EAAC,MAAM,EAAA,WAAA,EAAW,SAAS,EAAA;AAC/D,oBAAA,CAAA,CAAA,MAAA,EAAA,EAAM,CAAC,EAAC,2GAA2G,EAAC,IAAI,EAAC,cAAc,EAAA,CAAQ,CAC7I,CACA,CACC,CACJ;AAEX;AAEO,MAAM,WAAW,GAAG,MAAK;IAC9B,QACE,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAE,EAAE,CAAC,YAAY,GAAG,IAAI,EAAE,EAAA;AAClC,QAAA,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,WAAW,EAAC,OAAO,EAAC,WAAW,EAAA;YACxC,CAAM,CAAA,MAAA,EAAA,EAAA,IAAI,EAAC,cAAc,EAAC,CAAC,EAAC,8GAA8G,EAAG,CAAA,CACzI,CACF;AAEV;MACa,WAAW,GAAG,CAAC,EAAE,IAAI,EAAO,KAAI;AAC3C,IAAA,OAAO,cAAQ,KAAK,EAAE,EAAE,CAAC,aAAa,GAAG,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,EAAG,EAAA,IAAI,CAAU;AAC7F;AAEM,SAAU,WAAW,CAAC,CAAc,EAAA;IACxC,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;AAC7C,QAAA,OAAO,IAAI;;AAEb,IAAA,OAAO,CAAC,KAAA,IAAA,IAAD,CAAC,KAAA,MAAA,GAAA,MAAA,GAAD,CAAC,CAAE,OAAO,CAAC,CAAI,CAAA,EAAA,mBAAmB,CAAE,CAAA,CAAC;AAC9C;;;;", "names": [], "sources": ["src/plugins/filter/filter.button.tsx"], "sourcesContent": ["import { h } from '@stencil/core';\nimport { ColumnRegular } from '@type';\n\nexport const FILTER_BUTTON_CLASS = 'rv-filter';\nexport const FILTER_BUTTON_ACTIVE = 'active';\n\nexport const FILTER_PROP = 'hasFilter';\nexport const AND_OR_BUTTON = 'and-or-button';\nexport const TRASH_BUTTON = 'trash-button';\n\ntype Props = {\n  column: ColumnRegular;\n};\nexport const FilterButton = ({ column }: Props) => {\n  return (\n    <span>\n      <button\n        class={{\n          [FILTER_BUTTON_CLASS]: true,\n          [FILTER_BUTTON_ACTIVE]: column && !!column[FILTER_PROP],\n        }}\n      >\n        <svg class=\"filter-img\" viewBox=\"0 0 64 64\">\n          <g stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\n            <path d=\"M43,48 L43,56 L21,56 L21,48 L43,48 Z M53,28 L53,36 L12,36 L12,28 L53,28 Z M64,8 L64,16 L0,16 L0,8 L64,8 Z\" fill=\"currentColor\"></path>\n          </g>\n        </svg>\n      </button>\n    </span>\n  );\n};\n\nexport const TrashButton = () => {\n  return (\n    <div class={{ [TRASH_BUTTON]: true }}>\n      <svg class=\"trash-img\" viewBox=\"0 0 24 24\">\n        <path fill=\"currentColor\" d=\"M9,3V4H4V6H5V19A2,2 0 0,0 7,21H17A2,2 0 0,0 19,19V6H20V4H15V3H9M7,6H17V19H7V6M9,8V17H11V8H9M13,8V17H15V8H13Z\" />\n      </svg>\n    </div>\n  );\n};\nexport const AndOrButton = ({ text }: any) => {\n  return <button class={{ [AND_OR_BUTTON]: true, 'light revo-button': true }}>{text}</button>;\n};\n\nexport function isFilterBtn(e: HTMLElement) {\n  if (e.classList.contains(FILTER_BUTTON_CLASS)) {\n    return true;\n  }\n  return e?.closest(`.${FILTER_BUTTON_CLASS}`);\n}\n"], "version": 3}