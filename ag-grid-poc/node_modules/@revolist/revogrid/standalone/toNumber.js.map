{"file": "toNumber.js", "mappings": ";;;AAAA;AACG,IAAC,UAAU,GAAG,OAAO,MAAM,IAAI,QAAQ,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,IAAI;;ACCpF;AACA,IAAI,QAAQ,GAAG,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,IAAI,IAAI;;AAEhF;AACG,IAAC,IAAI,GAAG,UAAU,IAAI,QAAQ,IAAI,QAAQ,CAAC,aAAa,CAAC;;ACJ5D;AACG,IAAC,MAAM,GAAG,IAAI,CAAC;;ACDlB;AACA,IAAIA,aAAW,GAAG,MAAM,CAAC,SAAS;;AAElC;AACA,IAAI,cAAc,GAAGA,aAAW,CAAC,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA,IAAIC,sBAAoB,GAAGD,aAAW,CAAC,QAAQ;;AAE/C;AACA,IAAIE,gBAAc,GAAG,MAAM,GAAG,MAAM,CAAC,WAAW,GAAG,SAAS;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,EAAE,IAAI,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,EAAEA,gBAAc,CAAC;AACxD,MAAM,GAAG,GAAG,KAAK,CAACA,gBAAc,CAAC;;AAEjC,EAAE,IAAI;AACN,IAAI,KAAK,CAACA,gBAAc,CAAC,GAAG,SAAS;AACrC,IAAI,IAAI,QAAQ,GAAG,IAAI;AACvB,GAAG,CAAC,OAAO,CAAC,EAAE;;AAEd,EAAE,IAAI,MAAM,GAAGD,sBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC;AAC/C,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,KAAK,CAACC,gBAAc,CAAC,GAAG,GAAG;AACjC,KAAK,MAAM;AACX,MAAM,OAAO,KAAK,CAACA,gBAAc,CAAC;AAClC;AACA;AACA,EAAE,OAAO,MAAM;AACf;;AC3CA;AACA,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS;;AAElC;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,GAAG,WAAW,CAAC,QAAQ;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC;AACzC;;ACfA;AACA,IAAI,OAAO,GAAG,eAAe;AAC7B,IAAI,YAAY,GAAG,oBAAoB;;AAEvC;AACA,IAAI,cAAc,GAAG,MAAM,GAAG,MAAM,CAAC,WAAW,GAAG,SAAS;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,EAAE,IAAI,KAAK,IAAI,IAAI,EAAE;AACrB,IAAI,OAAO,KAAK,KAAK,SAAS,GAAG,YAAY,GAAG,OAAO;AACvD;AACA,EAAE,OAAO,CAAC,cAAc,IAAI,cAAc,IAAI,MAAM,CAAC,KAAK,CAAC;AAC3D,MAAM,SAAS,CAAC,KAAK;AACrB,MAAM,cAAc,CAAC,KAAK,CAAC;AAC3B;;ACzBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,EAAE,IAAI,IAAI,GAAG,OAAO,KAAK;AACzB,EAAE,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,UAAU,CAAC;AAClE;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,EAAE,OAAO,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,IAAI,QAAQ;AAClD;;ACvBA;AACA,IAAI,SAAS,GAAG,iBAAiB;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,EAAE,OAAO,OAAO,KAAK,IAAI,QAAQ;AACjC,KAAK,YAAY,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC;AAC3D;;AC1BA;AACA,IAAI,YAAY,GAAG,IAAI;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,MAAM,EAAE;AACjC,EAAE,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM;;AAE3B,EAAE,OAAO,KAAK,EAAE,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;AAC7D,EAAE,OAAO,KAAK;AACd;;ACdA;AACA,IAAI,WAAW,GAAG,MAAM;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,MAAM,EAAE;AAC1B,EAAE,OAAO;AACT,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE;AAC1E,MAAM,MAAM;AACZ;;ACZA;AACA,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC;;AAEf;AACA,IAAI,UAAU,GAAG,oBAAoB;;AAErC;AACA,IAAI,UAAU,GAAG,YAAY;;AAE7B;AACA,IAAI,SAAS,GAAG,aAAa;;AAE7B;AACA,IAAI,YAAY,GAAG,QAAQ;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,EAAE,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;AAChC,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACvB,IAAI,OAAO,GAAG;AACd;AACA,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACvB,IAAI,IAAI,KAAK,GAAG,OAAO,KAAK,CAAC,OAAO,IAAI,UAAU,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,KAAK;AAC5E,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK;AAClD;AACA,EAAE,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;AAChC,IAAI,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;AACvC;AACA,EAAE,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AACzB,EAAE,IAAI,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;AACvC,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;AAC3C,MAAM,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG,CAAC;AACnD,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC;AAC7C;;;;", "names": ["objectProto", "nativeObjectToString", "symToStringTag"], "sources": ["node_modules/lodash/_freeGlobal.js", "node_modules/lodash/_root.js", "node_modules/lodash/_Symbol.js", "node_modules/lodash/_getRawTag.js", "node_modules/lodash/_objectToString.js", "node_modules/lodash/_baseGetTag.js", "node_modules/lodash/isObject.js", "node_modules/lodash/isObjectLike.js", "node_modules/lodash/isSymbol.js", "node_modules/lodash/_trimmedEndIndex.js", "node_modules/lodash/_baseTrim.js", "node_modules/lodash/toNumber.js"], "sourcesContent": ["/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\nexport default freeGlobal;\n", "import freeGlobal from './_freeGlobal.js';\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\nexport default root;\n", "import root from './_root.js';\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\n\nexport default Symbol;\n", "import Symbol from './_Symbol.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\n\nexport default getRawTag;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\nexport default objectToString;\n", "import Symbol from './_Symbol.js';\nimport getRawTag from './_getRawTag.js';\nimport objectToString from './_objectToString.js';\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\nexport default baseGetTag;\n", "/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\nexport default isObject;\n", "/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return value != null && typeof value == 'object';\n}\n\nexport default isObjectLike;\n", "import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && baseGetTag(value) == symbolTag);\n}\n\nexport default isSymbol;\n", "/** Used to match a single whitespace character. */\nvar reWhitespace = /\\s/;\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace\n * character of `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the index of the last non-whitespace character.\n */\nfunction trimmedEndIndex(string) {\n  var index = string.length;\n\n  while (index-- && reWhitespace.test(string.charAt(index))) {}\n  return index;\n}\n\nexport default trimmedEndIndex;\n", "import trimmedEndIndex from './_trimmedEndIndex.js';\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim(string) {\n  return string\n    ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '')\n    : string;\n}\n\nexport default baseTrim;\n", "import baseTrim from './_baseTrim.js';\nimport isObject from './isObject.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = baseTrim(value);\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nexport default toNumber;\n"], "version": 3}