{"file": "platform.js", "mappings": ";;;AAAA,IAAK,KAkDJ;AAlDD,CAAA,UAAK,KAAK,EAAA;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAc;AACd,IAAA,KAAA,CAAA,KAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAe;AACf,IAAA,KAAA,CAAA,KAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAgB;AAChB,IAAA,KAAA,CAAA,KAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAa;AACb,IAAA,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,GAAA,CAAA,GAAA,OAAW;AACX,IAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,GAAA,EAAA,CAAA,GAAA,QAAW;AACX,IAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,GAAA,EAAA,CAAA,GAAA,QAAW;AACX,IAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAQ;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA,GAAA,OAAU;AACV,IAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,GAAA,EAAA,CAAA,GAAA,QAAW;AACX,IAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY;AACZ,IAAA,KAAA,CAAA,KAAA,CAAA,cAAA,CAAA,GAAA,EAAA,CAAA,GAAA,cAAiB;AACjB,IAAA,KAAA,CAAA,KAAA,CAAA,eAAA,CAAA,GAAA,EAAA,CAAA,GAAA,eAAkB;AAClB,IAAA,KAAA,CAAA,KAAA,CAAA,iBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,iBAAqB;AACrB,IAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAQ;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA,EAAA,CAAA,GAAA,MAAS;AACT,IAAA,KAAA,CAAA,KAAA,CAAA,WAAA,CAAA,GAAA,EAAA,CAAA,GAAA,WAAc;AACd,IAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY;AACZ,IAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,GAAA,GAAA,CAAA,GAAA,QAAY;AACZ,IAAA,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA,GAAA,OAAU;AACV,IAAA,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA,GAAA,OAAU;AACV,IAAA,KAAA,CAAA,KAAA,CAAA,WAAA,CAAA,GAAA,EAAA,CAAA,GAAA,WAAc;AACd,IAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACP,IAAA,KAAA,CAAA,KAAA,CAAA,aAAA,CAAA,GAAA,EAAA,CAAA,GAAA,aAAgB;AAChB,IAAA,KAAA,CAAA,KAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe;AACf,IAAA,KAAA,CAAA,KAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAa;AACb,IAAA,KAAA,CAAA,KAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe;AACf,IAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,GAAA,IAAQ;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,GAAA,IAAQ;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,GAAA,IAAQ;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,GAAA,IAAQ;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,GAAA,IAAQ;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,GAAA,IAAQ;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,GAAA,IAAQ;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,GAAA,IAAQ;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,GAAA,IAAQ;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,GAAA,CAAA,GAAA,KAAS;AACT,IAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,GAAA,CAAA,GAAA,KAAS;AACT,IAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,GAAA,CAAA,GAAA,KAAS;AACT,IAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,GAAA,GAAM;AACN,IAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,GAAA,GAAM;AACN,IAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,GAAA,GAAM;AACN,IAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,GAAA,GAAM;AACN,IAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,GAAA,GAAM;AACN,IAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,GAAA,GAAM;AACN,IAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,GAAA,GAAM;AACN,IAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,GAAA,GAAM;AACN,IAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,GAAA,GAAM;AACN,IAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,GAAA,GAAM;AACR,CAAC,EAlDI,KAAK,KAAL,KAAK,GAkDT,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACf,IAAA,WAAA,CAAA,WAAA,CAAA,GAAA,aAAyB;AACzB,IAAA,WAAA,CAAA,GAAA,CAAA,GAAA,MAAU;AACV,IAAA,WAAA,CAAA,GAAA,CAAA,GAAA,MAAU;AACV,IAAA,WAAA,CAAA,GAAA,CAAA,GAAA,MAAU;AACV,IAAA,WAAA,CAAA,GAAA,CAAA,GAAA,MAAU;AACV,IAAA,WAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACjB,IAAA,WAAA,CAAA,KAAA,CAAA,GAAA,KAAW;AACX,IAAA,WAAA,CAAA,WAAA,CAAA,GAAA,WAAuB;AACvB,IAAA,WAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACjB,IAAA,WAAA,CAAA,aAAA,CAAA,GAAA,YAA0B;AAC1B,IAAA,WAAA,CAAA,YAAA,CAAA,GAAA,WAAwB;AACxB,IAAA,WAAA,CAAA,UAAA,CAAA,GAAA,SAAoB;AACpB,IAAA,WAAA,CAAA,YAAA,CAAA,GAAA,WAAwB;AACxB,IAAA,WAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACjB,CAAC,EAhBW,WAAW,KAAX,WAAW,GAgBtB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,SAAS,EAAA;AACnB,IAAA,SAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACf,IAAA,SAAA,CAAA,KAAA,CAAA,GAAA,KAAW;AACb,CAAC,EAHW,SAAS,KAAT,SAAS,GAGpB,EAAA,CAAA,CAAA;AAED,mBAAe,KAAK;;AC3EpB,IAAK,UAEJ;AAFD,CAAA,UAAK,UAAU,EAAA;AACb,IAAA,UAAA,CAAA,KAAA,CAAA,GAAA,KAAW;AACb,CAAC,EAFI,UAAU,KAAV,UAAU,GAEd,EAAA,CAAA,CAAA;AACD,iBAAe,UAAU;;;;", "names": [], "sources": ["src/utils/key.codes.ts", "src/utils/platform.ts"], "sourcesContent": ["enum codes {\n  MOUSE_LEFT = 1,\n  MOUSE_RIGHT = 3,\n  MOUSE_MIDDLE = 2,\n  BACKSPACE = 8,\n  COMMA = 188,\n  INSERT = 45,\n  DELETE = 46,\n  END = 35,\n  ENTER = 13,\n  ESCAPE = 27,\n  CONTROL = 17,\n  COMMAND_LEFT = 91,\n  COMMAND_RIGHT = 93,\n  COMMAND_FIREFOX = 224,\n  ALT = 18,\n  HOME = 36,\n  PAGE_DOWN = 34,\n  PAGE_UP = 33,\n  PERIOD = 190,\n  SPACE = 32,\n  SHIFT = 16,\n  CAPS_LOCK = 20,\n  TAB = 9,\n  ARROW_RIGHT = 39,\n  ARROW_LEFT = 37,\n  ARROW_UP = 38,\n  ARROW_DOWN = 40,\n  F1 = 112,\n  F2 = 113,\n  F3 = 114,\n  F4 = 115,\n  F5 = 116,\n  F6 = 117,\n  F7 = 118,\n  F8 = 119,\n  F9 = 120,\n  F10 = 121,\n  F11 = 122,\n  F12 = 123,\n  A = 65,\n  C = 67,\n  D = 68,\n  F = 70,\n  L = 76,\n  O = 79,\n  P = 80,\n  S = 83,\n  V = 86,\n  X = 88,\n}\n\nexport enum codesLetter {\n  ENTER = 'Enter',\n  ENTER_NUM = 'NumpadEnter',\n  A = 'KeyA',\n  C = 'KeyC',\n  X = 'KeyX',\n  V = 'KeyV',\n  ESCAPE = 'Escape',\n  TAB = 'Tab',\n  BACKSPACE = 'Backspace',\n  DELETE = 'Delete',\n  ARROW_RIGHT = 'ArrowRight',\n  ARROW_LEFT = 'ArrowLeft',\n  ARROW_UP = 'ArrowUp',\n  ARROW_DOWN = 'ArrowDown',\n  SHIFT = 'Shift',\n}\n\nexport enum keyValues {\n  ENTER = 'Enter', // Enter + NumpadEnter\n  TAB = 'Tab',\n}\n\nexport default codes;\n", "enum osPlatform {\n  mac = 'Mac',\n}\nexport default osPlatform;\n"], "version": 3}