{"file": "debounce.js", "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,GAAG,GAAG,WAAW;AACrB,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;AACxB,CAAC;;AChBD;AACA,IAAI,eAAe,GAAG,qBAAqB;;AAE3C;AACA,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG;AACxB,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;AACvC,EAAE,IAAI,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,cAAc,GAAG,CAAC;AACxB,MAAM,OAAO,GAAG,KAAK;AACrB,MAAM,MAAM,GAAG,KAAK;AACpB,MAAM,QAAQ,GAAG,IAAI;;AAErB,EAAE,IAAI,OAAO,IAAI,IAAI,UAAU,EAAE;AACjC,IAAI,MAAM,IAAI,SAAS,CAAC,eAAe,CAAC;AACxC;AACA,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AAC5B,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE;AACzB,IAAI,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO;AAC/B,IAAI,MAAM,GAAG,SAAS,IAAI,OAAO;AACjC,IAAI,OAAO,GAAG,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,OAAO;AAChF,IAAI,QAAQ,GAAG,UAAU,IAAI,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,GAAG,QAAQ;AACpE;;AAEA,EAAE,SAAS,UAAU,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI,IAAI,GAAG,QAAQ;AACvB,QAAQ,OAAO,GAAG,QAAQ;;AAE1B,IAAI,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACnC,IAAI,cAAc,GAAG,IAAI;AACzB,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;AACtC,IAAI,OAAO,MAAM;AACjB;;AAEA,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE;AAC7B;AACA,IAAI,cAAc,GAAG,IAAI;AACzB;AACA,IAAI,OAAO,GAAG,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC;AAC5C;AACA,IAAI,OAAO,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM;AAC9C;;AAEA,EAAE,SAAS,aAAa,CAAC,IAAI,EAAE;AAC/B,IAAI,IAAI,iBAAiB,GAAG,IAAI,GAAG,YAAY;AAC/C,QAAQ,mBAAmB,GAAG,IAAI,GAAG,cAAc;AACnD,QAAQ,WAAW,GAAG,IAAI,GAAG,iBAAiB;;AAE9C,IAAI,OAAO;AACX,QAAQ,SAAS,CAAC,WAAW,EAAE,OAAO,GAAG,mBAAmB;AAC5D,QAAQ,WAAW;AACnB;;AAEA,EAAE,SAAS,YAAY,CAAC,IAAI,EAAE;AAC9B,IAAI,IAAI,iBAAiB,GAAG,IAAI,GAAG,YAAY;AAC/C,QAAQ,mBAAmB,GAAG,IAAI,GAAG,cAAc;;AAEnD;AACA;AACA;AACA,IAAI,QAAQ,YAAY,KAAK,SAAS,KAAK,iBAAiB,IAAI,IAAI,CAAC;AACrE,OAAO,iBAAiB,GAAG,CAAC,CAAC,KAAK,MAAM,IAAI,mBAAmB,IAAI,OAAO,CAAC;AAC3E;;AAEA,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,IAAI,IAAI,GAAG,GAAG,EAAE;AACpB,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AAC5B,MAAM,OAAO,YAAY,CAAC,IAAI,CAAC;AAC/B;AACA;AACA,IAAI,OAAO,GAAG,UAAU,CAAC,YAAY,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;AAC3D;;AAEA,EAAE,SAAS,YAAY,CAAC,IAAI,EAAE;AAC9B,IAAI,OAAO,GAAG,SAAS;;AAEvB;AACA;AACA,IAAI,IAAI,QAAQ,IAAI,QAAQ,EAAE;AAC9B,MAAM,OAAO,UAAU,CAAC,IAAI,CAAC;AAC7B;AACA,IAAI,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACnC,IAAI,OAAO,MAAM;AACjB;;AAEA,EAAE,SAAS,MAAM,GAAG;AACpB,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE;AAC/B,MAAM,YAAY,CAAC,OAAO,CAAC;AAC3B;AACA,IAAI,cAAc,GAAG,CAAC;AACtB,IAAI,QAAQ,GAAG,YAAY,GAAG,QAAQ,GAAG,OAAO,GAAG,SAAS;AAC5D;;AAEA,EAAE,SAAS,KAAK,GAAG;AACnB,IAAI,OAAO,OAAO,KAAK,SAAS,GAAG,MAAM,GAAG,YAAY,CAAC,GAAG,EAAE,CAAC;AAC/D;;AAEA,EAAE,SAAS,SAAS,GAAG;AACvB,IAAI,IAAI,IAAI,GAAG,GAAG,EAAE;AACpB,QAAQ,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC;;AAEvC,IAAI,QAAQ,GAAG,SAAS;AACxB,IAAI,QAAQ,GAAG,IAAI;AACnB,IAAI,YAAY,GAAG,IAAI;;AAEvB,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,IAAI,OAAO,KAAK,SAAS,EAAE;AACjC,QAAQ,OAAO,WAAW,CAAC,YAAY,CAAC;AACxC;AACA,MAAM,IAAI,MAAM,EAAE;AAClB;AACA,QAAQ,YAAY,CAAC,OAAO,CAAC;AAC7B,QAAQ,OAAO,GAAG,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC;AAChD,QAAQ,OAAO,UAAU,CAAC,YAAY,CAAC;AACvC;AACA;AACA,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE;AAC/B,MAAM,OAAO,GAAG,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC;AAC9C;AACA,IAAI,OAAO,MAAM;AACjB;AACA,EAAE,SAAS,CAAC,MAAM,GAAG,MAAM;AAC3B,EAAE,SAAS,CAAC,KAAK,GAAG,KAAK;AACzB,EAAE,OAAO,SAAS;AAClB;;;;", "names": [], "sources": ["node_modules/lodash/now.js", "node_modules/lodash/debounce.js"], "sourcesContent": ["import root from './_root.js';\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\nexport default now;\n", "import isObject from './isObject.js';\nimport now from './now.js';\nimport toNumber from './toNumber.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        timeWaiting = wait - timeSinceLastCall;\n\n    return maxing\n      ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke)\n      : timeWaiting;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        clearTimeout(timerId);\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\nexport default debounce;\n"], "version": 3}