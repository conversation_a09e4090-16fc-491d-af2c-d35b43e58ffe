{"file": "revogr-extra2.js", "mappings": ";;;;;MA0Ba,aAAa,iBAAAA,kBAAA,CAAA,MAAA,aAAA,SAAA,WAAA,CAAA;AAH1B,IAAA,WAAA,GAAA;;;AAIE;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAGP,EAAE;AACR;;AAEG;AACM,QAAA,IAAM,CAAA,MAAA,GAAG,CAAC;AAmCpB;AAjCC;;;AAGG;AACO,IAAA,MAAM,OAAO,GAAA;AACrB,QAAA,IAAI,CAAC,MAAM,IAAI,EAAE;;IAGnB,MAAM,GAAA;;AACJ,QAAA,OAAO,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,0CAAE,GAAG,CAAC,IAAI,IAAG;;;AAG5B,YAAA,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;gBAC9B,MAAM,MAAM,GAAiC,EAAE;gBAC/C,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAA,IAAA,EAAA,CAAA,CAAA,OAAA,CAAA,EAAA,GAAA,MAAM,CAAC,OAAO,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EAAE,CAAC,CAAC;AAEpE,gBAAA,QACE,CAAA,CAAA,cAAA,EAAA,EACE,KAAK,EAAE,QAAQ,EAAE,EACjB,GAAG,EAAE,CAAC,EAA2B,KAAI;wBACnC,IAAI,EAAE,EAAE;;AAEN,4BAAA,MAAM,CAAC,OAAO,GAAG,MAAK;AACpB,gCAAA,EAAE,CAAC,KAAK,GAAG,QAAQ,EAAE;AACvB,6BAAC;;qBAEJ,EAAA,CACD;;AAGN,YAAA,OAAO,IAAI;AACb,SAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/components/extra/revogr-extra.tsx"], "sourcesContent": ["import { h, Component, Method, Prop, State, VNode } from '@stencil/core';\nimport type { ExtraNodeFuncConfig } from '@type';\n\n/**\n * Contains extra elements for stencil components.\n * Performs rendering and updates for external components.\n * \n * @example\n * In Plugins if you want to add extra elements to grid and use stenciljs vnodes reactivity:\n * function paginationPanel(this: PaginationPlugin, config: { refresh: () => void }) {\n *    // use `config.refresh()` for component to re-render\n *    return h('div')\n * }\n * \n * revogrid.registerVNode = [\n *    ...existingNodes,\n *     paginationPanel.bind(this)\n * ];\n\n\n/**\n * @internal\n */\n@Component({\n  tag: 'revogr-extra',\n})\nexport class RevoGridExtra {\n  /**\n   * Nodes to render\n   */\n  @Prop() nodes: (\n    | VNode\n    | ((c: ExtraNodeFuncConfig) => VNode)\n  )[] = [];\n  /**\n   * Force component to re-render\n   */\n  @State() update = 1;\n\n  /**\n   * Refreshes the extra component. Useful if you want to manually\n   * force the component to re-render.\n   */\n  @Method() async refresh() {\n    this.update *= -1;\n  }\n\n  render() {\n    return this.nodes?.map(node => {\n      // Check if node is a function or a stencil component\n      // If function wrap it in a stencil component with the refresh function\n      if (typeof node === 'function') {\n        const config: Partial<ExtraNodeFuncConfig> = {};\n        const getNodes = () => [node({ refresh: () => config.refresh?.() })];\n\n        return (\n          <revogr-extra\n            nodes={getNodes()}\n            ref={(el?: HTMLRevogrExtraElement) => {\n              if (el) {\n                // Update exclusively for current node\n                config.refresh = () => {\n                  el.nodes = getNodes();\n                };\n              }\n            }}\n          />\n        );\n      }\n      return node;\n    });\n  }\n}\n"], "version": 3}