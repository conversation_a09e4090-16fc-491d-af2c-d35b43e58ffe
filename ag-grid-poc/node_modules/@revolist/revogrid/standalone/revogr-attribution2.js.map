{"file": "revogr-attribution2.js", "mappings": ";;;;;MAaa,WAAW,iBAAAA,kBAAA,CAAA,MAAA,WAAA,SAAA,WAAA,CAAA;;;;;IACtB,MAAM,GAAA;QACJ,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CACE,CAAA,GAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,uCAAuC,EAC5C,MAAM,EAAC,QAAQ,EACf,GAAG,EAAC,qBAAqB,EACzB,KAAK,EAAC,4CAAkC,EACxC,KAAK,EAAC,OAAO,EAAA,EAAA,UAAA,CAGX,CACC;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/components/attribution/revogr-attribution.tsx"], "sourcesContent": ["import { Component, Host, h } from '@stencil/core';\n/**\n * If you’re thinking about removing the attribution, we’d like to share a few thoughts first:\n * If you’re using RevoGrid in your organization and it’s helping you make money, we depend on your support to keep improving and maintaining RevoGrid under an MIT License.\n * Before you remove the attribution, please consider how you can support RevoGrid to help keep it running smoothly.\n * Using RevoGrid for a personal project? Awesome! Feel free to remove the attribution. We appreciate your support.\n */\n/**\n * @internal\n */\n@Component({\n  tag: 'revogr-attribution',\n})\nexport class Attribution {\n  render() {\n    return (\n      <Host>\n        <a\n          href=\"https://rv-grid.com/guide/attribution\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          title=\"Made with ❤️ by Revolist OU Team\"\n          class=\"value\"\n        >\n          RevoGrid\n        </a>\n      </Host>\n    );\n  }\n}\n"], "version": 3}