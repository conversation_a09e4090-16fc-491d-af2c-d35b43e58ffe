{"file": "consts.js", "mappings": ";;;AAAO,MAAM,YAAY,GAAG;AACrB,MAAM,eAAe,GAAG;AAExB,MAAM,QAAQ,GAAG;AACjB,MAAM,QAAQ,GAAG;AAEjB,MAAM,cAAc,GAAG;AACvB,MAAM,UAAU,GAAG;AACnB,MAAM,eAAe,GAAG;AACxB,MAAM,YAAY,GAAG;AACrB,MAAM,qBAAqB,GAAG;AAC9B,MAAM,gBAAgB,GAAG;AACzB,MAAM,uBAAuB,GAAG;AAEhC,MAAM,eAAe,GAAG;AACxB,MAAM,eAAe,GAAG;AAExB,MAAM,WAAW,GAAG;AACpB,MAAM,sBAAsB,GAAG;AAC/B,MAAM,YAAY,GAAG;AACrB,MAAM,sBAAsB,GAAG;AAE/B,MAAM,kBAAkB,GAAG;AAE3B,MAAM,aAAa,GAAG;AAEtB,MAAM,UAAU,GAAG;AACnB,MAAM,cAAc,GAAG;AACvB,MAAM,iBAAiB,GAAG;;;;", "names": [], "sources": ["src/utils/consts.ts"], "sourcesContent": ["export const MIN_COL_SIZE = 30;\nexport const RESIZE_INTERVAL = 40;\n\nexport const DATA_COL = 'data-rgCol';\nexport const DATA_ROW = 'data-rgRow';\n\nexport const DISABLED_CLASS = 'disabled';\nexport const CELL_CLASS = 'rgCell';\nexport const ROW_HEADER_TYPE = 'rowHeaders';\nexport const HEADER_CLASS = 'rgHeaderCell';\nexport const HEADER_SORTABLE_CLASS = 'sortable';\nexport const HEADER_ROW_CLASS = 'header-rgRow';\nexport const HEADER_ACTUAL_ROW_CLASS = 'actual-rgRow';\n\nexport const DRAG_ICON_CLASS = 'revo-drag-icon';\nexport const DRAGGABLE_CLASS = 'revo-draggable';\n\nexport const FOCUS_CLASS = 'focused-cell';\nexport const SELECTION_BORDER_CLASS = 'selection-border-range';\nexport const MOBILE_CLASS = 'mobile-handler';\nexport const TMP_SELECTION_BG_CLASS = 'temp-bg-range';\n\nexport const CELL_HANDLER_CLASS = 'autofill-handle';\n\nexport const EDIT_INPUT_WR = 'edit-input-wrapper';\n\nexport const DRAGG_TEXT = 'Draggable item';\nexport const GRID_INTERNALS = '__rvgr';\nexport const ROW_FOCUSED_CLASS = 'focused-rgRow';\n"], "version": 3}