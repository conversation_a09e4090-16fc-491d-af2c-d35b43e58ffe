{"file": "local.scroll.timer.js", "mappings": ";;;;;AAqBA,MAAM,aAAa,GAAW;AAC5B,IAAA,WAAW,EAAE,CAAC;AACd,IAAA,UAAU,EAAE,CAAC;AACb,IAAA,WAAW,EAAE,CAAC;AACd,IAAA,OAAO,EAAE,CAAC;CACX;AACD,MAAM,aAAa,GAAG,EAAE;AAExB;;;AAGG;AACG,SAAU,cAAc,CAC5B,WAAmB,EACnB,UAAkB,EAClB,WAAW,GAAG,CAAC,EAAA;AAEf,IAAA,IAAI,WAAW,GAAG,WAAW,EAAE;AAC7B,QAAA,OAAO,CAAC;;AAEV,IAAA,OAAO,WAAW,IAAI,WAAW,GAAG,UAAU,GAAG,WAAW,GAAG,CAAC,CAAC;AACnE;AAEc,MAAO,kBAAkB,CAAA;AAerC,IAAA,WAAA,CAAoB,GAAW,EAAA;QAAX,IAAG,CAAA,GAAA,GAAH,GAAG;AAdf,QAAA,IAAA,CAAA,uBAAuB,GAA+C;AAC5E,YAAA,KAAK,EAAE,IAAI;AACX,YAAA,KAAK,EAAE,IAAI;SACZ;;AAEO,QAAA,IAAA,CAAA,cAAc,GAAkC;AACtD,YAAA,KAAK,EAAE,aAAa;AACpB,YAAA,KAAK,EAAE,aAAa;SACrB;AACO,QAAA,IAAA,CAAA,MAAM,GAAkC;YAC9C,KAAK,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAO,aAAa,CAAE;YAC3B,KAAK,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAO,aAAa,CAAE;SAC5B;;IAID,SAAS,CAAC,MAAc,EAAE,SAAwB,EAAA;AAChD,QAAA,MAAM,kBAAkB,GAAG,cAAc,CACvC,MAAM,CAAC,WAAW,EAClB,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,WAAW,CACnB;AACD,QAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACjB,MAAM,CACT,EAAA,EAAA,OAAO,EAAE,kBAAkB,GAAG,MAAM,CAAC,UAAU,EAC/C,kBAAkB,GACnB;;;IAIH,MAAM,SAAS,CAAC,CAAsB,EAAA;AACpC,QAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC;;QAG9B,MAAM,cAAc,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,KAAI;;AAE3D,YAAA,IAAI,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE;gBAC/B,OAAO,OAAO,EAAE;;AAElB,YAAA,MAAM,WAAW,GAAG,MAAM,CAAC,qBAAqB,CAAC,MAAK;AACpD,gBAAA,OAAO,EAAE;AACX,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,IAAI,CACrD,IAAI,EACJ,WAAW,CACZ;AACH,SAAC,CAAC;AAEF,QAAA,IAAI;AACF,YAAA,MAAM,cAAc;YACpB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;YAC1C,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC;AACtC,YAAA,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CACpD,CAAC,CAAC,UAAU,EACZ,MAAM,CACP;YACD,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI;YAChD,IAAI,CAAC,GAAG,CAAC,WAAW,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACf,CAAC,CAAA,EAAA,EACJ,UAAU,EAAE,MAAM,CAAC;AACjB,sBAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,MAAM,EAAE,KAAK;AAC1C,sBAAE,CAAC,CAAC,UAAU,IAChB;;QACF,OAAO,EAAE,EAAE;AACX,YAAA,MAAM,CAAC,oBAAoB,CAAC,EAAE,CAAC;;;AAInC;;AAEG;AACH,IAAA,MAAM,CACJ,UAAkB,EAClB,SAAwB,EACxB,KAAK,GAAG,KAAK,EACb,KAAc,EACd,OAAO,GAAG,KAAK,EAAA;;AAGf,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;;AAG5B,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,UAAU,EAAE;AAC3D,YAAA,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,aAAa;YAC9C;;QAGF,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;;AAEvC,QAAA,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;AACjB,YAAA,SAAS,EAAE,SAAS;YACpB,UAAU,EAAE,KAAK,CAAC;kBACd,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK;AAChC,kBAAE,UAAU;YACd,KAAK;YACL,OAAO;AACR,SAAA,CAAC;;AAGI,IAAA,SAAS,CAAC,SAAwB,EAAA;AACxC,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;;;IAIvB,cAAc,CAAC,CAAS,EAAE,KAAa,EAAA;AAC7C,QAAA,IAAI,CAAC,GAAG,CAAC,EAAE;AACT,YAAA,OAAO,aAAa;;AAGtB,QAAA,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,EAAE;YAC1D,OAAO,KAAK,CAAC,OAAO;;AAEtB,QAAA,OAAO,CAAC;;;AAIF,IAAA,YAAY,CAAC,SAAwB,EAAA;;QAC3C,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,uBAAuB,EAAC,SAAS,CAAC,kDAAI;AAC3C,QAAA,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,GAAG,IAAI;;;AAIxC,IAAA,OAAO,CAAC,GAAW,EAAE,KAAa,EAAE,MAAM,GAAG,IAAI,EAAA;;AACvD,QAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU;AACjC,QAAA,MAAM,IAAI,GAAqB,CAAC,CAAC,EAAE,CAAC,CAAA,EAAA,GAAA,KAAK,CAAC,kBAAkB,mCAAI,QAAQ,IAAI,QAAQ,CAAC;AACrF,QAAA,MAAM,EAAE,GAAqB,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;QACvE,IAAI,MAAM,EAAE;YACV,OAAO,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC;;QAElC,OAAO,UAAU,CAAC,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC;;AAEnC;;AC/KD;;AAEG;MACU,gBAAgB,CAAA;AAc3B,IAAA,WAAA,CAAoB,mBAAmB,EAAE,EAAA;QAArB,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;AAbpC;;;AAGG;AACK,QAAA,IAAA,CAAA,yBAAyB,GAAkC;AACjE,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,KAAK,EAAE,CAAC;SACT;AACO,QAAA,IAAA,CAAA,yBAAyB,GAAkC;AACjE,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,KAAK,EAAE,CAAC;SACT;AA+BD;;;;AAIG;QACK,IAAyB,CAAA,yBAAA,GAK3B,EAAE;;AArCR,IAAA,aAAa,CAAC,CAAmD,EAAA;QAC/D,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU;;AAG5D;;AAEG;AACH,IAAA,kBAAkB,CAAC,SAAwB,EAAA;AACzC,QAAA,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE;;IAGlE,OAAO,CAAC,IAAmB,EAAE,UAAkB,EAAA;;AAE7C,QAAA,IAAI,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE;AACxC,YAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;;;QAGlC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC;;IAGpC,YAAY,CAAC,IAAmB,EAAE,UAAkB,EAAA;QAC1D,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE;QAChC,MAAM,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;AACzD,QAAA,OAAO,MAAM,GAAG,IAAI,CAAC,gBAAgB;AACrC,YAAA,UAAU,KAAK,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;;AAe7C,IAAA,qBAAqB,CAAC,IAAmB,EAAA;;AAC/C,QAAA,YAAY,CAAC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,OAAO,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,CAAC,CAAC;AAChE,QAAA,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;;AAG7C,IAAA,wBAAwB,CAAC,IAAmB,EAAE,UAAkB,EAAE,gBAA4B,EAAA;;;AAG5F,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;;YAEhC,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,GAAG;AACtD,gBAAA,QAAQ,EAAE,gBAAgB;AAC1B,gBAAA,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE;gBAC/B,UAAU;AACV,gBAAA,OAAO,EAAE,CAAQ;aAClB;AACD,YAAA,QAAQ,CAAC,OAAO,GAAG,UAAU,CAAC,MAAK;;AAEjC,gBAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;;;gBAGhC,IAAI,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,EAAE;oBAC7G,QAAQ,CAAC,QAAQ,EAAE;;AAEvB,aAAC,EAAE,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;;;AAGnC;;;;", "names": [], "sources": ["src/services/local.scroll.service.ts", "src/services/local.scroll.timer.ts"], "sourcesContent": ["import type { DimensionType, ViewPortScrollEvent } from '@type';\nimport { scaleValue } from '../utils';\n\ninterface Config {\n  skipAnimationFrame?: boolean;\n  // scroll event inited and direction cached\n  // scrollingService.proxyScroll get tiggered\n  // setScroll event called from scrollingService\n  runScroll(e: ViewPortScrollEvent): void;\n  // all operation finished, apply scroll values\n  applyScroll(e: ViewPortScrollEvent): void;\n}\n\ntype Params = {\n  contentSize: number;\n  virtualContentSize?: number;\n  clientSize: number;\n  virtualSize: number;\n  maxSize?: number;\n};\n\nconst initialParams: Params = {\n  contentSize: 0,\n  clientSize: 0,\n  virtualSize: 0,\n  maxSize: 0,\n};\nconst NO_COORDINATE = -1;\n\n/**\n * Based on content size, client size and virtual size\n * return full size\n */\nexport function getContentSize(\n  contentSize: number,\n  clientSize: number,\n  virtualSize = 0,\n): number {\n  if (virtualSize > contentSize) {\n    return 0;\n  }\n  return contentSize + (virtualSize ? clientSize - virtualSize : 0);\n}\n\nexport default class LocalScrollService {\n  private preventArtificialScroll: Record<DimensionType, (() => void) | null> = {\n    rgRow: null,\n    rgCol: null,\n  };\n  // to check if scroll changed\n  private previousScroll: Record<DimensionType, number> = {\n    rgRow: NO_COORDINATE,\n    rgCol: NO_COORDINATE,\n  };\n  private params: Record<DimensionType, Params> = {\n    rgRow: { ...initialParams },\n    rgCol: { ...initialParams },\n  };\n\n  constructor(private cfg: Config) {}\n\n  setParams(params: Params, dimension: DimensionType) {\n    const virtualContentSize = getContentSize(\n      params.contentSize,\n      params.clientSize,\n      params.virtualSize,\n    );\n    this.params[dimension] = {\n      ...params,\n      maxSize: virtualContentSize - params.clientSize,\n      virtualContentSize,\n    };\n  }\n\n  // apply scroll values after scroll done\n  async setScroll(e: ViewPortScrollEvent) {\n    this.cancelScroll(e.dimension);\n\n    // start frame animation\n    const frameAnimation = new Promise<void>((resolve, reject) => {\n      // for example safari desktop has issues with animation frame\n      if (this.cfg.skipAnimationFrame) {\n        return resolve();\n      }\n      const animationId = window.requestAnimationFrame(() => {\n        resolve();\n      });\n      this.preventArtificialScroll[e.dimension] = reject.bind(\n        null,\n        animationId,\n      );\n    });\n\n    try {\n      await frameAnimation;\n      const params = this.getParams(e.dimension);\n      e.coordinate = Math.ceil(e.coordinate);\n      this.previousScroll[e.dimension] = this.wrapCoordinate(\n        e.coordinate,\n        params,\n      );\n      this.preventArtificialScroll[e.dimension] = null;\n      this.cfg.applyScroll({\n        ...e,\n        coordinate: params.virtualSize\n          ? this.convert(e.coordinate, params, false)\n          : e.coordinate,\n      });\n    } catch (id) {\n      window.cancelAnimationFrame(id);\n    }\n  }\n\n  /**\n   * On scroll event started\n   */\n  scroll(\n    coordinate: number,\n    dimension: DimensionType,\n    force = false,\n    delta?: number,\n    outside = false,\n  ) {\n    // cancel all previous scrolls for same dimension\n    this.cancelScroll(dimension);\n\n    // drop if no change\n    if (!force && this.previousScroll[dimension] === coordinate) {\n      this.previousScroll[dimension] = NO_COORDINATE;\n      return;\n    }\n\n    const param = this.getParams(dimension);\n    // let component know about scroll event started\n    this.cfg.runScroll({\n      dimension: dimension,\n      coordinate: param.virtualSize\n        ? this.convert(coordinate, param)\n        : coordinate,\n      delta,\n      outside,\n    });\n  }\n\n  private getParams(dimension: DimensionType): Params {\n    return this.params[dimension];\n  }\n\n  // check if scroll outside of region to avoid looping\n  private wrapCoordinate(c: number, param: Params): number {\n    if (c < 0) {\n      return NO_COORDINATE;\n    }\n\n    if (typeof param.maxSize === 'number' && c > param.maxSize) {\n      return param.maxSize;\n    }\n    return c;\n  }\n\n  // prevent already started scroll, performance optimization\n  private cancelScroll(dimension: DimensionType) {\n    this.preventArtificialScroll[dimension]?.();\n    this.preventArtificialScroll[dimension] = null;\n  }\n\n  /* convert virtual to real and back, scale range */\n  private convert(pos: number, param: Params, toReal = true): number {\n    const minRange = param.clientSize;\n    const from: [number, number] = [0, (param.virtualContentSize ?? minRange) - minRange];\n    const to: [number, number] = [0, param.contentSize - param.virtualSize];\n    if (toReal) {\n      return scaleValue(pos, from, to);\n    }\n    return scaleValue(pos, to, from);\n  }\n}\n", "import type { DimensionType } from '@type';\n/**\n * Apply changes only if mousewheel event happened some time ago (scrollThrottling)\n */\nexport class LocalScrollTimer {\n  /**\n   * Last mw event time for trigger scroll function below\n   * If mousewheel function was ignored we still need to trigger render\n   */\n  private mouseWheelScrollTimestamp: Record<DimensionType, number> = {\n    rgCol: 0,\n    rgRow: 0,\n  };\n  private lastKnownScrollCoordinate: Record<DimensionType, number> = {\n    rgCol: 0,\n    rgRow: 0,\n  };\n\n  constructor(private scrollThrottling = 10) {}\n\n  setCoordinate(e: { dimension: DimensionType; coordinate: number }) {\n    this.lastKnownScrollCoordinate[e.dimension] = e.coordinate;\n  }\n\n  /**\n   * Remember last mw event time\n   */\n  latestScrollUpdate(dimension: DimensionType) {\n    this.mouseWheelScrollTimestamp[dimension] = new Date().getTime();\n  }\n\n  isReady(type: DimensionType, coordinate: number) {\n    // if there is a callback, clear it\n    if (this.lastScrollUpdateCallbacks[type]) {\n      this.clearLastScrollUpdate(type)\n    }\n    // apply after throttling\n    return this.verifyChange(type, coordinate);\n  }\n\n  private verifyChange(type: DimensionType, coordinate: number) {\n    const now = new Date().getTime();\n    const change = now - this.mouseWheelScrollTimestamp[type];\n    return change > this.scrollThrottling &&\n    coordinate !== this.lastKnownScrollCoordinate[type];\n  }\n\n  /**\n   * Check if scroll is ready to accept new value\n   * this is an edge case for scroll events\n   * when we need to apply scroll after throttling\n   */\n  private lastScrollUpdateCallbacks: Partial<Record<DimensionType, {\n    callback: () => void;\n    timestamp: number;\n    coordinate: number;\n    timeout: number;\n  }>> = {};\n\n  private clearLastScrollUpdate(type: DimensionType) {\n    clearTimeout(this.lastScrollUpdateCallbacks[type]?.timeout ?? 0);\n    delete this.lastScrollUpdateCallbacks[type];\n  }\n\n  throttleLastScrollUpdate(type: DimensionType, coordinate: number, lastScrollUpdate: () => void) {\n    // if scrollThrottling is set\n    // we need to throttle the last scroll event\n    if (this.scrollThrottling) {\n      this.clearLastScrollUpdate(type)\n      // save lastScrollUpdate callback\n      const callback = this.lastScrollUpdateCallbacks[type] = {\n        callback: lastScrollUpdate,\n        timestamp: new Date().getTime(),\n        coordinate,\n        timeout: 0 as any,\n      };\n      callback.timeout = setTimeout(() => {\n        // clear timeout\n        this.clearLastScrollUpdate(type)\n        // if scrollThrottling is set, and the last scroll event happened before the timeout started\n        // we need to throttle the last scroll event\n        if (this.mouseWheelScrollTimestamp[type] < callback.timestamp && this.verifyChange(type, callback.coordinate)) {\n          callback.callback();\n        }\n      }, this.scrollThrottling + 50);\n    }\n  }\n}\n"], "version": 3}