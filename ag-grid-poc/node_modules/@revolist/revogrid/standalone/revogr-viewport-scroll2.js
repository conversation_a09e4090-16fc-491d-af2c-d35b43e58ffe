/*!
 * Built by Revolist OU ❤️
 */
import { proxyCustomElement, HTMLElement, createEvent, h, Host } from '@stencil/core/internal/client';
import { t as throttle } from './throttle.js';
import { L as LocalScrollTimer, a as LocalScrollService } from './local.scroll.timer.js';

/**
 * Collects data for pinned columns in the required @ViewportProps format.
 */
/**
 * Represents the slot names for the viewport slots.
 */
const HEADER_SLOT = 'header'; // Slot name for the header slot
const FOOTER_SLOT = 'footer'; // Slot name for the footer slot
const CONTENT_SLOT = 'content'; // Slot name for the content slot
const DATA_SLOT = 'data'; // Slot name for the data slot
/**
 * Returns the last visible cell in the viewport for a given row type.
 * Coordinates are not zero-based and are relative to the viewport.
 * If needed to be zero-based they can be adjusted by subtracting 1.
 */
function getLastCell(data, rowType) {
    // Get the last visible column count from the viewport column data.
    const lastVisibleColumnCount = data.viewports[data.colType].store.get('realCount');
    // Get the last visible row count for the given row type from the viewport column data.
    const lastVisibleRowCount = data.viewports[rowType].store.get('realCount');
    // Return the last visible cell with the last visible column count and row count.
    return {
        x: lastVisibleColumnCount,
        y: lastVisibleRowCount,
    };
}
function viewportDataPartition(data, type, slot, fixed) {
    return {
        colData: data.colStore,
        viewportCol: data.viewports[data.colType].store,
        viewportRow: data.viewports[type].store,
        // lastCell is the last real coordinate + 1
        lastCell: getLastCell(data, type),
        slot,
        type,
        canDrag: !fixed,
        position: data.position,
        dataStore: data.rowStores[type].store,
        dimensionCol: data.dimensions[data.colType].store,
        dimensionRow: data.dimensions[type].store,
        style: fixed
            ? { height: `${data.dimensions[type].store.get('realSize')}px` }
            : undefined,
    };
}

class GridResizeService {
    constructor(el, resize, elements) {
        this.resize = resize;
        this.resizeObserver = null;
        this.previousSize = {
            width: 0,
            height: 0,
        };
        this.apply = throttle((e) => {
            var _a;
            const entry = {
                width: e.width,
                height: e.height,
            };
            (_a = this.resize) === null || _a === void 0 ? void 0 : _a.call(this, entry, this.previousSize);
            this.previousSize = entry;
        }, 40, {
            leading: false,
            trailing: true,
        });
        const extras = [];
        elements.forEach((element) => {
            if (element) {
                extras.push(element);
            }
        });
        this.init(el, extras);
    }
    init(el, extras = []) {
        const observer = this.resizeObserver = new ResizeObserver((e) => {
            if (e.length) {
                if (e[0].target === el) {
                    this.apply(e[0].contentRect);
                }
                else {
                    this.apply(el.getBoundingClientRect());
                }
            }
        });
        observer.observe(el);
        extras.forEach((extra) => {
            observer.observe(extra);
        });
    }
    destroy() {
        var _a;
        (_a = this.resizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();
        this.resizeObserver = null;
    }
}

const revogrViewportScrollStyleCss = ".revo-drag-icon{width:11px;opacity:0.8}.revo-drag-icon::before{content:\"::\"}.revo-alt-icon{-webkit-mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");width:11px;height:11px;background-size:cover;background-repeat:no-repeat}.arrow-down{position:absolute;right:5px;top:0}.arrow-down svg{width:8px;margin-top:5px;margin-left:5px;opacity:0.4}.cell-value-wrapper{margin-right:10px;overflow:hidden;text-overflow:ellipsis}.revo-button{position:relative;overflow:hidden;color:#fff;background-color:#4545ff;height:32px;line-height:32px;padding:0 15px;outline:0;border:0;border-radius:7px;box-sizing:border-box;cursor:pointer}.revo-button.green{background-color:#009037}.revo-button.red{background-color:#E0662E}.revo-button:disabled,.revo-button[disabled]{cursor:not-allowed !important;filter:opacity(0.35) !important}.revo-button.outline{border:1px solid #dbdbdb;line-height:30px;background:none;color:#000;box-shadow:none}revo-grid[theme^=dark] .revo-button.outline{border:1px solid #404040;color:#d8d8d8}.rowHeaders{z-index:2;font-size:10px;display:flex;height:100%}.rowHeaders revogr-data .rgCell{text-align:center}.rowHeaders .rgCell{padding:0 1em !important;min-width:100%}revogr-viewport-scroll{-ms-overflow-style:none;scrollbar-width:none;overflow-x:auto;overflow-y:hidden;position:relative;z-index:1;height:100%}revogr-viewport-scroll::-webkit-scrollbar{display:none;-webkit-appearance:none}revogr-viewport-scroll.colPinStart,revogr-viewport-scroll.colPinEnd{z-index:2}revogr-viewport-scroll.colPinEnd:has(.active){overflow:visible}revogr-viewport-scroll.rgCol{flex-grow:1}revogr-viewport-scroll .content-wrapper{overflow:hidden}revogr-viewport-scroll .inner-content-table{display:flex;flex-direction:column;max-height:100%;width:100%;min-width:100%;position:relative;z-index:0}revogr-viewport-scroll .vertical-inner{overflow-y:auto;position:relative;width:100%;flex-grow:1;outline:none;-ms-overflow-style:none;scrollbar-width:none;}revogr-viewport-scroll .vertical-inner::-webkit-scrollbar{display:none;-webkit-appearance:none}revogr-viewport-scroll .vertical-inner revogr-data,revogr-viewport-scroll .vertical-inner revogr-overlay-selection{height:100%}";

const RevogrViewportScroll = /*@__PURE__*/ proxyCustomElement(class RevogrViewportScroll extends HTMLElement {
    constructor() {
        super();
        this.__registerHost();
        this.scrollViewport = createEvent(this, "scrollviewport", 7);
        this.resizeViewport = createEvent(this, "resizeviewport", 7);
        this.scrollchange = createEvent(this, "scrollchange", 7);
        this.silentScroll = createEvent(this, "scrollviewportsilent", 7);
        /**
         * Width of inner content
         */
        this.contentWidth = 0;
        /**
         * Height of inner content
         */
        this.contentHeight = 0;
    }
    async setScroll(e) {
        var _a;
        this.localScrollTimer.latestScrollUpdate(e.dimension);
        (_a = this.localScrollService) === null || _a === void 0 ? void 0 : _a.setScroll(e);
    }
    /**
     * update on delta in case we don't know existing position or external change
     * @param e
     */
    async changeScroll(e, silent = false) {
        var _a, _b;
        if (silent) {
            if (e.coordinate && this.verticalScroll) {
                switch (e.dimension) {
                    // for mobile devices to skip negative scroll loop. only on vertical scroll
                    case 'rgRow':
                        this.verticalScroll.style.transform = `translateY(${ -1 * e.coordinate}px)`;
                        break;
                }
            }
            return;
        }
        if (e.delta) {
            switch (e.dimension) {
                case 'rgCol':
                    e.coordinate = this.horizontalScroll.scrollLeft + e.delta;
                    break;
                case 'rgRow':
                    e.coordinate = ((_b = (_a = this.verticalScroll) === null || _a === void 0 ? void 0 : _a.scrollTop) !== null && _b !== void 0 ? _b : 0) + e.delta;
                    break;
            }
            this.setScroll(e);
        }
        return e;
    }
    /**
     * Dispatch this event to trigger vertical mouse wheel from plugins
     */
    mousewheelVertical({ detail: e, }) {
        this.verticalMouseWheel(e);
    }
    /**
     * Dispatch this event to trigger horizontal mouse wheel from plugins
     */
    mousewheelHorizontal({ detail: e, }) {
        this.horizontalMouseWheel(e);
    }
    /**
     * Allows to use outside listener
     */
    scrollApply({ detail: { type, coordinate }, }) {
        this.applyOnScroll(type, coordinate, true);
    }
    connectedCallback() {
        /**
         * Bind scroll functions for farther usage
         */
        // allow mousewheel for all devices including mobile
        this.verticalMouseWheel = this.onVerticalMouseWheel.bind(this, 'rgRow', 'deltaY');
        this.horizontalMouseWheel = this.onHorizontalMouseWheel.bind(this, 'rgCol', 'deltaX');
        this.localScrollTimer = new LocalScrollTimer('ontouchstart' in document.documentElement ? 0 : 10);
        /**
         * Create local scroll service
         */
        this.localScrollService = new LocalScrollService({
            // to improve safari smoothnes on scroll
            // skipAnimationFrame: isSafariDesktop(),
            runScroll: e => this.scrollViewport.emit(e),
            applyScroll: e => {
                this.localScrollTimer.setCoordinate(e);
                switch (e.dimension) {
                    case 'rgCol':
                        // this will trigger on scroll event
                        this.horizontalScroll.scrollLeft = e.coordinate;
                        break;
                    case 'rgRow':
                        if (this.verticalScroll) {
                            // this will trigger on scroll event
                            this.verticalScroll.scrollTop = e.coordinate;
                            // for mobile devices to skip negative scroll loop. only on vertical scroll
                            if (this.verticalScroll.style.transform) {
                                this.verticalScroll.style.transform = '';
                            }
                        }
                        break;
                }
            },
        });
    }
    componentDidLoad() {
        // track viewport resize
        this.resizeService = new GridResizeService(this.horizontalScroll, (entry) => {
            var _a, _b, _c, _d, _e, _f, _g, _h;
            const els = {};
            let calculatedHeight = entry.height || 0;
            if (calculatedHeight) {
                calculatedHeight -=
                    ((_b = (_a = this.header) === null || _a === void 0 ? void 0 : _a.clientHeight) !== null && _b !== void 0 ? _b : 0) +
                        ((_d = (_c = this.footer) === null || _c === void 0 ? void 0 : _c.clientHeight) !== null && _d !== void 0 ? _d : 0);
            }
            els.rgRow = {
                size: calculatedHeight,
                contentSize: this.contentHeight,
                scroll: (_f = (_e = this.verticalScroll) === null || _e === void 0 ? void 0 : _e.scrollTop) !== null && _f !== void 0 ? _f : 0,
                noScroll: false,
            };
            const calculatedWidth = entry.width || 0;
            els.rgCol = {
                size: calculatedWidth,
                contentSize: this.contentWidth,
                scroll: this.horizontalScroll.scrollLeft,
                noScroll: this.colType !== 'rgCol',
            };
            // Process changes in order: width first, then height
            const dimensions = ['rgCol', 'rgRow'];
            for (const dimension of dimensions) {
                const item = els[dimension];
                if (!item)
                    continue;
                this.resizeViewport.emit({
                    dimension,
                    size: item.size,
                    rowHeader: this.rowHeader,
                });
                if (item.noScroll) {
                    continue;
                }
                (_g = this.localScrollService) === null || _g === void 0 ? void 0 : _g.scroll((_h = item.scroll) !== null && _h !== void 0 ? _h : 0, dimension, true);
                // track scroll visibility on outer element change
                this.setScrollVisibility(dimension, item.size, item.contentSize);
            }
        }, [this.footer, this.header]);
    }
    /**
     * Check if scroll present or not per type
     * Trigger this method on inner content size change or on outer element size change
     * If inner content bigger then outer size then scroll is present and mousewheel binding required
     * @param type - dimension type 'rgRow/y' or 'rgCol/x'
     * @param size - outer content size
     * @param innerContentSize - inner content size
     */
    setScrollVisibility(type, size, innerContentSize) {
        // test if scroll present
        const hasScroll = size < innerContentSize;
        let el;
        // event reference for binding
        switch (type) {
            case 'rgCol':
                el = this.horizontalScroll;
                break;
            case 'rgRow':
                el = this.verticalScroll;
                break;
        }
        // based on scroll visibility assign or remove class and event
        if (hasScroll) {
            el === null || el === void 0 ? void 0 : el.classList.add(`scroll-${type}`);
        }
        else {
            el === null || el === void 0 ? void 0 : el.classList.remove(`scroll-${type}`);
        }
        this.scrollchange.emit({ type, hasScroll });
    }
    disconnectedCallback() {
        var _a;
        (_a = this.resizeService) === null || _a === void 0 ? void 0 : _a.destroy();
    }
    async componentDidRender() {
        var _a, _b, _c, _d;
        this.localScrollService.setParams({
            contentSize: this.contentHeight,
            clientSize: (_b = (_a = this.verticalScroll) === null || _a === void 0 ? void 0 : _a.clientHeight) !== null && _b !== void 0 ? _b : 0,
            virtualSize: 0,
        }, 'rgRow');
        this.localScrollService.setParams({
            contentSize: this.contentWidth,
            clientSize: this.horizontalScroll.clientWidth,
            virtualSize: 0,
        }, 'rgCol');
        this.setScrollVisibility('rgRow', (_d = (_c = this.verticalScroll) === null || _c === void 0 ? void 0 : _c.clientHeight) !== null && _d !== void 0 ? _d : 0, this.contentHeight);
        this.setScrollVisibility('rgCol', this.horizontalScroll.clientWidth, this.contentWidth);
    }
    render() {
        return (h(Host, { key: '2be96df29e23c7a681a45675567b5f168786cf92', onWheel: this.horizontalMouseWheel, onScroll: (e) => this.applyScroll('rgCol', e) }, h("div", { key: '49152054dc9c8bdbffc430aa4ee2cf8c2732e05c', class: "inner-content-table", style: { width: `${this.contentWidth}px` } }, h("div", { key: '6a2124ed6345021fbbfbd78607dc280918ef9bc5', class: "header-wrapper", ref: e => (this.header = e) }, h("slot", { key: '0a25f0620be889d87fbf81325ffb596919105f4d', name: HEADER_SLOT })), h("div", { key: '7c986664c70ebc6f6d996864e591e484d709c3a5', class: "vertical-inner", ref: el => (this.verticalScroll = el), onWheel: this.verticalMouseWheel, onScroll: (e) => this.applyScroll('rgRow', e) }, h("div", { key: 'bf810ecbb62b35334b24ab72292ae37866002cb9', class: "content-wrapper", style: { height: `${this.contentHeight}px` } }, h("slot", { key: '7d0bf53549eb60d9e6942b05fe1037131562ca2e', name: CONTENT_SLOT }))), h("div", { key: '78d8bdc545e220b324665b5331d902d887ea5e2b', class: "footer-wrapper", ref: e => (this.footer = e) }, h("slot", { key: '76d5b39a94ba27411ca0b58e49714b21e0e837bf', name: FOOTER_SLOT })))));
    }
    /**
     * Extra layer for scroll event monitoring, where MouseWheel event is not passing
     * We need to trigger scroll event in case there is no mousewheel event
     */
    async applyScroll(type, e) {
        if (!(e.target instanceof HTMLElement)) {
            return;
        }
        let scroll = 0;
        switch (type) {
            case 'rgCol':
                scroll = e.target.scrollLeft;
                break;
            case 'rgRow':
                scroll = e.target.scrollTop;
                break;
        }
        // for mobile devices to skip negative scroll loop
        if (scroll < 0) {
            this.silentScroll.emit({ dimension: type, coordinate: scroll });
            return;
        }
        this.applyOnScroll(type, scroll);
    }
    /**
     * Applies change on scroll event only if mousewheel event happened some time ago
     */
    applyOnScroll(type, coordinate, outside = false) {
        const lastScrollUpdate = () => {
            var _a;
            (_a = this.localScrollService) === null || _a === void 0 ? void 0 : _a.scroll(coordinate, type, undefined, undefined, outside);
        };
        // apply after throttling
        if (this.localScrollTimer.isReady(type, coordinate)) {
            lastScrollUpdate();
        }
        else {
            this.localScrollTimer.throttleLastScrollUpdate(type, coordinate, () => lastScrollUpdate());
        }
    }
    /**
     * On vertical mousewheel event
     * @param type
     * @param delta
     * @param e
     */
    onVerticalMouseWheel(type, delta, e) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const scrollTop = (_b = (_a = this.verticalScroll) === null || _a === void 0 ? void 0 : _a.scrollTop) !== null && _b !== void 0 ? _b : 0;
        const clientHeight = (_d = (_c = this.verticalScroll) === null || _c === void 0 ? void 0 : _c.clientHeight) !== null && _d !== void 0 ? _d : 0;
        const scrollHeight = (_f = (_e = this.verticalScroll) === null || _e === void 0 ? void 0 : _e.scrollHeight) !== null && _f !== void 0 ? _f : 0;
        // Detect if the user has reached the bottom
        const atBottom = scrollTop + clientHeight >= scrollHeight && e.deltaY > 0;
        const atTop = scrollTop === 0 && e.deltaY < 0;
        if (!atBottom && !atTop) {
            (_g = e.preventDefault) === null || _g === void 0 ? void 0 : _g.call(e);
        }
        const pos = scrollTop + e[delta];
        (_h = this.localScrollService) === null || _h === void 0 ? void 0 : _h.scroll(pos, type, undefined, e[delta]);
        this.localScrollTimer.latestScrollUpdate(type);
    }
    /**
     * On horizontal mousewheel event
     * @param type
     * @param delta
     * @param e
     */
    onHorizontalMouseWheel(type, delta, e) {
        var _a, _b;
        if (!e.deltaX) {
            return;
        }
        const { scrollLeft, scrollWidth, clientWidth } = this.horizontalScroll;
        // Detect if the user has reached the right end
        const atRight = scrollLeft + clientWidth >= scrollWidth && e.deltaX > 0;
        // Detect if the user has reached the left end
        const atLeft = scrollLeft === 0 && e.deltaX < 0;
        if (!atRight && !atLeft) {
            (_a = e.preventDefault) === null || _a === void 0 ? void 0 : _a.call(e);
        }
        const pos = scrollLeft + e[delta];
        (_b = this.localScrollService) === null || _b === void 0 ? void 0 : _b.scroll(pos, type, undefined, e[delta]);
        this.localScrollTimer.latestScrollUpdate(type);
    }
    get horizontalScroll() { return this; }
    static get style() { return revogrViewportScrollStyleCss; }
}, [4, "revogr-viewport-scroll", {
        "rowHeader": [4, "row-header"],
        "contentWidth": [2, "content-width"],
        "contentHeight": [2, "content-height"],
        "colType": [1, "col-type"],
        "setScroll": [64],
        "changeScroll": [64],
        "applyScroll": [64]
    }, [[0, "mousewheel-vertical", "mousewheelVertical"], [0, "mousewheel-horizontal", "mousewheelHorizontal"], [0, "scroll-coordinate", "scrollApply"]]]);
function defineCustomElement() {
    if (typeof customElements === "undefined") {
        return;
    }
    const components = ["revogr-viewport-scroll"];
    components.forEach(tagName => { switch (tagName) {
        case "revogr-viewport-scroll":
            if (!customElements.get(tagName)) {
                customElements.define(tagName, RevogrViewportScroll);
            }
            break;
    } });
}

export { CONTENT_SLOT as C, DATA_SLOT as D, FOOTER_SLOT as F, HEADER_SLOT as H, RevogrViewportScroll as R, defineCustomElement as d, viewportDataPartition as v };
//# sourceMappingURL=revogr-viewport-scroll2.js.map

//# sourceMappingURL=revogr-viewport-scroll2.js.map