{"file": "vnode-converter.js", "mappings": ";;;;;MAqBa,WAAW,iBAAAA,kBAAA,CAAA,MAAA,WAAA,SAAA,WAAA,CAAA;AAHxB,IAAA,WAAA,GAAA;;;;AAIU,QAAA,IAAM,CAAA,MAAA,GAAuC,IAAI;AAIjD,QAAA,IAAM,CAAA,MAAA,GAAmB,EAAE;AAmBpC;IAjBC,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACb,YAAA,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,SAAS;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;AACpB,SAAA,CAAC;;IAGJ,MAAM,GAAA;;QACJ,IAAI,CAAC,MAAM,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAI,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,IAAI;AACrC,QAAA,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EACH,KAAK,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAA,EAEpD,IAAI,CAAC,MAAM,CACP;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/components/vnode/vnode-converter.tsx"], "sourcesContent": ["import {\n  Component,\n  Element,\n  Event,\n  EventEmitter,\n  Host,\n  Prop,\n  type VNode,\n  h,\n} from '@stencil/core';\n\n/**\n * VNode to html converter for stencil components.\n * Transform VNode to html string.\n */\n/**\n * @internal\n */\n@Component({\n  tag: 'vnode-html',\n})\nexport class VNodeToHtml {\n  @Prop() redraw: (() => VNode[]) | null | undefined = null;\n  @Event() html: EventEmitter<{ html: string; vnodes: (VNode[]) | null }>;\n  @Element() el: HTMLElement;\n\n  private vnodes: VNode[] | null = [];\n\n  componentDidRender() {\n    this.html.emit({\n      html: this.el.innerHTML,\n      vnodes: this.vnodes,\n    });\n  }\n\n  render() {\n    this.vnodes = this.redraw?.() ?? null;\n    return (\n      <Host\n        style={{ visibility: 'hidden', position: 'absolute' }}\n      >\n        {this.vnodes}\n      </Host>\n    );\n  }\n}\n"], "version": 3}