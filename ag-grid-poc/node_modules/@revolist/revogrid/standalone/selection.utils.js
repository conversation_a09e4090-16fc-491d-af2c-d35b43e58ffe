/*!
 * Built by Revolist OU ❤️
 */
import { b as getSourceItem } from './data.store.js';
import '@stencil/core/internal/client';
import './platform.js';
import { a as getItemByIndex, g as getItemByPosition } from './dimension.helpers.js';

function isTouch(e) {
    return !!e.touches;
}
function verifyTouchTarget(touchEvent, focusClass) {
    if (focusClass && touchEvent) {
        if (!(touchEvent.target instanceof Element && touchEvent.target.classList.contains(focusClass))) {
            return false;
        }
    }
    return true;
}
/**
 * Function to get the value of a specific property from a MouseEvent or TouchEvent object.
 */
function getPropertyFromEvent(e, prop, focusClass // for touch events
) {
    // Check if the event is a touch event
    if (isTouch(e)) {
        // If the event has touches, get the first touch
        if (e.touches.length > 0) {
            const touchEvent = e.touches[0];
            // Check if the target of the touch event is the specified element
            if (!verifyTouchTarget(touchEvent, focusClass)) {
                // If not, return null
                return null;
            }
            // Get the value of the specified property from the touch event and return it
            return touchEvent[prop] || 0;
        }
        // If there are no touches, return null
        return null;
    }
    // If the event is not a touch event, get the value of the specified property from the event and return it
    return e[prop] || 0;
}

function collectModelsOfRange(data, store) {
    const models = {};
    for (let i in data) {
        const rowIndex = parseInt(i, 10);
        models[rowIndex] = getSourceItem(store, rowIndex);
    }
    return models;
}
function getFocusCellBasedOnEvent(e, data) {
    // If event default is prevented, return
    if (e.defaultPrevented) {
        return null;
    }
    // Get coordinates from event object
    const x = getPropertyFromEvent(e, 'clientX');
    const y = getPropertyFromEvent(e, 'clientY');
    // If coordinates are not available, return
    if (x === null || y === null) {
        return null;
    }
    // Get current cell based on coordinates and data
    const focusCell = getCurrentCell({ x, y }, data);
    // If current cell is not available, return
    if (isAfterLast(focusCell, data.lastCell)) {
        return null;
    }
    return focusCell;
}
/**
 * Calculate cell based on x, y position
 */
function getCurrentCell({ x, y }, { el, rows, cols }) {
    // Get the bounding rectangle of the element
    const { top, left, height, width } = el.getBoundingClientRect();
    // Calculate the cell position relative to the element
    let cellY = y - top;
    let cellX = x - left;
    // Limit the cell position to the element height
    if (cellY >= height) {
        cellY = height - 1;
    }
    // Limit the cell position to the element width
    if (cellX >= width) {
        cellX = width - 1;
    }
    // Get the row and column items based on the cell position
    const rgRow = getItemByPosition(rows, cellY);
    const rgCol = getItemByPosition(cols, cellX);
    // Set the row and column index to 0 if they are before the first item
    if (rgCol.itemIndex < 0) {
        rgCol.itemIndex = 0;
    }
    if (rgRow.itemIndex < 0) {
        rgRow.itemIndex = 0;
    }
    return { x: rgCol.itemIndex, y: rgRow.itemIndex };
}
function getCoordinate(range, focus, changes, isMulti = false) {
    const updateCoordinate = (c, pos = 0) => {
        const start = { x: range.x, y: range.y };
        const end = isMulti ? { x: range.x1, y: range.y1 } : start;
        const point = end[c] > focus[c] ? end : start;
        point[c] += pos;
        return { start, end };
    };
    if (changes.x) {
        return updateCoordinate('x', changes['x']);
    }
    if (changes.y) {
        return updateCoordinate('y', changes['y']);
    }
    return null;
}
/**
 * Check if the x coordinate of the cell position is after or equal to the x coordinate of the last cell position
 * or if the y coordinate of the cell position is after or equal to the y coordinate of the last cell position
 */
function isAfterLast({ x, y }, lastCell) {
    return x >= lastCell.x || y >= lastCell.y;
}
/** check if out of range */
function isBeforeFirst({ x, y }) {
    return x < 0 || y < 0;
}
/** Compare cells, only 1 coordinate difference is possible */
// export function getDirectionCoordinate(initial: Cell, last: Cell): Partial<Cell> | null {
//   const c: (keyof Cell)[] = ['x', 'y'];
//   for (let k of c) {
//     if (initial[k] !== last[k]) {
//       return { [k]: 1 };
//     }
//   }
//   return null;
// }
// export function getLargestAxis(initial: Cell, last: Cell): Partial<Cell> | null {
//   const cell: Partial<Cell> = {};
//   const c: (keyof Cell)[] = ['x', 'y'];
//   for (let k of c) {
//     cell[k] = Math.abs(initial[k] - last[k]);
//   }
//   if (cell.x > cell.y) {
//     return { x: 1 };
//   }
//   if (cell.y > cell.x) {
//     return { y: 1 };
//   }
//   return null;
// }
function styleByCellProps(styles) {
    return {
        left: `${styles.left}px`,
        top: `${styles.top}px`,
        width: `${styles.width}px`,
        height: `${styles.height}px`,
    };
}
function getCell({ x, y, x1, y1 }, dimensionRow, dimensionCol) {
    const top = getItemByIndex(dimensionRow, y).start;
    const left = getItemByIndex(dimensionCol, x).start;
    const bottom = getItemByIndex(dimensionRow, y1).end;
    const right = getItemByIndex(dimensionCol, x1).end;
    return {
        left,
        right,
        top,
        bottom,
        width: right - left,
        height: bottom - top,
    };
}

export { getCell as a, getCoordinate as b, isBeforeFirst as c, getCurrentCell as d, collectModelsOfRange as e, getFocusCellBasedOnEvent as f, getPropertyFromEvent as g, isAfterLast as i, styleByCellProps as s, verifyTouchTarget as v };
//# sourceMappingURL=selection.utils.js.map

//# sourceMappingURL=selection.utils.js.map