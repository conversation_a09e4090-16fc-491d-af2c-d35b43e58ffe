{"file": "dimension.helpers.js", "mappings": ";;;;;;AAEA;AACA,IAAIA,kBAAgB,GAAG,UAAU;AACjC,IAAI,eAAe,GAAGA,kBAAgB,GAAG,CAAC;;AAE1C;AACA,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK;AAC5B,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE;AAC/D,EAAE,IAAI,GAAG,GAAG,CAAC;AACb,MAAM,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM;AAC7C,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE;AAClB,IAAI,OAAO,CAAC;AACZ;;AAEA,EAAE,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AACzB,EAAE,IAAI,QAAQ,GAAG,KAAK,KAAK,KAAK;AAChC,MAAM,SAAS,GAAG,KAAK,KAAK,IAAI;AAChC,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC;AACnC,MAAM,cAAc,GAAG,KAAK,KAAK,SAAS;;AAE1C,EAAE,OAAO,GAAG,GAAG,IAAI,EAAE;AACrB,IAAI,IAAI,GAAG,GAAG,WAAW,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC;AAC3C,QAAQ,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACvC,QAAQ,YAAY,GAAG,QAAQ,KAAK,SAAS;AAC7C,QAAQ,SAAS,GAAG,QAAQ,KAAK,IAAI;AACrC,QAAQ,cAAc,GAAG,QAAQ,KAAK,QAAQ;AAC9C,QAAQ,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC;;AAExC,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,IAAI,MAAM,GAAiB,cAAc;AAC/C,KAAK,MAAM,IAAI,cAAc,EAAE;AAC/B,MAAM,MAAM,GAAG,cAAc,KAAmB,YAAY,CAAC;AAC7D,KAAK,MAAM,IAAI,SAAS,EAAE;AAC1B,MAAM,MAAM,GAAG,cAAc,IAAI,YAAY,KAAmB,CAAC,SAAS,CAAC;AAC3E,KAAK,MAAM,IAAI,WAAW,EAAE;AAC5B,MAAM,MAAM,GAAG,cAAc,IAAI,YAAY,IAAI,CAAC,SAAS,KAAmB,CAAC,WAAW,CAAC;AAC3F,KAAK,MAAM,IAAI,SAAS,IAAI,WAAW,EAAE;AACzC,MAAM,MAAM,GAAG,KAAK;AACpB,KAAK,MAAM;AACX,MAAM,MAAM,GAAsC,CAAC,QAAQ,GAAG,KAAK,CAAC;AACpE;AACA,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC;AACnB,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,GAAG;AAChB;AACA;AACA,EAAE,OAAO,SAAS,CAAC,IAAI,EAAE,eAAe,CAAC;AACzC;;AC5DA;AACA,IAAI,gBAAgB,GAAG,UAAU;AACjC,IAAI,qBAAqB,GAAG,gBAAgB,KAAK,CAAC;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE;AACnD,EAAE,IAAI,GAAG,GAAG,CAAC;AACb,MAAM,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,MAAM;;AAE/C,EAAE,IAAI,OAAO,KAAK,IAAI,QAAQ,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,qBAAqB,EAAE;AACpF,IAAI,OAAO,GAAG,GAAG,IAAI,EAAE;AACvB,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC;AAClC,UAAU,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC;;AAE/B,MAAM,IAAI,QAAQ,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAClD,WAA8C,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,EAAE;AACnE,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;AACrB,OAAO,MAAM;AACb,QAAQ,IAAI,GAAG,GAAG;AAClB;AACA;AACA,IAAI,OAAO,IAAI;AACf;AACA,EAAE,OAAO,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,QAAoB,CAAC;AAC9D;;ACrCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE;AACnC,EAAE,OAAO,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC;AACtC;;ACIA;;;;AAIG;SACa,sBAAsB,CACpC,cAAsB,EACtB,WAAgC,EAAE,EAAA;IAElC,MAAM,eAAe,GAAa,EAAE;IACpC,MAAM,mBAAmB,GAAyC,EAAE;IACpE,MAAM,WAAW,GAAsC,EAAE;;IAGzD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1E,IAAA,IAAI,QAAkC;AACtC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAA,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC;AAC/B,QAAA,MAAM,OAAO,GAAiB;YAC5B,SAAS;AACT,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,GAAG,EAAE,CAAC;SACP;;QAED,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,YAAY,GAChB,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,GAAG,CAAC,IAAI,cAAc;YACvD,OAAO,CAAC,KAAK,GAAG,YAAY,GAAG,QAAQ,CAAC,GAAG;;aACtC;AACL,YAAA,OAAO,CAAC,KAAK,GAAG,SAAS,GAAG,cAAc;;QAE5C,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC;AACjD,QAAA,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACnC,WAAW,CAAC,SAAS,CAAC,GAAG,mBAAmB,CAAC,CAAC,CAAC,GAAG,OAAO;QACzD,QAAQ,GAAG,OAAO;;IAEpB,OAAO;AACL,QAAA,OAAO,EAAE,UAAU;AACnB,QAAA,eAAe,EAAE,CAAC,GAAG,eAAe,CAAC;QACrC,mBAAmB,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAO,mBAAmB,CAAE;QAC/C,WAAW;KACZ;AACH;AAEA;;AAEG;AACU,MAAA,iBAAiB,GAAG,CAC/B,EACE,OAAO,EACP,eAAe,EACf,cAAc,EACd,mBAAmB,GAC+E,EACpG,GAAW,KACT;AACF,IAAA,MAAM,IAAI,GAAiB;AACzB,QAAA,SAAS,EAAE,CAAC;AACZ,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,GAAG,EAAE,CAAC;KACP;AACD,IAAA,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,eAAe,EAAE,GAAG,CAAC,GAAG,CAAC;;IAE3E,IAAI,CAAC,YAAY,EAAE;QACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,cAAc,CAAC;QACjD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,GAAG,cAAc;QAC5C,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,cAAc;AACtC,QAAA,OAAO,IAAI;;IAEb,MAAM,YAAY,GAAG,mBAAmB,CAAC,YAAY,GAAG,CAAC,CAAC;;AAE1D,IAAA,IAAI,YAAY,CAAC,GAAG,GAAG,GAAG,EAAE;AAC1B,QAAA,OAAO,YAAY;;;AAGrB,IAAA,MAAM,WAAW,GAAG,GAAG,GAAG,YAAY,CAAC,GAAG;IAC1C,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,cAAc,CAAC;IAC9D,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,GAAG,CAAC,GAAG,aAAa;IAC3D,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,GAAG,GAAG,aAAa,GAAG,cAAc;IAC9D,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,cAAc;AACtC,IAAA,OAAO,IAAI;AACb;AAEgB,SAAA,cAAc,CAC5B,SAGC,EACD,KAAa,EAAA;AAEb,IAAA,IAAI,IAAI,GAAiB;AACvB,QAAA,SAAS,EAAE,KAAK;AAChB,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,GAAG,EAAE,CAAC;KACP;;AAED,IAAA,IAAI,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAChC,QAAA,OAAO,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC;;AAGrC,IAAA,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,CAAC;UACnC,WAAW,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK;UACpC,CAAC;;IAEL,IAAI,CAAC,YAAY,EAAE;QACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,cAAc;QACtD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,cAAc;AAChD,QAAA,OAAO,IAAI;;;AAGb,IAAA,MAAM,YAAY,GAChB,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;AAC5D,IAAA,IAAI,CAAC,KAAK;AACR,QAAA,YAAY,CAAC,GAAG;AAChB,YAAA,CAAC,KAAK,GAAG,YAAY,CAAC,SAAS,GAAG,CAAC,IAAI,SAAS,CAAC,cAAc;IACjE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,cAAc;AAChD,IAAA,OAAO,IAAI;AACb;;;;", "names": ["MAX_ARRAY_LENGTH"], "sources": ["node_modules/lodash/_baseSortedIndexBy.js", "node_modules/lodash/_baseSortedIndex.js", "node_modules/lodash/sortedIndex.js", "src/store/dimension/dimension.helpers.ts"], "sourcesContent": ["import isSymbol from './isSymbol.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295,\n    MAX_ARRAY_INDEX = MAX_ARRAY_LENGTH - 1;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeFloor = Math.floor,\n    nativeMin = Math.min;\n\n/**\n * The base implementation of `_.sortedIndexBy` and `_.sortedLastIndexBy`\n * which invokes `iteratee` for `value` and each element of `array` to compute\n * their sort ranking. The iteratee is invoked with one argument; (value).\n *\n * @private\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {Function} iteratee The iteratee invoked per element.\n * @param {boolean} [retHighest] Specify returning the highest qualified index.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n */\nfunction baseSortedIndexBy(array, value, iteratee, retHighest) {\n  var low = 0,\n      high = array == null ? 0 : array.length;\n  if (high === 0) {\n    return 0;\n  }\n\n  value = iteratee(value);\n  var valIsNaN = value !== value,\n      valIsNull = value === null,\n      valIsSymbol = isSymbol(value),\n      valIsUndefined = value === undefined;\n\n  while (low < high) {\n    var mid = nativeFloor((low + high) / 2),\n        computed = iteratee(array[mid]),\n        othIsDefined = computed !== undefined,\n        othIsNull = computed === null,\n        othIsReflexive = computed === computed,\n        othIsSymbol = isSymbol(computed);\n\n    if (valIsNaN) {\n      var setLow = retHighest || othIsReflexive;\n    } else if (valIsUndefined) {\n      setLow = othIsReflexive && (retHighest || othIsDefined);\n    } else if (valIsNull) {\n      setLow = othIsReflexive && othIsDefined && (retHighest || !othIsNull);\n    } else if (valIsSymbol) {\n      setLow = othIsReflexive && othIsDefined && !othIsNull && (retHighest || !othIsSymbol);\n    } else if (othIsNull || othIsSymbol) {\n      setLow = false;\n    } else {\n      setLow = retHighest ? (computed <= value) : (computed < value);\n    }\n    if (setLow) {\n      low = mid + 1;\n    } else {\n      high = mid;\n    }\n  }\n  return nativeMin(high, MAX_ARRAY_INDEX);\n}\n\nexport default baseSortedIndexBy;\n", "import baseSortedIndexBy from './_baseSortedIndexBy.js';\nimport identity from './identity.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295,\n    HALF_MAX_ARRAY_LENGTH = MAX_ARRAY_LENGTH >>> 1;\n\n/**\n * The base implementation of `_.sortedIndex` and `_.sortedLastIndex` which\n * performs a binary search of `array` to determine the index at which `value`\n * should be inserted into `array` in order to maintain its sort order.\n *\n * @private\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {boolean} [retHighest] Specify returning the highest qualified index.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n */\nfunction baseSortedIndex(array, value, retHighest) {\n  var low = 0,\n      high = array == null ? low : array.length;\n\n  if (typeof value == 'number' && value === value && high <= HALF_MAX_ARRAY_LENGTH) {\n    while (low < high) {\n      var mid = (low + high) >>> 1,\n          computed = array[mid];\n\n      if (computed !== null && !isSymbol(computed) &&\n          (retHighest ? (computed <= value) : (computed < value))) {\n        low = mid + 1;\n      } else {\n        high = mid;\n      }\n    }\n    return high;\n  }\n  return baseSortedIndexBy(array, value, identity, retHighest);\n}\n\nexport default baseSortedIndex;\n", "import baseSortedIndex from './_baseSortedIndex.js';\n\n/**\n * Uses a binary search to determine the lowest index at which `value`\n * should be inserted into `array` in order to maintain its sort order.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n * @example\n *\n * _.sortedIndex([30, 50], 40);\n * // => 1\n */\nfunction sortedIndex(array, value) {\n  return baseSortedIndex(array, value);\n}\n\nexport default sortedIndex;\n", "import sortedIndex from 'lodash/sortedIndex';\n\nimport type {\n  DimensionSettingsState,\n  PositionItem,\n  ViewSettingSizeProp,\n} from '@type';\n\nexport type DimensionPosition = Pick<\n  DimensionSettingsState,\n  'indexes' | 'positionIndexes' | 'originItemSize' | 'positionIndexToItem'\n>;\nexport type DimensionIndexInput = Pick<\n  DimensionSettingsState,\n  'indexes' | 'originItemSize' | 'indexToItem'\n>;\nexport type DimensionSize = Pick<\n  DimensionSettingsState,\n  | 'indexes'\n  | 'positionIndexes'\n  | 'positionIndexToItem'\n  | 'indexToItem'\n  | 'realSize'\n  | 'sizes'\n>;\n/**\n * Pre-calculation\n * Dimension custom sizes for each cell\n * Keeps only changed sizes, skips origin size\n */\nexport function calculateDimensionData(\n  originItemSize: number,\n  newSizes: ViewSettingSizeProp = {},\n) {\n  const positionIndexes: number[] = [];\n  const positionIndexToItem: { [position: number]: PositionItem } = {};\n  const indexToItem: { [index: number]: PositionItem } = {};\n\n  // prepare order sorted new sizes and calculate changed real size\n  const newIndexes = Object.keys(newSizes).map(Number).sort((a, b) => a - b);\n  let previous: PositionItem | undefined;\n  for (let i = 0; i < newIndexes.length; i++) {\n    const itemIndex = newIndexes[i];\n    const newItem: PositionItem = {\n      itemIndex,\n      start: 0,\n      end: 0,\n    };\n    // if previous item was changed too\n    if (previous) {\n      const itemsBetween =\n        (itemIndex - previous.itemIndex - 1) * originItemSize;\n      newItem.start = itemsBetween + previous.end;\n    } else {\n      newItem.start = itemIndex * originItemSize;\n    }\n    newItem.end = newItem.start + newSizes[itemIndex];\n    positionIndexes.push(newItem.start);\n    indexToItem[itemIndex] = positionIndexToItem[i] = newItem;\n    previous = newItem;\n  }\n  return {\n    indexes: newIndexes,\n    positionIndexes: [...positionIndexes],\n    positionIndexToItem: { ...positionIndexToItem },\n    indexToItem,\n  };\n}\n\n/**\n * Calculate item by position\n */\nexport const getItemByPosition = (\n  {\n    indexes,\n    positionIndexes,\n    originItemSize,\n    positionIndexToItem,\n  }: Pick<DimensionPosition, 'indexes' | 'positionIndexes' | 'originItemSize' | 'positionIndexToItem'>,\n  pos: number,\n) => {\n  const item: PositionItem = {\n    itemIndex: 0,\n    start: 0,\n    end: 0,\n  };\n  const currentPlace = indexes.length ? sortedIndex(positionIndexes, pos) : 0;\n  // not found or first index\n  if (!currentPlace) {\n    item.itemIndex = Math.floor(pos / originItemSize);\n    item.start = item.itemIndex * originItemSize;\n    item.end = item.start + originItemSize;\n    return item;\n  }\n  const positionItem = positionIndexToItem[currentPlace - 1];\n  // if item has specified size\n  if (positionItem.end > pos) {\n    return positionItem;\n  }\n  // special size item was present before\n  const relativePos = pos - positionItem.end;\n  const relativeIndex = Math.floor(relativePos / originItemSize);\n  item.itemIndex = positionItem.itemIndex + 1 + relativeIndex;\n  item.start = positionItem.end + relativeIndex * originItemSize;\n  item.end = item.start + originItemSize;\n  return item;\n};\n\nexport function getItemByIndex(\n  dimension: Pick<\n    DimensionIndexInput,\n    'indexToItem' | 'indexes' | 'originItemSize'\n  >,\n  index: number,\n) {\n  let item: PositionItem = {\n    itemIndex: index,\n    start: 0,\n    end: 0,\n  };\n  // if item has specified size\n  if (dimension.indexToItem[index]) {\n    return dimension.indexToItem[index];\n  }\n\n  const currentPlace = dimension.indexes.length\n    ? sortedIndex(dimension.indexes, index)\n    : 0;\n  // not found or first index\n  if (!currentPlace) {\n    item.start = item.itemIndex * dimension.originItemSize;\n    item.end = item.start + dimension.originItemSize;\n    return item;\n  }\n  // special size item was present before\n  const positionItem =\n    dimension.indexToItem[dimension.indexes[currentPlace - 1]];\n  item.start =\n    positionItem.end +\n    (index - positionItem.itemIndex - 1) * dimension.originItemSize;\n  item.end = item.start + dimension.originItemSize;\n  return item;\n}\n"], "version": 3}