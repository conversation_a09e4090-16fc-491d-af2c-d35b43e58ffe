{"file": "revogr-clipboard2.js", "mappings": ";;;;;MAaa,SAAS,iBAAAA,kBAAA,CAAA,MAAA,SAAA,SAAA,WAAA,CAAA;;;;;;;;;;;;;;AAkFqB,IAAA,OAAO,CAAC,CAAiB,EAAA;;AAEhE,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB;;QAEF,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AACrC,QAAA,MAAM,MAAM,GAAG,CAAC,CAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,MAAA,GAAA,MAAA,GAAA,aAAa,CAAE,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,KAAI,EAAE,IAAI,EAAE;QACrE,MAAM,IAAI,GAAG,CAAC;cACV,aAAa,KAAA,IAAA,IAAb,aAAa,KAAA,MAAA,GAAA,MAAA,GAAb,aAAa,CAAE,OAAO,CAAC,WAAW;cAClC,aAAa,KAAb,IAAA,IAAA,aAAa,uBAAb,aAAa,CAAE,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;AACzC,QAAA,MAAM,QAAQ,GAAG,CAAA,aAAa,aAAb,aAAa,KAAA,MAAA,GAAA,MAAA,GAAb,aAAa,CAAE,OAAO,CAAC,MAAM,CAAC,KAAI,EAAE;AAErD,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AACxC,YAAA,GAAG,EAAE,IAAI;YACT,QAAQ;YACR,MAAM;AACN,YAAA,KAAK,EAAE,CAAC;AACT,SAAA,CAAC;AAEF,QAAA,IAAI,WAAW,CAAC,gBAAgB,EAAE;YAChC;;AAGF,QAAA,IAAI,UAAsB;;AAE1B,QAAA,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE;AAC7B,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC;;YAEpD,UAAU,GAAG,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,EAAE,CAAC;;aAC/C;YACL,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC;;AAErD,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AAClD,YAAA,GAAG,EAAE,IAAI;AACT,YAAA,MAAM,EAAE,UAAU;AAClB,YAAA,KAAK,EAAE,CAAC;AACT,SAAA,CAAC;AACF,QAAA,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;YACrC;;QAEF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC;;AAErD,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AAChD,YAAA,GAAG,EAAE,IAAI;AACT,YAAA,MAAM,EAAE,UAAU;AAClB,YAAA,KAAK,EAAE,CAAC;AACT,SAAA,CAAC;;AAEF,QAAA,IAAI,eAAe,CAAC,gBAAgB,EAAE;YACpC;;QAEF,CAAC,CAAC,cAAc,EAAE;;AAGpB;;AAEG;AACqC,IAAA,WAAW,CAAC,CAAiB,EAAA;AACnE,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACtC,YAAA,KAAK,EAAE,CAAC;AACT,SAAA,CAAC;AACF,QAAA,IAAI,UAAU,CAAC,gBAAgB,EAAE;YAC/B;;AAEF,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC;QAClD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,SAAS,CAAC;QACvC,CAAC,CAAC,cAAc,EAAE;;AAGpB;;AAEG;AACoC,IAAA,UAAU,CAAC,CAAiB,EAAA;AACjE,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AACpC,YAAA,KAAK,EAAE,CAAC;AACT,SAAA,CAAC;AACF,QAAA,IAAI,SAAS,CAAC,gBAAgB,EAAE;YAC9B;;AAEF,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC;AACjD,QAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;AAGnB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB;;QAGF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,SAAS,CAAC;QACxC,CAAC,CAAC,cAAc,EAAE;;AAGV,IAAA,MAAM,MAAM,CAAC,CAAe,EAAE,IAAqB,EAAA;AAC3D,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AAChD,YAAA,KAAK,EAAE,CAAC;YACR,IAAI;AACL,SAAA,CAAC;AACF,QAAA,IAAI,eAAe,CAAC,gBAAgB,EAAE;YACpC;;AAEF,QAAA,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE;AAChD,QAAA,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;;AAGjC,IAAA,UAAU,CAAC,IAAoB,EAAA;QAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;AAG/C,IAAA,SAAS,CAAC,IAAY,EAAA;QAC5B,MAAM,MAAM,GAAe,EAAE;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;AACrC,QAAA,KAAK,IAAI,CAAC,IAAI,IAAI,EAAE;AAClB,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;;AAElC,QAAA,OAAO,MAAM;;AAGP,IAAA,SAAS,CAAC,IAAY,EAAA;QAC5B,MAAM,MAAM,GAAe,EAAE;QAC7B,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,wBAAwB,CAAC,IAAI,CAAC;QACtE,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;QAC7C,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,OAAO,IAAI;;AAEb,QAAA,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;;AAElE,QAAA,OAAO,MAAM;;AAGP,IAAA,OAAO,CAAC,CAAiB,EAAA;QAC/B,QACE,CAAC,CAAC,aAAa;aACd,MAA4D,KAAA,IAAA,IAA5D,MAAM,KAAN,MAAA,GAAA,MAAA,GAAA,MAAM,CACH,aAAa,CAAA;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/components/clipboard/revogr-clipboard.tsx"], "sourcesContent": ["import {\n  Component,\n  Listen,\n  Method,\n  Event,\n  EventEmitter,\n  Prop,\n} from '@stencil/core';\nimport { DataFormat } from '@type';\n/**\n * This Clipboard provides functionality for handling clipboard events in a web application.\n */\n@Component({ tag: 'revogr-clipboard' })\nexport class Clipboard {\n  /**\n   * If readonly mode - disabled Paste event\n   */\n  @Prop() readonly: boolean;\n\n  /**\n   * Paste 1. Fired before paste applied to the grid\n   * defaultPrevented - if true, paste will be canceled\n   */\n  @Event({ eventName: 'beforepaste' }) beforePaste: EventEmitter<{\n    raw: string;\n    isHTML: boolean;\n    event: ClipboardEvent;\n    dataText: string;\n  }>;\n\n  /**\n   * Paste 2. Fired before paste applied to the grid and after data parsed\n   */\n  @Event({ eventName: 'beforepasteapply' })\n  beforePasteApply: EventEmitter<{\n    raw: string;\n    parsed: string[][];\n    event: ClipboardEvent;\n  }>;\n\n  /**\n   * Paste 3. Internal method. When data region is ready pass it to the top.\n   * @property {string[][]} data - data to paste\n   * @property {boolean} defaultPrevented - if true, paste will be canceled\n   */\n  @Event({ eventName: 'pasteregion' })\n  pasteRegion: EventEmitter<string[][]>;\n\n  /**\n   * Paste 4. Fired after paste applied to the grid\n   * defaultPrevented - if true, paste will be canceled\n   */\n  @Event({ eventName: 'afterpasteapply' }) afterPasteApply: EventEmitter<{\n    raw: string;\n    parsed: string[][];\n    event: ClipboardEvent;\n  }>;\n\n  /**\n   * Cut 1. Fired before cut triggered\n   * defaultPrevented - if true, cut will be canceled\n   */\n  @Event({ eventName: 'beforecut' }) beforeCut: EventEmitter<{\n    event: ClipboardEvent;\n  }>;\n\n  /**\n   * Cut 2. Clears region when cut is done\n   */\n  @Event({ eventName: 'clearregion' }) clearRegion: EventEmitter<DataTransfer>;\n\n  /**\n   * Copy 1. Fired before copy triggered\n   * defaultPrevented - if true, copy will be canceled\n   */\n  @Event({ eventName: 'beforecopy' }) beforeCopy: EventEmitter<{\n    event: ClipboardEvent;\n  }>;\n\n  /**\n   * Copy Method 1. Fired before copy applied to the clipboard from outside.\n   * defaultPrevented - if true, copy will be canceled\n   */\n  @Event({ eventName: 'beforecopyapply' }) beforeCopyApply: EventEmitter<{\n    event: DataTransfer;\n    data?: string[][];\n  }>;\n\n  /**\n   * Copy 2. Fired when region copied\n   * defaultPrevented - if true, copy will be canceled\n   */\n  @Event({ eventName: 'copyregion' })\n  copyRegion: EventEmitter<DataTransfer>;\n\n  @Listen('paste', { target: 'document' }) onPaste(e: ClipboardEvent) {\n    // if readonly do nothing\n    if (this.readonly) {\n      return;\n    }\n    const clipboardData = this.getData(e);\n    const isHTML = (clipboardData?.types.indexOf('text/html') || -1) > -1;\n    const data = (isHTML\n      ? clipboardData?.getData('text/html')\n      : clipboardData?.getData('text')) || '';\n    const dataText = clipboardData?.getData('text') || '';\n\n    const beforePaste = this.beforePaste.emit({\n      raw: data,\n      dataText,\n      isHTML,\n      event: e,\n    });\n\n    if (beforePaste.defaultPrevented) {\n      return;\n    }\n\n    let parsedData: string[][];\n    // if html, then search for table if no table fallback to regular text parsing\n    if (beforePaste.detail.isHTML) {\n      const table = this.htmlParse(beforePaste.detail.raw);\n      // fallback to text if not possible to parse as html\n      parsedData = table || this.textParse(dataText || '');\n    } else {\n      parsedData = this.textParse(beforePaste.detail.raw);\n    }\n    const beforePasteApply = this.beforePasteApply.emit({\n      raw: data,\n      parsed: parsedData,\n      event: e,\n    });\n    if (beforePasteApply.defaultPrevented) {\n      return;\n    }\n    this.pasteRegion.emit(beforePasteApply.detail.parsed);\n    // post paste action\n    const afterPasteApply = this.afterPasteApply.emit({\n      raw: data,\n      parsed: parsedData,\n      event: e,\n    });\n    // keep default behavior if needed\n    if (afterPasteApply.defaultPrevented) {\n      return;\n    }\n    e.preventDefault();\n  }\n\n  /**\n   * Listen to copy event and emit copy region event\n   */\n  @Listen('copy', { target: 'document' }) copyStarted(e: ClipboardEvent) {\n    const beforeCopy = this.beforeCopy.emit({\n      event: e,\n    });\n    if (beforeCopy.defaultPrevented) {\n      return;\n    }\n    const data = this.getData(beforeCopy.detail.event);\n    this.copyRegion.emit(data || undefined);\n    e.preventDefault();\n  }\n\n  /**\n   * Listen to copy event and emit copy region event\n   */\n  @Listen('cut', { target: 'document' }) cutStarted(e: ClipboardEvent) {\n    const beforeCut = this.beforeCut.emit({\n      event: e,\n    });\n    if (beforeCut.defaultPrevented) {\n      return;\n    }\n    const data = this.getData(beforeCut.detail.event);\n    this.copyStarted(e);\n\n    // if readonly do nothing\n    if (this.readonly) {\n      return;\n    }\n\n    this.clearRegion.emit(data || undefined);\n    e.preventDefault();\n  }\n\n  @Method() async doCopy(e: DataTransfer, data?: DataFormat[][]) {\n    const beforeCopyApply = this.beforeCopyApply.emit({\n      event: e,\n      data,\n    });\n    if (beforeCopyApply.defaultPrevented) {\n      return;\n    }\n    const parsed = data ? this.parserCopy(data) : '';\n    e.setData('text/plain', parsed);\n  }\n\n  parserCopy(data: DataFormat[][]) {\n    return data.map(rgRow => rgRow.join('\\t')).join('\\n');\n  }\n\n  private textParse(data: string) {\n    const result: string[][] = [];\n    const rows = data.split(/\\r\\n|\\n|\\r/);\n    for (let y in rows) {\n      result.push(rows[y].split('\\t'));\n    }\n    return result;\n  }\n\n  private htmlParse(data: string) {\n    const result: string[][] = [];\n    const fragment = document.createRange().createContextualFragment(data);\n    const table = fragment.querySelector('table');\n    if (!table) {\n      return null;\n    }\n    for (const rgRow of Array.from(table.rows)) {\n      result.push(Array.from(rgRow.cells).map(cell => cell.innerText));\n    }\n    return result;\n  }\n\n  private getData(e: ClipboardEvent) {\n    return (\n      e.clipboardData ||\n      (window as unknown as { clipboardData: DataTransfer | null })\n        ?.clipboardData\n    );\n  }\n}\n"], "version": 3}