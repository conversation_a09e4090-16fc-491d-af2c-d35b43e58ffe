{"file": "revogr-focus2.js", "mappings": ";;;;;;;;;AAAA,MAAM,mBAAmB,GAAG,kqMAAkqM;;MCkCjrM,WAAW,iBAAAA,kBAAA,CAAA,MAAA,WAAA,SAAA,WAAA,CAAA;AAJxB,IAAA,WAAA,GAAA;;;;;;AA8BE;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAA6B,IAAI;AAuB9C,QAAA,IAAW,CAAA,WAAA,GAAgB,IAAI;AA2ExC;IAzEC,kBAAkB,GAAA;;QAChB,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;QACrD,IACE,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,CAAC,OAAK,YAAY,aAAZ,YAAY,KAAA,MAAA,GAAA,MAAA,GAAZ,YAAY,CAAE,CAAC,CAAA;YACvC,CAAA,MAAA,IAAI,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,CAAC,OAAK,YAAY,KAAZ,IAAA,IAAA,YAAY,uBAAZ,YAAY,CAAE,CAAC,CAAA,EACvC;YACA;;AAEF,QAAA,IAAI,CAAC,WAAW,GAAG,YAAY;AAC/B,QAAA,IAAI,YAAY,IAAI,IAAI,CAAC,EAAE,EAAE;AAC3B,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;AACtE,YAAA,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE;AACpC,gBAAA,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;AACrB,oBAAA,KAAK,EAAE,SAAS;AAChB,oBAAA,MAAM,EAAE,SAAS;AAClB,iBAAA,CAAC;;AAEJ,YAAA,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;AAC3D,YAAA,MAAM,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;AAC1D,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;gBACnB,KAAK;gBACL,MAAM;gBACN,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,QAAQ,EAAE,YAAY,CAAC,CAAC;gBACxB,QAAQ,EAAE,YAAY,CAAC,CAAC;AACzB,aAAA,CAAC;;;IAIN,MAAM,GAAA;;QACJ,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC;QAChD,IAAI,QAAQ,EAAE;YACZ;;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;QAClD,IAAI,CAAC,SAAS,EAAE;YACd;;AAEF,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;YACxC,KAAK,EACA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,SAAS,CACZ,EAAA,EAAA,EAAE,EAAE,SAAS,CAAC,CAAC,EACf,EAAE,EAAE,SAAS,CAAC,CAAC,EAChB,CAAA;YACD,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO;AACrB,YAAA,YAAY,oBAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAE;AAC5C,YAAA,YAAY,oBAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAE;AAC7C,SAAA,CAAC;AACF,QAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;AAC1B,YAAA,OAAO,eAAQ;;AAEjB,QAAA,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK;QAExB,MAAM,IAAI,GAAG,OAAO,CAClB,MAAM,CAAC,KAAK,EACZ,KAAK,CAAC,MAAM,CAAC,YAAY,EACzB,KAAK,CAAC,MAAM,CAAC,YAAY,CAC1B;AACD,QAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC;AACrC,QAAA,MAAM,KAAK,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAG,CAAC,EAAE,MAAM,CAAC;AAC7C,QAAA,MAAM,KAAK,GAAG;AACZ,YAAA,KAAK,EAAE,EAAE,CAAC,WAAW,GAAG,IAAI,EAAE;AAC9B,YAAA,KAAK,EAAE,MAAM;SACd;QACD,QACE,CAAA,CAAC,IAAI,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAK,KAAK,CAAA,EACb,CAAQ,CAAA,MAAA,EAAA,IAAA,CAAA,EACP,KAAK,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/components/selectionFocus/revogr-focus-style.scss?tag=revogr-focus", "src/components/selectionFocus/revogr-focus.tsx"], "sourcesContent": ["revogr-focus.focused-cell {\n  @include selection(1px);\n\n  position: absolute;\n  pointer-events: none;\n  z-index: 9;\n  display: block !important;\n}\n", "import {\n  Component,\n  Prop,\n  h,\n  Host,\n  Event,\n  Element,\n  EventEmitter,\n} from '@stencil/core';\nimport { FOCUS_CLASS } from '../../utils/consts';\nimport { getCell, styleByCellProps } from '../overlay/selection.utils';\nimport { DSourceState, getSourceItem } from '@store';\nimport type {\n  Cell,\n  SelectionStoreState,\n  ColumnRegular,\n  DataType,\n  DimensionSettingsState,\n  FocusRenderEvent,\n  FocusTemplateFunc,\n  DimensionCols,\n  DimensionRows,\n  FocusAfterRenderEvent,\n} from '@type';\nimport type { Observable } from '../../utils';\n\n/**\n * Focus component. Shows focus layer around the cell that is currently in focus.\n * @slot focus-${view.type}-${data.type}. @example focus-rgCol-rgRow\n */\n@Component({\n  tag: 'revogr-focus',\n  styleUrl: 'revogr-focus-style.scss',\n})\nexport class RevogrFocus {\n  /**\n   * Column type\n   */\n  @Prop() colType!: DimensionCols;\n  /**\n   * Row type\n   */\n  @Prop() rowType!: DimensionRows;\n\n  /** Dynamic stores */\n  /** Selection, range, focus for selection */\n  @Prop() selectionStore!: Observable<SelectionStoreState>;\n  /** Dimension settings Y */\n  @Prop() dimensionRow!: Observable<DimensionSettingsState>;\n  /** Dimension settings X */\n  @Prop() dimensionCol!: Observable<DimensionSettingsState>;\n  /**\n   * Data rows source\n   */\n  @Prop() dataStore!: Observable<DSourceState<DataType, DimensionRows>>;\n  /**\n   * Column source\n   */\n  @Prop() colData!: Observable<DSourceState<ColumnRegular, DimensionCols>>;\n\n  /**\n   * Focus template custom function. Can be used to render custom focus layer.\n   */\n  @Prop() focusTemplate: FocusTemplateFunc | null = null;\n\n  /**\n   * Before focus render event.\n   * Can be prevented by event.preventDefault().\n   * If preventDefault used slot will be rendered.\n   */\n  @Event({ eventName: 'beforefocusrender' })\n  beforeFocusRender: EventEmitter<FocusRenderEvent>;\n\n  /**\n   * Before focus changed verify if it's in view and scroll viewport into this view\n   * Can be prevented by event.preventDefault()\n   */\n  @Event({ eventName: 'beforescrollintoview' })\n  beforeScrollIntoView: EventEmitter<{ el: HTMLElement }>;\n  /**\n   * Used to setup properties after focus was rendered\n   */\n  @Event({ eventName: 'afterfocus' })\n  afterFocus: EventEmitter<FocusAfterRenderEvent>;\n\n  @Element() el: HTMLElement;\n  private activeFocus: Cell | null = null;\n\n  componentDidRender() {\n    const currentFocus = this.selectionStore.get('focus');\n    if (\n      this.activeFocus?.x === currentFocus?.x &&\n      this.activeFocus?.y === currentFocus?.y\n    ) {\n      return;\n    }\n    this.activeFocus = currentFocus;\n    if (currentFocus && this.el) {\n      const beforeScrollIn = this.beforeScrollIntoView.emit({ el: this.el });\n      if (!beforeScrollIn.defaultPrevented) {\n        this.el.scrollIntoView({\n          block: 'nearest',\n          inline: 'nearest',\n        });\n      }\n      const model = getSourceItem(this.dataStore, currentFocus.y);\n      const column = getSourceItem(this.colData, currentFocus.x);\n      this.afterFocus.emit({\n        model,\n        column,\n        rowType: this.rowType,\n        colType: this.colType,\n        rowIndex: currentFocus.y,\n        colIndex: currentFocus.x,\n      });\n    }\n  }\n\n  render() {\n    const editCell = this.selectionStore.get('edit');\n    if (editCell) {\n      return;\n    }\n    const focusCell = this.selectionStore.get('focus');\n    if (!focusCell) {\n      return;\n    }\n    const event = this.beforeFocusRender.emit({\n      range: {\n        ...focusCell,\n        x1: focusCell.x,\n        y1: focusCell.y,\n      },\n      rowType: this.rowType,\n      colType: this.colType,\n      rowDimension: { ...this.dimensionRow.state },\n      colDimension: { ...this.dimensionCol.state },\n    });\n    if (event.defaultPrevented) {\n      return <slot />;\n    }\n    const { detail } = event;\n\n    const cell = getCell(\n      detail.range,\n      event.detail.rowDimension,\n      event.detail.colDimension,\n    );\n    const styles = styleByCellProps(cell);\n    const extra = this.focusTemplate?.(h, detail);\n    const props = {\n      class: { [FOCUS_CLASS]: true },\n      style: styles,\n    };\n    return (\n      <Host {...props}>\n        <slot />\n        {extra}\n      </Host>\n    );\n  }\n}\n"], "version": 3}