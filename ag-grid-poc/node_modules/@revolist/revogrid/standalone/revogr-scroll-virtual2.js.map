{"file": "revogr-scroll-virtual2.js", "mappings": ";;;;;;;AAEA;;AAEG;MACU,oBAAoB,CAAA;AAE/B,IAAA,WAAA,CAAoB,OAAoB,EAAA;QAApB,IAAO,CAAA,OAAA,GAAP,OAAO;QADnB,IAAqB,CAAA,qBAAA,GAAG,CAAC;;AAIjC;;AAEG;AACH,IAAA,aAAa,CAAC,CAAS,EAAA;QACrB,IAAI,CAAC,CAAC,EAAE;YACN,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC;;aACxC;AACL,YAAA,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC;;;AAI5C;;AAEG;AACH,IAAA,WAAW,CAAC,EACV,UAAU,EACV,WAAW,EACX,WAAW,GAKZ,EAAA;AACC,QAAA,MAAM,SAAS,GAAG,WAAW,GAAG,WAAW;AAC3C,QAAA,MAAM,QAAQ,GAAG,CAAC,UAAU,IAAI,SAAS;QACzC,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC;AAC5C,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,IAAI,CACpC,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,qBAAqB,CAC3B;;;IAIG,IAAI,CAAC,OAAqB,EAAE,OAAgB,EAAA;QAClD,YAAY,CAAC,OAAO,CAAC;AACrB,QAAA,OAAO,MAAM,CACX,UAAU,CAAC,MAAK;YACd,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,MAAA,GAAA,MAAA,GAAA,OAAO,CAAE,eAAe,CAAC,SAAS,CAAC;AACrC,SAAC,EAAE,IAAI,CAAC,CACT;;IAEH,KAAK,GAAA;AACH,QAAA,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC;;AAE3C;;ACvDD,MAAM,oBAAoB,GAAG,qiNAAqiN;;MC4BrjN,mBAAmB,iBAAAA,kBAAA,CAAA,MAAA,mBAAA,SAAA,WAAA,CAAA;AAJhC,IAAA,WAAA,GAAA;;;;AAKE;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAkB,OAAO;AAyBlC,QAAA,IAAU,CAAA,UAAA,GAAG,CAAC;AAwHvB;IAnHC,MAAM,SAAS,CAAC,CAAsB,EAAA;;QACpC,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS,EAAE;YAClC;;QAEF,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC;QACrD,CAAA,EAAA,GAAA,IAAI,CAAC,kBAAkB,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,SAAS,CAAC,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,CAAC,UAAU,EAAE;AAChB,YAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC;gBACpC,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,WAAW,EAAE,IAAI,CAAC,QAAQ;gBAC1B,WAAW,EAAE,IAAI,CAAC,WAAW;AAC9B,aAAA,CAAC;;;AAIN;;AAEG;IAEH,MAAM,YAAY,CAAC,CAAsB,EAAA;AACvC,QAAA,IAAI,CAAC,CAAC,KAAK,EAAE;AACX,YAAA,QAAQ,CAAC,CAAC,SAAS;AACjB,gBAAA,KAAK,OAAO;AACV,oBAAA,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK;oBAChD;AACF,gBAAA,KAAK,OAAO;AACV,oBAAA,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK;oBAC/C;;AAEJ,YAAA,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;AAEnB,QAAA,OAAO,CAAC;;IAGV,iBAAiB,GAAA;QACf,IAAI,CAAC,oBAAoB,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC;QAClE,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAC1C,cAAc,IAAI,QAAQ,CAAC,eAAe,GAAG,CAAC,GAAG,EAAE,CACpD;AACD,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAAC;AAC/C,YAAA,SAAS,EAAE,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;YAC1C,WAAW,EAAE,CAAC,IAAG;AACf,gBAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC;AACtC,gBAAA,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,KAAK,OAAO,GAAG,WAAW,GAAG,YAAY;;gBAEjE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU;aAClC;AACF,SAAA,CAAC;;IAGJ,oBAAoB,GAAA;AAClB,QAAA,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE;;IAGnC,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,QAAQ,CAAC;;IAG9C,kBAAkB,GAAA;QAChB,IAAI,UAAU,GAAG,CAAC;AAClB,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,OAAO,EAAE;YAC9B,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC;YACxF,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAA,EAAG,UAAU,CAAA,EAAA,CAAI;;aAC1C;YACL,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC;YACtF,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,CAAA,EAAG,UAAU,CAAA,EAAA,CAAI;;AAElD,QAAA,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,UAAU,CAAC;AACnD,QAAA,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAC/B;YACE,WAAW,EAAE,IAAI,CAAC,QAAQ;YAC1B,UAAU,EAAE,IAAI,CAAC,SAAS,KAAK,OAAO,GAAI,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;YAC9F,WAAW,EAAE,IAAI,CAAC,UAAU;AAC7B,SAAA,EACD,IAAI,CAAC,SAAS,CACf;;AAGH,IAAA,QAAQ,CAAC,CAAa,EAAA;QACpB,IAAI,EAAE,CAAC,CAAC,MAAM,YAAY,OAAO,CAAC,EAAE;YAClC;;AAEF,QAAA,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;QACvB,IAAI,IAAI,GAA+B,YAAY;AACnD,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,OAAO,EAAE;YAC9B,IAAI,GAAG,WAAW;;QAGpB,MAAM,SAAS,GAAG,MAAK;;AACrB,YAAA,CAAA,EAAA,GAAA,IAAI,CAAC,kBAAkB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC;AACpE,SAAC;;AAED,QAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAC/D,YAAA,SAAS,EAAE;;aACN;YACL,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,SAAS,EAAE,CAAC;;;IAIxG,MAAM,GAAA;AACJ,QAAA,MAAM,IAAI,GAAG,cAAc,CACzB,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,KAAK,OAAO,GAAI,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAClF,IAAI,CAAC,UAAU,CAChB;AACD,QAAA,QACE,CAAC,CAAA,IAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,QAAQ,EAAE,CAAC,CAAa,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAA,EACjD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,CAAC,IAAI,CAAC,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,OAAO,GAAG,CAAA,EAAG,IAAI,CAAI,EAAA,CAAA;aAC/D,EACD,CAAA,CACG;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["__stencil_proxyCustomElement"], "sources": ["src/components/scrollable/autohide-scroll.plugin.ts", "src/components/scrollable/revogr-scroll-style.scss?tag=revogr-scroll-virtual", "src/components/scrollable/revogr-scroll-virtual.tsx"], "sourcesContent": ["\n\n/**\n * Autohide scroll for MacOS when scroll is visible only for 1 sec\n */\nexport class AutohideScrollPlugin {\n  private autohideScrollTimeout = 0;\n  constructor(private element: HTMLElement) {\n  }\n\n  /**\n   * When scroll size updates set it up for autohide\n   */\n  setScrollSize(s: number) {\n    if (!s) {\n      this.element.setAttribute('autohide', 'true');\n    } else {\n      this.element.removeAttribute('autohide');\n    }\n  }\n\n  /**\n   * On each scroll check if it's time to show\n   */\n  checkScroll({\n    scrollSize,\n    contentSize,\n    virtualSize,\n  }: {\n    scrollSize: number;\n    contentSize: number;\n    virtualSize: number;\n  }) {\n    const hasScroll = contentSize > virtualSize;\n    const isHidden = !scrollSize && hasScroll;\n    if (isHidden) {\n      this.element.setAttribute('visible', 'true');\n      this.autohideScrollTimeout = this.show(\n        this.element,\n        this.autohideScrollTimeout,\n      );\n    }\n  }\n\n  private show(element?: HTMLElement, timeout?: number): number {\n    clearTimeout(timeout);\n    return Number(\n      setTimeout(() => {\n        element?.removeAttribute('visible');\n      }, 1000),\n    );\n  }\n  clear() {\n    clearTimeout(this.autohideScrollTimeout);\n  }\n}\n", "revogr-scroll-virtual {\n  $min-size: 20px;\n\n  // show scroll only for 1 sec\n  &[autohide] {\n    position: absolute;\n    z-index: 100 !important;\n\n    &.vertical {\n      top: 0;\n      right: 0;\n\n      &[visible] {\n        min-width: $min-size !important;\n      }\n    }\n\n    &.horizontal {\n      bottom: 0;\n      left: 0;\n\n      &[visible] {\n        min-height: $min-size !important;\n      }\n    }\n  }\n\n  &.vertical {\n    overflow-y: auto;\n    overflow-x: hidden;\n    height: 100%;\n\n    > div {\n      width: 1px;\n    }\n  }\n\n  &.horizontal {\n    overflow-x: auto;\n    overflow-y: hidden;\n    width: 100%;\n\n    > div {\n      height: 1px;\n    }\n  }\n}\n", "import {\n  Component,\n  Element as StencilElement,\n  Event,\n  EventEmitter,\n  h,\n  Host,\n  Method,\n  Prop,\n} from '@stencil/core';\nimport LocalScrollService, {\n  getContentSize,\n} from '../../services/local.scroll.service';\nimport type {\n  DimensionType,\n  ViewPortScrollEvent,\n} from '@type';\nimport { AutohideScrollPlugin } from './autohide-scroll.plugin';\nimport { LocalScrollTimer } from '../../services/local.scroll.timer';\nimport { getScrollbarSize } from '../../utils';\n\n/**\n * Virtual scroll component\n */\n@Component({\n  tag: 'revogr-scroll-virtual',\n  styleUrl: 'revogr-scroll-style.scss',\n})\nexport class RevogrScrollVirtual {\n  /**\n   * Scroll dimension (`X` - `rgCol` or `Y` - `rgRow`)\n   */\n  @Prop() dimension: DimensionType = 'rgRow';\n\n  /**\n   * Dimensions\n   */\n  @Prop() realSize!: number;\n\n  /**\n   * Virtual size\n   */\n  @Prop() virtualSize!: number;\n\n  /**\n   * Client size\n   */\n  @Prop() clientSize!: number;\n\n  /**\n   * Scroll event\n   */\n  @Event({ eventName: 'scrollvirtual' })\n  scrollVirtual: EventEmitter<ViewPortScrollEvent>;\n\n  @StencilElement() element: HTMLElement;\n  private autohideScrollPlugin: AutohideScrollPlugin;\n  private scrollSize = 0;\n  private localScrollService: LocalScrollService;\n  private localScrollTimer: LocalScrollTimer;\n\n  @Method()\n  async setScroll(e: ViewPortScrollEvent): Promise<void> {\n    if (this.dimension !== e.dimension) {\n      return;\n    }\n    this.localScrollTimer.latestScrollUpdate(e.dimension);\n    this.localScrollService?.setScroll(e);\n    if (e.coordinate) {\n      this.autohideScrollPlugin.checkScroll({\n        scrollSize: this.scrollSize,\n        contentSize: this.realSize,\n        virtualSize: this.virtualSize,\n      });\n    }\n  }\n\n  /**\n   * Update if `delta` exists in case we don't know current position or if it's external change\n   */\n  @Method()\n  async changeScroll(e: ViewPortScrollEvent): Promise<ViewPortScrollEvent> {\n    if (e.delta) {\n      switch (e.dimension) {\n        case 'rgCol':\n          e.coordinate = this.element.scrollLeft + e.delta;\n          break;\n        case 'rgRow':\n          e.coordinate = this.element.scrollTop + e.delta;\n          break;\n      }\n      this.setScroll(e);\n    }\n    return e;\n  }\n\n  connectedCallback() {\n    this.autohideScrollPlugin = new AutohideScrollPlugin(this.element);\n    this.localScrollTimer = new LocalScrollTimer(\n      'ontouchstart' in document.documentElement ? 0 : 10,\n    );\n    this.localScrollService = new LocalScrollService({\n      runScroll: e => this.scrollVirtual.emit(e),\n      applyScroll: e => {\n        this.localScrollTimer.setCoordinate(e);\n        const type = e.dimension === 'rgRow' ? 'scrollTop' : 'scrollLeft';\n        // this will trigger on scroll event\n        this.element[type] = e.coordinate;\n      },\n    });\n  }\n\n  disconnectedCallback() {\n    this.autohideScrollPlugin.clear();\n  }\n\n  componentWillLoad() {\n    this.scrollSize = getScrollbarSize(document);\n  }\n\n  componentDidRender() {\n    let scrollSize = 0;\n    if (this.dimension === 'rgRow') {\n      scrollSize = this.element.scrollHeight > this.element.clientHeight ? this.scrollSize : 0;\n      this.element.style.minWidth = `${scrollSize}px`;\n    } else {\n      scrollSize = this.element.scrollWidth > this.element.clientWidth ? this.scrollSize : 0;\n      this.element.style.minHeight = `${scrollSize}px`;\n    }\n    this.autohideScrollPlugin.setScrollSize(scrollSize);\n    this.localScrollService.setParams(\n      {\n        contentSize: this.realSize,\n        clientSize: this.dimension === 'rgRow' ?  this.element.clientHeight : this.element.clientWidth,\n        virtualSize: this.clientSize,\n      },\n      this.dimension,\n    );\n  }\n\n  onScroll(e: MouseEvent) {\n    if (!(e.target instanceof Element)) {\n      return;\n    }\n    const target = e.target;\n    let type: 'scrollLeft' | 'scrollTop' = 'scrollLeft';\n    if (this.dimension === 'rgRow') {\n      type = 'scrollTop';\n    }\n\n    const setScroll = () => {\n      this.localScrollService?.scroll(target[type] || 0, this.dimension);\n    };\n    // apply after throttling\n    if (this.localScrollTimer.isReady(this.dimension, target[type])) {\n      setScroll();\n    } else {\n      this.localScrollTimer.throttleLastScrollUpdate(this.dimension, target[type] || 0, () => setScroll());\n    }\n  }\n\n  render() {\n    const size = getContentSize(\n      this.realSize,\n      this.dimension === 'rgRow' ?  this.element.clientHeight : this.element.clientWidth,\n      this.clientSize, // content viewport size\n    );\n    return (\n      <Host onScroll={(e: MouseEvent) => this.onScroll(e)}>\n        <div\n          style={{\n            [this.dimension === 'rgRow' ? 'height' : 'width']: `${size}px`,\n          }}\n        />\n      </Host>\n    );\n  }\n}\n"], "version": 3}