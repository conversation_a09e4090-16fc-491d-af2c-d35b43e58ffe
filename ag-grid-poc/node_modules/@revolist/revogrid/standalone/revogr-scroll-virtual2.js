/*!
 * Built by Revolist OU ❤️
 */
import { proxyCustomElement, HTMLElement, createEvent, h, Host } from '@stencil/core/internal/client';
import { L as LocalScrollTimer, a as LocalScrollService, g as getContentSize } from './local.scroll.timer.js';
import { g as getScrollbarSize } from './index2.js';

/**
 * Autohide scroll for MacOS when scroll is visible only for 1 sec
 */
class AutohideScrollPlugin {
    constructor(element) {
        this.element = element;
        this.autohideScrollTimeout = 0;
    }
    /**
     * When scroll size updates set it up for autohide
     */
    setScrollSize(s) {
        if (!s) {
            this.element.setAttribute('autohide', 'true');
        }
        else {
            this.element.removeAttribute('autohide');
        }
    }
    /**
     * On each scroll check if it's time to show
     */
    checkScroll({ scrollSize, contentSize, virtualSize, }) {
        const hasScroll = contentSize > virtualSize;
        const isHidden = !scrollSize && hasScroll;
        if (isHidden) {
            this.element.setAttribute('visible', 'true');
            this.autohideScrollTimeout = this.show(this.element, this.autohideScrollTimeout);
        }
    }
    show(element, timeout) {
        clearTimeout(timeout);
        return Number(setTimeout(() => {
            element === null || element === void 0 ? void 0 : element.removeAttribute('visible');
        }, 1000));
    }
    clear() {
        clearTimeout(this.autohideScrollTimeout);
    }
}

const revogrScrollStyleCss = ".revo-drag-icon{width:11px;opacity:0.8}.revo-drag-icon::before{content:\"::\"}.revo-alt-icon{-webkit-mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");mask-image:url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg viewBox='0 0 384 383' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath d='M192.4375,383 C197.424479,383 201.663411,381.254557 205.154297,377.763672 L205.154297,377.763672 L264.25,318.667969 C270.234375,312.683594 271.605794,306.075846 268.364258,298.844727 C265.122721,291.613607 259.51237,287.998047 251.533203,287.998047 L251.533203,287.998047 L213.382812,287.998047 L213.382812,212.445312 L288.935547,212.445312 L288.935547,250.595703 C288.935547,258.57487 292.551107,264.185221 299.782227,267.426758 C307.013346,270.668294 313.621094,269.296875 319.605469,263.3125 L319.605469,263.3125 L378.701172,204.216797 C382.192057,200.725911 383.9375,196.486979 383.9375,191.5 C383.9375,186.513021 382.192057,182.274089 378.701172,178.783203 L378.701172,178.783203 L319.605469,119.6875 C313.621094,114.201823 307.013346,112.955078 299.782227,115.947266 C292.551107,118.939453 288.935547,124.42513 288.935547,132.404297 L288.935547,132.404297 L288.935547,170.554688 L213.382812,170.554688 L213.382812,95.0019531 L251.533203,95.0019531 C259.51237,95.0019531 264.998047,91.3863932 267.990234,84.1552734 C270.982422,76.9241536 269.735677,70.3164062 264.25,64.3320312 L264.25,64.3320312 L205.154297,5.23632812 C201.663411,1.74544271 197.424479,0 192.4375,0 C187.450521,0 183.211589,1.74544271 179.720703,5.23632812 L179.720703,5.23632812 L120.625,64.3320312 C114.640625,70.3164062 113.269206,76.9241536 116.510742,84.1552734 C119.752279,91.3863932 125.36263,95.0019531 133.341797,95.0019531 L133.341797,95.0019531 L171.492188,95.0019531 L171.492188,170.554688 L95.9394531,170.554688 L95.9394531,132.404297 C95.9394531,124.42513 92.3238932,118.814779 85.0927734,115.573242 C77.8616536,112.331706 71.2539062,113.703125 65.2695312,119.6875 L65.2695312,119.6875 L6.17382812,178.783203 C2.68294271,182.274089 0.9375,186.513021 0.9375,191.5 C0.9375,196.486979 2.68294271,200.725911 6.17382812,204.216797 L6.17382812,204.216797 L65.2695312,263.3125 C71.2539062,268.798177 77.8616536,270.044922 85.0927734,267.052734 C92.3238932,264.060547 95.9394531,258.57487 95.9394531,250.595703 L95.9394531,250.595703 L95.9394531,212.445312 L171.492188,212.445312 L171.492188,287.998047 L133.341797,287.998047 C125.36263,287.998047 119.876953,291.613607 116.884766,298.844727 C113.892578,306.075846 115.139323,312.683594 120.625,318.667969 L120.625,318.667969 L179.720703,377.763672 C183.211589,381.254557 187.450521,************,383 Z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E\");width:11px;height:11px;background-size:cover;background-repeat:no-repeat}.arrow-down{position:absolute;right:5px;top:0}.arrow-down svg{width:8px;margin-top:5px;margin-left:5px;opacity:0.4}.cell-value-wrapper{margin-right:10px;overflow:hidden;text-overflow:ellipsis}.revo-button{position:relative;overflow:hidden;color:#fff;background-color:#4545ff;height:32px;line-height:32px;padding:0 15px;outline:0;border:0;border-radius:7px;box-sizing:border-box;cursor:pointer}.revo-button.green{background-color:#009037}.revo-button.red{background-color:#E0662E}.revo-button:disabled,.revo-button[disabled]{cursor:not-allowed !important;filter:opacity(0.35) !important}.revo-button.outline{border:1px solid #dbdbdb;line-height:30px;background:none;color:#000;box-shadow:none}revo-grid[theme^=dark] .revo-button.outline{border:1px solid #404040;color:#d8d8d8}revogr-scroll-virtual[autohide]{position:absolute;z-index:100 !important}revogr-scroll-virtual[autohide].vertical{top:0;right:0}revogr-scroll-virtual[autohide].vertical[visible]{min-width:20px !important}revogr-scroll-virtual[autohide].horizontal{bottom:0;left:0}revogr-scroll-virtual[autohide].horizontal[visible]{min-height:20px !important}revogr-scroll-virtual.vertical{overflow-y:auto;overflow-x:hidden;height:100%}revogr-scroll-virtual.vertical>div{width:1px}revogr-scroll-virtual.horizontal{overflow-x:auto;overflow-y:hidden;width:100%}revogr-scroll-virtual.horizontal>div{height:1px}";

const RevogrScrollVirtual = /*@__PURE__*/ proxyCustomElement(class RevogrScrollVirtual extends HTMLElement {
    constructor() {
        super();
        this.__registerHost();
        this.scrollVirtual = createEvent(this, "scrollvirtual", 7);
        /**
         * Scroll dimension (`X` - `rgCol` or `Y` - `rgRow`)
         */
        this.dimension = 'rgRow';
        this.scrollSize = 0;
    }
    async setScroll(e) {
        var _a;
        if (this.dimension !== e.dimension) {
            return;
        }
        this.localScrollTimer.latestScrollUpdate(e.dimension);
        (_a = this.localScrollService) === null || _a === void 0 ? void 0 : _a.setScroll(e);
        if (e.coordinate) {
            this.autohideScrollPlugin.checkScroll({
                scrollSize: this.scrollSize,
                contentSize: this.realSize,
                virtualSize: this.virtualSize,
            });
        }
    }
    /**
     * Update if `delta` exists in case we don't know current position or if it's external change
     */
    async changeScroll(e) {
        if (e.delta) {
            switch (e.dimension) {
                case 'rgCol':
                    e.coordinate = this.element.scrollLeft + e.delta;
                    break;
                case 'rgRow':
                    e.coordinate = this.element.scrollTop + e.delta;
                    break;
            }
            this.setScroll(e);
        }
        return e;
    }
    connectedCallback() {
        this.autohideScrollPlugin = new AutohideScrollPlugin(this.element);
        this.localScrollTimer = new LocalScrollTimer('ontouchstart' in document.documentElement ? 0 : 10);
        this.localScrollService = new LocalScrollService({
            runScroll: e => this.scrollVirtual.emit(e),
            applyScroll: e => {
                this.localScrollTimer.setCoordinate(e);
                const type = e.dimension === 'rgRow' ? 'scrollTop' : 'scrollLeft';
                // this will trigger on scroll event
                this.element[type] = e.coordinate;
            },
        });
    }
    disconnectedCallback() {
        this.autohideScrollPlugin.clear();
    }
    componentWillLoad() {
        this.scrollSize = getScrollbarSize(document);
    }
    componentDidRender() {
        let scrollSize = 0;
        if (this.dimension === 'rgRow') {
            scrollSize = this.element.scrollHeight > this.element.clientHeight ? this.scrollSize : 0;
            this.element.style.minWidth = `${scrollSize}px`;
        }
        else {
            scrollSize = this.element.scrollWidth > this.element.clientWidth ? this.scrollSize : 0;
            this.element.style.minHeight = `${scrollSize}px`;
        }
        this.autohideScrollPlugin.setScrollSize(scrollSize);
        this.localScrollService.setParams({
            contentSize: this.realSize,
            clientSize: this.dimension === 'rgRow' ? this.element.clientHeight : this.element.clientWidth,
            virtualSize: this.clientSize,
        }, this.dimension);
    }
    onScroll(e) {
        if (!(e.target instanceof Element)) {
            return;
        }
        const target = e.target;
        let type = 'scrollLeft';
        if (this.dimension === 'rgRow') {
            type = 'scrollTop';
        }
        const setScroll = () => {
            var _a;
            (_a = this.localScrollService) === null || _a === void 0 ? void 0 : _a.scroll(target[type] || 0, this.dimension);
        };
        // apply after throttling
        if (this.localScrollTimer.isReady(this.dimension, target[type])) {
            setScroll();
        }
        else {
            this.localScrollTimer.throttleLastScrollUpdate(this.dimension, target[type] || 0, () => setScroll());
        }
    }
    render() {
        const size = getContentSize(this.realSize, this.dimension === 'rgRow' ? this.element.clientHeight : this.element.clientWidth, this.clientSize);
        return (h(Host, { key: '57f81ec9deb2395e96b283338c03b9ad44f1e929', onScroll: (e) => this.onScroll(e) }, h("div", { key: '1a8c869adab53b362c351dae8d53664f33c4212c', style: {
                [this.dimension === 'rgRow' ? 'height' : 'width']: `${size}px`,
            } })));
    }
    get element() { return this; }
    static get style() { return revogrScrollStyleCss; }
}, [0, "revogr-scroll-virtual", {
        "dimension": [1],
        "realSize": [2, "real-size"],
        "virtualSize": [2, "virtual-size"],
        "clientSize": [2, "client-size"],
        "setScroll": [64],
        "changeScroll": [64]
    }]);
function defineCustomElement() {
    if (typeof customElements === "undefined") {
        return;
    }
    const components = ["revogr-scroll-virtual"];
    components.forEach(tagName => { switch (tagName) {
        case "revogr-scroll-virtual":
            if (!customElements.get(tagName)) {
                customElements.define(tagName, RevogrScrollVirtual);
            }
            break;
    } });
}

export { RevogrScrollVirtual as R, defineCustomElement as d };
//# sourceMappingURL=revogr-scroll-virtual2.js.map

//# sourceMappingURL=revogr-scroll-virtual2.js.map