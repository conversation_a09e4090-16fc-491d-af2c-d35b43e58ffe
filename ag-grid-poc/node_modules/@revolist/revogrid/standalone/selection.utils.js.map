{"file": "selection.utils.js", "mappings": ";;;;;;;;AAAA,SAAS,OAAO,CAAC,CAA0B,EAAA;AACzC,IAAA,OAAO,CAAC,CAAE,CAAgB,CAAC,OAAO;AACpC;AAEgB,SAAA,iBAAiB,CAAC,UAAkB,EAAE,UAAmB,EAAA;AACvE,IAAA,IAAI,UAAU,IAAI,UAAU,EAAE;QAC5B,IAAI,EAAE,UAAU,CAAC,MAAM,YAAY,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE;AAC/F,YAAA,OAAO,KAAK;;;AAGhB,IAAA,OAAO,IAAI;AACb;AAGA;;AAEG;AACG,SAAU,oBAAoB,CAClC,CAA0B,EAC1B,IAAsE,EACtE,UAAmB;;;AAGnB,IAAA,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;;QAEd,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,MAAM,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;;YAE/B,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE;;AAE9C,gBAAA,OAAO,IAAI;;;AAGb,YAAA,OAAQ,UAAU,CAAC,IAAI,CAAY,IAAI,CAAC;;;AAG1C,QAAA,OAAO,IAAI;;;AAGb,IAAA,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACrB;;ACVgB,SAAA,oBAAoB,CAClC,IAAgB,EAChB,KAAwD,EAAA;IAExD,MAAM,MAAM,GAAwB,EAAE;AACtC,IAAA,KAAK,IAAI,CAAC,IAAI,IAAI,EAAE;QAClB,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;QAChC,MAAM,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC;;AAGnD,IAAA,OAAO,MAAM;AACf;AAEgB,SAAA,wBAAwB,CACtC,CAA0B,EAC1B,IAAe,EAAA;;AAGf,IAAA,IAAI,CAAC,CAAC,gBAAgB,EAAE;AACtB,QAAA,OAAO,IAAI;;;IAIb,MAAM,CAAC,GAAG,oBAAoB,CAAC,CAAC,EAAE,SAAS,CAAC;IAC5C,MAAM,CAAC,GAAG,oBAAoB,CAAC,CAAC,EAAE,SAAS,CAAC;;IAG5C,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;AAC5B,QAAA,OAAO,IAAI;;;AAIb,IAAA,MAAM,SAAS,GAAG,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC;;IAEhD,IAAI,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE;AACzC,QAAA,OAAO,IAAI;;AAGb,IAAA,OAAO,SAAS;AAClB;AAEA;;AAEG;AACa,SAAA,cAAc,CAC5B,EAAE,CAAC,EAAE,CAAC,EAAQ,EACd,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAA2C,EAAA;;AAG3D,IAAA,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,qBAAqB,EAAE;;AAG/D,IAAA,IAAI,KAAK,GAAG,CAAC,GAAG,GAAG;AACnB,IAAA,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI;;AAGpB,IAAA,IAAI,KAAK,IAAI,MAAM,EAAE;AACnB,QAAA,KAAK,GAAG,MAAM,GAAG,CAAC;;;AAIpB,IAAA,IAAI,KAAK,IAAI,KAAK,EAAE;AAClB,QAAA,KAAK,GAAG,KAAK,GAAG,CAAC;;;IAInB,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC;IAC5C,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC;;AAG5C,IAAA,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE;AACvB,QAAA,KAAK,CAAC,SAAS,GAAG,CAAC;;AAGrB,IAAA,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE;AACvB,QAAA,KAAK,CAAC,SAAS,GAAG,CAAC;;AAGrB,IAAA,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,EAAE;AACnD;AAEM,SAAU,aAAa,CAC3B,KAAgB,EAChB,KAAW,EACX,OAAsB,EACtB,OAAO,GAAG,KAAK,EAAA;IAEf,MAAM,gBAAgB,GAAG,CAAC,CAAa,EAAE,GAAG,GAAG,CAAC,KAAI;AAClD,QAAA,MAAM,KAAK,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE;QACxC,MAAM,GAAG,GAAG,OAAO,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,GAAG,KAAK;AAC1D,QAAA,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK;AAC7C,QAAA,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG;AACf,QAAA,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;AACvB,KAAC;AAED,IAAA,IAAI,OAAO,CAAC,CAAC,EAAE;QACb,OAAO,gBAAgB,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;AAE5C,IAAA,IAAI,OAAO,CAAC,CAAC,EAAE;QACb,OAAO,gBAAgB,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;AAE5C,IAAA,OAAO,IAAI;AACb;AAEA;;;AAGG;AACG,SAAU,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,EAAQ,EAAE,QAAc,EAAA;IACxD,OAAO,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC;AAC3C;AAEA;SACgB,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,EAAQ,EAAA;AAC1C,IAAA,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;AACvB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEM,SAAU,gBAAgB,CAAC,MAAiC,EAAA;IAChE,OAAO;AACL,QAAA,IAAI,EAAE,CAAA,EAAG,MAAM,CAAC,IAAI,CAAI,EAAA,CAAA;AACxB,QAAA,GAAG,EAAE,CAAA,EAAG,MAAM,CAAC,GAAG,CAAI,EAAA,CAAA;AACtB,QAAA,KAAK,EAAE,CAAA,EAAG,MAAM,CAAC,KAAK,CAAI,EAAA,CAAA;AAC1B,QAAA,MAAM,EAAE,CAAA,EAAG,MAAM,CAAC,MAAM,CAAI,EAAA,CAAA;KAC7B;AACH;AAEgB,SAAA,OAAO,CACrB,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAa,EAC3B,YAGC,EACD,YAGC,EAAA;IAED,MAAM,GAAG,GAAG,cAAc,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,KAAK;IACjD,MAAM,IAAI,GAAG,cAAc,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,KAAK;IAClD,MAAM,MAAM,GAAG,cAAc,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,GAAG;IACnD,MAAM,KAAK,GAAG,cAAc,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,GAAG;IAElD,OAAO;QACL,IAAI;QACJ,KAAK;QACL,GAAG;QACH,MAAM;QACN,KAAK,EAAE,KAAK,GAAG,IAAI;QACnB,MAAM,EAAE,MAAM,GAAG,GAAG;KACrB;AACH;;;;", "names": [], "sources": ["src/utils/events.ts", "src/components/overlay/selection.utils.ts"], "sourcesContent": ["function isTouch(e: MouseEvent | TouchEvent): e is TouchEvent {\n  return !!(e as TouchEvent).touches;\n}\n\nexport function verifyTouchTarget(touchEvent?: Touch, focusClass?: string) {\n  if (focusClass && touchEvent) {\n    if (!(touchEvent.target instanceof Element && touchEvent.target.classList.contains(focusClass))) { \n      return false;\n    }\n  }\n  return true;\n}\n\n\n/**\n * Function to get the value of a specific property from a MouseEvent or TouchEvent object.\n */\nexport function getPropertyFromEvent(\n  e: MouseEvent | TouchEvent,\n  prop: keyof Pick<Touch, 'clientX' | 'clientY' | 'screenX' | 'screenY'>,\n  focusClass?: string // for touch events\n): number | null {\n  // Check if the event is a touch event\n  if (isTouch(e)) {\n    // If the event has touches, get the first touch\n    if (e.touches.length > 0) {\n      const touchEvent = e.touches[0];\n      // Check if the target of the touch event is the specified element\n      if (!verifyTouchTarget(touchEvent, focusClass)) {\n        // If not, return null\n        return null;\n      }\n      // Get the value of the specified property from the touch event and return it\n      return (touchEvent[prop] as number) || 0;\n    }\n    // If there are no touches, return null\n    return null;\n  }\n  // If the event is not a touch event, get the value of the specified property from the event and return it\n  return e[prop] || 0;\n}\n", "import {\n  DimensionIndexInput,\n  DSourceState,\n  getItemByIndex,\n  getItemByPosition,\n  getSourceItem,\n} from '@store';\nimport type {\n  DimensionSettingsState,\n  Cell,\n  RangeArea,\n  RangeAreaCss,\n  DataLookup,\n  DimensionRows,\n  DataType,\n  EditCellStore,\n} from '@type';\nimport { getPropertyFromEvent } from '../../utils/events';\nimport { Observable } from '../../utils';\n\nexport type EventData = {\n  el: HTMLElement;\n  rows: DimensionSettingsState;\n  cols: DimensionSettingsState;\n  lastCell: Cell;\n  focus: Cell | null;\n  range: RangeArea | null;\n  edit: EditCellStore | null;\n};\n\nexport function collectModelsOfRange(\n  data: DataLookup,\n  store: Observable<DSourceState<DataType, DimensionRows>>,\n) {\n  const models: Partial<DataLookup> = {};\n  for (let i in data) {\n    const rowIndex = parseInt(i, 10);\n    models[rowIndex] = getSourceItem(store, rowIndex);\n  }\n\n  return models;\n}\n\nexport function getFocusCellBasedOnEvent(\n  e: MouseEvent | TouchEvent,\n  data: EventData,\n): Cell | null {\n  // If event default is prevented, return\n  if (e.defaultPrevented) {\n    return null;\n  }\n\n  // Get coordinates from event object\n  const x = getPropertyFromEvent(e, 'clientX');\n  const y = getPropertyFromEvent(e, 'clientY');\n\n  // If coordinates are not available, return\n  if (x === null || y === null) {\n    return null;\n  }\n\n  // Get current cell based on coordinates and data\n  const focusCell = getCurrentCell({ x, y }, data);\n  // If current cell is not available, return\n  if (isAfterLast(focusCell, data.lastCell)) {\n    return null;\n  }\n\n  return focusCell;\n}\n\n/**\n * Calculate cell based on x, y position\n */\nexport function getCurrentCell(\n  { x, y }: Cell,\n  { el, rows, cols }: Pick<EventData, 'el' | 'rows' | 'cols'>,\n): Cell {\n  // Get the bounding rectangle of the element\n  const { top, left, height, width } = el.getBoundingClientRect();\n\n  // Calculate the cell position relative to the element\n  let cellY = y - top;\n  let cellX = x - left;\n\n  // Limit the cell position to the element height\n  if (cellY >= height) {\n    cellY = height - 1;\n  }\n\n  // Limit the cell position to the element width\n  if (cellX >= width) {\n    cellX = width - 1;\n  }\n\n  // Get the row and column items based on the cell position\n  const rgRow = getItemByPosition(rows, cellY);\n  const rgCol = getItemByPosition(cols, cellX);\n\n  // Set the row and column index to 0 if they are before the first item\n  if (rgCol.itemIndex < 0) {\n    rgCol.itemIndex = 0;\n  }\n\n  if (rgRow.itemIndex < 0) {\n    rgRow.itemIndex = 0;\n  }\n\n  return { x: rgCol.itemIndex, y: rgRow.itemIndex };\n}\n\nexport function getCoordinate(\n  range: RangeArea,\n  focus: Cell,\n  changes: Partial<Cell>,\n  isMulti = false,\n) {\n  const updateCoordinate = (c: keyof Cell, pos = 0) => {\n    const start = { x: range.x, y: range.y };\n    const end = isMulti ? { x: range.x1, y: range.y1 } : start;\n    const point = end[c] > focus[c] ? end : start;\n    point[c] += pos;\n    return { start, end };\n  };\n\n  if (changes.x) {\n    return updateCoordinate('x', changes['x']);\n  }\n  if (changes.y) {\n    return updateCoordinate('y', changes['y']);\n  }\n  return null;\n}\n\n/**\n * Check if the x coordinate of the cell position is after or equal to the x coordinate of the last cell position\n * or if the y coordinate of the cell position is after or equal to the y coordinate of the last cell position\n */\nexport function isAfterLast({ x, y }: Cell, lastCell: Cell) {\n  return x >= lastCell.x || y >= lastCell.y;\n}\n\n/** check if out of range */\nexport function isBeforeFirst({ x, y }: Cell) {\n  return x < 0 || y < 0;\n}\n\n/** Compare cells, only 1 coordinate difference is possible */\n// export function getDirectionCoordinate(initial: Cell, last: Cell): Partial<Cell> | null {\n//   const c: (keyof Cell)[] = ['x', 'y'];\n//   for (let k of c) {\n//     if (initial[k] !== last[k]) {\n//       return { [k]: 1 };\n//     }\n//   }\n//   return null;\n// }\n\n// export function getLargestAxis(initial: Cell, last: Cell): Partial<Cell> | null {\n//   const cell: Partial<Cell> = {};\n//   const c: (keyof Cell)[] = ['x', 'y'];\n//   for (let k of c) {\n//     cell[k] = Math.abs(initial[k] - last[k]);\n//   }\n//   if (cell.x > cell.y) {\n//     return { x: 1 };\n//   }\n//   if (cell.y > cell.x) {\n//     return { y: 1 };\n//   }\n//   return null;\n// }\n\nexport function styleByCellProps(styles: { [key: string]: number }): RangeAreaCss {\n  return {\n    left: `${styles.left}px`,\n    top: `${styles.top}px`,\n    width: `${styles.width}px`,\n    height: `${styles.height}px`,\n  };\n}\n\nexport function getCell(\n  { x, y, x1, y1 }: RangeArea,\n  dimensionRow: Pick<\n    DimensionIndexInput,\n    'indexToItem' | 'indexes' | 'originItemSize'\n  >,\n  dimensionCol: Pick<\n    DimensionIndexInput,\n    'indexToItem' | 'indexes' | 'originItemSize'\n  >,\n) {\n  const top = getItemByIndex(dimensionRow, y).start;\n  const left = getItemByIndex(dimensionCol, x).start;\n  const bottom = getItemByIndex(dimensionRow, y1).end;\n  const right = getItemByIndex(dimensionCol, x1).end;\n\n  return {\n    left,\n    right,\n    top,\n    bottom,\n    width: right - left,\n    height: bottom - top,\n  };\n}\n"], "version": 3}