{"file": "index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,OAAO,CAAC,GAAG,IAAG;AACZ,IAAA,IAAI,KAAK,GAAI,GAA2B,CAAC,KAAK,IAAI,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC;AAC3E,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE;;AAGtB,IAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC;AACnC,IAAA,IAAI,WAAW,KAAK,KAAK,EAAE;AACzB,QAAA,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,WAAW,CAAC;;AAExC,IAAA,OAAO,WAAW;AACpB,CAAC,CAAC;;AC+FW,MAAA,eAAe,GAAG,IAAI,GAAG,CAAiC;IACrE,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;IAC5C,CAAC,YAAY,EAAE,YAAY,CAAC;IAC5B,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,WAAW,EAAE,WAAW,CAAC;IAC1B,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,YAAY,EAAE,YAAY,CAAC;IAC5B,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,eAAe,EAAE,eAAe,CAAC;IAClC,CAAC,0BAA0B,EAAE,0BAA0B,CAAC;IACxD,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;IAC5C,CAAC,cAAc,EAAE,cAAc,CAAC;IAChC,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;IAC9C,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IAC1C,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;IAC9C,CAAC,eAAe,EAAE,eAAe,CAAC;IAClC,CAAC,cAAc,EAAE,cAAc,CAAC;IAChC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,cAAc,EAAE,cAAc,CAAC;IAChC,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IAC1C,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;IAC9C,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;IAC9C,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;IAChD,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IAC1C,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,eAAe,EAAE,eAAe,CAAC;IAClC,CAAC,uBAAuB,EAAE,uBAAuB,CAAC;IAClD,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IAC1C,CAAC,SAAS,EAAE,SAAS,CAAC;IACtB,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,WAAW,EAAE,WAAW,CAAC;IAC1B,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,YAAY,EAAE,YAAY,CAAC;IAC5B,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,YAAY,EAAE,YAAY,CAAC;IAC5B,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,eAAe,EAAE,eAAe,CAAC;IAClC,CAAC,cAAc,EAAE,cAAc,CAAC;IAChC,CAAC,WAAW,EAAE,WAAW,CAAC;IAC1B,CAAC,cAAc,EAAE,cAAc,CAAC;IAChC,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IAC1C,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;IAChD,CAAC,YAAY,EAAE,YAAY,CAAC;IAC5B,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IAC1C,CAAC,cAAc,EAAE,cAAc,CAAC;IAChC,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;IAC5C,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;IAC5C,CAAC,yBAAyB,EAAE,yBAAyB,CAAC;IACtD,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IAC1C,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IACtC,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IAC1C,CAAC,eAAe,EAAE,eAAe,CAAC;IAClC,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;IAC9C,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IAC1C,CAAC,SAAS,EAAE,SAAS,CAAC;IACtB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,UAAU,EAAE,UAAU,CAAC;IACxB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,WAAW,EAAE,WAAW,CAAC;IAC1B,CAAC,YAAY,EAAE,YAAY,CAAC;IAC5B,CAAC,cAAc,EAAE,cAAc,CAAC;IAChC,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;IAC5C,CAAC,YAAY,EAAE,YAAY,CAAC;IAC5B,CAAC,WAAW,EAAE,WAAW,CAAC;IAC1B,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;IAChD,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;IAC9C,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;IAChD,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;IAC5C,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;IAC9C,CAAC,eAAe,EAAE,eAAe,CAAC;IAClC,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,UAAU,EAAE,UAAU,CAAC;IACxB,CAAC,YAAY,EAAE,YAAY,CAAC;IAC5B,CAAC,KAAK,EAAE,KAAK,CAAC;IACd,CAAC,eAAe,EAAE,eAAe,CAAC;IAClC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,cAAc,EAAE,cAAc,CAAC;IAChC,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;IAChD,CAAC,MAAM,EAAE,MAAM;AAChB,CAAA;;ACvND;;;AAGG;AACG,MAAO,iBAAkB,SAAQ,UAAU,CAAA;IAC/C,WAAY,CAAA,QAA6B,EAAE,SAA0B,EAAA;AACnE,QAAA,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;AAC1B,QAAA,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,IAC3C,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CACjC;;AAGH,IAAA,sBAAsB,CACpB,KAIE,EAAA;QAEF,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI;AAEvD,QAAA,IAAI,CAAC,KAAK,IAAI,SAAS,EAAE;YACvB;;QAGF,MAAM,SAAS,GACb,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM;AAE5D,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM;AAEnD,QAAA,IAAI,SAAS,GAAG,MAAM,EAAE;AACtB,YAAA,MAAM,KAAK,GAAG,MAAM,GAAG,SAAS;AAChC,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM;gBACvD,KAAK,EAAE,SAAS,GAAG,CAAC;AACpB,gBAAA,IAAI,EAAE,EAAE;AACT,aAAA,CAAC,CAAC;AAEH,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;AAExD,YAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;gBAC1B;;AAGF,YAAA,MAAM,KAAK,GAAG;AACZ,gBAAA,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;AACvD,gBAAA,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;aACzC;YAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;;;AAGvC;;;;", "names": [], "sources": ["src/global/global.ts", "src/types/events.ts", "src/plugins/add-rows-on-paste.plugin.ts"], "sourcesContent": ["import { setMode } from '@stencil/core';\nimport { getTheme } from '../themeManager/theme.service';\n\nsetMode(elm => {\n  let theme = (elm as HTMLRevoGridElement).theme || elm.getAttribute('theme');\n  if (typeof theme === 'string') {\n    theme = theme.trim();\n  }\n\n  const parsedTheme = getTheme(theme);\n  if (parsedTheme !== theme) {\n    elm.setAttribute('theme', parsedTheme);\n  }\n  return parsedTheme;\n});\n", "\nexport type RevogridEvents = 'contentsizechanged'|\n  'beforeedit'|\n  'beforerangeedit'|\n  'afteredit'|\n  'beforeautofill'|\n  'beforerange'|\n  'afterfocus'|\n  'roworderchanged'|\n  'beforesorting'|\n  'beforesourcesortingapply'|\n  'beforesortingapply'|\n  'rowdragstart'|\n  'headerclick'|\n  'beforecellfocus'|\n  'beforefocuslost'|\n  'beforesourceset'|\n  'beforeanysource'|\n  'aftersourceset'|\n  'afteranysource'|\n  'beforecolumnsset'|\n  'beforecolumnapplied'|\n  'aftercolumnsset'|\n  'beforefilterapply'|\n  'beforefiltertrimmed'|\n  'beforetrimmed'|\n  'aftertrimmed'|\n  'viewportscroll'|\n  'beforeexport'|\n  'beforeeditstart'|\n  'aftercolumnresize'|\n  'beforerowdefinition'|\n  'filterconfigchanged'|\n  'sortingconfigchanged'|\n  'rowheaderschanged'|\n  'beforegridrender'|\n  'aftergridrender'|\n  'aftergridinit'|\n  'additionaldatachanged'|\n  'afterthemechanged'|\n  'created'|\n  'beforepaste'|\n  'beforepasteapply'|\n  'pasteregion'|\n  'afterpasteapply'|\n  'beforecut'|\n  'clearregion'|\n  'beforecopy'|\n  'beforecopyapply'|\n  'copyregion'|\n  'beforerowrender'|\n  'afterrender'|\n  'beforecellrender'|\n  'beforedatarender'|\n  'dragstartcell'|\n  'celleditinit'|\n  'closeedit'|\n  'filterChange'|\n  'resetChange'|\n  'beforefocusrender'|\n  'beforescrollintoview'|\n  'afterfocus'|\n  'beforeheaderclick'|\n  'headerresize'|\n  'beforeheaderresize'|\n  'headerdblclick'|\n  'beforeheaderrender'|\n  'beforegroupheaderrender'|\n  'afterheaderrender'|\n  'rowdragstartinit'|\n  'rowdragendinit'|\n  'rowdragmoveinit'|\n  'rowdragmousemove'|\n  'rowdropinit'|\n  'roworderchange'|\n  'beforecopyregion'|\n  'beforepasteregion'|\n  'celleditapply'|\n  'beforecellfocusinit'|\n  'beforenextvpfocus'|\n  'setedit'|\n  'beforeapplyrange'|\n  'beforesetrange'|\n  'setrange'|\n  'beforeeditrender'|\n  'selectall'|\n  'canceledit'|\n  'settemprange'|\n  'beforesettemprange'|\n  'applyfocus'|\n  'focuscell'|\n  'beforerangedataapply'|\n  'selectionchangeinit'|\n  'beforerangecopyapply'|\n  'rangeeditapply'|\n  'clipboardrangecopy'|\n  'clipboardrangepaste'|\n  'beforekeydown'|\n  'beforekeyup'|\n  'beforecellsave'|\n  'celledit'|\n  'scrollview'|\n  'ref'|\n  'scrollvirtual'|\n  'scrollviewport'|\n  'resizeviewport'|\n  'scrollchange'|\n  'scrollviewportsilent'|\n  'html'\nexport const REVOGRID_EVENTS = new Map<RevogridEvents, RevogridEvents>([\n  ['contentsizechanged', 'contentsizechanged'],\n  ['beforeedit', 'beforeedit'],\n  ['beforerangeedit', 'beforerangeedit'],\n  ['afteredit', 'afteredit'],\n  ['beforeautofill', 'beforeautofill'],\n  ['beforerange', 'beforerange'],\n  ['afterfocus', 'afterfocus'],\n  ['roworderchanged', 'roworderchanged'],\n  ['beforesorting', 'beforesorting'],\n  ['beforesourcesortingapply', 'beforesourcesortingapply'],\n  ['beforesortingapply', 'beforesortingapply'],\n  ['rowdragstart', 'rowdragstart'],\n  ['headerclick', 'headerclick'],\n  ['beforecellfocus', 'beforecellfocus'],\n  ['beforefocuslost', 'beforefocuslost'],\n  ['beforesourceset', 'beforesourceset'],\n  ['beforeanysource', 'beforeanysource'],\n  ['aftersourceset', 'aftersourceset'],\n  ['afteranysource', 'afteranysource'],\n  ['beforecolumnsset', 'beforecolumnsset'],\n  ['beforecolumnapplied', 'beforecolumnapplied'],\n  ['aftercolumnsset', 'aftercolumnsset'],\n  ['beforefilterapply', 'beforefilterapply'],\n  ['beforefiltertrimmed', 'beforefiltertrimmed'],\n  ['beforetrimmed', 'beforetrimmed'],\n  ['aftertrimmed', 'aftertrimmed'],\n  ['viewportscroll', 'viewportscroll'],\n  ['beforeexport', 'beforeexport'],\n  ['beforeeditstart', 'beforeeditstart'],\n  ['aftercolumnresize', 'aftercolumnresize'],\n  ['beforerowdefinition', 'beforerowdefinition'],\n  ['filterconfigchanged', 'filterconfigchanged'],\n  ['sortingconfigchanged', 'sortingconfigchanged'],\n  ['rowheaderschanged', 'rowheaderschanged'],\n  ['beforegridrender', 'beforegridrender'],\n  ['aftergridrender', 'aftergridrender'],\n  ['aftergridinit', 'aftergridinit'],\n  ['additionaldatachanged', 'additionaldatachanged'],\n  ['afterthemechanged', 'afterthemechanged'],\n  ['created', 'created'],\n  ['beforepaste', 'beforepaste'],\n  ['beforepasteapply', 'beforepasteapply'],\n  ['pasteregion', 'pasteregion'],\n  ['afterpasteapply', 'afterpasteapply'],\n  ['beforecut', 'beforecut'],\n  ['clearregion', 'clearregion'],\n  ['beforecopy', 'beforecopy'],\n  ['beforecopyapply', 'beforecopyapply'],\n  ['copyregion', 'copyregion'],\n  ['beforerowrender', 'beforerowrender'],\n  ['afterrender', 'afterrender'],\n  ['beforecellrender', 'beforecellrender'],\n  ['beforedatarender', 'beforedatarender'],\n  ['dragstartcell', 'dragstartcell'],\n  ['celleditinit', 'celleditinit'],\n  ['closeedit', 'closeedit'],\n  ['filterChange', 'filterChange'],\n  ['resetChange', 'resetChange'],\n  ['beforefocusrender', 'beforefocusrender'],\n  ['beforescrollintoview', 'beforescrollintoview'],\n  ['afterfocus', 'afterfocus'],\n  ['beforeheaderclick', 'beforeheaderclick'],\n  ['headerresize', 'headerresize'],\n  ['beforeheaderresize', 'beforeheaderresize'],\n  ['headerdblclick', 'headerdblclick'],\n  ['beforeheaderrender', 'beforeheaderrender'],\n  ['beforegroupheaderrender', 'beforegroupheaderrender'],\n  ['afterheaderrender', 'afterheaderrender'],\n  ['rowdragstartinit', 'rowdragstartinit'],\n  ['rowdragendinit', 'rowdragendinit'],\n  ['rowdragmoveinit', 'rowdragmoveinit'],\n  ['rowdragmousemove', 'rowdragmousemove'],\n  ['rowdropinit', 'rowdropinit'],\n  ['roworderchange', 'roworderchange'],\n  ['beforecopyregion', 'beforecopyregion'],\n  ['beforepasteregion', 'beforepasteregion'],\n  ['celleditapply', 'celleditapply'],\n  ['beforecellfocusinit', 'beforecellfocusinit'],\n  ['beforenextvpfocus', 'beforenextvpfocus'],\n  ['setedit', 'setedit'],\n  ['beforeapplyrange', 'beforeapplyrange'],\n  ['beforesetrange', 'beforesetrange'],\n  ['setrange', 'setrange'],\n  ['beforeeditrender', 'beforeeditrender'],\n  ['selectall', 'selectall'],\n  ['canceledit', 'canceledit'],\n  ['settemprange', 'settemprange'],\n  ['beforesettemprange', 'beforesettemprange'],\n  ['applyfocus', 'applyfocus'],\n  ['focuscell', 'focuscell'],\n  ['beforerangedataapply', 'beforerangedataapply'],\n  ['selectionchangeinit', 'selectionchangeinit'],\n  ['beforerangecopyapply', 'beforerangecopyapply'],\n  ['rangeeditapply', 'rangeeditapply'],\n  ['clipboardrangecopy', 'clipboardrangecopy'],\n  ['clipboardrangepaste', 'clipboardrangepaste'],\n  ['beforekeydown', 'beforekeydown'],\n  ['beforekeyup', 'beforekeyup'],\n  ['beforecellsave', 'beforecellsave'],\n  ['celledit', 'celledit'],\n  ['scrollview', 'scrollview'],\n  ['ref', 'ref'],\n  ['scrollvirtual', 'scrollvirtual'],\n  ['scrollviewport', 'scrollviewport'],\n  ['resizeviewport', 'resizeviewport'],\n  ['scrollchange', 'scrollchange'],\n  ['scrollviewportsilent', 'scrollviewportsilent'],\n  ['html', 'html']\n]);", "import type { PluginProviders } from '../types/plugin.types';\nimport { BasePlugin } from './base.plugin';\n\n/**\n * Automatically adds new rows when pasted data is larger than current rows\n * @event newRows - is triggered when new rows are added. Data of new rows can be filled with default values. If the event is prevented, no rows will be added\n */\nexport class AutoAddRowsPlugin extends BasePlugin {\n  constructor(revogrid: HTMLRevoGridElement, providers: PluginProviders) {\n    super(revogrid, providers);\n    this.addEventListener('beforepasteapply', evt =>\n      this.handleBeforePasteApply(evt),\n    );\n  }\n\n  handleBeforePasteApply(\n    event: CustomEvent<{\n      raw: string;\n      parsed: string[][];\n      event: ClipboardEvent;\n    }>,\n  ) {\n    const start = this.providers.selection.focused;\n    const isEditing = this.providers.selection.edit != null;\n\n    if (!start || isEditing) {\n      return;\n    }\n\n    const rowLength =\n      this.providers.data.stores.rgRow.store.get('items').length;\n\n    const endRow = start.y + event.detail.parsed.length;\n\n    if (rowLength < endRow) {\n      const count = endRow - rowLength;\n      const newRows = Array.from({ length: count }, (_, i) => ({\n        index: rowLength + i,\n        data: {},\n      }));\n\n      const event = this.emit('newRows', { newRows: newRows });\n\n      if (event.defaultPrevented) {\n        return;\n      }\n\n      const items = [\n        ...this.providers.data.stores.rgRow.store.get('source'),\n        ...event.detail.newRows.map(j => j.data),\n      ];\n\n      this.providers.data.setData(items);\n    }\n  }\n}\n"], "version": 3}