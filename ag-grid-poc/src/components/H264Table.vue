<script setup lang="ts">
import { ref, onMounted, h } from "vue";
import { AgGridVue } from "ag-grid-vue3";
import type { ColDef, GridOptions, ICellRendererParams } from "ag-grid-community";
import DetailPanel from "./DetailPanel.vue";

// Sample H.264 data
const h264RowData = ref([
  { id: 1, name: "H.264 Stream Alpha", bandwidth: "25 Mbps" },
  { id: 2, name: "H.264 Stream Beta", bandwidth: "30 Mbps" },
  { id: 3, name: "H.264 Stream Gamma", bandwidth: "18 Mbps" },
  { id: 4, name: "H.264 Stream Delta", bandwidth: "22 Mbps" },
  { id: 5, name: "H.264 Stream Epsilon", bandwidth: "35 Mbps" },
  { id: 6, name: "H.264 Stream Zeta", bandwidth: "28 Mbps" },
]);

// Column definitions for H.264 table
const h264ColumnDefs: ColDef[] = [
  {
    field: "id",
    headerName: "ID",
    width: 80,
    sortable: true,
    pinned: "left",
  },
  {
    field: "name",
    headerName: "Stream Name",
    flex: 1,
    sortable: true,
    filter: true,
  },
  {
    field: "bandwidth",
    headerName: "Bandwidth",
    width: 150,
    sortable: true,
    filter: true,
  },
];

// Custom detail cell renderer component
const DetailCellRenderer = (params: ICellRendererParams) => {
  return h(DetailPanel, {
    parentData: params.data,
    tableType: "H.264",
  });
};

// Grid options with master-detail configuration
const h264GridOptions: GridOptions = {
  columnDefs: h264ColumnDefs,
  rowData: h264RowData.value,
  masterDetail: true,
  detailCellRenderer: DetailCellRenderer,
  detailRowHeight: 300,
  animateRows: true,
  suppressRowClickSelection: true,
  rowSelection: "single",
  headerHeight: 50,
  rowHeight: 50,
  // Enable pinned headers
  floatingFilter: false,
  suppressMenuHide: true,
  // Pagination
  pagination: true,
  paginationPageSize: 10,
};

const gridApi = ref<any>(null);

const onGridReady = (params: any) => {
  gridApi.value = params.api;
};
</script>

<template>
  <div class="table-container">
    <div class="table-header">
      <h2>H.264 Streams</h2>
      <p>Click the expand icon to view Sources and Destinations for each stream</p>
    </div>

    <div class="ag-theme-alpine grid-container">
      <AgGridVue
        :gridOptions="h264GridOptions"
        :rowData="h264RowData"
        :columnDefs="h264ColumnDefs"
        @grid-ready="onGridReady"
        class="ag-grid"
      />
    </div>
  </div>
</template>

<style scoped>
.table-container {
  width: 100%;
}

.table-header {
  padding: 1.5rem;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
}

.table-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.table-header p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.grid-container {
  height: 500px;
  width: 100%;
}

.ag-grid {
  height: 100%;
  width: 100%;
}

/* Custom styling for AG Grid */
:deep(.ag-header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #28a745;
  font-weight: 600;
}

:deep(.ag-header-cell) {
  border-right: 1px solid #dee2e6;
}

:deep(.ag-row) {
  border-bottom: 1px solid #f1f3f4;
}

:deep(.ag-row:hover) {
  background-color: #f8f9fa;
}

:deep(.ag-row-selected) {
  background-color: #e8f5e8 !important;
}

:deep(.ag-detail-row) {
  background-color: #f8f9fa;
}

/* Pinned header styling */
:deep(.ag-pinned-left-header) {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
}

:deep(.ag-cell-focus) {
  border: 2px solid #28a745;
}

/* Master detail expand/collapse button styling */
:deep(.ag-group-expanded) {
  color: #28a745;
}

:deep(.ag-group-contracted) {
  color: #6c757d;
}
</style>
