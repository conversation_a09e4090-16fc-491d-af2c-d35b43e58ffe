<script setup lang="ts">
import { ref, onMounted, h } from "vue";
import { AgGridVue } from "ag-grid-vue3";
import type { ColDef, GridOptions, ICellRendererParams } from "ag-grid-community";
import DetailPanel from "./DetailPanel.vue";
import { myTheme } from "../main";

// Sample H.264 data with expanded state
const h264RowData = ref([
  { id: 1, name: "H.264 Stream Alpha", bandwidth: "25 Mbps", expanded: false },
  { id: 2, name: "H.264 Stream Beta", bandwidth: "30 Mbps", expanded: false },
  { id: 3, name: "H.264 Stream Gamma", bandwidth: "18 Mbps", expanded: false },
  { id: 4, name: "H.264 Stream Delta", bandwidth: "22 Mbps", expanded: false },
  { id: 5, name: "H.264 Stream Epsilon", bandwidth: "35 Mbps", expanded: false },
  { id: 6, name: "H.264 Stream Zeta", bandwidth: "28 Mbps", expanded: false },
]);

// Track expanded rows
const expandedRows = ref(new Set<number>());

// Expand/collapse button cell renderer
const ExpandButtonRenderer = (params: any) => {
  const isExpanded = expandedRows.value.has(params.data.id);
  const button = document.createElement("button");
  button.innerHTML = isExpanded ? "▼" : "▶";
  button.style.cssText =
    "border: none; background: none; cursor: pointer; font-size: 12px; padding: 4px 8px;";
  button.addEventListener("click", () => toggleRow(params.data.id));
  return button;
};

// Column definitions for H.264 table
const h264ColumnDefs: ColDef[] = [
  {
    headerName: "",
    width: 50,
    cellRenderer: ExpandButtonRenderer,
    sortable: false,
    filter: false,
    pinned: "left",
  },
  {
    field: "id",
    headerName: "ID",
    width: 80,
    sortable: true,
    pinned: "left",
  },
  {
    field: "name",
    headerName: "Stream Name",
    flex: 1,
    sortable: true,
    filter: true,
  },
  {
    field: "bandwidth",
    headerName: "Bandwidth",
    width: 150,
    sortable: true,
    filter: true,
  },
];

// Toggle row expansion
const toggleRow = (rowId: number) => {
  if (expandedRows.value.has(rowId)) {
    expandedRows.value.delete(rowId);
  } else {
    expandedRows.value.add(rowId);
  }
  // Refresh the grid to update button states
  if (gridApi.value) {
    gridApi.value.refreshCells();
  }
};

// Grid options configuration
const h264GridOptions: GridOptions = {
  columnDefs: h264ColumnDefs,
  rowData: h264RowData.value,
  animateRows: true,
  suppressRowClickSelection: true,
  rowSelection: "single",
  // Enable pinned headers
  floatingFilter: false,
  suppressMenuHide: true,
  // Pagination
  pagination: true,
  paginationPageSize: 10,
  // Apply custom theme
  theme: myTheme,
};

const gridApi = ref<any>(null);

const onGridReady = (params: any) => {
  gridApi.value = params.api;
};
</script>

<template>
  <div class="table-container">
    <div class="table-header">
      <h2>H.264 Streams</h2>
      <p>Click the expand icon to view Sources and Destinations for each stream</p>
    </div>

    <div class="grid-container">
      <AgGridVue
        :gridOptions="h264GridOptions"
        :rowData="h264RowData"
        :columnDefs="h264ColumnDefs"
        @grid-ready="onGridReady"
        class="ag-grid"
      />
    </div>

    <!-- Detail panels for expanded rows -->
    <div v-for="rowId in Array.from(expandedRows)" :key="rowId" class="detail-container">
      <DetailPanel :parentData="h264RowData.find((row) => row.id === rowId)" tableType="H.264" />
    </div>
  </div>
</template>

<style scoped>
.table-container {
  width: 100%;
}

.table-header {
  padding: 1.5rem;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
}

.table-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.table-header p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.grid-container {
  height: 500px;
  width: 100%;
}

.ag-grid {
  height: 100%;
  width: 100%;
}

/* Detail container styling */
.detail-container {
  margin-top: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
}
</style>
