<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { AgGridVue } from "ag-grid-vue3";
import type { ColDef, GridOptions } from "ag-grid-community";

interface Props {
  parentData: any;
  tableType: "ASI" | "H.264";
}

const props = defineProps<Props>();

// Sample data for Sources (max 2 rows)
const sourcesData = computed(() => [
  { id: 1, name: `Source 1 for ${props.parentData.name}`, building: "Building A" },
  { id: 2, name: `Source 2 for ${props.parentData.name}`, building: "Building B" },
]);

// Sample data for Destinations (unlimited rows)
const destinationsData = computed(() => [
  { id: 1, name: `Dest 1 for ${props.parentData.name}`, building: "Building C" },
  { id: 2, name: `Dest 2 for ${props.parentData.name}`, building: "Building D" },
  { id: 3, name: `Dest 3 for ${props.parentData.name}`, building: "Building E" },
  { id: 4, name: `Dest 4 for ${props.parentData.name}`, building: "Building F" },
]);

// Column definitions for Sources and Destinations
const sourcesColumnDefs: ColDef[] = [
  { field: "id", headerName: "ID", width: 80, sortable: true },
  { field: "name", headerName: "Name", flex: 1, sortable: true },
  { field: "building", headerName: "Building", width: 150, sortable: true },
];

const destinationsColumnDefs: ColDef[] = [
  { field: "id", headerName: "ID", width: 80, sortable: true },
  { field: "name", headerName: "Name", flex: 1, sortable: true },
  { field: "building", headerName: "Building", width: 150, sortable: true },
];

// Grid options
const sourcesGridOptions: GridOptions = {
  columnDefs: sourcesColumnDefs,
  rowData: sourcesData.value,
  domLayout: "autoHeight",
  suppressHorizontalScroll: false,
  suppressRowClickSelection: true,
};

const destinationsGridOptions: GridOptions = {
  columnDefs: destinationsColumnDefs,
  rowData: destinationsData.value,
  domLayout: "autoHeight",
  suppressHorizontalScroll: false,
  suppressRowClickSelection: true,
};
</script>

<template>
  <div class="detail-panel">
    <div class="detail-section">
      <h4 class="section-title">Sources</h4>
      <div class="ag-theme-alpine detail-grid">
        <AgGridVue
          :gridOptions="sourcesGridOptions"
          :rowData="sourcesData"
          :columnDefs="sourcesColumnDefs"
        />
      </div>
    </div>

    <div class="detail-section">
      <h4 class="section-title">Destinations</h4>
      <div class="ag-theme-alpine detail-grid">
        <AgGridVue
          :gridOptions="destinationsGridOptions"
          :rowData="destinationsData"
          :columnDefs="destinationsColumnDefs"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.detail-panel {
  padding: 1rem;
  background-color: #f8f9fa;
  border-left: 4px solid #007bff;
}

.detail-section {
  margin-bottom: 1.5rem;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dee2e6;
}

.detail-grid {
  width: 100%;
  min-height: 100px;
}

/* Use default AG Grid styling for detail tables */
</style>
