import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import ASITable from '../ASITable.vue'

describe('ASITable', () => {
  it('renders properly', () => {
    const wrapper = mount(ASITable)
    expect(wrapper.text()).toContain('ASI Streams')
  })

  it('has correct table structure', () => {
    const wrapper = mount(ASITable)
    expect(wrapper.find('.table-container').exists()).toBe(true)
    expect(wrapper.find('.table-header').exists()).toBe(true)
    expect(wrapper.find('.grid-container').exists()).toBe(true)
  })
})
