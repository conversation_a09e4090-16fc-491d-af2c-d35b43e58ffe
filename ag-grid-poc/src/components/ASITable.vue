<script setup lang="ts">
import { ref, onMounted, h } from "vue";
import { AgGridVue } from "ag-grid-vue3";
import type { ColDef, GridOptions, ICellRendererParams } from "ag-grid-community";
import DetailPanel from "./DetailPanel.vue";
import { myTheme } from "../main";

// Sample ASI data - we'll create a flattened structure with detail rows
const baseAsiData = [
  { id: 1, name: "ASI Stream 1", bandwidth: "10 Mbps" },
  { id: 2, name: "ASI Stream 2", bandwidth: "15 Mbps" },
  { id: 3, name: "ASI Stream 3", bandwidth: "8 Mbps" },
  { id: 4, name: "ASI Stream 4", bandwidth: "12 Mbps" },
  { id: 5, name: "ASI Stream 5", bandwidth: "20 Mbps" },
];

// Track expanded rows
const expandedRows = ref(new Set<number>());

// Create flattened data function
function createFlattenedData() {
  const flattened: any[] = [];

  baseAsiData.forEach((item) => {
    // Add the main row
    flattened.push({ ...item, rowType: "main" });

    // Add detail row if expanded
    if (expandedRows.value.has(item.id)) {
      flattened.push({
        id: `${item.id}-detail`,
        parentId: item.id,
        rowType: "detail",
        parentData: item,
      });
    }
  });

  return flattened;
}

// Create flattened row data that includes detail rows
const asiRowData = ref(createFlattenedData());

// Expand/collapse button cell renderer
const ExpandButtonRenderer = (params: any) => {
  if (params.data.rowType === "detail") return "";

  const isExpanded = expandedRows.value.has(params.data.id);
  const button = document.createElement("button");
  button.innerHTML = isExpanded ? "▼" : "▶";
  button.style.cssText =
    "border: none; background: none; cursor: pointer; font-size: 12px; padding: 4px 8px;";
  button.addEventListener("click", () => toggleRow(params.data.id));
  return button;
};

// Detail cell renderer for detail rows
const DetailCellRenderer = (params: any) => {
  if (params.data.rowType !== "detail") return "";

  const container = document.createElement("div");
  container.style.cssText = "width: 100%; padding: 0;";

  // We'll mount a Vue component here
  const detailComponent = h(DetailPanel, {
    parentData: params.data.parentData,
    tableType: "ASI",
  });

  return container;
};

// Column definitions for ASI table
const asiColumnDefs: ColDef[] = [
  {
    headerName: "",
    width: 50,
    cellRenderer: (params: any) => {
      if (params.data.rowType === "detail") return "";
      return ExpandButtonRenderer(params);
    },
    sortable: false,
    filter: false,
    pinned: "left",
  },
  {
    field: "id",
    headerName: "ID",
    width: 80,
    sortable: true,
    pinned: "left",
    cellRenderer: (params: any) => {
      if (params.data.rowType === "detail") return "";
      return params.value;
    },
  },
  {
    field: "name",
    headerName: "Name",
    flex: 1,
    sortable: true,
    filter: true,
    cellRenderer: (params: any) => {
      if (params.data.rowType === "detail") {
        // Create a container that spans the full row width
        const container = document.createElement("div");
        container.style.cssText = `
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          width: 100%;
          height: 100%;
          padding: 15px;
          background-color: #f9f9f9;
          border-top: 1px solid #e0e0e0;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          overflow-y: auto;
          z-index: 1;
        `;
        container.innerHTML = `
          <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: #495057; font-size: 14px; font-weight: 600;">Sources</h4>
            <div style="background: white; border: 1px solid #dee2e6; border-radius: 4px; padding: 8px;">
              <div style="display: grid; grid-template-columns: 30px 60px 1fr 120px; gap: 8px; font-size: 12px; font-weight: 600; padding: 4px 0; border-bottom: 1px solid #eee;">
                <div></div><div>ID</div><div>Name</div><div>Building</div>
              </div>
              <div style="display: grid; grid-template-columns: 30px 60px 1fr 120px; gap: 8px; font-size: 12px; padding: 4px 0; align-items: center;">
                <input type="checkbox" style="margin: 0;"><div>1</div><div>Source 1 for ${params.data.parentData.name}</div><div>Building A</div>
              </div>
              <div style="display: grid; grid-template-columns: 30px 60px 1fr 120px; gap: 8px; font-size: 12px; padding: 4px 0; align-items: center;">
                <input type="checkbox" style="margin: 0;"><div>2</div><div>Source 2 for ${params.data.parentData.name}</div><div>Building B</div>
              </div>
            </div>
          </div>
          <div>
            <h4 style="margin: 0 0 10px 0; color: #495057; font-size: 14px; font-weight: 600;">Destinations</h4>
            <div style="background: white; border: 1px solid #dee2e6; border-radius: 4px; padding: 8px;">
              <div style="display: grid; grid-template-columns: 30px 60px 1fr 120px; gap: 8px; font-size: 12px; font-weight: 600; padding: 4px 0; border-bottom: 1px solid #eee;">
                <div></div><div>ID</div><div>Name</div><div>Building</div>
              </div>
              <div style="display: grid; grid-template-columns: 30px 60px 1fr 120px; gap: 8px; font-size: 12px; padding: 4px 0; align-items: center;">
                <input type="checkbox" style="margin: 0;"><div>1</div><div>Dest 1 for ${params.data.parentData.name}</div><div>Building C</div>
              </div>
              <div style="display: grid; grid-template-columns: 30px 60px 1fr 120px; gap: 8px; font-size: 12px; padding: 4px 0; align-items: center;">
                <input type="checkbox" style="margin: 0;"><div>2</div><div>Dest 2 for ${params.data.parentData.name}</div><div>Building D</div>
              </div>
              <div style="display: grid; grid-template-columns: 30px 60px 1fr 120px; gap: 8px; font-size: 12px; padding: 4px 0; align-items: center;">
                <input type="checkbox" style="margin: 0;"><div>3</div><div>Dest 3 for ${params.data.parentData.name}</div><div>Building E</div>
              </div>
              <div style="display: grid; grid-template-columns: 30px 60px 1fr 120px; gap: 8px; font-size: 12px; padding: 4px 0; align-items: center;">
                <input type="checkbox" style="margin: 0;"><div>4</div><div>Dest 4 for ${params.data.parentData.name}</div><div>Building F</div>
              </div>
            </div>
          </div>
        `;
        return container;
      }
      return params.value;
    },
  },
  {
    field: "bandwidth",
    headerName: "Bandwidth",
    width: 150,
    sortable: true,
    filter: true,
    cellRenderer: (params: any) => {
      if (params.data.rowType === "detail") return "";
      return params.value;
    },
  },
];

// Toggle row expansion
const toggleRow = (rowId: number) => {
  if (expandedRows.value.has(rowId)) {
    expandedRows.value.delete(rowId);
  } else {
    expandedRows.value.add(rowId);
  }

  // Recreate the flattened data and update the grid
  asiRowData.value = createFlattenedData();

  if (gridApi.value) {
    gridApi.value.setGridOption("rowData", asiRowData.value);
  }
};

// Grid options configuration
const asiGridOptions: GridOptions = {
  columnDefs: asiColumnDefs,
  rowData: asiRowData.value,
  animateRows: true,
  rowSelection: {
    mode: "singleRow",
    enableClickSelection: false,
    isRowSelectable: (params: any) => {
      // Only allow selection of main rows, not detail rows
      return params.data.rowType !== "detail";
    },
  },
  suppressMenuHide: true,
  // Pagination
  pagination: true,
  paginationPageSize: 10,
  paginationPageSizeSelector: [10, 20, 50, 100],
  // Apply custom theme
  theme: myTheme,
  // Use auto height for dynamic sizing
  domLayout: "autoHeight",
  // Custom row height function for detail rows
  getRowHeight: (params: any) => {
    if (params.data.rowType === "detail") {
      return 400; // Increased height to show both Sources and Destinations tables
    }
    return undefined; // Use default height for main rows
  },
};

const gridApi = ref<any>(null);

const onGridReady = (params: any) => {
  gridApi.value = params.api;
};
</script>

<template>
  <div class="table-container">
    <div class="table-header">
      <h2>ASI Streams</h2>
      <p>Click the expand icon to view Sources and Destinations for each stream</p>
    </div>

    <div class="grid-container">
      <AgGridVue
        :gridOptions="asiGridOptions"
        :rowData="asiRowData"
        :columnDefs="asiColumnDefs"
        @grid-ready="onGridReady"
        class="ag-grid"
      />
    </div>
  </div>
</template>

<style scoped>
.table-container {
  width: 100%;
}

.table-header {
  padding: 1.5rem;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
}

.table-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.table-header p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.grid-container {
  width: 100%;
}

.ag-grid {
  height: 100%;
  width: 100%;
}

/* Hide AG Grid selection checkbox for detail rows */
:deep(.ag-row[row-id*="-detail"] .ag-selection-checkbox) {
  display: none !important;
}

/* Hide the entire selection column for detail rows */
:deep(.ag-row[row-id*="-detail"] .ag-cell[col-id="ag-Grid-AutoColumn"]) {
  display: none !important;
}

/* Detail container styling */
.detail-container {
  margin-top: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
}
</style>
