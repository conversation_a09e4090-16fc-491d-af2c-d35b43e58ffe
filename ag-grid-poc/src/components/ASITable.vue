<script setup lang="ts">
import { ref, h, computed } from "vue";
import RevoGrid, { type ColumnRegular } from "@revolist/vue3-datagrid";

// Sample ASI data with expandable rows
const asiRowData = ref([
  {
    id: 1,
    name: "ASI Stream 1",
    bandwidth: "10 Mbps",
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 1", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 1", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 1", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 1", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 1", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 1", building: "Building F", selected: false },
    ],
  },
  {
    id: 2,
    name: "ASI Stream 2",
    bandwidth: "15 Mbps",
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 2", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 2", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 2", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 2", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 2", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 2", building: "Building F", selected: false },
    ],
  },
  {
    id: 3,
    name: "ASI Stream 3",
    bandwidth: "8 Mbps",
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 3", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 3", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 3", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 3", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 3", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 3", building: "Building F", selected: false },
    ],
  },
  {
    id: 4,
    name: "ASI Stream 4",
    bandwidth: "12 Mbps",
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 4", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 4", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 4", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 4", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 4", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 4", building: "Building F", selected: false },
    ],
  },
  {
    id: 5,
    name: "ASI Stream 5",
    bandwidth: "20 Mbps",
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 5", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 5", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 5", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 5", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 5", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 5", building: "Building F", selected: false },
    ],
  },
]);

// Track expanded rows
const expandedRows = ref(new Set<number>());

// Add expand buttons to data for RevoGrid
const asiRowDataWithExpand = computed(() =>
  asiRowData.value.map((row) => ({
    ...row,
    expandButton: expandedRows.value.has(row.id) ? "▼" : "▶",
  }))
);

// Column definitions for RevoGrid with expand column and advanced features
const asiColumns: ColumnRegular[] = [
  {
    prop: "expandButton",
    name: "",
    size: 50,
    pin: "colPinStart",
    readonly: true,
    sortable: false,
    filter: false,
  },
  {
    prop: "id",
    name: "ID",
    size: 80,
    pin: "colPinStart",
    columnType: "number",
    sortable: true,
    filter: true,
  },
  {
    prop: "name",
    name: "Name",
    size: 300,
    columnType: "string",
    sortable: true,
    filter: true,
  },
  {
    prop: "bandwidth",
    name: "Bandwidth",
    size: 150,
    columnType: "string",
    sortable: true,
    filter: true,
  },
];

// Sub-table columns for Sources and Destinations with RevoGrid features
const subTableColumns: ColumnRegular[] = [
  {
    prop: "selected",
    name: "✓",
    size: 40,
    columnType: "boolean",
    sortable: false,
    filter: false,
  },
  {
    prop: "id",
    name: "ID",
    size: 60,
    columnType: "number",
    sortable: true,
    filter: true,
  },
  {
    prop: "name",
    name: "Name",
    size: 200,
    columnType: "string",
    sortable: true,
    filter: true,
  },
  {
    prop: "building",
    name: "Building",
    size: 120,
    columnType: "string",
    sortable: true,
    filter: true,
  },
];

// Toggle row expansion
const toggleExpansion = (rowId: number) => {
  if (expandedRows.value.has(rowId)) {
    expandedRows.value.delete(rowId);
  } else {
    expandedRows.value.add(rowId);
  }
};

// Handle row click to expand/collapse
const handleRowClick = (event: any) => {
  console.log("Row clicked:", event);
  const rowId = event.detail?.model?.id || event.detail?.data?.id;
  if (rowId) {
    toggleExpansion(rowId);
  }
};

// Handle cell click for expand button
const handleCellClick = (event: any) => {
  console.log("Cell clicked:", event);

  // Check if it's the expand button column
  if (event.detail?.prop === "expandButton") {
    const rowId = event.detail?.model?.id || event.detail?.data?.id;
    if (rowId) {
      console.log("Toggling expansion for row:", rowId);
      toggleExpansion(rowId);
    }
  }
};

// Handle general grid click
const handleGridClick = (event: any) => {
  console.log("Grid clicked:", event);
};

// Handle before edit to intercept expand button clicks
const handleBeforeEdit = (event: any) => {
  console.log("Before edit:", event.detail);

  // If it's the expand button column, prevent editing and toggle instead
  if (event.detail?.prop === "expandButton") {
    console.log("Expand button clicked, preventing edit");
    event.preventDefault();
    const rowId = event.detail?.model?.id || event.detail?.data?.id;
    if (rowId) {
      console.log("Toggling expansion for row via beforeedit:", rowId);
      toggleExpansion(rowId);
    }
    return false;
  }
  return true;
};

// Handle after edit to update data
const handleAfterEdit = (event: any) => {
  console.log("After edit:", event);
};

// Handle cell click for expand button
const handleCellClick = (event: any) => {
  console.log("Cell clicked:", event.detail);

  // Check if it's the expand button column
  if (event.detail?.prop === "expandButton") {
    const rowId = event.detail?.model?.id || event.detail?.data?.id;
    if (rowId) {
      console.log("Expand button clicked, toggling row:", rowId);
      toggleExpansion(rowId);
    }
  }
};

// Handle general grid click
const handleGridClick = (event: any) => {
  console.log("Grid clicked:", event);
};

// Handle grid double click
const handleGridDoubleClick = (event: any) => {
  console.log("Grid double clicked:", event);
};

// Handle focus
const handleFocus = (event: any) => {
  console.log("Grid focus:", event.detail);

  // Check if it's the expand button column
  if (event.detail?.prop === "expandButton") {
    const rowId = event.detail?.model?.id || event.detail?.data?.id;
    if (rowId) {
      console.log("Expand button focused, toggling row:", rowId);
      toggleExpansion(rowId);
    }
  }
};

// Handle before focus render
const handleBeforeFocusRender = (event: any) => {
  console.log("Before focus render:", event.detail);
};

// Get row data by ID
const getRowData = (rowId: number) => {
  return asiRowData.value.find((row) => row.id === rowId);
};
</script>

<template>
  <div class="table-container">
    <div class="table-header">
      <h2>ASI Streams</h2>
      <p>
        Click the expand button (▶) to view Sources and Destinations. Double-click cells to edit
        with RevoGrid's native editing.
      </p>
    </div>

    <!-- Main RevoGrid with Master-Detail -->
    <div class="main-grid-section">
      <div class="grid-container" style="position: relative">
        <RevoGrid
          :source="asiRowDataWithExpand"
          :columns="asiColumns"
          theme="compact"
          class="revo-grid"
          :readonly="false"
          :can-focus="true"
          :range="true"
          :resize="true"
          :filter="true"
          :auto-size-column="true"
          @beforeedit="handleBeforeEdit"
          @afteredit="handleAfterEdit"
        />

        <!-- Overlay expand buttons -->
        <div class="expand-buttons-overlay">
          <div
            v-for="(row, index) in asiRowData"
            :key="row.id"
            class="expand-button-overlay"
            :style="{ top: 32 + index * 32 + 'px' }"
            @click="toggleExpansion(row.id)"
          >
            {{ expandedRows.has(row.id) ? "▼" : "▶" }}
          </div>
        </div>
      </div>
    </div>

    <!-- Master-Detail Sections with RevoGrid Sub-tables -->
    <div v-for="rowId in Array.from(expandedRows)" :key="rowId" class="detail-section">
      <div class="detail-content">
        <div class="detail-tables">
          <!-- Sources Table -->
          <div class="sub-table">
            <h4>Sources for {{ getRowData(rowId)?.name }}</h4>
            <div class="sub-grid-container">
              <RevoGrid
                :source="getRowData(rowId)?.sources || []"
                :columns="subTableColumns"
                theme="compact"
                class="sub-revo-grid"
                :readonly="false"
                :can-focus="true"
                :range="true"
                :resize="true"
                :filter="true"
                :auto-size-column="true"
              />
            </div>
          </div>

          <!-- Destinations Table -->
          <div class="sub-table">
            <h4>Destinations for {{ getRowData(rowId)?.name }}</h4>
            <div class="sub-grid-container">
              <RevoGrid
                :source="getRowData(rowId)?.destinations || []"
                :columns="subTableColumns"
                theme="compact"
                class="sub-revo-grid"
                :readonly="false"
                :can-focus="true"
                :range="true"
                :resize="true"
                :filter="true"
                :auto-size-column="true"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.table-container {
  width: 100%;
}

.table-header {
  padding: 1.5rem;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
}

.table-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.table-header p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.main-grid-section {
  margin-bottom: 1rem;
}

.grid-container {
  width: 100%;
  height: 400px;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.revo-grid {
  height: 100%;
  width: 100%;
}

.sub-grid-container {
  width: 100%;
  height: 200px;
  background: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.sub-revo-grid {
  height: 100%;
  width: 100%;
}

/* Overlay expand buttons */
.expand-buttons-overlay {
  position: absolute;
  left: 0;
  top: 0;
  width: 50px;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.expand-button-overlay {
  position: absolute;
  left: 0;
  width: 50px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
  color: #111827;
  pointer-events: auto;
  background: rgba(255, 255, 255, 0.9);
  transition: background-color 0.2s ease;
}

.expand-button-overlay:hover {
  background: rgba(243, 244, 246, 0.95);
}

/* Detail section styling */
.detail-section {
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-top: none;
  padding: 20px;
}

.detail-content {
  width: 100%;
}

.detail-tables {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sub-table {
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  overflow: hidden;
}

.sub-table h4 {
  margin: 0;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}
</style>
