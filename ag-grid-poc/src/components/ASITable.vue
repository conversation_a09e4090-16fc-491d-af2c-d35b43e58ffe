<script setup lang="ts">
import { ref, h, computed } from "vue";
import RevoGrid, { type ColumnRegular } from "@revolist/vue3-datagrid";

// Sample ASI data with expandable rows
const asiRowData = ref([
  {
    id: 1,
    name: "ASI Stream 1",
    bandwidth: "10 Mbps",
    selected: false,
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 1", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 1", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 1", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 1", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 1", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 1", building: "Building F", selected: false },
    ],
  },
  {
    id: 2,
    name: "ASI Stream 2",
    bandwidth: "15 Mbps",
    selected: true,
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 2", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 2", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 2", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 2", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 2", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 2", building: "Building F", selected: false },
    ],
  },
  {
    id: 3,
    name: "ASI Stream 3",
    bandwidth: "8 Mbps",
    selected: false,
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 3", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 3", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 3", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 3", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 3", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 3", building: "Building F", selected: false },
    ],
  },
  {
    id: 4,
    name: "ASI Stream 4",
    bandwidth: "12 Mbps",
    selected: true,
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 4", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 4", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 4", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 4", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 4", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 4", building: "Building F", selected: false },
    ],
  },
  {
    id: 5,
    name: "ASI Stream 5",
    bandwidth: "20 Mbps",
    selected: false,
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 5", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 5", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 5", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 5", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 5", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 5", building: "Building F", selected: false },
    ],
  },
]);

// Track expanded rows
const expandedRows = ref(new Set<number>());

// Add empty expand column for RevoGrid (content handled by overlay)
const asiRowDataWithExpand = computed(() =>
  asiRowData.value.map((row) => ({
    ...row,
    expandButton: "", // Empty - overlay will handle the visual
  }))
);

// Column definitions for RevoGrid with expand column and advanced features
const asiColumns: ColumnRegular[] = [
  {
    prop: "expandButton",
    name: "",
    size: 50,
    pin: "colPinStart",
    readonly: true,
    sortable: false,
    filter: false,
  },
  {
    prop: "id",
    name: "ID",
    size: 80,
    pin: "colPinStart",
    columnType: "number",
    sortable: true,
    filter: true,
  },
  {
    prop: "name",
    name: "Name",
    size: 300,
    columnType: "string",
    sortable: true,
    filter: true,
  },
  {
    prop: "bandwidth",
    name: "Bandwidth",
    size: 150,
    columnType: "string",
    sortable: true,
    filter: true,
  },
];

// Sub-table columns for Sources and Destinations with RevoGrid features
const subTableColumns: ColumnRegular[] = [
  {
    prop: "selected",
    name: "✓",
    size: 40,
    columnType: "boolean",
    sortable: false,
    filter: false,
  },
  {
    prop: "id",
    name: "ID",
    size: 60,
    columnType: "number",
    sortable: true,
    filter: true,
  },
  {
    prop: "name",
    name: "Name",
    size: 200,
    columnType: "string",
    sortable: true,
    filter: true,
  },
  {
    prop: "building",
    name: "Building",
    size: 120,
    columnType: "string",
    sortable: true,
    filter: true,
  },
];

// Toggle row expansion
const toggleExpansion = (rowId: number) => {
  if (expandedRows.value.has(rowId)) {
    expandedRows.value.delete(rowId);
  } else {
    expandedRows.value.add(rowId);
  }
};

// Handle row click to expand/collapse
const handleRowClick = (event: any) => {
  console.log("Row clicked:", event);
  const rowId = event.detail?.model?.id || event.detail?.data?.id;
  if (rowId) {
    toggleExpansion(rowId);
  }
};

// Handle cell click for expand button
const handleCellClick = (event: any) => {
  console.log("Cell clicked:", event);

  // Check if it's the expand button column
  if (event.detail?.prop === "expandButton") {
    const rowId = event.detail?.model?.id || event.detail?.data?.id;
    if (rowId) {
      console.log("Toggling expansion for row:", rowId);
      toggleExpansion(rowId);
    }
  }
};

// Handle general grid click
const handleGridClick = (event: any) => {
  console.log("Grid clicked:", event);
};

// Handle before edit to intercept expand button clicks
const handleBeforeEdit = (event: any) => {
  console.log("Before edit:", event.detail);

  // If it's the expand button column, prevent editing and toggle instead
  if (event.detail?.prop === "expandButton") {
    console.log("Expand button clicked, preventing edit");
    event.preventDefault();
    const rowId = event.detail?.model?.id || event.detail?.data?.id;
    if (rowId) {
      console.log("Toggling expansion for row via beforeedit:", rowId);
      toggleExpansion(rowId);
    }
    return false;
  }
  return true;
};

// Handle after edit to update data
const handleAfterEdit = (event: any) => {
  console.log("After edit:", event);
};

// Simple cell editing function
const editCell = (event: Event, row: any, field: string) => {
  const target = event.target as HTMLElement;
  const currentValue = row[field];

  const input = document.createElement("input");
  input.value = currentValue;
  input.style.width = "100%";
  input.style.border = "2px solid #6b7280";
  input.style.padding = "4px";
  input.style.fontSize = "14px";

  target.innerHTML = "";
  target.appendChild(input);
  input.focus();
  input.select();

  const saveEdit = () => {
    row[field] = input.value;
    target.innerHTML = input.value;
  };

  const cancelEdit = () => {
    target.innerHTML = currentValue;
  };

  input.addEventListener("blur", saveEdit);
  input.addEventListener("keydown", (e) => {
    if (e.key === "Enter") {
      saveEdit();
    } else if (e.key === "Escape") {
      cancelEdit();
    }
  });
};

// Get row data by ID
const getRowData = (rowId: number) => {
  return asiRowData.value.find((row) => row.id === rowId);
};
</script>

<template>
  <div class="table-container">
    <div class="table-header">
      <h2>ASI</h2>
    </div>

    <!-- Custom table with interleaved rows and details -->
    <div class="custom-table-container">
      <!-- Table Header -->
      <div class="custom-table-header">
        <div class="header-cell expand-col"></div>
        <div class="header-cell checkbox-col">✓</div>
        <div class="header-cell id-col">ID</div>
        <div class="header-cell name-col">Name</div>
        <div class="header-cell bandwidth-col">Bandwidth</div>
      </div>

      <!-- Table Body with interleaved rows -->
      <div class="custom-table-body">
        <template v-for="row in asiRowData" :key="row.id">
          <!-- Main Row -->
          <div class="custom-table-row" :class="{ expanded: expandedRows.has(row.id) }">
            <div class="table-cell expand-col" @click="toggleExpansion(row.id)">
              {{ expandedRows.has(row.id) ? "▼" : "▶" }}
            </div>
            <div class="table-cell checkbox-col" @click="row.selected = !row.selected">
              <input type="checkbox" v-model="row.selected" @click.stop />
            </div>
            <div class="table-cell id-col" @dblclick="editCell($event, row, 'id')">
              {{ row.id }}
            </div>
            <div class="table-cell name-col" @dblclick="editCell($event, row, 'name')">
              {{ row.name }}
            </div>
            <div class="table-cell bandwidth-col" @dblclick="editCell($event, row, 'bandwidth')">
              {{ row.bandwidth }}
            </div>
          </div>

          <!-- Detail Row (if expanded) -->
          <div v-if="expandedRows.has(row.id)" class="detail-section">
            <div class="detail-content">
              <div class="detail-tables">
                <!-- Sources Table -->
                <div class="sub-table">
                  <h4>Sources for {{ row.name }}</h4>
                  <div class="sub-grid-container">
                    <RevoGrid
                      :source="row.sources || []"
                      :columns="subTableColumns"
                      theme="compact"
                      class="sub-revo-grid"
                      :readonly="false"
                      :can-focus="true"
                      :range="true"
                      :resize="true"
                      :filter="true"
                      :auto-size-column="true"
                      :row-size="32"
                    />
                  </div>
                </div>

                <!-- Destinations Table -->
                <div class="sub-table">
                  <h4>Destinations for {{ row.name }}</h4>
                  <div class="sub-grid-container">
                    <RevoGrid
                      :source="row.destinations || []"
                      :columns="subTableColumns"
                      theme="compact"
                      class="sub-revo-grid"
                      :readonly="false"
                      :can-focus="true"
                      :range="true"
                      :resize="true"
                      :filter="true"
                      :auto-size-column="true"
                      :row-size="32"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped>
.table-container {
  width: 100%;
}

.table-header {
  padding: 0rem 1.5rem;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
}

.table-header h2 {
  margin: 0 0 0 0;
  color: #2c3e50;
  font-size: 1.2rem;
  line-height: 3.5rem;
  font-weight: 600;
}

.table-header p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

/* Custom table styling */
.custom-table-container {
  width: 100%;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  overflow: hidden;
  background: white;
}

.custom-table-header {
  display: grid;
  grid-template-columns: 50px 40px 80px 1fr 150px;
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  font-size: 14px;
  color: #111827; /* gray-900 */
}

.header-cell {
  padding: 12px 8px;
  border-right: 1px solid #dee2e6;
  display: flex;
  align-items: center;
}

.header-cell:last-child {
  border-right: none;
}

.custom-table-body {
  background: white;
}

.custom-table-row {
  display: grid;
  grid-template-columns: 50px 40px 80px 1fr 150px;
  border-bottom: 1px solid #dee2e6;
  transition: background-color 0.2s ease;
}

.custom-table-row:hover {
  background-color: #f8f9fa;
}

.custom-table-row.expanded {
  background-color: #f9fafb; /* gray-50 equivalent */
  color: #111827; /* gray-900 equivalent */
}

.table-cell {
  padding: 12px 8px;
  border-right: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #111827; /* gray-900 equivalent */
  cursor: pointer;
}

.table-cell:last-child {
  border-right: none;
}

.expand-col {
  justify-content: center;
  font-weight: bold;
  user-select: none;
}

.expand-col:hover {
  background-color: #f3f4f6; /* gray-100 equivalent */
}

.checkbox-col {
  justify-content: center;
  cursor: pointer;
}

.checkbox-col input[type="checkbox"] {
  cursor: pointer;
  transform: scale(1.2);
}

.id-col {
  font-weight: 600;
}

.name-col {
  font-weight: 500;
}

.bandwidth-col {
  color: #6c757d;
}

.sub-grid-container {
  width: 100%;
  min-height: 100px;
  max-height: 300px;
  background: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  overflow: hidden;
}

.sub-revo-grid {
  width: 100%;
  min-height: 100px;
}

/* Detail section styling */
.detail-section {
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-top: none;
  padding: 20px;
}

.detail-content {
  width: 100%;
}

.detail-tables {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sub-table {
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  overflow: hidden;
}

.sub-table h4 {
  margin: 0;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}
</style>
