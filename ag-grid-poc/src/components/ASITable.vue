<script setup lang="ts">
import { ref, h } from "vue";
import RevoGrid, { type ColumnRegular } from "@revolist/vue3-datagrid";

// Sample ASI data with expandable rows
const asiRowData = ref([
  {
    id: 1,
    name: "ASI Stream 1",
    bandwidth: "10 Mbps",
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 1", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 1", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 1", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 1", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 1", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 1", building: "Building F", selected: false },
    ],
  },
  {
    id: 2,
    name: "ASI Stream 2",
    bandwidth: "15 Mbps",
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 2", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 2", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 2", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 2", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 2", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 2", building: "Building F", selected: false },
    ],
  },
  {
    id: 3,
    name: "ASI Stream 3",
    bandwidth: "8 Mbps",
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 3", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 3", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 3", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 3", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 3", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 3", building: "Building F", selected: false },
    ],
  },
  {
    id: 4,
    name: "ASI Stream 4",
    bandwidth: "12 Mbps",
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 4", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 4", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 4", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 4", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 4", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 4", building: "Building F", selected: false },
    ],
  },
  {
    id: 5,
    name: "ASI Stream 5",
    bandwidth: "20 Mbps",
    sources: [
      { id: 1, name: "Source 1 for ASI Stream 5", building: "Building A", selected: false },
      { id: 2, name: "Source 2 for ASI Stream 5", building: "Building B", selected: false },
    ],
    destinations: [
      { id: 1, name: "Dest 1 for ASI Stream 5", building: "Building C", selected: false },
      { id: 2, name: "Dest 2 for ASI Stream 5", building: "Building D", selected: false },
      { id: 3, name: "Dest 3 for ASI Stream 5", building: "Building E", selected: false },
      { id: 4, name: "Dest 4 for ASI Stream 5", building: "Building F", selected: false },
    ],
  },
]);

// Column definitions for RevoGrid
const asiColumns: ColumnRegular[] = [
  {
    prop: "id",
    name: "ID",
    size: 80,
    pin: "colPinStart",
  },
  {
    prop: "name",
    name: "Name",
    size: 300,
  },
  {
    prop: "bandwidth",
    name: "Bandwidth",
    size: 150,
  },
];

// Track expanded rows
const expandedRows = ref(new Set<number>());

// Toggle row expansion
const toggleExpansion = (rowId: number) => {
  if (expandedRows.value.has(rowId)) {
    expandedRows.value.delete(rowId);
  } else {
    expandedRows.value.add(rowId);
  }
};

// Handle row click to expand/collapse
const handleRowClick = (event: any) => {
  const rowId = event.detail.model.id;
  toggleExpansion(rowId);
};

// Get row data by ID
const getRowData = (rowId: number) => {
  return asiRowData.value.find((row) => row.id === rowId);
};
</script>

<template>
  <div class="table-container">
    <div class="table-header">
      <h2>ASI Streams</h2>
      <p>Click on a row to expand and view Sources and Destinations</p>
    </div>

    <div class="grid-container">
      <RevoGrid
        :source="asiRowData"
        :columns="asiColumns"
        theme="compact"
        class="revo-grid"
        @rowclick="handleRowClick"
      />
    </div>

    <!-- Expandable detail sections -->
    <div v-for="rowId in Array.from(expandedRows)" :key="rowId" class="detail-section">
      <div class="detail-content">
        <div class="detail-tables">
          <!-- Sources Table -->
          <div class="sub-table">
            <h4>Sources</h4>
            <div class="sub-table-content">
              <div class="sub-table-header">
                <div class="checkbox-col"></div>
                <div class="id-col">ID</div>
                <div class="name-col">Name</div>
                <div class="building-col">Building</div>
              </div>
              <div
                v-for="source in getRowData(rowId)?.sources"
                :key="source.id"
                class="sub-table-row"
              >
                <div class="checkbox-col">
                  <input type="checkbox" v-model="source.selected" />
                </div>
                <div class="id-col">{{ source.id }}</div>
                <div class="name-col">{{ source.name }}</div>
                <div class="building-col">{{ source.building }}</div>
              </div>
            </div>
          </div>

          <!-- Destinations Table -->
          <div class="sub-table">
            <h4>Destinations</h4>
            <div class="sub-table-content">
              <div class="sub-table-header">
                <div class="checkbox-col"></div>
                <div class="id-col">ID</div>
                <div class="name-col">Name</div>
                <div class="building-col">Building</div>
              </div>
              <div
                v-for="destination in getRowData(rowId)?.destinations"
                :key="destination.id"
                class="sub-table-row"
              >
                <div class="checkbox-col">
                  <input type="checkbox" v-model="destination.selected" />
                </div>
                <div class="id-col">{{ destination.id }}</div>
                <div class="name-col">{{ destination.name }}</div>
                <div class="building-col">{{ destination.building }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.table-container {
  width: 100%;
}

.table-header {
  padding: 1.5rem;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
}

.table-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.table-header p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.grid-container {
  width: 100%;
  height: 400px;
}

.revo-grid {
  height: 100%;
  width: 100%;
}

/* Detail section styling */
.detail-section {
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-top: none;
  padding: 20px;
}

.detail-content {
  width: 100%;
}

.detail-tables {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sub-table {
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  overflow: hidden;
}

.sub-table h4 {
  margin: 0;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.sub-table-content {
  font-size: 12px;
}

.sub-table-header {
  display: grid;
  grid-template-columns: 40px 60px 1fr 120px;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f1f3f4;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
  color: #495057;
}

.sub-table-row {
  display: grid;
  grid-template-columns: 40px 60px 1fr 120px;
  gap: 8px;
  padding: 8px 12px;
  border-bottom: 1px solid #f1f3f4;
  align-items: center;
}

.sub-table-row:last-child {
  border-bottom: none;
}

.sub-table-row:hover {
  background-color: #f8f9fa;
}

.checkbox-col {
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkbox-col input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.id-col,
.building-col {
  text-align: left;
}

.name-col {
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* RevoGrid row hover effect */
:deep(.revo-grid .rgRow:hover) {
  background-color: #f8f9fa;
  cursor: pointer;
}

/* RevoGrid header styling */
:deep(.revo-grid .rgHeaderCell) {
  font-weight: 600;
  background-color: #f8f9fa;
  border-bottom: 2px solid #007bff;
}
</style>
