<script setup lang="ts">
import { ref, onMounted, h } from "vue";
import { AgGridVue } from "ag-grid-vue3";
import type { ColDef, GridOptions, ICellRendererParams } from "ag-grid-community";
import DetailPanel from "./DetailPanel.vue";

// Sample ASI data
const asiRowData = ref([
  { id: 1, name: "ASI Stream 1", bandwidth: "10 Mbps" },
  { id: 2, name: "ASI Stream 2", bandwidth: "15 Mbps" },
  { id: 3, name: "ASI Stream 3", bandwidth: "8 Mbps" },
  { id: 4, name: "ASI Stream 4", bandwidth: "12 Mbps" },
  { id: 5, name: "ASI Stream 5", bandwidth: "20 Mbps" },
]);

// Column definitions for ASI table
const asiColumnDefs: ColDef[] = [
  {
    field: "id",
    headerName: "ID",
    width: 80,
    sortable: true,
    pinned: "left",
  },
  {
    field: "name",
    headerName: "Name",
    flex: 1,
    sortable: true,
    filter: true,
  },
  {
    field: "bandwidth",
    headerName: "Bandwidth",
    width: 150,
    sortable: true,
    filter: true,
  },
];

// Custom detail cell renderer component
const DetailCellRenderer = (params: ICellRendererParams) => {
  return h(DetailPanel, {
    parentData: params.data,
    tableType: "ASI",
  });
};

// Grid options with master-detail configuration
const asiGridOptions: GridOptions = {
  columnDefs: asiColumnDefs,
  rowData: asiRowData.value,
  masterDetail: true,
  detailCellRenderer: DetailCellRenderer,
  detailRowHeight: 300,
  animateRows: true,
  suppressRowClickSelection: true,
  rowSelection: "single",
  headerHeight: 50,
  rowHeight: 50,
  // Enable pinned headers
  floatingFilter: false,
  suppressMenuHide: true,
  // Pagination
  pagination: true,
  paginationPageSize: 10,
};

const gridApi = ref<any>(null);

const onGridReady = (params: any) => {
  gridApi.value = params.api;
};
</script>

<template>
  <div class="table-container">
    <div class="table-header">
      <h2>ASI Streams</h2>
      <p>Click the expand icon to view Sources and Destinations for each stream</p>
    </div>

    <div class="ag-theme-alpine grid-container">
      <AgGridVue
        :gridOptions="asiGridOptions"
        :rowData="asiRowData"
        :columnDefs="asiColumnDefs"
        @grid-ready="onGridReady"
        class="ag-grid"
      />
    </div>
  </div>
</template>

<style scoped>
.table-container {
  width: 100%;
}

.table-header {
  padding: 1.5rem;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
}

.table-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.table-header p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.grid-container {
  height: 500px;
  width: 100%;
}

.ag-grid {
  height: 100%;
  width: 100%;
}

/* Custom styling for AG Grid */
:deep(.ag-header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #007bff;
  font-weight: 600;
}

:deep(.ag-header-cell) {
  border-right: 1px solid #dee2e6;
}

:deep(.ag-row) {
  border-bottom: 1px solid #f1f3f4;
}

:deep(.ag-row:hover) {
  background-color: #f8f9fa;
}

:deep(.ag-row-selected) {
  background-color: #e3f2fd !important;
}

:deep(.ag-detail-row) {
  background-color: #f8f9fa;
}

/* Pinned header styling */
:deep(.ag-pinned-left-header) {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
}

:deep(.ag-cell-focus) {
  border: 2px solid #007bff;
}

/* Master detail expand/collapse button styling */
:deep(.ag-group-expanded) {
  color: #007bff;
}

:deep(.ag-group-contracted) {
  color: #6c757d;
}
</style>
