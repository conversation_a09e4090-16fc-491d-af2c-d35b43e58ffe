@import "./base.css";

#app {
  width: 100%;
  margin: 0;
  padding: 0;
  font-weight: normal;
}

/* AG Grid specific styles for better pinned headers */
.ag-theme-alpine {
  --ag-header-height: 50px;
  --ag-row-height: 50px;
}

/* Ensure pinned headers stay on top */
.ag-theme-alpine .ag-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.ag-theme-alpine .ag-pinned-left-header {
  z-index: 101;
}

.ag-theme-alpine .ag-pinned-right-header {
  z-index: 101;
}

/* Custom scrollbar for better UX */
.ag-theme-alpine .ag-body-viewport::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ag-theme-alpine .ag-body-viewport::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.ag-theme-alpine .ag-body-viewport::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.ag-theme-alpine .ag-body-viewport::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
