<script setup lang="ts">
import { ref } from "vue";
import ASITable from "./components/ASITable.vue";
import H264Table from "./components/H264Table.vue";
</script>

<template>
  <div class="app-container">
    <main class="main-content">
      <div class="table-section">
        <ASITable />
      </div>

      <div class="table-section">
        <H264Table />
      </div>
    </main>
  </div>
</template>

<style scoped>
.app-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.table-section {
  margin-bottom: 3rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
</style>
