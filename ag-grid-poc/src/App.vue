<script setup lang="ts">
import { ref } from "vue";
import ASITable from "./components/ASITable.vue";
import H264Table from "./components/H264Table.vue";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
</script>

<template>
  <div class="app-container">
    <header class="app-header">
      <h1>AG Grid Master-Detail POC</h1>
      <p>Vue.js with AG Grid featuring pinned headers and master-detail functionality</p>
    </header>

    <main class="main-content">
      <div class="table-section">
        <ASITable />
      </div>

      <div class="table-section">
        <H264Table />
      </div>
    </main>
  </div>
</template>

<style scoped>
.app-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 300;
}

.app-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.main-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.table-section {
  margin-bottom: 3rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
</style>
