import "./assets/main.css";

import { createApp } from "vue";
import { createPinia } from "pinia";

// AG Grid module registration and theme setup
import { ModuleRegistry, AllCommunityModule, themeQuartz } from "ag-grid-community";
ModuleRegistry.registerModules([AllCommunityModule]);

// Create custom theme
export const myTheme = themeQuartz.withParams({
  browserColorScheme: "inherit",
  fontFamily: {
    googleFont: "Inter",
  },
  headerFontSize: 14,
});

import App from "./App.vue";
import router from "./router";

const app = createApp(App);

app.use(createPinia());
app.use(router);

app.mount("#app");
